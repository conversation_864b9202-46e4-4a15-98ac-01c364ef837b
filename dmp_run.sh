#!/bin/bash
set -e
: ${TZ:=Asia/Shanghai}
ln -snf /usr/share/zoneinfo/$TZ /etc/localtime
echo $TZ > /etc/timezone
if [ $? == 0 ]
then
	if [ "$RUNC"x == celeryx ]
	then
    cd /home/<USER>/webapp
    celery -A app_celery worker --loglevel=INFO --concurrency=1 -n worker@%h $GOSSIP  -P gevent
 	else
 	  cd /home/<USER>/webapp
 	  if [ "$FAST_ENABLE" == true ]
    then
      FastTracker_ConfigPath=FastTracker.json \
      FastTracker_Enable=${FAST_ENABLE} \
      FastTracker_EnvCode=${FAST_ENV_CODE:-prod} \
      FastTracker_ProductCode=${FAST_PRODUCT_CODE:-dmp} \
      FastTracker_AppCode=${FAST_APP_CODE} \
      fast-boot run-program \
      gunicorn app:__hug_wsgi__ -c gunicorn.py
    else
      gunicorn app:__hug_wsgi__ -c gunicorn.py
    fi
	fi
fi
