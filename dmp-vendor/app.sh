#!/bin/bash
set -e
/dmp-agent/agent
if [ $? == 0 ]
then
	if [ "$RUNC"x == celeryx ]
	then
    cd /home/<USER>/webapp
    celery -A app_celery worker --loglevel=ERROR --concurrency=1 -n worker@%h
 	else
	export APPHOME=/home/<USER>/webapp && gunicorn app:__hug_wsgi__ --reload --timeout=$TIMEOUT \
	    --env prometheus_multiproc_dir=/tmp/prom-metrics-dir \
	    --config=$APPHOME/gunicorn.py \
	    --bind=$APP_BIND -w $APP_WORKS --log-level $APP_LOG_LEVEL -k gevent --pythonpath $APPHOME
	fi
fi
