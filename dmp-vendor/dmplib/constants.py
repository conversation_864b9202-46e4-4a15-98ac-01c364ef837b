# -*- coding: utf-8 -*-

DATA_CLEAN_PRIMARY_KEY_NAME = "master_id"
DATA_CLEAN_PRIMARY_TABLE_NAME = "fact_master"
DATA_CLEAN_ORGANIZATION_TABLE_NAME = "dim_organization"
DATA_CLEAN_ORGANIZATION_CODE_TABLE_NAME = "dim_organization_code"
DATA_CLEAN_ORGANIZATION_KEY_NAME = "org_id"
DATA_CLEAN_LABEL_360_TABLE_NAME = "label_360"
DATA_CLEAN_DEFAULT_360_FLOW_ID = "00000000-1111-2222-1111-000000000000"
DATA_COMPONENT_DEMO_TABLE_NAME = "dashboard_chart_demo"
DATA_DASHBOARD_CHART_LABEL_DETAIL_CODE = "label_detail"

DATA_SOURCE_ODPS_ID = "00000000-1111-1111-1111-000000000000"
DATA_SOURCE_RDS_ID = "00000000-1111-1111-2222-000000000000"
DATA_SOURCE_ERP_ID = "00000000-1111-1111-3333-000000000000"

LABEL_TABLE_NAME_PREFIX = 'label'
LABEL_DETAIL_TABLE_NAME_SUFFIX = 'detail'
LABEL_STATISTICS_TABLE_NAME_SUFFIX = 'statistics'
LABEL_CHECK_TABLE_NAME_SUFFIX = 'check'

TENANT_CONFIG_TABLE_NAME = "tenant_for_dmp"

# 产品环境代码的环境变量key
ENV_CODE_KEY = 'CONFIG_AGENT_CLIENT_CODE'

# 环境变量区分当前是celery中运行还是dmp web
ENV_CELERY_APP_KEY = 'CELERY_APP'

# 系统默认的超级用户组
ADMINISTRATORS_GROUP_ID = "00000000-0000-0000-1111-000000000000"
ADMINISTRATORS_GROUP_CODE = '0001-'
# 系统默认的用户组
DEFAULT_GROUP_ID = "00000000-0000-0000-1000-000000000000"
DEFAULT_GROUP_CODE = '1111-'

ADMINISTRATORS_ID = "22b11db4-e907-4f1f-8835-b9daab6e1f23"
ADMINISTRATORS_AUTHORITY_GROUP_ID = "00000000-0000-1111-1111-000000000000"

# 超级管理员角色
ADMIN_ROLE_ID = "00000001-0000-0000-0000-000000000001"

SSH_LOCAL_BIND_ADDRESS_IP = "0.0.0.0"  # NOSONAR
MYSQL_LOCAL_SOURCE_DB_ID = "127.0.0.1"  # NOSONAR

LOOPBACK_ADDRESS = '127.0.0.1'  # NOSONAR
ALL_ZERO_ADDRESS = '0.0.0.0'  # NOSONAR

# 数据集配置项 #####

DATASET_TABLE_NAME_PREFIX = 'dataset'


# 阿里云ESC API 接口
ALIYUN_ECS_API_URL = "http://***************/latest/meta-data"  # NOSONAR


# 证书服务地址
LICENSE_SERVER = 'https://ls.mypaas.com.cn/api/license/verify'
LICENSE_TEST_SERVER = 'https://ls.mypaas.com.cn/api/license/verify'

# 阿里日志存储
LOG_ACCESS_KEY_ID = 'LTAIzU45RjFe84dF'
LOG_ACCESS_KEY_SECRET = '88Nnx38HS8gUwTJ3A8MnnP9iu7itrx'
LOG_ENDPOINT = 'cn-hangzhou.log.aliyuncs.com'
LOG_PROJECT = 'dmp-app'
LOG_LOGSTORE = 'dmp_flow'
LOG_LOGSTORE_FEED = 'dmp_feed'
LOG_PROFILE_STORE = 'dmp_py_profile'

# redis模式
REDIS_SENTINEL = 'sentinel'
REDIS_STANDALONE = 'standalone'
REDIS_SINGLE = 'single'
REDIS_CLUSTER = 'cluster'

