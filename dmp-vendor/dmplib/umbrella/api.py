#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : env_detect.py
# @Author: guq
# @Date  : 2021/5/25
# @Desc  :

import os
import builtins
from collections import OrderedDict

import xmltodict

from .core import exceptions
from .validator.license_validator import License2Validator
from .validator.license_dog.validator import LicenseDogValidator
import xml.etree.ElementTree as ET
from .core.tools import Tools

__all__ = ['LicenseApi']


class LicenseApi:
    def __init__(self):
        self.license_path = None
        self.license_dog_path = None
        self.dog_type_from_env = None

    # 设置license.xml文件路径
    def set_license_path(self, license_path):
        if not os.path.exists(license_path):
            raise exceptions.LicenseNotFoundError('license文件未找到!')

        self.license_path = license_path
        return self

    # 设置licenseDog.xml文件路径
    def set_license_dog_path(self, license_dog_path):
        if not os.path.exists(license_dog_path):
            raise exceptions.LicenseNotFoundError('licenseDog文件未找到!')

        self.license_dog_path = license_dog_path
        return self

    # 设置狗类型
    def set_dog_type(self, dog_type):
        self.dog_type_from_env = dog_type
        return self

    # 校验所有的license
    def do_validate(self):
        self.validate_license()
        self.validate_license_dog()

    # 校验产品license.xml信息
    def validate_license(self):
        # 校验产品license信息
        license_validator = License2Validator(self.license_path)
        license_validator.set_dog_type(self.dog_type_from_env)
        license_validator.validate_all()

    # 校验产品licenseDog.xml信息
    def validate_license_dog(self):
        # ！校验licenseDog信息， 校验licenseDog必须同时指定license2.xml以及licenseDog.xml
        if any([not self.license_path, not self.license_dog_path]):
            raise exceptions.LicenseNotFoundError('校验licenseDog必须同时指定license2.xml以及licenseDog.xml路径！')

        license_dog_validator = LicenseDogValidator(self.license_dog_path)
        license_dog_validator.set_dog_type(self.dog_type_from_env)
        license_dog_validator.set_license2(license2_obj=License2Validator(self.license_path))
        license_dog_validator.validate_all()

    def load_license(self):
        # 从license中载入产品信息
        return License2Validator(self.license_path).root

    def load_shujian_license(self):
        # 只载入我们自己的产品配置信息
        """
        <system enabled="1" name="天际开放平台" code="Skyline">
            <subsystem enabled="1" name="数见" code="shujian" version="5.0.0.0" expires="2022-01-01">
              <resource name="reportNumber" bgndate="2021-01-01" enddate="2021-12-31" value="100" />
              <resource name="reportNumber" bgndate="2022-01-01" enddate="2023-12-31" value="100" />
            </subsystem>
        </system>
        """
        # OrderedDict([('subsystem',
        #               OrderedDict([('@code', 'shujian'),
        #                            ('@enabled', '1'),
        #                            ('@expires', '2022-01-01'),
        #                            ('@name', '数见'),
        #                            ('@version', '5.0.0.0'),
        #                            ('resource',
        #                             [OrderedDict([('@bgndate', '2021-01-01'),
        #                                           ('@enddate', '2021-12-31'),
        #                                           ('@name', 'reportNumber'),
        #                                           ('@value', '100')]),
        #                              OrderedDict([('@bgndate', '2022-01-01'),
        #                                           ('@enddate', '2023-12-31'),
        #                                           ('@name', 'reportNumber'),
        #                                           ('@value', '100')])])]))])
        license_root = self.load_license()
        # product_conf = {}
        subsystem_code = license_root.find('.//subsystem[@name="数见"]')
        if subsystem_code is not None:
            # 是数见的产品
            data_xml = ET.tostring(subsystem_code, encoding='utf8', method='xml').decode('utf8')  # type: str
            product_conf = xmltodict.parse(data_xml)
            if product_conf['subsystem']['@enabled'] != '1':
                raise exceptions.PermissionError("权限不可用：当前产品功能被设置为未开启!")

            now = Tools.now().strftime('%Y-%m-%d %H:%M:%S')
            bgndate = product_conf['subsystem']['@bgndate']
            enddate = product_conf['subsystem']['@enddate']

            if now < '%s 00:00:00' % bgndate or now >= '%s 23:59:59' % enddate:
                builtins.license_validate['is_invalid'] = True
                # raise exceptions.PermissionError("权限不可用：当前产品未到开启时间!")
            # elif now >= '%s 23:59:59' % enddate:
            #     builtins.license_validate['is_invalid'] = True
                # raise exceptions.PermissionError("权限不可用：当前产品功能已经过期!")
        else:
            raise exceptions.NotFoundError("资源未找到：未从license中找到产品信息，请检查license文件！")
        return product_conf

    @staticmethod
    def valid_report_nums(product_conf):
        # xmltodict 当取出只有一个元素的时候会出现直接取出属性, 判断下总数
        resource = product_conf['subsystem']['resource']
        resource = [resource] if isinstance(resource, OrderedDict) else resource
        reports = [i for i in resource if i['@name'] == 'reportNumber']
        valid_num = LicenseApi.calc_valid_report_nums_v2(reports)
        return valid_num

    # # 计算出当前有效期内的报表数
    # def calc_valid_report_nums(self, reports: list) -> int:
    #     now = Tools.now()
    #     now = now.strftime('%Y-%m-%d %H:%M:%S')
    #
    #     valid_list = []
    #     for report in reports:
    #         st = '%s 00:00:00' % report['@bgndate']
    #         ed = '%s 23:59:59' % report['@enddate']
    #         if st < now < ed:
    #             num = int(report['@value'])
    #         else:
    #             num = 0
    #         valid_list.append(num)
    #     return sum(valid_list)

    # 计算出当前有效期内的报表数
    @staticmethod
    def calc_valid_report_nums_v2(reports: list) -> int:
        valid_list = []
        for report in reports:
            num = int(report['@value'])
            valid_list.append(num)
        return sum(valid_list)
