#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : pub_keys.py
# @Author: guq  
# @Date  : 2021/5/24
# @Desc  :


# /// <summary>
# /// 各种验证服务的证书公钥
# /// </summary>
CloudShake = """-----BEGIN CERTIFICATE-----
MIIDJDCCAgygAwIBAgIQb2hQGlNmxZ1HaBJMiGPRaTANBgkqhkiG9w0BAQ0FADAh
****************************************************************
MFoYDzIwOTkxMjMxMTYwMDAwWjAhMR8wHQYDVQQDExZNeW9zZnQtQ2xvdWRTaGFr
ZS1DRVJUMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApFBzjjkWQd9k
Kh2IXhAibrHixJQo6u7Dc9BMyMJYgzSzLIuWZuwIdB61UCJTKnw3YmK9RbEntrct
cCBl6mYFvlSnaHk9AjaAgVoOah5+6O13fVUFwhRIgcFrTxuIBZ1wK/rLMEkv5Hk2
AQ4JL0eCxhWWaWyexhbfF7PCmbzMEvbUHpQ0jbvFXtgRKdaLSpbGMuG8dCjCfL56
kTmi9aJl9mRs3VVbm2RWgq1zoyoa9YSDO8DkSnd9Tj8sfzo1TehSFJoUYeMYY16l
bD30ybFw5Nl9+Zb2qxNizImc4AoKgiNDahiLMXoOtBOT3ncT7V+1HrLkf6/1QJC1
kqSrZm7MFwIDAQABo1YwVDBSBgNVHQEESzBJgBCTIAvmRCN06FXnnGOtq0UGoSMw
ITEfMB0GA1UEAxMWTXlvc2Z0LUNsb3VkU2hha2UtQ0VSVIIQb2hQGlNmxZ1HaBJM
iGPRaTANBgkqhkiG9w0BAQ0FAAOCAQEAkep12baEZ8dI5TvIDG0bNzH16QAFWujF
aWfiRXGked8xscb9FTYzFYd7I+jhY/uhhn5CQ8YBhXswlj2L66iJhCg/13OtBL+I
JSLuaBNkJAavtlapmX4pEuK/tH5UtrhGNVwM8ju/WpymuOmRuqRgUKVGQt0vOabD
pOsvmTvNWPu9oWsHXaixHW2j6SitCBxG+8f5/IXUFS9dEowOSml+Gu1NW/V+QGds
RBEcc3t5TS507tkQohLGNajDFXnxjj27Cy7iqlGIlh90HvFgpTY+ICIJ7QJ+0GTq
LxE+YLvsij8GWmabhvHLQmg5WHXVWNY03JvDweHL3dNTf9tn0IgibA==
-----END CERTIFICATE-----
"""

# /// <summary>
# /// 狗证书公钥
# /// </summary>
DogCertificate = """-----BEGIN CERTIFICATE-----
MIIDJDCCAgygAwIBAgIQmkVq2qCGgaRJ+l5g2PqadDANBgkqhkiG9w0BAQ0FADAh
****************************************************************
MFoYDzIwOTkxMjMxMTYwMDAwWjAhMR8wHQYDVQQDExZNeW9zZnQtTmV3RG9nU2ln
bi1DRVJUMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzGo1KFXEgX3Z
J3Dh0H/9ZuKbjMfJ2De1KrnoEG+kBQNmSdapIHsD1Cp0+ZizFaTnQHtX+qW2Nu6p
oKMDqNS7wySiVHFlv9ak1GP2C6TPvLp3BlSpeOHtTbq9Q9KK0mviVmSurU9K3i2j
F9hIdm+x8N3E7itIZUUU3XUYPvVt/6fVKINx8izG0EPnVlSo0dDbKj4vjTi82FQu
z17cKXkTDmVF5D4HK6Ol/LpRsU9y6kR2i3FOaOxWOS4MlhA+9R3dbLGDFFC0OHpy
nmfCQX87ZncfDK3aHfvP0wee6bk8TlY32JCUKdcM+sK63ExrUk7FDi+yPJ9Zvihg
p2FZF6FGJQIDAQABo1YwVDBSBgNVHQEESzBJgBBIYpZnKTBMuJpcGqglBaaCoSMw
ITEfMB0GA1UEAxMWTXlvc2Z0LU5ld0RvZ1NpZ24tQ0VSVIIQmkVq2qCGgaRJ+l5g
2PqadDANBgkqhkiG9w0BAQ0FAAOCAQEAulK1/s/3OPGL6VOzEWCr5kqOam+OXvfL
GDEBb1Kk4sQzXmzJUItGrjbKzh9ylKAgb3voYboy+R0Y5cUThmdSRseyWxiJY6rU
coIAxUooaLxDeNg0er/8HMoU4WfoygfnCgmTDI2rdGh9fSvRzCWZSe4amR+lP/Vv
vy3Ttrbqtpwfz5Bovp4voapIuY6x7KHpVv5ah8iRpmxtedD92GZaclt1hoKa2YHP
smmsknFXsZ5kQThvow3IWAkLfQrWq9Cw8i0fMTXVAGaugj6F+MhD4ZGj70yKEEAT
4TbycalXtOjmIt+ZufjKj+Ng7OK7UoVlJCPF+Eb7z7P1J1ZfT0JEpg==
-----END CERTIFICATE-----
"""

# /// <summary>
# /// License签名公钥
# /// </summary>

LicenseSign = """-----BEGIN CERTIFICATE-----
MIIDJzCCAg+gAwIBAgIQI9IJqsqDa51GuvF5CI6LejANBgkqhkiG9w0BAQ0FADAi
****************************************************************
MDBaGA8yMDk5MTIzMTE2MDAwMFowIjEgMB4GA1UEAxMXTXlvc2Z0LUxpY2Vuc2VT
aWduLUNFUlQwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCgvgR4pvO2
JCjkr3DJ3sU6xtTywtX5eqMg+dWKIx5hyTmwJU6LdXsd8diZ804p+PyKDk97h6Uq
O35muM3pFTQT52oNgYOXsZCYhPtLJqOJNzga/pBTuPcjsY8A8sSbJdo2pz1ghSXi
ai2cbtFDC4VZJ78MBvhv1cq/ZHSid/12zpWMyzv9r/vfP+hoLyCdlpWk01iNhF0l
528ZDHd8++FOVQgLsrQIu+7tXz5ZgBo3fs2ajrESnUPYjhfj6TVV8UremobbGR6R
vcKQ4si6KAmS0UvGglr8es33u9kVbCCK1kuY8Gef4c1+/REBSKeJxlwWWcsj+V60
jItmUcAihPINAgMBAAGjVzBVMFMGA1UdAQRMMEqAENORXKfkVSkQ3kUGKfTQuoeh
JDAiMSAwHgYDVQQDExdNeW9zZnQtTGljZW5zZVNpZ24tQ0VSVIIQI9IJqsqDa51G
uvF5CI6LejANBgkqhkiG9w0BAQ0FAAOCAQEAJBneumS0hk3xhZ2YEN5GKk4tKS1l
ZCHKYkWR7RQlNJAKV7e63kS21XWroi++bFPmnr/1NcaCla4Cl4eE6o47Ce0nZt85
x5bvgzMI5b3P7ZokyRxd82/YCH/QF06WAx+B48nEbHFOBEgEsdKKAqQ9OwBnmoa8
rvRVkTsz3tROK5g/FlGTS8NwwFFLlYPquNIRGvLNE+uE8oPuKtfJpVvyxsRfl2nB
HMQKBelLfq/VzqwqpEPW/x17Z9I45sfELELdbwBiH7b9K1798XVPsBWV7J6buF8d
CAkDPMX6WHFoq5R21+oP6YM5J2aRhYJv1zCeVq4yzMnF5/EKUtU+482QEA==
-----END CERTIFICATE-----
"""
# /// <summary>
# /// 网络开发狗验证服务公钥
# /// </summary>
DeveloperDog = """-----BEGIN CERTIFICATE-----
MIIDKjCCAhKgAwIBAgIQA/ryveCTR7BHN2rKRQXQ6TANBgkqhkiG9w0BAQ0FADAj
MSEwHwYDVQQDExhNeW9zZnQtRGV2ZWxvcGVyRG9nLUNFUlQwIBcNMDQxMjMxMTYw
MDAwWhgPMjA5OTEyMzExNjAwMDBaMCMxITAfBgNVBAMTGE15b3NmdC1EZXZlbG9w
ZXJEb2ctQ0VSVDCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKwgpMDD
IM0O9rZTrRa7qm0Cw6a+hcxJEGgC3+l39nwnPgvTwXZ74FWDtdT8JHr3Rzma/JfQ
+cBowPqLwrbGQHAbrTDfGmFmA9uGfhDh+RSpGhY3hHKt9/88tDfna7gdH/GZbiDN
eR/xMN2tcOYThHYIa5ZAv2Lhy7DuRfR1xdv9mQQpmP5Cm8TS1a5rMvOrs/g03Lqu
ZSiFPEXVfSaTo0gu4j8fJHNjM7A5F/7PeTqxWDDaMfHi5pRdfgYt5panWJqh+cOB
M0pX4qnGGblNJYaP2fCgnPy2+bOq+LJvUhp95moWWTkIk/kUCDZ8zKzJHu27R3Je
OkekWb8i0p2pMhUCAwEAAaNYMFYwVAYDVR0BBE0wS4AQrG5CTJq/YZJVWzvBQJdo
yKElMCMxITAfBgNVBAMTGE15b3NmdC1EZXZlbG9wZXJEb2ctQ0VSVIIQA/ryveCT
R7BHN2rKRQXQ6TANBgkqhkiG9w0BAQ0FAAOCAQEAMBiCsH73b16XKysuj/vebYVJ
KT1Hife/ngX4wkCAr8D+pVkcR4kZkSsufLxRaRwqjSja1Fkl/J/YcDqORg0/gw5q
/jW9xOGDY6esjkYXDAFY0nR3Lg6YQGRSLoCn/iV2r1YbcqYXVxACW6qpPB3VNEtc
Jbhdezb8McgEMQVDomDvnWc2eI0M3ti1NQ2vaepBMIU7bild6pTR8IFMT2WifDef
BpRP0U+nwK5ZJZC8JsEbNz3HnEfwalOvU9L67ZCJCBAVMJXVyG4yY4SQh0zZYqN3
I2HO3mnffKR5n/7iWU8lGJYf+VImUKdIOtsUhxTkdGis5A7JED72dXMFqjn4bQ==
-----END CERTIFICATE-----
"""

# // / < summary >
# // / 软件狗Rsa公钥
# // / < / summary >
SoftDogRsa = """-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDerJa0ybEMC63m4voF5nDjc2fg
di6HHxpSXN7AV/bXRtsf6sZ/02t1gqwsgb1V54E12ck+QshEBlP+qZpkbksePN3s
e8T74AdHTWLDMzHkDCnFP2uAjmtctc51EKdMGqwPCEgRbLM+lwc+eli+8e0UQ9V3
7UeYrioBphfj7ZfsWQIDAQAB
-----END PUBLIC KEY-----
"""
