#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : dog_hardware_validator.py
# @Author: guq
# @Date  : 2021/5/24
# @Desc  :
import datetime

import requests
from ..core import exceptions
from ..core.dog_type import DogType
from ..core.base import BaseValidator, BaseFactory
from ..dog.product_dog.adaptor import ProductDogAdaptor
from ..dog.develop_dog.adaptor import DevelopDogAdaptor
from ..dog.saas_dog.adaptor import SaasDogAdaptor
from ..dog.cloud_dog.adaptor import CloudDogAdaptor


class HardwareValidatorFactory(BaseFactory):
    @staticmethod
    def create(dog_type):
        hd_map = {
            DogType.product_dog.value: HardwareProductValidator,
            DogType.product_sl_dog.value: HardwareProductValidator,
            DogType.developer_dog.value: HardwareDeveloperValidator,
            DogType.saas_dog.value: HardwareSaasDogValidator,
            DogType.cloud_dog.value: HardwareCloudDogValidator,
        }
        if dog_type not in hd_map:
            raise exceptions.DogTypeError('dog_type: %s 校验不存在' % dog_type)
        sub_class = hd_map.get(dog_type)
        return sub_class


class HardwareProductValidator(BaseValidator):
    def __init__(self):
        super().__init__()

    @staticmethod
    def do_validate(dog_id1, dog_id2, validator_obj):
        # 调用硬件功能校验连通性
        dog_ids = [dog_id1, dog_id2]
        activate_dog_id = None
        errs = []
        for index, dog_id in enumerate(dog_ids):
            # 主狗和备用狗至少有一个要可用
            try:
                hd_dog = ProductDogAdaptor(login_args=(dog_id.text,))
                hd_dog.verify()
                activate_dog_id = dog_id.text
                break
            except Exception as e:
                errs.append(e)
                if index == (len(dog_ids) - 1):
                    raise exceptions.LicenseError('License验证失败：两个狗ID都校验失败。原因: %s' % ';'.join([str(i) for i in errs]))

        # 过期时间校验
        expired1, expired2 = validator_obj.get_expire()
        hd_rtc = ProductDogAdaptor(login_args=(activate_dog_id,)).get_real_time()
        validate_expired(expired1, hd_rtc)
        validate_expired(expired2, hd_rtc)


class HardwareDeveloperValidator(BaseValidator):
    def __init__(self):
        super().__init__()

    @staticmethod
    def do_validate(uuid, cert, validator_obj) -> requests.Response:
        # 连接域服务
        # 不用校验狗证书中的时间
        dd_dog = DevelopDogAdaptor(login_args=(uuid, cert,))
        return dd_dog.verify()


class HardwareSaasDogValidator(BaseValidator):
    def __init__(self):
        super().__init__()

    @staticmethod
    def do_validate(cloud_name, validator_obj):
        # 不用测试硬件
        # 过期时间校验
        expired1, expired2 = validator_obj.get_expire()
        hd_rtc = SaasDogAdaptor(login_args=(cloud_name,)).get_real_time()
        validate_expired(expired1, hd_rtc)
        validate_expired(expired2, hd_rtc)


class HardwareCloudDogValidator(BaseValidator):
    def __init__(self):
        super().__init__()

    @staticmethod
    def do_validate(cloud_name, validator_obj):
        # 不用测试硬件
        # 过期时间校验
        expired1, expired2 = validator_obj.get_expire()
        hd_rtc = CloudDogAdaptor(login_args=(cloud_name,)).get_real_time()
        validate_expired(expired1, hd_rtc)
        validate_expired(expired2, hd_rtc)


def validate_expired(expired, rtc: datetime.datetime):
    rtc = rtc.strftime('%Y-%m-%d %H:%M:%S')
    if expired:
        expired = '%s 23:59:59' % expired
        if expired < rtc:
            raise exceptions.LicenseError('License验证失败：狗证书过期')
