#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : validator.py
# @Author: guq  
# @Date  : 2021/6/3
# @Desc  :

from .base import LicenseDogValidatorBase
from ...core.dog_type import DogType
from ...core import exceptions, rsa
from .saas_dog import SaasDogValidator
from .developer_dog import DeveloperDogValidator
from .product_dog import ProductDogValidator


class LicenseDogValidator(LicenseDogValidatorBase):

    @staticmethod
    def create(dog_type):
        hd_map = {
            DogType.product_dog.value: ProductDogValidator,
            DogType.product_sl_dog.value: ProductDogValidator,
            DogType.developer_dog.value: DeveloperDogValidator,
            DogType.saas_dog.value: SaasDogValidator,
        }
        if dog_type not in hd_map:
            raise exceptions.DogTypeError('狗验证失败: 没有找对应的狗类型[%s]' % dog_type)
        validate_class = hd_map.get(dog_type)
        return validate_class

    def validate_all(self, *args, **kwargs):
        validate_class = LicenseDogValidator.create(self.dog_type_from_env)
        vc = validate_class(root=self.root, xml_data=self.xml_data)
        vc.set_dog_type(self.dog_type_from_env)
        setattr(vc, 'license2', self.license2)
        vc.validate_info()
        vc.validate_sign()
