#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : product_dog.py
# @Author: guq  
# @Date  : 2021/6/3
# @Desc  :


import base64
import uuid

from .base import LicenseDogValidatorBase
from ...core import exceptions, rsa
from ...validator.dog_hardware_validator import HardwareValidatorFactory
from .. import pub_keys


class DeveloperDogValidator(LicenseDogValidatorBase):
    # developer_dog

    def validate_info(self, *args, **kwargs):
        # 开发狗不用校验通用格式
        # self.validate_common_format()

        if self.root.find('.//Password') is None or self.root.find('.//UserName') is None:
            raise exceptions.LicenseNodeNotFoundError('License验证失败：开发狗cert格式不正确')

        guid = str(uuid.uuid4())
        cert_base64 = base64.b64encode(self.xml_data.encode())

        hd_class = HardwareValidatorFactory.create(self.dog_type_from_env)
        try:
            server_response = hd_class().do_validate(guid, cert_base64, self)
        except Exception as e:
            raise exceptions.LicenseError('狗验证失败：调用开发狗验证服务失败： %s' % str(e))

        if server_response.status_code != 200:
            raise exceptions.LicenseError("狗验证失败：开发狗验证不通过，服务端返回结果： %s" % server_response.text)

        result = server_response.json()
        # // 如果GUID不匹配，就不用再校验签名了
        if guid != result.get('Guid'):
            raise exceptions.LicenseError("狗验证失败：开发狗验证服务响应结果与请求不匹配！")

        bb = "{0}\n{1}\n{2}".format(guid, result['Result'], cert_base64.decode())
        sign_args = (bb, result['Signature'], pub_keys.DeveloperDog)
        setattr(self, 'sign_args', sign_args)

    def validate_sign(self, *args, **kwargs):
        sign_args = getattr(self, 'sign_args')
        verify_result = rsa.public_key_verify(*sign_args)
        if not verify_result:
            raise exceptions.LicenseSignatureError("狗验证失败：开发狗验证服务响应结果的签名无效！")
        return True
