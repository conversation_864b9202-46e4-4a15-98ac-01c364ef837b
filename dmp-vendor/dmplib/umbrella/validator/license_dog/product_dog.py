#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : product_dog.py
# @Author: guq  
# @Date  : 2021/6/3
# @Desc  :

import re

from .base import LicenseDogValidatorBase
from ...core.dog_type import DogType
from ...core import exceptions, rsa
from ...validator.dog_hardware_validator import HardwareValidatorFactory
from .. import pub_keys


class ProductDogValidator(LicenseDogValidatorBase):
    # product_dog

    def validate_info(self, *args, **kwargs):
        # 先校验通用格式
        self.validate_common_format()

        # 剩下的一些格式校验
        # dog_type = DogType.product_dog.value
        dog_id1 = self.root.find(".//DogId")
        dog_id2 = self.root.find(".//DogId2")
        if all([dog_id1 is None, dog_id2 is None]):
            raise exceptions.LicenseNodeNotFoundError('狗验证失败：狗证书文件中没有指定狗ID！')

        # if dog_type == DogType.test_dog.value and self.root.find(".//PdogId") is None:
        #     raise exceptions.LicenseNodeNotFoundError('狗验证失败：狗证书文件中没有指定主狗ID！')

        # 硬件测试+过期时间校验
        hd_class = HardwareValidatorFactory.create(self.dog_type_from_env)
        hd_class().do_validate(dog_id1, dog_id2, self)

    def validate_sign(self, *args, **kwargs):

        license2_content = self.license2.xml_data
        # 如果换行不是\n， 签名校验不能通过，必须是\r\n
        if '\r\n' not in license2_content:
            license2_content = license2_content.replace('\n', '\r\n')

        data_node_xml = re.findall(r'(<Data>.*?</Data>)', self.xml_data, re.S)
        signature_node = self.root.find('.//Signature')
        if (not data_node_xml) or (signature_node is None):
            raise exceptions.LicenseError('狗验证失败：狗证书文件的Data节点或者数字签名节点不存在')

        data_node_xml = re.sub(r">(\s*|\n|\t|\r)<", "><", data_node_xml[0], re.A).strip()
        signature = signature_node.text

        verify_result = rsa.public_key_verify_dog(license2_content + data_node_xml, signature, pub_keys.DogCertificate)
        if not verify_result:
            raise exceptions.LicenseSignatureError("狗验证失败：非法的授权文件！RSA签名不一致！")
        return True
