#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : product_dog.py
# @Author: guq  
# @Date  : 2021/6/3
# @Desc  :

import re

import xmltodict

from .base import LicenseDogValidatorBase
from ...core import exceptions, rsa
from ...validator.dog_hardware_validator import HardwareValidatorFactory
from ...dog.cloud_dog.provider import CLOUD_PROVIDER_MAP
from .. import pub_keys


class CloudDogValidator(LicenseDogValidatorBase):
    # cloud dog 现在已经被废弃

    def validate_info(self, *args, **kwargs):
        # # 先校验通用格式
        self.validate_common_format()

        idc_cloud = 'AliCloud'
        provider = CLOUD_PROVIDER_MAP.get(idc_cloud, None)
        if not provider:
            raise exceptions.LicenseError('狗验证失败：狗文件格式错误，暂不支持该云服务商[%s]' % provider)

        # 硬件测试+过期时间校验
        hd_class = HardwareValidatorFactory.create(self.dog_type_from_env)
        hd_class().do_validate(idc_cloud, self)

        try:
            identity = provider().get_identity()
        except:
            raise exceptions.LicenseError('狗验证失败: 请求云服务器厂商identity失败')

        # TODO cloud dog
        license_data, signature, customer = self.load_license_data(self.xml_data, self.license2.xml_data)
        mac = customer['Environments']['ServerEnvironment']['MacAddress']
        if mac != identity:
            raise exceptions.LicenseSignatureError("狗验证失败：非法的授权文件！Identity签名不一致！")

        self.license_data, self.signature = license_data, signature
        return True

    def load_license_data(self, dog_data, license_data):
        doc = xmltodict.parse(dog_data, encoding='utf-8')
        customer = doc['CloudDog']['Customer']
        signature = doc['CloudDog']['Signature']
        customer_str = self._parse_customer_str_from_dog_content(dog_data)
        return '%s%s' % (license_data, customer_str), signature, customer

    def _parse_customer_str_from_dog_content(self, dog_content):
        regex = r'<Customer>.*</Customer>'
        matches = re.search(regex, dog_content, re.DOTALL)
        customer_str = matches.group()
        return re.sub(r'>(\s*\r*\n*)<', '><', customer_str)

    def validate_sign(self, *args, **kwargs):
        verify_result = rsa.public_key_verify_dog(self.license_data, self.signature, pub_keys.DogCertificate)
        if not verify_result:
            raise exceptions.LicenseSignatureError("狗验证失败：非法的授权文件！RSA签名不一致！")

        return True
