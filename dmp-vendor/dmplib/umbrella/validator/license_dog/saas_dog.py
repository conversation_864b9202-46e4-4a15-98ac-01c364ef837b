#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : product_dog.py
# @Author: guq  
# @Date  : 2021/6/3
# @Desc  :

import re

from .base import LicenseDogValidatorBase
from ...core import exceptions, rsa
from ...validator.dog_hardware_validator import HardwareValidatorFactory
from ...dog.saas_dog.provider import SAAS_PROVIDER_MAP
from .. import pub_keys


class SaasDogValidator(LicenseDogValidatorBase):
    # saas dog

    def validate_info(self, *args, **kwargs):
        # # 先校验通用格式
        self.validate_common_format()

        # TODO 验证identity与云服务， 是否一致
        idc_cloud = self.root.find('.//Provider')
        if idc_cloud is None:
            raise exceptions.LicenseNodeNotFoundError('狗验证失败：狗文件格式错误，不存在Provider节点')
        idc_cloud = idc_cloud.text

        provider = SAAS_PROVIDER_MAP.get(idc_cloud, None)
        if not provider:
            raise exceptions.SaasDogUnsupportedIDCCloudError('狗验证失败：狗文件格式错误，暂不支持该云服务商[%s]' % provider)

        # 硬件测试+过期时间校验
        hd_class = HardwareValidatorFactory.create(self.dog_type_from_env)
        hd_class().do_validate(idc_cloud, self)

        try:
            identity = provider().get_identity()
        except:
            raise exceptions.SaasDogIDCCloudRequestError('狗验证失败: 请求云服务器厂商identity失败')

        # identity = '1353452237400551'
        signature = self.root.find('.//Identity').text

        verify_result = rsa.public_key_verify_dog(identity, signature, pub_keys.DogCertificate)
        if not verify_result:
            raise exceptions.LicenseSignatureError("狗验证失败：非法的授权文件！Identity签名不一致！")

        return True

    def validate_sign(self, *args, **kwargs):
        license2_content = self.license2.xml_data
        # 如果换行不是\n， 签名校验不能通过，必须是\r\n
        if '\r\n' not in license2_content:
            license2_content = license2_content.replace('\n', '\r\n')

        data_node_xml = re.findall(r'(<Data>.*?</Data>)', self.xml_data, re.S)
        signature_node = self.root.find('.//Signature')
        if (not data_node_xml) or (signature_node is None):
            raise exceptions.LicenseError('狗验证失败：云狗证书文件的Data节点或者数字签名节点不存在')

        data_node_xml = re.sub(r">(\s*|\n|\t|\r)<", "><", data_node_xml[0], re.A).strip()
        signature = signature_node.text

        verify_result = rsa.public_key_verify_dog(license2_content + data_node_xml, signature, pub_keys.DogCertificate)
        if not verify_result:
            raise exceptions.LicenseSignatureError("狗验证失败：非法的授权文件！RSA签名不一致！")

        return True
