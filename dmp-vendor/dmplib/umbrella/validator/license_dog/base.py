#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : base.py
# @Author: guq  
# @Date  : 2021/6/3
# @Desc  :


from ...core.base import BaseValidator
from ...validator.license_validator import License2Validator
from ...core import exceptions, dog_type


class LicenseDogValidatorBase(BaseValidator):
    def __init__(self, license_dog_path='', root=None, xml_data=None):
        super(LicenseDogValidatorBase, self).__init__()
        self.root = self.load_xml(license_dog_path) if license_dog_path else root
        self.xml_data = self.load_raw_xml(license_dog_path) if license_dog_path else xml_data
        self.dog_type_from_env = None
        self.license2 = None

    def set_license2(self, license2_obj: License2Validator):
        """设置license2的信息"""
        self.license2 = license2_obj
        return self

    def get_expire(self):
        expired1 = self.root.find(".//Expires")
        expired2 = self.root.find(".//Expires2")
        if all([expired2 is None, expired1 is None]):
            raise exceptions.LicenseNodeNotFoundError('狗验证失败: 狗证书缺少过期时间')

        return (expired1.text if expired1 is not None else '', expired2.text if expired2 is not None else '')

    def validate_common_format(self):
        """通用的一些格式校验"""
        seq_guid = self.root.find(".//SeqGuid")

        # 选出来的节点只能使用None判断，不能使用bool，原因未知
        if seq_guid is None:
            raise exceptions.LicenseNodeNotFoundError('狗验证失败：License文件中不存在seqGuid节点')

        dog_type_from_license = self.root.find(".//DogType")
        if dog_type_from_license is None:
            raise exceptions.LicenseNodeNotFoundError('狗验证失败: licenseDog文件缺少狗的类型')

        exclude_list = [dog_type.DogType.product_sl_dog.value, dog_type.DogType.product_dog.value]
        if dog_type_from_license.text not in exclude_list and dog_type_from_license.text != self.dog_type_from_env:
            raise exceptions.LicenseError('狗验证失败: licenseDog文件中狗类型与环境配置的类型不一致 [%s/%s]' % (
                    dog_type_from_license.text, self.dog_type_from_env
                ))
