# #!/usr/bin/env python
# # -*- coding: utf-8 -*-
# # @File  : license_validator.py
# # @Author: guq
# # @Date  : 2021/5/24
# # @Desc  :
#
# import base64
# import re
# import xml.etree.ElementTree as ET
# import uuid
#
# from ..core.base import BaseValidator
# from ..core.dog_type import DogType
# from ..validator.license_validator import License2Validator
# from ..core import exceptions, rsa
# from ..validator.dog_hardware_validator import HardwareValidatorFactory
# from ..dog.saas_dog.provider import SAAS_PROVIDER_MAP
# from . import pub_keys
#
#
# class LicenseDogValidator(BaseValidator):
#     def __init__(self, license_dog_path='', root=None, xml_data=None):
#         super(LicenseDogValidator, self).__init__()
#         self.root = self.load_xml(license_dog_path) if license_dog_path else root
#         self.xml_data = self.load_raw_xml(license_dog_path) if license_dog_path else xml_data
#         self.dog_type_from_env = None
#         self.license2 = None
#
#     @staticmethod
#     def create(dog_type):
#         hd_map = {
#             DogType.product_dog.value: ProductDogValidator,
#             DogType.developer_dog.value: DeveloperDogValidator,
#             DogType.saas_dog.value: SaasDogValidator,
#         }
#         if dog_type not in hd_map:
#             raise exceptions.DogTypeError('狗验证失败（D001）: 没有找对应的狗类型[%s]' % dog_type)
#         validate_class = hd_map.get(dog_type)
#         return validate_class
#
#     def load_xml(self, path):
#         tree = ET.parse(path)
#         return tree.getroot()
#
#     def set_license2(self, license2_obj: License2Validator):
#         """设置license2的信息"""
#         self.license2 = license2_obj
#         return self
#
#     def validate_all(self, *args, **kwargs):
#         validate_class = LicenseDogValidator.create(self.dog_type_from_env)
#         vc = validate_class(root=self.root, xml_data=self.xml_data)
#         vc.set_dog_type(self.dog_type_from_env)
#         setattr(vc, 'license2', self.license2)
#         vc.validate_info()
#         vc.validate_sign()
#
#     def get_expire(self):
#         expired1 = self.root.find(".//Expires")
#         expired2 = self.root.find(".//Expires2")
#         if all([expired2 is None, expired1 is None]):
#             raise exceptions.LicenseNodeNotFoundError('狗验证失败（D002）: 狗证书缺少过期时间')
#
#         return (expired1.text if expired1 is not None else '', expired2.text if expired2 is not None else '')
#
#     def validate_common_format(self):
#         """通用的一些格式校验"""
#         seq_guid = self.root.find(".//SeqGuid")
#
#         # 选出来的节点只能使用None判断，不能使用bool，原因未知
#         if seq_guid is None:
#             raise exceptions.LicenseNodeNotFoundError('狗验证失败（D003）：License文件中不存在seqGuid节点')
#
#         dog_type_from_license = self.root.find(".//DogType")
#         if dog_type_from_license is None:
#             raise exceptions.LicenseNodeNotFoundError('狗验证失败（D004）licenseDog文件缺少狗的类型')
#
#         if dog_type_from_license.text != self.dog_type_from_env:
#             raise exceptions.LicenseError('狗验证失败（D005）licenseDog文件中狗类型与环境配置的类型不一致 [%s/%s]' % (
#                 dog_type_from_license.text, self.dog_type_from_env
#             ))
#
#
# class ProductDogValidator(LicenseDogValidator):
#     # product_dog
#
#     def validate_info(self, *args, **kwargs):
#         # 先校验通用格式
#         self.validate_common_format()
#         dog_type = DogType.product_dog.value
#         dog_id1 = self.root.find(".//DogId")
#         dog_id2 = self.root.find(".//DogId2")
#         if all([dog_id1 is None, dog_id2 is None]):
#             raise exceptions.LicenseNodeNotFoundError('狗验证失败（D008）：狗证书文件中没有指定狗ID！')
#
#         if dog_type == DogType.test_dog.value and self.root.find(".//PdogId") is None:
#             raise exceptions.LicenseNodeNotFoundError('狗验证失败（D009）：狗证书文件中没有指定主狗ID！')
#
#         # 硬件测试+过期时间校验
#         hd_class = HardwareValidatorFactory.create(self.dog_type_from_env)
#         hd_class().do_validate(dog_id1, dog_id2, self)
#         return
#
#     def validate_sign(self, *args, **kwargs):
#
#         license2_content = self.license2.xml_data
#         # 如果换行不是\n， 签名校验不能通过，必须是\r\n
#         if '\r\n' not in license2_content:
#             license2_content = license2_content.replace('\n', '\r\n')
#
#         data_node_xml = re.findall(r'(<Data>.*?</Data>)', self.xml_data, re.S)
#         signature_node = self.root.find('.//Signature')
#         if (not data_node_xml) or (signature_node is None):
#             raise exceptions.LicenseError('狗验证失败（D010）：狗证书文件的Data节点或者数字签名节点不存在')
#
#         data_node_xml = re.sub(r">(\s*|\n|\t|\r)<", "><", data_node_xml[0], re.A).strip()
#         signature = signature_node.text
#
#         verify_result = rsa.public_key_verify_dog(license2_content + data_node_xml, signature, pub_keys.DogCertificate)
#         if not verify_result:
#             raise exceptions.LicenseSignatureError("狗验证失败（D011）：非法的授权文件！RSA签名不一致！")
#         return True
#
#
# class DeveloperDogValidator(LicenseDogValidator):
#     # developer_dog
#
#     def validate_info(self, *args, **kwargs):
#         # # 先校验通用格式
#         # self.validate_common_format()
#
#         if self.root.find('.//Password') is None or self.root.find('.//UserName') is None:
#             raise exceptions.LicenseNodeNotFoundError('License验证失败：开发狗cert格式不正确')
#
#         guid = str(uuid.uuid4())
#         cert_base64 = base64.b64encode(self.xml_data.encode())
#
#         hd_class = HardwareValidatorFactory.create(self.dog_type_from_env)
#         try:
#             server_response = hd_class().do_validate(guid, cert_base64, self)
#         except Exception as e:
#             raise exceptions.LicenseError('狗验证失败（D012）：调用开发狗验证服务失败： %s' % str(e))
#
#         if server_response.status_code != 200:
#             raise exceptions.LicenseError("狗验证失败（D013）：开发狗验证不通过，服务端返回结果： %s" % server_response.text)
#
#         result = server_response.json()
#         # // 如果GUID不匹配，就不用再校验签名了
#         if guid != result.get('Guid'):
#             raise exceptions.LicenseError("狗验证失败（D014）：开发狗验证服务响应结果与请求不匹配！")
#
#         bb = "{0}\n{1}\n{2}".format(guid, result['Result'], cert_base64.decode())
#         sign_args = (bb, result['Signature'], pub_keys.DeveloperDog)
#         return sign_args
#
#     def validate_sign(self, *args, **kwargs):
#         sign_args = self.validate_info()
#         verify_result = rsa.public_key_verify(*sign_args)
#         if not verify_result:
#             raise exceptions.LicenseSignatureError("狗验证失败（D015）：开发狗验证服务响应结果的签名无效！")
#         return True
#
#
# class SaasDogValidator(LicenseDogValidator):
#     # saas dog
#
#     def validate_info(self, *args, **kwargs):
#         # # 先校验通用格式
#         self.validate_common_format()
#
#         # TODO 验证identity与云服务， 是否一致
#         idc_cloud = self.root.find('.//Provider')
#         if idc_cloud is None:
#             raise exceptions.LicenseNodeNotFoundError('狗验证失败（D016）：狗文件格式错误，不存在Provider节点')
#         idc_cloud = idc_cloud.text
#
#         provider = SAAS_PROVIDER_MAP.get(idc_cloud, None)
#         if not provider:
#             raise exceptions.SaasDogUnsupportedIDCCloudError('狗验证失败（D017）：狗文件格式错误，暂不支持该云服务商[%s]' % provider)
#
#         # 硬件测试+过期时间校验
#         hd_class = HardwareValidatorFactory.create(self.dog_type_from_env)
#         hd_class().do_validate(idc_cloud, self)
#
#         try:
#             identity = provider().get_identity()
#         except:
#             raise exceptions.SaasDogIDCCloudRequestError('狗验证失败（D018）:SaaSDog请求identity失败')
#
#         # identity = '1353452237400551'
#         signature = self.root.find('.//Identity').text
#
#         verify_result = rsa.public_key_verify_dog(identity, signature, pub_keys.DogCertificate)
#         if not verify_result:
#             raise exceptions.LicenseSignatureError("狗验证失败（D011）：非法的授权文件！Identity签名不一致！")
#
#         return True
#
#     def validate_sign(self, *args, **kwargs):
#         license2_content = self.license2.xml_data
#         # 如果换行不是\n， 签名校验不能通过，必须是\r\n
#         if '\r\n' not in license2_content:
#             license2_content = license2_content.replace('\n', '\r\n')
#
#         data_node_xml = re.findall(r'(<Data>.*?</Data>)', self.xml_data, re.S)
#         signature_node = self.root.find('.//Signature')
#         if (not data_node_xml) or (signature_node is None):
#             raise exceptions.LicenseError('狗验证失败（D303）：云狗证书文件的Data节点或者数字签名节点不存在')
#
#         data_node_xml = re.sub(r">(\s*|\n|\t|\r)<", "><", data_node_xml[0], re.A).strip()
#         signature = signature_node.text
#
#         verify_result = rsa.public_key_verify_dog(license2_content + data_node_xml, signature, pub_keys.DogCertificate)
#         if not verify_result:
#             raise exceptions.LicenseSignatureError("狗验证失败（D011）：非法的授权文件！RSA签名不一致！")
#         return True
