#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : license_validator.py
# @Author: guq  
# @Date  : 2021/5/24
# @Desc  :


import re

from ..core.base import BaseValidator
from ..core import exceptions, rsa
from . import pub_keys
from ..core.dog_type import DogType


class License2Validator(BaseValidator):
    def __init__(self, license2_path):
        super(License2Validator, self).__init__()
        self.license2_path = license2_path
        self.root = self.load_xml(license2_path)
        self.xml_data = self.load_raw_xml(license2_path)

    def validate_all(self, *args, **kwargs):
        self.validate_format()
        # 每个data节点下会校验签名
        # self.validate_sign()

    def validate_format(self, *args, **kwargs):
        seq_guid = self.root.find(".//licenseInfo//seqGuid")
        # 选出来的节点只能使用None判断，不能使用bool，原因未知
        if seq_guid is None:
            raise exceptions.LicenseNodeNotFoundError('License验证失败：License文件中不存在seqGuid节点')

        for node in self.root.findall('.//license'):
            self.node_validate(node)

    def node_validate(self, node):
        data_node = node.find('./data')
        if data_node is None:
            raise exceptions.LicenseNodeNotFoundError('License验证失败：License文件中不存在data节点')

        signature_node = node.find('./signature')
        if signature_node is None:
            raise exceptions.LicenseNodeNotFoundError('License验证失败：License文件中不存在signature节点')

        signature = signature_node.text
        # 校验每个data节点下的签名
        self.validate_sign(data_node, signature, pub_keys.LicenseSign)

    def validate_sign(self, data_node, signature, pub_key):
        # data_xml = ET.tostring(data_node, encoding='utf8', method='xml').decode('utf8')  # type: str
        # 这里不能使用tree逆转回xml，标签属性会变换位置，与实际文本不一致，所以只能手动提取

        data_xml = re.findall(r'<data>(.*?)</data>.*?' + signature[:10], self.xml_data, re.S)[0]
        data_xml = re.sub(r">(\s*|\n|\t|\r)<", "><", data_xml, re.A).strip()

        # 开发狗只校验格式，不校验签名
        if self.dog_type_from_env not in [DogType.developer_dog.value]:
            verify_result = rsa.public_key_verify(data_xml, signature, pub_key)
            if not verify_result:
                raise exceptions.LicenseSignatureError("License验证失败：非法的授权文件！RSA签名不一致！")
