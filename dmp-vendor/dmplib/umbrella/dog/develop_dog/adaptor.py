#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : adaptor.py
# @Author: guq  
# @Date  : 2021/5/20
# @Desc  :

import datetime
from ...core.base import BaseAdaptor, BaseDog
from ...dog.develop_dog.driver import DevelopDogDriver


class DevelopDog(BaseDog):
    def __init__(self, login_args: tuple = (), *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.driver = DevelopDogDriver()
        self.login_args = login_args

    def login(self, *args, **kwargs):
        return self.driver.login()

    def logout(self, *args, **kwargs):
        return self.driver.logout()

    def get_exchange_data(self, uuid, cert_base64):
        return self.driver.get_exchange_data(uuid, cert_base64)

    def get_real_time(self, *args, **kwargs) -> datetime.datetime:
        timestamp = self.driver.get_rtc()
        return datetime.datetime.fromtimestamp(timestamp)


class DevelopDogAdaptor(BaseAdaptor):
    def __init__(self, login_args: tuple = (), *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.dog = DevelopDog(login_args)

    # 验证连接
    def verify(self):
        try:
            return self.dog.get_exchange_data(*self.dog.login_args)
        finally:
            self.dog.logout()

    # 获取狗当前时间
    def get_real_time(self) -> datetime.datetime:
        return self.dog.get_real_time()
