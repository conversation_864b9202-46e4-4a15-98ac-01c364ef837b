#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : driver.py
# @Author: guq  
# @Date  : 2021/5/20
# @Desc  :

import requests
from ...core.tools import Tools


class DevelopDogDriver:

    def __init__(self):
        """DevelopDogDriver"""

    def login(self):
        """没有实际的登陆形式，只是校验证书中的account-id与云服务器厂商返回的是否一致"""
        return True

    def logout(self):
        return True

    def get_rtc(self):
        """东八区标准时间 """
        return int(Tools.now().timestamp())

    def get_exchange_data(self, uuid, cert_base64):
        # kwargs = {
        #     'url': 'http://dogservice.mysoft.com.cn:52723/api/GetDogStatus.aspx',
        #     'data': {
        #         "Guid": "FED1102E-499A-A96B-1AB8-790B9A199979",
        #         "CertBase64": "PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPERldmVsb3BlckRvZ0NlcnQgeG1sbnM6eHNkPSJodHRwOi8vd3d3LnczLm9yZy8yMDAxL1hNTFNjaGVtYSIgeG1sbnM6eHNpPSJodHRwOi8vd3d3LnczLm9yZy8yMDAxL1hNTFNjaGVtYS1pbnN0YW5jZSI+CiAgICA8VXNlck5hbWU+Z3VxMDE8L1VzZXJOYW1lPgogICAgPFBhc3N3b3JkPmQ3ajFONzhoNWE2QXNsYTZFS2xsWnc9PTwvUGFzc3dvcmQ+CjwvRGV2ZWxvcGVyRG9nQ2VydD4="
        #     },
        #     'timeout': 10
        # }

        kwargs = {
            'url': 'http://dogservice.mysoft.com.cn:52723/api/GetDogStatus.aspx',
            'data': {
                "Guid": uuid,
                "CertBase64": cert_base64
            },
            'timeout': 10
        }
        response = requests.post(**kwargs)
        return response


if __name__ == '__main__':
    ssd = DevelopDogDriver()
    print(ssd.get_exchange_data("FED1102E-499A-A96B-1AB8-790B9A199979",
                                "PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPERldmVsb3BlckRvZ0NlcnQgeG1sbnM6eHNkPSJodHRwOi8vd3d3LnczLm9yZy8yMDAxL1hNTFNjaGVtYSIgeG1sbnM6eHNpPSJodHRwOi8vd3d3LnczLm9yZy8yMDAxL1hNTFNjaGVtYS1pbnN0YW5jZSI+CiAgICA8VXNlck5hbWU+Z3VxMDE8L1VzZXJOYW1lPgogICAgPFBhc3N3b3JkPmQ3ajFONzhoNWE2QXNsYTZFS2xsWnc9PTwvUGFzc3dvcmQ+CjwvRGV2ZWxvcGVyRG9nQ2VydD4="
                                ))
