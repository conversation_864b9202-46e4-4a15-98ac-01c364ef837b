#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : provider.py
# @Author: guq
# @Date  : 2021/5/22
# @Desc  :

from ...core.base import BaseIDCCloudProvider
from ...core.tools import retry, CachedProperty


# class HUAWEICloudProvider(BaseIDCCloudProvider):
#     name = 'HuaweiCloud'
#
#     def __init__(self):
#         super().__init__()
#         self.idc_meta_url = 'http://169.254.169.254/openstack/latest/meta_data.json'
#
#     @CachedProperty
#     @retry(tries=3, delay=1)
#     def get_identity(self):
#         kwargs = {"url": self.idc_meta_url, 'timeout': 2}
#         response = self.http_request(**kwargs).json()
#         enterprise_project_id = response.get('meta', {}).get('enterprise_project_id', '')
#         project_id = response.get('project_id', '')
#
#         # return enterprise_project_id + project_id
#         return '%s%s' % (enterprise_project_id, project_id)


class AliCloudProvider(BaseIDCCloudProvider):
    name = 'AliCloud'

    def __init__(self):
        super().__init__()
        self.idc_meta_url = 'http://***************/latest/meta-data/vpc-id'

    # @CachedProperty
    @retry(tries=3, delay=1)
    def get_identity(self):
        kwargs = {"url": self.idc_meta_url, 'timeout': 2}
        return self.http_request(**kwargs).content.decode().strip()


#
# class TencentCloudProvider(BaseIDCCloudProvider):
#     name = 'TencentCloud'
#
#     def __init__(self):
#         super().__init__()
#         self.idc_meta_url = 'http://metadata.tencentyun.com/latest/meta-data/app-id'
#
#     @CachedProperty
#     @retry(tries=3, delay=1)
#     def get_identity(self):
#         kwargs = {"url": self.idc_meta_url, 'timeout': 2}
#         return self.http_request(**kwargs).content.decode().strip()
#

CLOUD_PROVIDER_MAP = {
    # HUAWEICloudProvider.name: HUAWEICloudProvider,
    AliCloudProvider.name: AliCloudProvider,
    # TencentCloudProvider.name: TencentCloudProvider,
}
