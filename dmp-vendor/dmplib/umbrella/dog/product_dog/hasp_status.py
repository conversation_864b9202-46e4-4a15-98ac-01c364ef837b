#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : hasp_status.py
# @Author: guq  
# @Date  : 2021/5/21
# @Desc  :

import enum


# 厂商提供的sdk中的状态
@enum.unique
class HaspStatus(enum.Enum):
    # Request successfully completed

    HASP_STATUS_OK = 0

    # Request exceeds memory range of a Sentinel file

    HASP_MEM_RANGE = 1

    # Legacy HASP HL Run-time API: UnknownInvalid Feature ID option

    HASP_INV_PROGNUM_OPT = 2

    # System is out of memory

    HASP_INSUF_MEM = 3

    # Too many open Featureslogin sessions

    HASP_TMOF = 4

    # Access to Feature, Sentinel protection key or functionality denied

    HASP_ACCESS_DENIED = 5

    # Legacy decryption function cannot work on Feature

    HASP_INCOMPAT_FEATURE = 6

    # Sentinel protection key not available

    HASP_HASP_NOT_FOUND = 7

    # Encrypteddecrypted data length too short to execute function call

    HASP_TOO_SHORT = 8

    # Invalid login handle passed to function

    HASP_INV_HND = 9

    # Specified File ID not recognized by API

    HASP_INV_FILEID = 10

    # Installed driver or daemon too old to execute function

    HASP_OLD_DRIVER = 11

    # Real-time clock (rtc) not available

    HASP_NO_TIME = 12

    # Generic error from host system call

    HASP_SYS_ERR = 13

    # Required driver not installed

    HASP_NO_DRIVER = 14

    # Invalid XML format

    HASP_INV_FORMAT = 15

    # Unable to execute function in this context the requested
    # functionality is not implemented

    HASP_REQ_NOT_SUPP = 16

    # Binary data passed to function does not contain valid update

    HASP_INV_UPDATE_OBJ = 17

    # Sentinel protection key not found

    HASP_KEYID_NOT_FOUND = 18

    # Required XML tags not found Contents in binary data are missing
    # or invalid

    HASP_INV_UPDATE_DATA = 19

    # Update request not supported by Sentinel protection key

    HASP_INV_UPDATE_NOTSUPP = 20

    # Update counter set incorrectly

    HASP_INV_UPDATE_CNTR = 21

    # Invalid Vendor Code passed

    HASP_INV_VCODE = 22

    # Sentinel protection key does not support encryption type

    HASP_ENC_NOT_SUPP = 23

    # Passed time value outside supported value range

    HASP_INV_TIME = 24

    # Real-time clock battery out of power

    HASP_NO_BATTERY_POWER = 25

    # Acknowledge data requested by update, but ack_data parameter
    # is NULL

    HASP_NO_ACK_SPACE = 26

    # Program running on a terminal server

    HASP_TS_DETECTED = 27

    # Requested Feature type not implemented

    HASP_FEATURE_TYPE_NOT_IMPL = 28

    # Unknown algorithm used in H2RV2C file

    HASP_UNKNOWN_ALG = 29

    # Signature verification operation failed

    HASP_INV_SIG = 30

    # Requested Feature not available

    HASP_FEATURE_NOT_FOUND = 31

    # Access log not enabled

    HASP_NO_LOG = 32

    # Communication error between API and local Sentinel License Manager

    HASP_LOCAL_COMM_ERR = 33

    # Vendor Code not recognized by API

    HASP_UNKNOWN_VCODE = 34

    # Invalid XML specification

    HASP_INV_SPEC = 35

    # Invalid XML scope

    HASP_INV_SCOPE = 36

    # Too many Sentinel HASP protection keys currently connected

    HASP_TOO_MANY_KEYS = 37

    # Too many concurrent user sessions currently connected

    HASP_TOO_MANY_USERS = 38

    # Session has been interrupted

    HASP_BROKEN_SESSION = 39

    # Communication error between local and remote HASP License Manager

    HASP_REMOTE_COMM_ERR = 40

    # Feature expired

    HASP_FEATURE_EXPIRED = 41

    # HASP License Manager version too old

    HASP_OLD_LM = 42

    # InputOutput error occurred in secure storage area of HASP SL key OR
    # a USB error occurred when communicating with a HASP HL key

    HASP_DEVICE_ERR = 43

    # Update installation not permitted This update was already applied

    HASP_UPDATE_BLOCKED = 44

    # System time has been tampered with

    HASP_TIME_ERR = 45

    # Communication error occurred in secure channel

    HASP_SCHAN_ERR = 46

    # Corrupt data exists in secure storage area of HASP SL protection key

    HASP_STORAGE_CORRUPT = 47

    # Unable to find Vendor library

    HASP_NO_VLIB = 48

    # Unable to load Vendor library

    HASP_INV_VLIB = 49

    # Unable to locate any Features matching scope

    HASP_SCOPE_RESULTS_EMPTY = 50

    # Program running on a virtual machine

    HASP_VM_DETECTED = 51

    # HASP SL key incompatible with machine hardware HASP SL key is locked
    # to different hardware. OR:
    # In the case of a V2C file, conflict between HASP SL key data and machine
    # hardware data HASP SL key locked to different hardware

    HASP_HARDWARE_MODIFIED = 52

    # Login denied because of user restrictions

    HASP_USER_DENIED = 53

    # Trying to install a V2C file with an update counter that is out of
    # sequence with the update counter on Sentinel HASP protection key. The
    # update counter value in the V2C file is lower than the value in Sentinel
    # HASP protection key.

    HASP_UPDATE_TOO_OLD = 54

    # Trying to install a V2C file with an update counter that is out of
    # sequence with update counter in Sentinel HASP protection key. The first
    # value in the V2C file is greater than the value in Sentinel HASP
    # protection key.

    HASP_UPDATE_TOO_NEW = 55

    # Vendor library version too old

    HASP_OLD_VLIB = 56

    # Upload via ACC failed, e.g. because of illegal format

    HASP_UPLOAD_ERROR = 57

    # Invalid XML "recipient" parameter

    HASP_INV_RECIPIENT = 58

    # Invalid XML "action" parameter

    HASP_INV_DETACH_ACTION = 59

    # Scope does not specify a unique Product

    HASP_TOO_MANY_PRODUCTS = 60

    # Invalid Product information

    HASP_INV_PRODUCT = 61

    # Unknown Recipient update can only be applied to the
    # Recipient specified in detach(), and not to this computer

    HASP_UNKNOWN_RECIPIENT = 62

    # Invalid duration

    HASP_INV_DURATION = 63

    # Cloned secure storage area detected

    HASP_CLONE_DETECTED = 64

    # Specified V2C update already installed in the LLM

    HASP_UPDATE_ALREADY_ADDED = 65

    # Specified Hasp Id is in Inactive state

    HASP_HASP_INACTIVE = 66

    # No detachable feature exists
    # No detachable feature exists (typo kept for compatibility)

    HASP_NO_DETACHABLE_FEATURE = 67

    # scope does not specify a unique Host

    HASP_TOO_MANY_HOSTS = 68

    # Rehost is not allowed for any license

    HASP_REHOST_NOT_ALLOWED = 69

    # License is rehosted to other machine

    HASP_LICENSE_REHOSTED = 70

    # Old rehost license try to apply

    HASP_REHOST_ALREADY_APPLIED = 71

    # File not found or access denied

    HASP_CANNOT_READ_FILE = 72

    # Extension of license not allowed as number of detached
    # licenses is greater than current concurrency count

    HASP_EXTENSION_NOT_ALLOWED = 73

    # Detach of license not allowed as product
    # contains VM disabled feature and host machine is a virtual machine

    HASP_DETACH_DISABLED = 74

    # Rehost of license not allowed as container
    # contains VM disabled feature and host machine is a virtual machine

    HASP_REHOST_DISABLED = 75

    # Format SL-AdminMode or migrate SL-Legacy to SL-AdminMode not allowed
    # as container has detached license

    HASP_DETACHED_LICENSE_FOUND = 76

    # Recipient of the requested operation is older than expected

    HASP_RECIPIENT_OLD_LM = 77

    # Secure storage ID mismatch

    HASP_SECURE_STORE_ID_MISMATCH = 78

    # Duplicate Hostname found while key contains Hostname Fingerprinting

    HASP_DUPLICATE_HOSTNAME = 79

    # The Sentinel License Manager is required for this operation

    HASP_MISSING_LM = 80

    # Attempting to consume multiple executions during log in to a Feature.
    # However, the license for the Feature does not contain enough remaining executions

    HASP_FEATURE_INSUFFICIENT_EXECUTION_COUNT = 81

    # Attempting to perform an operation not compatible with target platform

    HASP_INCOMPATIBLE_PLATFORM = 82

    # The key is disabled due to suspected tampering

    HASP_HASP_DISABLED = 83

    # The key is inaccessible due to sharing

    HASP_SHARING_VIOLATION = 84

    # API dispatcher: API for this Vendor Code was not found

    HASP_NO_API_DYLIB = 400

    # API dispatcher: Unable to load API DLL possibly corrupt?

    HASP_INV_API_DYLIB = 401

    # Invalid function parameter

    HASP_INV_PARAM = 501

    #
    # Internal use: no classic memory extension block available

    HASP_NO_EXTBLOCK = 600

    #
    # Internal use: invalid port type

    HASP_INV_PORT_TYPE = 650

    # Internal use: invalid port value

    HASP_INV_PORT = 651

    # Requested function not implemented

    HASP_NOT_IMPL = 698

    # Internal error occurred in API

    HASP_INT_ERR = 699

    # Reserved for Sentinel helper libraries

    HASP_FIRST_HELPER = 2001

    # Reserved for Sentinel Activation API

    HASP_FIRST_HASP_ACT = 3001


# HASP_STATUS_MAP = {v.value: k for k, v in HaspStatus._member_map_.items()}


if __name__ == '__main__':
    print(HaspStatus.__dict__)
    # print(HASP_STATUS_MAP)
