#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : adaptor.py
# @Author: guq  
# @Date  : 2021/5/20
# @Desc  :

import datetime
from ...core.base import BaseAdaptor, BaseDog
from ...dog.product_dog.driver import HaspDriver


class ProductDog(BaseDog):
    def __init__(self, login_args: tuple = (), *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.hasp_driver = HaspDriver()
        self.login_args = login_args

    def login(self, *args, **kwargs):
        return self.hasp_driver.hasp_login_scope(*self.login_args)

    def logout(self, *args, **kwargs):
        return self.hasp_driver.logout()

    def get_real_time(self, *args, **kwargs) -> datetime.datetime:
        timestamp = self.hasp_driver.get_rtc()
        return datetime.datetime.fromtimestamp(timestamp)


# 产品硬软狗驱动适配
# TODO 检测是否运行在docker中
class ProductDogAdaptor(BaseAdaptor):
    def __init__(self, login_args: tuple = (), *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.dog = ProductDog(login_args)

    # 验证
    def verify(self):
        try:
            self.dog.login()
            return True
        finally:
            self.dog.logout()

    # 获取狗当前时间
    def get_real_time(self) -> datetime.datetime:
        try:
            self.dog.login()
            return self.dog.get_real_time()
        finally:
            self.dog.logout()


