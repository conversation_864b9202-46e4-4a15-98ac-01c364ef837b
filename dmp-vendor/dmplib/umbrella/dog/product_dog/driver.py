#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : driver.py
# @Author: guq  
# @Date  : 2021/5/20
# @Desc  :

import os
import ctypes
import platform

from ...core import exceptions
from ...dog.product_dog.hasp_status import HaspStatus

HANDLE = ctypes.c_uint32(0)
FEATURE_ID = 0  # 默认给0


# 参考提供的C语言版API
class HaspDriver:
    def __init__(self):
        self.handle = HANDLE
        self.feature_id = FEATURE_ID
        self.cdll = None
        self.load_lib()

    def load_lib(self):
        # 在此不会支持Windows
        platform_system, lib_path = self.get_lib_path()
        self.cdll = ctypes.cdll.LoadLibrary(lib_path)
        return self

    def get_lib_path(self):
        platform_system = platform.system().lower()  # Linux/Windows
        platform_arch = platform.architecture()[0]  # 32bit/64bit

        supported_lib_map = dict(
            linux={'64bit': 'libhasp_linux_x86_64_79674.so', '32bit': 'libhasp_linux_79674.so'},
            # windows={'64bit': 'hasp_windows_x64_79674.dll', '32bit': 'hasp_windows_79674.dll'},
            windows={'64bit': '', '32bit': ''},  # Windows必须使用硬件狗，不能使用软狗，dmp也不支持Windows部署
        )
        lib_file = supported_lib_map.get(platform_system, {}).get(platform_arch, '')

        if not lib_file:
            raise exceptions.ProductDogUnSupportedPlatformError(
                '当前系统平台：[%s %s], 暂时不支持！' % (platform_system, platform_arch)
            )

        # lib_so_path = os.environ.get('DOG_LIB_PATH', '')
        lib_so_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'lib')
        # if not lib_so_path:
        #     raise exceptions.BootEnvError('请先在环境变量中指定产品狗的库文件地址, 当前为：%s' % lib_so_path)

        lib_path = os.path.join(lib_so_path, lib_file)
        if not os.path.exists(lib_path):
            raise exceptions.ProductDogLibNotFoundError('没有找到狗的库文件[%s]，请检查存放库文件的目录！' % lib_path)

        return platform_system, lib_path

    def get_humans_reason_by_status(self, status) -> str:
        humans_reasons = {
            1: "Request exceeds the Sentinel protection key memory range",
            400: "Unable to locate dynamic library for API",
            401: "Sentinel API dynamic library is corrupt",
            7: "Sentinel protection key is no longer available",
            14: "Required driver is not installed",
            11: "outdated driver version or no driver installed",
            22: "invalid vendor code",
            9: "handle not active",
            10: "invalid file id",
        }
        reason = humans_reasons.get(status, None)
        if reason:
            return '[code: %s] %s' % (status, reason)
        else:
            return '[code: %s] %s' % (status, 'unexpected error')

    def exec_func_via_so(self, func_name, *args):
        """
        :param func_name:  so里面的方法
        :param args:       要使用的参数
        :return:
        """
        status = getattr(self.cdll, func_name)(*args)
        # logger.info('hasp %s status: %s' % (func_name, status))

        if status != HaspStatus.HASP_STATUS_OK.value:
            reason = self.get_humans_reason_by_status(status)
            raise exceptions.ProductDogExecProcessError(reason)

        return status

    def hasp_login_scope(self, dog_id: int):
        scope = ctypes.c_char_p()
        scope.value = ("<haspscope><hasp id=\"%s\" /></haspscope>" % dog_id).encode()
        vendor_code = self.get_vendor_code().encode()

        status = self.exec_func_via_so(
            'hasp_login_scope',
            *(self.feature_id, scope, vendor_code, ctypes.byref(self.handle))
        )
        return status == 0

    def logout(self):
        status = self.exec_func_via_so('hasp_logout', *(self.handle,))
        return status == 0

    def get_rtc(self):
        _time = ctypes.c_uint32()
        _ = self.exec_func_via_so('hasp_get_rtc', *(self.handle, ctypes.byref(_time)))
        return _time.value

    def get_vendor_code(self, mode=1):
        """mode==1 正式环境"""

        # code3 = "QIoHKAn5RnxGE04eCzY5wkFk/52fJBsgacmciQkbA9EigxCtFklGUYGZk4Xbn+pEMQKRFs6BR74ygKj4"
        # code1 = "jE/H8cWLtSeVIyRQTtS/S1J9kgRZQ7WtI0/FetR4T8d+Wl36m3gGTVqcsGY/XRF6NGFA3p8LcG5jg5q/"
        # code2 = "xTfBF8EJQWn9aht0vxKPc2mTJe0aCHns09w/G9LbhVM0lxi9naqYy0rtAsKA01zvx65K6ZR6OQOgvqQK"
        # code1 = code1 + "TKegXKNEGa/atu0bL7WRFUgzay93paF4uPnX3/LRuwN3lplq42NfbNMoEDE9wNXgV6YvhN/l8vKVLA2G"
        # code3 = code3 + "QNZt6ixF2iXv96xxKC5y4GDZc4eAPlxPvw32+18f59cOloSDoBs8mjQTfj0C/E/8ejGwEq3VuyU5w/5+"
        # code1 = code1 + "3mdC5nQZ6pk86qyfdPPxBGSETrMiRBcX+wNC7kG3O2wxu6reLwFy0I6rU+uByXs1+Q83Tm2GMraKoqD/"
        # code2 = code2 + "vWvYes2AmydXEcrBxs7N+NlUmjOtjGJcQXftwlNost1sBszQY52fngbAHTTXX1mYI9ieyqtNZ9wcE+L4"
        # code1 = code1 + "b82FpU5X6aF/szr7bRv+CeOc8HyOVkUBA5TFkLn+hYyD8YZK2e6UBhhdPOxZPvOjrBlpGjhauBecTTWE"
        # code3 = code3 + "edftXwzowzzPOXDvAwSUlae4Ort6vMsXzCORhh2LvbPFgC+KO9iFcw71CQpLQCaSxW84Iiarmq5Az5ki"
        # code2 = code2 + "euH5wWQ6JcBaa8RaOtCuqimhJc7Okak5i9c+fGQZNNbt7sTrf3w0ZzUihXPqH+CnuUCQpJvDNtSwCF8i"
        # code2 = code2 + "3ti5KZ/3nga2INOiYGeUWkhZVkeXNGe01/UW3dpe7Nl1G/sHqYBkX1UNV9orEghGduZS6huUpxHQ1iyL"
        # code3 = code3 + "iE6Wbp9E+Qdrg0gZhELN8ioB7y/HYZE8eFX1E9dZfsOjBDC/B1kgeEmuhyA="
        # return code1 + code2 + code3

        if mode == 1:
            return ("vOtU65csBAXc9uls4XFRbE66SLrgYrEyRU+oWVoLB/lm2GO4y2Ym7YMCs9/xYtpjEyfmxSUj1mYdonRJ" +
                    "XAjmnnGzAK8iR7bvQPbcDjQdhem/ipLk36JpqzICfNQWndzKUK72lzAXA6A+vBIrMIiUxXwwtfLP+DR8" +
                    "fNErl4TDxDeUe5juyZkB39LaBwcVOK1vyiPY5pd++fXCCDOgwnDnIQ7NZAdmSdJz4n1iAGB1ion7eF0S" +
                    "FEJ47WgZ2dBO97ASNKS5fRNlUq/14gMmdFmZmu40iC/gMY+vub2/XOyjWkdfuHdZm2g+2v6mIjWKVJjX" +
                    "9yv5ChuQ4x6XIUtMuFLhpI44I2C26P9VH9ZLCrfeH6GFrvNz3FXVdNQcFA+QDDPqRDVQ57Hiv0S8QDGp" +
                    "T2GK6No0pDrp6Yxq//6DG+GO5taEdla93RyEmKpT1GbJOtPEwHnzVjXa3aUbfkYp9y5PmfgxB7/5W+IU" +
                    "Jkdbv+B85NrebM016aVc3sXj97wyusYJiAJyY/dh8JuJpk0XEXoQR8zl6ETID7UnJoxk32aziP3sFZn6" +
                    "SRknrJ4zKjTJcZa5LKpA6TrJPLpEkpuqhi/WN0rU56hXiYfLsWeaLL8UOEQUUNNEnyAW0W4+80lHGilN" +
                    "7QJDi9TwMzh/sWWm5+U+DS5rfjHtlSCd/gI9mlreV0SF4YeWh2H0bmq5U+kudSA84Xpd2YwLIdUpMMzy" +
                    "Vh1BqtcKUi5JNixZeF9Eai4SqImvGYCuAF5J1vpB623Zc9YwW1bfr7usffWJUozipek8iKHhE70CgPVT" +
                    "JN/xO1r0SM60RWkT8lj60+mlL18kMwbYYWek0Ohb6Te28+G+jxjcuYZsKSpQrJRot69ycmC4lLwuEyj7" +
                    "PC0JcnC5/pWz6sY9NwUs4bnQBxTpoWd1vXhi9MnE3GWBy7/vOHAZWY565/G802LP6lPRMDoH47VBeiBD" +
                    "W8/lAcGOa2Pl7I4EhnRApg==")

        else:
            return ("AzIceaqfA1hX5wS+M8cGnYh5ceevUnOZIzJBbXFD6dgf3tBkb9cvUF/Tkd/iKu2fsg9wAysYKw7RMAsV" +
                    "vIp4KcXle/v1RaXrLVnNBJ2H2DmrbUMOZbQUFXe698qmJsqNpLXRA367xpZ54i8kC5DTXwDhfxWTOZrB" +
                    "rh5sRKHcoVLumztIQjgWh37AzmSd1bLOfUGI0xjAL9zJWO3fRaeB0NS2KlmoKaVT5Y04zZEc06waU2r6" +
                    "AU2Dc4uipJqJmObqKM+tfNKAS0rZr5IudRiC7pUwnmtaHRe5fgSI8M7yvypvm+13Wm4Gwd4VnYiZvSxf" +
                    "8ImN3ZOG9wEzfyMIlH2+rKPUVHI+igsqla0Wd9m7ZUR9vFotj1uYV0OzG7hX0+huN2E/IdgLDjbiapj1" +
                    "e2fKHrMmGFaIvI6xzzJIQJF9GiRZ7+0jNFLKSyzX/K3JAyFrIPObfwM+y+zAgE1sWcZ1YnuBhICyRHBh" +
                    "aJDKIZL8MywrEfB2yF+R3k9wFG1oN48gSLyfrfEKuB/qgNp+BeTruWUk0AwRE9XVMUuRbjpxa4YA67SK" +
                    "unFEgFGgUfHBeHJTivvUl0u4Dki1UKAT973P+nXy2O0u239If/kRpNUVhMg8kpk7s8i6Arp7l/705/bL" +
                    "Cx4kN5hHHSXIqkiG9tHdeNV8VYo5+72hgaCx3/uVoVLmtvxbOIvo120uTJbuLVTvT8KtsOlb3DxwUrwL" +
                    "zaEMoAQAFk6Q9bNipHxfkRQER4kR7IYTMzSoW5mxh3H9O8Ge5BqVeYMEW36q9wnOYfxOLNw6yQMf8f9s" +
                    "JN4KhZty02xm707S7VEfJJ1KNq7b5pP/3RjE0IKtB2gE6vAPRvRLzEohu0m7q1aUp8wAvSiqjZy7FLaT" +
                    "tLEApXYvLvz6PEJdj4TegCZugj7c8bIOEqLXmloZ6EgVnjQ7/ttys7VFITB3mazzFiyQuKf4J6+b/a/Y")


if __name__ == '__main__':
    sd = HaspDriver()
    sd.hasp_login_scope(1879543462)
    print(sd.get_rtc())
    sd.logout()
