#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : driver.py
# @Author: guq  
# @Date  : 2021/5/20
# @Desc  :


from ...core import exceptions
from ...core.tools import Tools
from ...dog.saas_dog.provider import HUAWEICloudProvider, AliCloudProvider, TencentCloudProvider


class SaasDogDriver:

    def __init__(self):
        """SaasDogDriver"""

    def login(self):
        """没有实际的登陆形式，只是校验证书中的account-id与云服务器厂商返回的是否一致"""
        return True

    def logout(self):
        return True

    def get_rtc(self):
        """东八区标准时间 """
        return int(Tools.now().timestamp())

    def get_identity(self, cloud_name):
        cloud_provider = {
            HUAWEICloudProvider.name: HUAWEICloudProvider,
            AliCloudProvider.name: AliCloudProvider,
            TencentCloudProvider.name: TencentCloudProvider,
        }
        if cloud_name not in cloud_provider:
            raise exceptions.SaasDogUnsupportedIDCCloudError('暂时不支持当前云服务商部署, 当前服务商： %s' % cloud_name)
        return cloud_provider.get(cloud_name)().get_identity()

