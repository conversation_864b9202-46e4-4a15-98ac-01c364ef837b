#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : adaptor.py
# @Author: guq  
# @Date  : 2021/5/20
# @Desc  :

import datetime
from ...core.base import BaseAdaptor, BaseDog
from ...dog.saas_dog.driver import SaasDogDriver


class SaasDog(BaseDog):
    def __init__(self, login_args: tuple = (), *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.driver = SaasDogDriver()
        self.login_args = login_args

    def login(self, *args, **kwargs):
        return self.driver.login()

    def logout(self, *args, **kwargs):
        return self.driver.logout()

    def get_identity(self, cloud_name):
        return self.driver.get_identity(cloud_name)

    def get_real_time(self, *args, **kwargs) -> datetime.datetime:
        timestamp = self.driver.get_rtc()
        return datetime.datetime.fromtimestamp(timestamp)


# 产品硬软狗驱动适配
class SaasDogAdaptor(BaseAdaptor):
    def __init__(self, login_args: tuple = (), *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.dog = SaasDog(login_args)

    # 验证连接
    def verify(self):
        try:
            self.dog.get_identity(*self.dog.login_args)
            return True
        finally:
            self.dog.logout()

    # 获取狗当前时间
    def get_real_time(self) -> datetime.datetime:
        return self.dog.get_real_time()

