#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : test_sign.py
# @Author: guq
# @Date  : 2021/5/24
# @Desc  :
import base64
from base64 import b64encode, b64decode

from Crypto.Hash import SHA  # noqa
from Crypto.PublicKey import RSA  # noqa
from Crypto.Signature import PKCS1_v1_5  # noqa
from OpenSSL import crypto


def public_key_verify(message, sign, public_key):
    # use public key certificate to verify
    cert = crypto.load_certificate(crypto.FILETYPE_PEM, public_key.strip().encode('utf-8'))
    public_key = crypto.dump_publickey(crypto.FILETYPE_PEM, cert.get_pubkey())
    _rsa = RSA.importKey(public_key)
    data_hash = SHA.new(message.encode('utf-8'))  # noqa
    _signer = PKCS1_v1_5.new(_rsa)
    return _signer.verify(data_hash, base64.decodebytes(sign.encode('utf-8')))


def public_key_verify_dog(message, sign, public_key):
    cert = crypto.load_certificate(crypto.FILETYPE_PEM, public_key.strip().encode('ascii'))
    public_key = crypto.dump_publickey(crypto.FILETYPE_PEM, cert.get_pubkey())
    _rsa = RSA.importKey(public_key)
    data_hash = SHA.new(message.encode('utf-8'))  # noqa
    _signer = PKCS1_v1_5.new(_rsa)
    return _signer.verify(data_hash, base64.decodebytes(sign.encode('utf-8')))
