#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : base.py
# @Author: guq
# @Date  : 2021/5/20
# @Desc  :
import requests
import xml.etree.ElementTree as ET
from . import exceptions


class BaseDog:

    def __init__(self, *args, **kwargs):
        """BaseDog"""

    def login(self, *args, **kwargs):
        raise NotImplementedError

    def logout(self, *args, **kwargs):
        raise NotImplementedError

    def get_real_time(self, *args, **kwargs):
        raise NotImplementedError


class BaseFactory:
    def __init__(self, *args, **kwargs):
        """BaseFactory"""

    def create(self, *args, **kwargs):
        raise NotImplementedError


class BaseAdaptor:
    def __init__(self, *args, **kwargs):
        """BaseAdaptor"""

    def verify(self, *args, **kwargs):
        raise NotImplementedError

    def get_real_time(self, *args, **kwargs):
        raise NotImplementedError


class Singleton(type):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super(Singleton, cls).__call__(*args, **kwargs)
        return cls._instances[cls]


class BaseIDCCloudProvider(metaclass=Singleton):
    name = 'base_idc_provider'

    def __init__(self, *args, **kwargs):
        self.idc_meta_url = ''

    def get_identity(self, *args, **kwargs):
        raise NotImplementedError

    @staticmethod
    def http_request(method='get', **kwargs) -> requests.Response:
        if 'timeout' not in kwargs:
            kwargs['timeout'] = 15
        return getattr(requests, method.lower())(**kwargs)


class BaseValidator:
    def __init__(self, *args, **kwargs):
        """BaseValidator"""

    def load_raw_xml(self, path):
        with open(path, 'r', encoding='utf-8', newline='\r') as f:
            return f.read()

    @staticmethod
    def load_xml(path):
        try:
            tree = ET.parse(path)
        except:
            raise exceptions.LicenseError('License验证失败（L000）：License不是合法的xml')
        return tree.getroot()

    def set_dog_type(self, dog_type_from_env):
        self.dog_type_from_env = dog_type_from_env
        return self

    def validate_all(self, *args, **kwargs):
        raise NotImplementedError

    def validate_format(self, *args, **kwargs):
        raise NotImplementedError

    def validate_sign(self, *args, **kwargs):
        raise NotImplementedError

    def validate_info(self, *args, **kwargs):
        raise NotImplementedError
