#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : exceptions.py
# @Author: guq
# @Date  : 2021/5/20
# @Desc  :


# 异常基类
class BaseBootException(Exception):
    def __init__(self, msg='', code=503):
        self.code = code
        self.msg = msg

    def __str__(self):
        return '[code:%s] %s' % (self.code, self.msg)


class BaseValidateError(BaseBootException):
    def __init__(self, msg='', code=500):
        self.code = code
        self.msg = msg


# 异常基类
class BaseDogException(BaseBootException):
    def __init__(self, msg='', code=503):
        self.code = code
        self.msg = msg


# 狗库文件找不到
class ProductDogLibNotFoundError(BaseDogException):
    """pass"""


# 不支持的系统平台
class ProductDogUnSupportedPlatformError(BaseDogException):
    """pass"""


# 调用狗异常
class ProductDogExecProcessError(BaseDogException):
    """pass"""


# saas狗请求云服务商失败
class SaasDogIDCCloudRequestError(BaseDogException):
    """pass"""


# 不支持的系统平台
class SaasDogUnsupportedIDCCloudError(BaseDogException):
    """pass"""


# license guid未找到
class LicenseNodeNotFoundError(BaseValidateError):
    """pass"""


# 启动环境异常
class BootEnvError(BaseBootException):
    def __init__(self, msg='', code=503):
        self.code = code
        self.msg = msg


# license 文件未找到
class LicenseNotFoundError(BaseValidateError):
    """pass"""


# license 签名错误
class LicenseSignatureError(BaseValidateError):
    def __init__(self, msg='', code=502):
        self.code = code
        self.msg = msg


# license 错误
class LicenseError(BaseValidateError):
    def __init__(self, msg='', code=502):
        self.code = code
        self.msg = msg


# license 错误
class DogTypeError(BaseBootException):
    """pass"""


# license 错误
class NotFoundError(BaseBootException):
    """pass"""


# license 错误
class PermissionError(BaseBootException):
    """pass"""


# license 错误
class ConfigCenterError(BaseBootException):
    """pass"""


# license 错误
class BootProcessError(BaseBootException):
    """pass"""


# license 错误
class VerifyNetworkError(BaseBootException):
    """pass"""
