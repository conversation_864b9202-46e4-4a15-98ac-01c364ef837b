#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : dog_type.py
# @Author: guq  
# @Date  : 2021/5/25
# @Desc  :

from enum import Enum


class DogType(Enum):
    product_dog = 'ProductDog'
    product_sl_dog = 'ProductSLDog'
    saas_dog = 'CloudDogForSaas'
    # test_dog = 'CloudDogForSaas'
    developer_dog = 'DeveloperDog'
    cloud_dog = 'CloudDog'


SUPPORTED_DOG_TYPES = [v.value for v in DogType._member_map_.values()]
SUPPORTED_DOG_TYPES_LOWER = [i.lower() for i in SUPPORTED_DOG_TYPES]
