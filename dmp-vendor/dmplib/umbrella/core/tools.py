#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : tools.py
# @Author: guq  
# @Date  : 2021/5/26
# @Desc  :


import datetime
import time
import functools
from .dog_type import SUPPORTED_DOG_TYPES_LOWER
from ... import config


class Tools:
    @staticmethod
    def now() -> datetime.datetime:
        return datetime.datetime.utcnow() + datetime.timedelta(hours=8)


def retry(tries=1, delay=2):
    def retry_wrapper_1(func):
        @functools.wraps(func)
        def retry_wrapper_2(*args, **kwargs):
            for retry_time in range(1, tries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if retry_time == tries:
                        raise e
                    time.sleep(delay)

        return retry_wrapper_2

    return retry_wrapper_1


class CachedProperty(object):  # 这是官方的
    def __init__(self, func):
        self.func = func

    def __get__(self, obj, cls):
        if obj is None:
            return self
        value = obj.__dict__[self.func.__name__] = self.func(obj)
        return value


# 判断是否走老环境
def dispatch_old_env():
    return True
