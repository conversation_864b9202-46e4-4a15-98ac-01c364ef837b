
### umbrella

 Python版写狗和license的SDK

![img.png](img.png)

### 说明
 现支持：
  - ProductDog
  - CloudDogForSaas
  - DeveloperDog
  - CloudDog(已不在出库，功能可能存在缺陷，还在验证)


### 使用
  1. 准备一种狗类型的license文件
     
  2. 实例化api
```python
from umbrella.api import LicenseApi # noqa

license_api = LicenseApi()
```

  3. 设置狗类型
```python
license_api.set_dog_type('ProductDog') # noqa
``` 

  4. 校验license(同时校验license.xml和licenseDog.xml)
```python
license_api.set_license_path('/abc/license2.xml') # noqa
license_api.set_license_dog_path('/abc/licenseDog.xml') # noqa
license_api.do_validate() # noqa
```

  5. 校验license.xml(单独)
```python
license_api.set_license_path('/abc/license2.xml') # noqa
license_api.validate_license() # noqa
```

  6. 校验licenseDog.xml(单独), 校验dog同时也需要license2.xml的内容
```python
license_api.set_license_path('/abc/license2.xml') # noqa
license_api.set_license_dog_path('/abc/licenseDog.xml') # noqa
license_api.validate_license_dog() # noqa
```

  7. 返回license2.xml产品信息节点, 返回对象是OrderedDict
```python
result = license_api.load_license() # noqa
print(result)

# <system enabled="1" name="天际开放平台" code="Skyline">
#     <subsystem enabled="1" name="数见" code="shujian" version="5.0.0.0" expires="2022-01-01">
#       <resource name="reportNumber" bgndate="2021-01-01" enddate="2021-12-31" value="100" />
#       <resource name="reportNumber" bgndate="2022-01-01" enddate="2023-12-31" value="100" />
#     </subsystem>
# </system>
# 
# 
# OrderedDict([('subsystem',
#               OrderedDict([('@code', 'shujian'),
#                            ('@enabled', '1'),
#                            ('@expires', '2022-01-01'),
#                            ('@name', '数见'),
#                            ('@version', '5.0.0.0'),
#                            ('resource',
#                             [OrderedDict([('@bgndate', '2021-01-01'),
#                                           ('@enddate', '2021-12-31'),
#                                           ('@name', 'reportNumber'),
#                                           ('@value', '100')]),
#                              OrderedDict([('@bgndate', '2022-01-01'),
#                                           ('@enddate', '2023-12-31'),
#                                           ('@name', 'reportNumber'),
#    

```