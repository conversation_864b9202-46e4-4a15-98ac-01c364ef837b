#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @File  : middleware.py
# @Author: guq
# @Date  : 2021/5/28
# @Desc  :

import builtins
import time
import hug
import logging
# import threading
from gevent.pool import Pool

pool = Pool(1)


class LicenseMiddleware(object):

    def __init__(self):
        self.verify_interval = builtins.interval['verify']  # noqa
        self.pull_interval = builtins.interval['pull']  # noqa
        self.boot = None
        self.start_boot_validate()

    def start_boot_validate(self):
        if not hasattr(builtins, 'boot_process_obj'):
            raise ValueError('builtins没有绑定boot对象')

        self.boot = getattr(builtins, 'boot_process_obj')
        self.boot.start_validate()

    @staticmethod
    def now():
        return time.time()

    def process_request(self, req, resp):
        """
        license_validate参数
        is_invalid:             是否异常
        failed_times:           失败次数
        limit_times:            失败限制次数
        last_verify_result:     上次校验结果
        last_verify_at:         上次校验时间
        message:                异常信息

        终止关系（优先级）：
        1.  is_invalid -> 立即终止
        2.  连续失败次数达到限制 -> 立即终止

        运行情况：
        1. 一直校验未失败过，正常运行
        2. 有失败次数未达到限制，达到限制之前，后续校验成功，清空失败次数，正常运行
        3. 有失败次数达到限制，终止运行
        """
        license_validate = builtins.license_validate  # noqa type: dict

        # 1. 检测当前是否需要立即终止
        if license_validate['is_invalid']:
            return

        now = self.now()

        # lock = threading.Lock()
        # with lock:
        # 更新license本地文件
        if self.start_a_task(
                last_at=license_validate['last_pull_at'],
                now=now,
                limit=self.pull_interval,
                task=self.boot.boot_files_prepare
        ):
            license_validate['last_pull_at'] = now

        # 2. 不需要终止，达到间隔时间进行校验license， 本地校验
        if self.start_a_task(
                last_at=license_validate['last_verify_at'],
                now=now,
                limit=self.verify_interval,
                task=self.boot.runtime_validate
        ):
            logging.info('license_validate: %s', license_validate)
            license_validate['last_verify_at'] = now

    @staticmethod
    def start_a_task(now, last_at, limit, task) -> bool:
        if (now - last_at) > limit:
            pool.spawn(task)
            return True
        return False

    # pylint: disable=unused-argument
    def process_response(self, req, resp, resource, process_response=True):
        pass
        # license_validate = builtins.license_validate  # noqa type: dict
        # if license_validate['is_invalid']:
        #     resp.content_type = 'text/plain'
        #     resp.body = license_validate['message']  # noqa
        #     resp.status = hug.falcon.HTTP_403
