#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os
from loguru import logger
import traceback
from .. import config


class FastLoggerUtil:
    __instance = {}

    NAME_PREFIX = '__fast_log'

    LOG_PATH = '/data/logs'

    @staticmethod
    def get_instance(name, folder_name):
        if not name or not folder_name:
            raise Exception('参数错误，天眼日志初始化失败')
        if FastLoggerUtil.__instance.get(name):
            return FastLoggerUtil.__instance.get(name)
        FastLoggerUtil.__instance[name] = FastLoggerUtil._init_logger(name, folder_name)
        return FastLoggerUtil.__instance[name]

    @staticmethod
    def _init_logger(name, folder_name):
        file_name = "{time:YYYY-MM-DD HH}.log"
        file_path = os.path.join(FastLoggerUtil._get_path(folder_name), file_name)

        # 天眼文件日志最大保留个数，默认2个
        fast_retention = int(config.get('Log.fast_retention', 2))
        logger.add(
            file_path, format='{message}', encoding="utf-8",
            filter=FastLoggerUtil.make_filter(name), rotation="1 h", retention=fast_retention
        )
        log = logger.bind(name=name)
        return log

    @staticmethod
    def _get_path(folder_name):
        try:
            folder_path = os.path.join(FastLoggerUtil.LOG_PATH, folder_name)
            if not os.path.exists(folder_path):
                os.makedirs(folder_path)
        except Exception as e:
            logger.error(f"创建目录{folder_path}失败，errs:{str(e)}")
        return folder_path

    @staticmethod
    def make_filter(name):
        """
        天眼日志写入过滤器
        """
        def name_filter(record):
            return record["extra"].get("name") == name
        return name_filter

    @classmethod
    def not_make_filter(cls):
        """
        控制台日志输出过滤器函数
        """
        def name_filter(record):
            try:
                record_name = record["extra"].get("name")
                return not (record_name and record_name.startswith(cls.NAME_PREFIX))
            except Exception as e:
                logger.error(f"控制台输出天眼日志过滤错误，errs：{str(e)} , traceback: \n{traceback.format_exc()}")
                return True
        return name_filter


def fast_logger_instance(log_name, folder_name):
    try:
        return FastLoggerUtil.get_instance(log_name, folder_name)
    except Exception as e:
        logger.error(f"天眼loguru实例化错误，errs：{str(e)} , traceback: \n{traceback.format_exc()}")

