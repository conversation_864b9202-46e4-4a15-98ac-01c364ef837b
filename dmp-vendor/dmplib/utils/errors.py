# -*- coding: utf-8 -*-
"""
    Errors
"""
import falcon


class Error(Exception):
    pass  # 错误基类


class UserError(Error):
    def __init__(self, code=None, message='', trans=False):
        """
        业务异常基类, 返回message给用户
        :param int code:
        :param str message:
        """
        from dmplib.locale import trans_msg

        self.code = code or 500
        self.message = message if trans is False else trans_msg(message)
        super().__init__()

    def __str__(self):
        return self.message


class FriendlyError(Error):
    def __init__(self, code=None, message=''):
        """
        业务异常捕获温和类, 返回message给用户
        :param int code:
        :param str message:
        """
        super().__init__()
        self.code = code or 600
        self.message = message

    def __str__(self):
        return self.message


class UserErrorWithExtraInfo(Error):
    def __init__(self, code=None, message='', extra_info=None):
        """
        返回message和extra_info给用户
        :param int code:
        :param str message:
        :param dict extra_info:
        """
        super().__init__()
        self.code = code or 500
        self.message = message
        self.extra_info = extra_info

    def __str__(self):
        return self.message


class HttpError(falcon.HTTPError):
    """
    http异常类类, 需要返回http status
    """

    def __init__(self, status, message=None, code=None):
        super(HttpError, self).__init__(status=status, title='HTTPError', description=message, code=code)


class InvalidArgumentError(UserError):
    pass  # 无效参数异常


class ServerError(Exception):
    def __init__(self, message=''):
        """
        系统异常
        :param str message:
        """
        super().__init__(500, message)


class DatasetQueryError(UserError):
    def __init__(self, code: int = 0, message: str = '', error_code: int = 0):

        self.code = code or 503
        self.message = message
        self.error_code = error_code