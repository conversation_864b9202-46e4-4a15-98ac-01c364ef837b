# -*- coding: utf-8 -*-
"""
    for description
"""
import csv
import datetime
import hashlib
import io
import math
import random
import re
import time
import uuid

from pypinyin import lazy_pinyin, FIRST_LETTER
from pypinyin.constants import PINYIN_DICT

reg_fletcher = re.compile(r'^[A-Z]+_([0-9]{5,})$')


def _get_random_chars(char_length):
    chars = 'abcdef0123456789'
    i = 0
    res = ''
    while i < char_length:
        idx = math.floor(1 + random.random() * 16)
        res += chars[idx - 1 : idx]
        i += 1
    return res


def seq_id():
    """
    获取有序GUID与 db中fn_newSeqId 算法保持一致
    :return:str
    """
    now = datetime.datetime.utcnow().timestamp()
    ticks = hex(round(now * 1000000))[2:]
    old_ticks = hex(round(now * 1000 + 62135625600000))[2:]
    return '%s-%s-%s%s-%s-%s' % (
        old_ticks[:8],
        old_ticks[8:12],
        ticks[10:13],
        _get_random_chars(1),
        _get_random_chars(4),
        _get_random_chars(12),
    )


def uid(node=None, close_seq=None):  # pylint: disable=W0613
    """ GUID for db """

    timestamp = int(round(time.time() * 1000)) + 62135596800000
    max_uint = 4294967295

    high = int(timestamp / max_uint)
    low = int((timestamp % max_uint) - high)

    sl_high = str(hex(high))  # 十六进制字符串, 例如：0x39db
    sl_low = str(hex(low))

    guid = sl_high[2:] + sl_low[2:6] + '-' + sl_low[6:10]

    # 填充其余
    chars = 'abcdef0123456789'
    str_list = []
    i = 1
    while i <= 3:
        j = 1
        tmp = ''
        max_ = 12 if i == 3 else 4
        while j <= max_:
            ind = random.randint(0, 15)
            tmp = tmp + chars[ind : (ind + 1)]
            j = j + 1

        str_list.append(tmp)
        i = i + 1

    guid = guid + '-' + '-'.join(str_list)
    return guid


def uid4():
    return str(uuid.uuid4())


def str_getcsv(string, delimiter=',', enclosure='"', escape='\\'):
    with io.StringIO(string) as f:
        reader = csv.reader(f, delimiter=delimiter, quotechar=enclosure, escapechar=escape)
        return next(reader)


def conver_to_utf8(string):
    return string.encode('utf-8')


def is_number(string):
    """
    判断字符串是否是数字
    :param s: 字符串，或还有数字字符串,'abc' 或者 '32423423'
    :return: True or False
    """

    try:
        float(string)  # 将字符串转换成数字成功则返回True
        return True
    except ValueError:
        return False  # 如果出现异常则返回False


def uniqid(prefix=''):
    """
    获取唯一id，长度13
    :param prefix string 前缀
    :return: True or False
    """
    return prefix + hex(int(time.time()))[2:10] + hex(int(time.time() * 1000000) % 0x100000)[2:7]


def is_alpha(word):
    try:
        return word.encode('ascii').isalpha()
    except UnicodeEncodeError:
        return False


def is_ascii(word):
    try:
        word.encode("ascii")
        return True
    except UnicodeEncodeError:
        return False


def get_first_pinyin_hanzi(text):
    """
    提取字符中的拼音首字母和字母字符
    示例: get_first_pinyin_hanzi('s程1序*员!')  -> SCXY
    :param text:
    :return:
    """
    if not text:
        return ''

    pinyin_chars = []
    for single_char in text:
        if is_alpha(single_char):
            pinyin_chars.append(single_char)
        elif ord(single_char) in PINYIN_DICT:
            pinyin_chars = pinyin_chars + lazy_pinyin(single_char, FIRST_LETTER, errors='ignore')

    return ''.join(pinyin_chars).upper()


def fletcher32(text):
    """
    将字符串转换成数字
    参考: https://en.wikipedia.org/wiki/Fletcher%27s_checksum
    :param text:
    :return:
    """
    matcher = reg_fletcher.fullmatch(text)
    if matcher:
        groups = matcher.groups()
        if groups and len(groups) == 1:
            return fletcher32(hashlib.sha1(text.encode('utf-8')).hexdigest())

    a = list(map(ord, text))
    b = [sum(a[:i]) % 65535 for i in range(len(a) + 1)]
    return (sum(b) << 16) | max(b)
