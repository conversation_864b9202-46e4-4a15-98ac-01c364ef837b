from time import time
import random

class SnowflakeGenerator:
    def __init__(self, machine_id=1, datacenter_id=1):
        self.machine_id = machine_id
        self.datacenter_id = datacenter_id
        self.sequence = 0
        self.last_timestamp = -1

        # 这些常量是用于计算 ID 的位数
        self.timestamp_bits = 41  # 时间戳部分的位数
        self.datacenter_id_bits = 5  # 数据中心 ID 的位数
        self.machine_id_bits = 5  # 机器 ID 的位数
        self.sequence_bits = 12  # 序列号的位数

        # 位移量
        self.datacenter_id_shift = self.sequence_bits + self.machine_id_bits
        self.machine_id_shift = self.sequence_bits
        self.timestamp_shift = self.sequence_bits + self.machine_id_bits + self.datacenter_id_bits

        # 最大值
        self.max_datacenter_id = (1 << self.datacenter_id_bits) - 1
        self.max_machine_id = (1 << self.machine_id_bits) - 1
        self.max_sequence = (1 << self.sequence_bits) - 1

        # 生成器参数检查
        if self.datacenter_id > self.max_datacenter_id or self.datacenter_id < 0:
            raise ValueError(f"datacenter_id must be between 0 and {self.max_datacenter_id}")
        if self.machine_id > self.max_machine_id or self.machine_id < 0:
            raise ValueError(f"machine_id must be between 0 and {self.max_machine_id}")

    def _current_timestamp(self):
        """获取当前时间戳（毫秒）"""
        return int(time() * 1000)

    def generate_id(self):
        """生成雪花 ID"""
        timestamp = self._current_timestamp()

        if timestamp < self.last_timestamp:
            raise Exception("Clock is moving backwards. Rejecting requests until {}.".format(self.last_timestamp))

        if self.last_timestamp == timestamp:
            self.sequence = (self.sequence + 1) & self.max_sequence
            if self.sequence == 0:
                while timestamp <= self.last_timestamp:
                    timestamp = self._current_timestamp()
        else:
            self.sequence = random.randint(0, self.max_sequence)

        self.last_timestamp = timestamp

        # 生成雪花 ID
        snowflake_id = ((timestamp << self.timestamp_shift) |
                        (self.datacenter_id << self.datacenter_id_shift) |
                        (self.machine_id << self.machine_id_shift) |
                        self.sequence)

        return snowflake_id

# 创建生成器实例
generator = SnowflakeGenerator(machine_id=1, datacenter_id=1)

# 生成 ID
snowflake_id = generator.generate_id()
print("Generated Snowflake ID:", snowflake_id)

# 确保生成的 ID 不超过 20 位
print("Length of ID:", len(str(snowflake_id)))
