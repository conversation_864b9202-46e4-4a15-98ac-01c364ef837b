import sys
import logging
from loguru import logger

from .. import config

from raven.handlers.logging import <PERSON><PERSON><PERSON><PERSON><PERSON>
from raven import Client, setup_logging

from .fast_logger import FastLoggerUtil
from ..conf_constants import LOG_LEVEL, LOG_SENTRY_DSN

LOGURU_FORMAT = ' '.join(
    (
        '<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green>',
        '<level>{level.name}</level>',
        '<cyan>{name}.{function}:{line}</cyan>',
        '<level>{message}</level>',
    )
)

LOG_LEVEL_TO_NAME = {5: 'TRACE', 10: 'DEBUG', 20: 'INFO', 25: 'SUCCESS', 30: 'WARNING', 40: 'ERROR', 50: 'CRITICAL'}

LOG_NAME_TO_LEVEL = {v: k for k, v in LOG_LEVEL_TO_NAME.items()}


class InterceptHandler(logging.Handler):
    def emit(self, record):
        # Get corresponding Loguru level if it exists
        try:
            level = logger.level(record.levelname).name
        except (ValueError, AttributeError):
            level = "ERROR"

        # Find caller from where originated the logging call
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

        # level_name = LOG_LEVEL_TO_NAME.get(level, 'INFO')
        logger.opt(depth=depth, exception=record.exc_info).log(level, record.getMessage())


def init_logging():
    level_name = LOG_LEVEL
    backtrace = False
    logger.stop()
    # 排除天眼日志的控制台输出
    logger.add(
        sys.stderr, colorize=True, format=LOGURU_FORMAT,
        level=level_name, backtrace=backtrace, filter=FastLoggerUtil.not_make_filter()
    )
    logging.basicConfig(handlers=[InterceptHandler()], level=level_name)
    setup_sentry(level_name)
    logging.getLogger("pika").setLevel(logging.WARNING)

def setup_sentry(level_name: str):
    _handlers = 'console'

    if 'sentry' in _handlers:
        sentry_dsn = LOG_SENTRY_DSN
        if sentry_dsn is not None and sentry_dsn != '':
            tags = {'env_code': config.get_env_code()}
            client = Client(
                dsn=sentry_dsn,
                level=level_name,
                tags=tags,
                string_max_length=1024,
                sanitize_keys=['password', 'pwd', 'token', 'apikey', 'secret', 'passwd'],
            )
            _sentry_handler = SentryHandler(client)
            setup_logging(_sentry_handler)
