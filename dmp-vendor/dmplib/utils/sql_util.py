#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    Created by chenc04 on 2017/6/19
"""
import re
import dmPython

from .errors import UserError
from ..components.enums import DBType


def validate_key_word(sql_str): # NOSONAR
    """
    校验sql语句是否带有关键字
    :param sql_str:
    :return:
    """
    if not sql_str:
        raise UserError(message='sql内容不能为空')
    sql_key_word = [
        'truncate',
        'create',
        'add',
        'alert',
        'modify',
        'delete',
        'insert',
        'update',
        'drop',
        'set',
        'goto',
        'show',
        # 'index',
        'prepare',
        'execute',
        'deallocate',
        'begin',
        # 'if',
        'call',
        'return',
        'sleep',
        'user',
    ]
    pattern = r'(\b' + r'\b)|(\b'.join(sql_key_word) + r'\b)'
    match = re.compile(pattern, re.IGNORECASE).search(sql_str)
    if match:
        is_keyword_forbidden = True
        kw = match.group()
        if match.group().lower() == "index":
            # force index特殊处理
            force_index_pattern = r'(\b' + r'force\s+index' + r'\b)'
            force_index_match_all = re.compile(force_index_pattern, re.IGNORECASE).findall(sql_str)
            index_pattern = r'(\b' + 'index' + r'\b)'
            index_match_all = re.compile(index_pattern, re.IGNORECASE).findall(sql_str)

            if index_match_all and force_index_match_all and len(index_match_all) == len(force_index_match_all):
                is_keyword_forbidden = False  # NOSONAR
        elif match.group().lower() == "sleep":
            # 禁用 sleep() 函数，不禁用sleep关键字
            sleep_pattern = r'(\b' + 'sleep' + r'\b\s*\(\s*\d+\s*\))'
            sleep_match_all = re.compile(sleep_pattern, re.IGNORECASE).findall(sql_str)
            if not sleep_match_all:
                is_keyword_forbidden = False  # NOSONAR
            kw = f'{kw}()'
        elif match.group().lower() == "user":
            # 禁用 user() 函数，不禁用 user 关键字
            user_pattern = r'(\b' + 'user' + r'\b\s*\(\s*\))'
            user_match_all = re.compile(user_pattern, re.IGNORECASE).findall(sql_str)
            if not user_match_all:
                is_keyword_forbidden = False  # NOSONAR
            kw = f'{kw}()'
        if is_keyword_forbidden:
            raise UserError(message='禁止使用关键字：' + kw)


def get_select_match(sql_str, select_match):
    table_names = []
    select_prog = re.compile(select_match, re.S | re.I | re.M)
    search_data = select_prog.findall(sql_str)
    if search_data:
        for data in search_data:
            table_names.append(data.strip())
    return table_names


def get_select_table_name(sql_str):
    """
    获取sql语句中的所有表名
    :param sql_str:
    :return:
    """
    select_match = r'[from|join]\s+({\w+}|\w+)'
    return get_select_match(sql_str, select_match)


def get_select_table_col_name(sql_str):
    """
    获取sql语句中的所有字段
    :param sql_str:
    :return:
    """
    table_col_names = []
    result = re.findall("select(.*)from", sql_str, flags=re.I)
    for x in result:
        for col in x.split(','):
            table_col_names.append(col)
    return table_col_names


col_data_type = {
    1: 'tinyint',
    2: 'smallint',
    3: 'int',
    4: 'float',
    5: 'double',
    7: 'timestamp',
    8: 'bigint',
    9: 'mediumint',
    12: 'datetime',
    15: 'varchar',
    252: 'text',
    253: 'varchar',
    254: 'char',
    246: 'decimal',
}

# 维度类型
dim_type = ['char', 'varchar', 'text', 'datetime', 'timestamp', 'time', 'date']

# 数值类型
measure_type = [
    'tinyint', 'smallint', 'int', 'float', 'double', 'bigint', 'mediumint', 'decimal', 'numeric', 'dec', 'integer',
    'byte', 'binary', 'varbinary', 'raw', 'real', 'double precision', 'bit', 'number', 'interval month'
]
# 字符串类型
str_type = [
    'char',
    'varchar',
    'tinyblob',
    'tinytext',
    'blob',
    'text',
    'mediumblob',
    'mediumtext',
    'longblob',
    'longtext',
    'character',
    'varchar2',
    'nvarchar',
    'clob'
]
# 日期和时间类型
date_type = ['date', 'time', 'year', 'datetime', 'timestamp']

# 枚举类型
enumerate_type = ['set', 'enum']


class Description:
    # NOSONAR
    def __init__(
            self, name, type_code, display_size, internal_size, precision, scale, null_ok, alias_name=None  # NOSONAR
    ):
        self.name = name
        self.type_code = type_code
        self.display_size = display_size
        self.internal_size = internal_size
        self.precision = precision
        self.scale = scale
        self.null_ok = null_ok
        self.alias_name = alias_name
        self._data_type = None
        self._col_type = None

    @property
    def data_type(self):
        if not self._data_type:
            if self.type_code == 252 and self.internal_size == -1:
                self._data_type = 'longtext'
            else:
                self._data_type = col_data_type.get(self.type_code, '')
        return self._data_type

    @property
    def col_type(self):
        if not self._col_type:
            self._col_type = self.data_type
            data_length = self._get_data_length()
            if data_length:
                self._col_type += '(' + data_length + ')'
        return self._col_type

    def get_format_col(self):
        return '`%s` %s' % (self.name, self.col_type)

    def _get_data_length(self):
        if self.type_code in [253, 254]:
            return str(self.internal_size)
        elif self.type_code in [1, 2, 8, 9]:
            return str(self.internal_size)
        elif self.type_code in [246]:
            # 解决decimal长度超过65创建表报错的问题
            if self._data_type == 'decimal' and self.precision > 65:
                return '65,' + str(self.scale)
            else:
                return str(self.precision) + ',' + str(self.scale)
        else:
            return ''


class DmDescription(Description):

    col_data_type = {
        dmPython.STRING: 'VARCHAR',
        dmPython.NUMBER: 'NUMBER',
        dmPython.DATETIME: 'DATETIME',
        dmPython.BINARY: 'BINARY',
        dmPython.CLOB: 'CLOB',
        dmPython.BLOB: 'BLOB',
        dmPython.DATE: 'DATE',
        dmPython.TIME: 'TIME',
        dmPython.TIMESTAMP: 'TIMESTAMP',
        dmPython.DECIMAL: 'DECIMAL',
        dmPython.DOUBLE: 'FLOAT',
        dmPython.BIGINT: 'BIGINT',
        dmPython.REAL: 'REAL',
        dmPython.FIXED_STRING: 'CHAR',
        dmPython.BOOLEAN: 'BOOLEAN',
        dmPython.INTERVAL: 'INTERVAL',
    }

    @property
    def data_type(self):
        if not self._data_type:
            self._data_type = self.col_data_type.get(self.type_code, '')
        return self._data_type

    def _get_data_length(self):
        if self.type_code in [dmPython.STRING, dmPython.FIXED_STRING]:
            return str(self.internal_size)
        elif self.type_code in [dmPython.NUMBER, dmPython.DECIMAL]:
            return str(self.precision) + ',' + str(self.scale)
        else:
            return ''


class DescriptionFactory(object):

    def __init__(self, db_type=DBType.MYSQL.value):
        self.db_type = db_type

    def create_description(
            self, name, type_code, display_size, internal_size, precision, scale, null_ok, alias_name=None
    ):
        if self.db_type == DBType.DM.value:
            return DmDescription(
                name, type_code, display_size, internal_size, precision, scale, null_ok, alias_name
            )
        else:
            return Description(
                name, type_code, display_size, internal_size, precision, scale, null_ok, alias_name
            )