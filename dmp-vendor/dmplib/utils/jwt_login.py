# -*- coding: utf-8 -*-
##################################
#  使用jwt方式登录, 支持刷新token
##################################
import time
import logging
import jwt
from jwt import DecodeError

from .. import config
from ..hug.globals import g
from ..utils.errors import UserError
from ..redis import conn_custom_prefix as conn_redis, conn
import hashlib

logger = logging.getLogger(__name__)

# user_id和token关系 hash user_id=>token
# 一个用户可以在多个设备上登录，会产生多个不同token，会导致先登录的用户点退出登录无效
USER_ID_TOKEN_RELATION = 'user_id_token_relation:'

# 用户活跃状态 + user_id => 1 表示用户活跃
USER_ID_ACTIVE = 'user_id_active:'

# 用户活跃的过期时间
USER_ID_ACTIVE_EXPIRE = 60
# 关闭浏览器，+ user_id+flag=>1 实际这里存的是浏览器最后心跳时间
USER_CLOSE_BROWSER = 'user_close_browser:'

# 保持登录状态 + user_id + flag => 1 用户要保持登录态
USER_KEEP_LOGIN = 'user_id_keep_login:'
# token存的key为 "logindmp:jwt:token:"+hash(user_id+token)


class LoginToken:
    __instance = None
    _local = {'refresh': 0}
    __login_cache_prefix = 'SKYLINE:SHUXIN:DAP:login'

    def __new__(cls):
        if LoginToken.__instance is None:
            LoginToken.__instance = object.__new__(cls)
        return LoginToken.__instance

    def __init__(self):
        self.secret = config.get('JWT.secret')
        self.expire_in = int(config.get('JWT.expires'))

    def _caching_id(self, userid, token):
        key = '%s:%s' % (userid, token)
        hash_str = hashlib.sha1(key.encode('utf-8')).hexdigest()
        return 'bigdata:jwt:token:%s' % hash_str

    def verify_single_user(self, userid, code):
        # dmp限制单个用户登录, 需要判断缓存中是否有值
        tokens = conn().lrange(USER_ID_TOKEN_RELATION + userid, 0, -1)
        if tokens:
            for token in tokens:
                key = self._caching_id(userid, token)
                if conn_redis(self.__login_cache_prefix).exists(key):
                    # 需要判断该登录用户是否活跃
                    if conn().get(USER_ID_ACTIVE + userid):
                        raise UserError(message=u'当前用户已登录！')

                    # 不活跃的情况下，可以登录，将上个设备用户踢下线
                    self.delete_token(userid, code if code else g.code)

    def delete_token(self, userid, code, only_this_token=None):
        """删除用户token"""
        setattr(g, "code", code)
        tokens = conn().lrange(USER_ID_TOKEN_RELATION + userid, 0, -1)
        if tokens:
            # 这里没有处理同一个用户名并发的场景
            left_tokens = []
            for token in tokens:
                if not only_this_token or only_this_token == token:
                    conn_redis(self.__login_cache_prefix).delete(self._caching_id(userid, token))
                else:
                    left_tokens.append(token)
            # 先删除存在的token再把剩余的token写入
            conn().delete(USER_ID_TOKEN_RELATION + userid)
            if left_tokens:
                conn().lpush(USER_ID_TOKEN_RELATION + userid, *left_tokens)
        conn().set(USER_ID_ACTIVE + userid, 1, 1)

    def create_token(self, userid, extra=None, code=None, expire_in=None, single=False):
        """

        :param userid: str
        :param extra: dict
        :return: str
        """
        if single:
            self.verify_single_user(userid, code=code)

        import random

        now = int(time.time())
        flag = now + random.randint(1, 1000)
        data = {'id': userid, '_flag': flag}
        if extra:
            for k in extra:
                if k == '_flag':
                    raise Exception('用户数据不能包含_flag属性')
                data[k] = extra[k]

        token = jwt.encode(data, self.secret)
        key = self._caching_id(userid, token)
        expire_time = expire_in if expire_in else self.expire_in
        conn_redis(self.__login_cache_prefix).set(key, data, expire_time)
        # 保存user_id和token关系
        if userid:
            conn().lpush(USER_ID_TOKEN_RELATION + userid, token)
        # TODO 这里需要设置过期时间吗？
        # conn().expire(USER_ID_TOKEN_RELATION + userid, expire_time * 100)
        if single and userid:
            # 限制单用户登陆活跃
            conn().set(USER_ID_ACTIVE + userid, '1', USER_ID_ACTIVE_EXPIRE)
        # 判断浏览器是否关闭（区分同一用户不同token), 密码访问没有userid
        if userid:
            conn().set(USER_CLOSE_BROWSER + userid + str(data.get("_flag")), '1', USER_ID_ACTIVE_EXPIRE)
        return token

    def verify_token(self, token, auto_logout=False):
        if not token:
            return False
        try:
            data = jwt.decode(token, self.secret, algorithms=["HS256"])
        except DecodeError:
            return False

        if not data or 'id' not in data:
            return False
        userid = data['id']
        cache_conn = conn_redis(self.__login_cache_prefix)
        key = self._caching_id(userid, token)
        jwt_data = cache_conn.get(key)
        if jwt_data:
            if auto_logout and data.get("code"):
                setattr(g, "code", data.get("code"))
                # 关闭浏览器不会自动下线
                if conn().get(USER_CLOSE_BROWSER + userid + str(data.get("_flag"))) and not conn().get(
                    USER_KEEP_LOGIN + userid + str(data.get("_flag"))
                ):
                    conn().set(USER_ID_ACTIVE + userid, 1, 1)
                    cache_conn.delete(key)
                    return False
            # 更新过期时间
            key_ttl = cache_conn.ttl(key)
            expire_time = self.expire_in
            if key_ttl < expire_time:
                cache_conn.expire(key, expire_time)
            return data
        else:
            return data

    def refresh_user_status(self, token, userid, code, keep_login=False, keep_expire=None, auto_logout=False):
        try:
            data = jwt.decode(token, config.get('JWT.secret'), algorithms=["HS256"])
        except DecodeError:
            return False
        if not data or 'id' not in data:
            return False
        cache_conn = conn_redis(self.__login_cache_prefix)
        key = self._caching_id(userid, token)
        jwt_data = cache_conn.get(key)
        if jwt_data:
            # 保存活跃状态, 这里使用set，不用expire的原因是，有可能key已经过期
            setattr(g, "code", code)
            if (
                auto_logout
                and conn().get(USER_CLOSE_BROWSER + userid + str(data.get("_flag")))
                and not conn().get(USER_KEEP_LOGIN + userid + str(data.get("_flag")))
            ):
                # 考虑有一些页面不请求数据，只调refresh接口，会导致一直不退出
                conn().set(USER_ID_ACTIVE + userid, 1, 1)
                cache_conn.delete(key)
                return False
            conn().set(USER_ID_ACTIVE + userid, '1', USER_ID_ACTIVE_EXPIRE)
            # 判断浏览器是否关闭
            conn().set(USER_CLOSE_BROWSER + userid + str(data.get("_flag")), '1', USER_ID_ACTIVE_EXPIRE)
            if keep_login:
                # 保持登录状态, 这里使用set，不用expire的原因是，有可能key已经过期
                conn().set(USER_KEEP_LOGIN + userid + str(data.get("_flag")), '1', keep_expire)
            return True
        return False
