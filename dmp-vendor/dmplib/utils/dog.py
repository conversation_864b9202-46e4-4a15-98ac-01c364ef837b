"""
license verify

example:

cert_public_key = '-----BEGIN CERTIFICATE-----xxxxxxxxxxxxxx-----END CERTIFICATE-----'
dog_file = '/home/<USER>/Documents/license_dog/licenseDog.xml'
license_file = '/home/<USER>/Documents/license_dog/license2.xml'
success = verify_license(cert_public_key, dog_file, license_file)
print(success)

"""
import re
import base64
import os
import io
import xmltodict
from Crypto import Random
from Crypto.Hash import SHA
from Crypto.PublicKey import RSA
from Crypto.Signature import PKCS1_v1_5
from Crypto.Cipher import PKCS1_OAEP as PKCS1_OAEP_Cipher

from OpenSSL import crypto

# 证书公钥
cert_public_key = """-----BEGIN CERTIFICATE-----
MIIDJDCCAgygAwIBAgIQmkVq2qCGgaRJ+l5g2PqadDANBgkqhkiG9w0BAQ0FADAh
MR8wHQYDVQQDExZNeW9zZnQtTmV3RG9nU2lnbi1DRVJUMCAXDTA0MTIzMTE2MDAw
MFoYDzIwOTkxMjMxMTYwMDAwWjAhMR8wHQYDVQQDExZNeW9zZnQtTmV3RG9nU2ln
bi1DRVJUMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzGo1KFXEgX3Z
J3Dh0H/9ZuKbjMfJ2De1KrnoEG+kBQNmSdapIHsD1Cp0+ZizFaTnQHtX+qW2Nu6p
oKMDqNS7wySiVHFlv9ak1GP2C6TPvLp3BlSpeOHtTbq9Q9KK0mviVmSurU9K3i2j
F9hIdm+x8N3E7itIZUUU3XUYPvVt/6fVKINx8izG0EPnVlSo0dDbKj4vjTi82FQu
z17cKXkTDmVF5D4HK6Ol/LpRsU9y6kR2i3FOaOxWOS4MlhA+9R3dbLGDFFC0OHpy
nmfCQX87ZncfDK3aHfvP0wee6bk8TlY32JCUKdcM+sK63ExrUk7FDi+yPJ9Zvihg
p2FZF6FGJQIDAQABo1YwVDBSBgNVHQEESzBJgBBIYpZnKTBMuJpcGqglBaaCoSMw
ITEfMB0GA1UEAxMWTXlvc2Z0LU5ld0RvZ1NpZ24tQ0VSVIIQmkVq2qCGgaRJ+l5g
2PqadDANBgkqhkiG9w0BAQ0FAAOCAQEAulK1/s/3OPGL6VOzEWCr5kqOam+OXvfL
GDEBb1Kk4sQzXmzJUItGrjbKzh9ylKAgb3voYboy+R0Y5cUThmdSRseyWxiJY6rU
coIAxUooaLxDeNg0er/8HMoU4WfoygfnCgmTDI2rdGh9fSvRzCWZSe4amR+lP/Vv
vy3Ttrbqtpwfz5Bovp4voapIuY6x7KHpVv5ah8iRpmxtedD92GZaclt1hoKa2YHP
smmsknFXsZ5kQThvow3IWAkLfQrWq9Cw8i0fMTXVAGaugj6F+MhD4ZGj70yKEEAT
4TbycalXtOjmIt+ZufjKj+Ng7OK7UoVlJCPF+Eb7z7P1J1ZfT0JEpg==
-----END CERTIFICATE-----"""

license_pub_key = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAm7QqIIK3YNV467wy3ErD
6eHnS/h+xXVtoMkdQpZNKbwCzzx/VxR1YfppuslQ01vNIkUTCRdi5csVCcuvMGqM
8QIzrMfHZSibqS0XZsnbY2D9HVoN4PYPKYLJKfAB8V2ee4IAfwqUFf6JYTw7wQie
yYXUDxsPYymmJDyL5wFfhEKuiY4f1OB/fXoXdCnWf5l63YGTH6y8PTGlBWpVN+rT
aQifUZ9umCMZ9ippV43mgSORJMkoPFAxU6OYr2cegOGwSgfy6rJA+xOd1nCESgfZ
ZP5ZDwiLIBNLtYeCWNdPEJBuBGzRzA5OQrLEeCxDEnzo+1dKAaJzuIlu+FNcTd7h
4QIDAQAB
-----END PUBLIC KEY-----"""

license_pub_key_test = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAm7QqIIK3YNV467wy3ErD
6eHnS/h+xXVtoMkdQpZNKbwCzzx/VxR1YfppuslQ01vNIkUTCRdi5csVCcuvMGqM
8QIzrMfHZSibqS0XZsnbY2D9HVoN4PYPKYLJKfAB8V2ee4IAfwqUFf6JYTw7wQie
yYXUDxsPYymmJDyL5wFfhEKuiY4f1OB/fXoXdCnWf5l63YGTH6y8PTGlBWpVN+rT
aQifUZ9umCMZ9ippV43mgSORJMkoPFAxU6OYr2cegOGwSgfy6rJA+xOd1nCESgfZ
ZP5ZDwiLIBNLtYeCWNdPEJBuBGzRzA5OQrLEeCxDEnzo+1dKAaJzuIlu+FNcTd7h
4QIDAQAB
-----END PUBLIC KEY-----"""


def read_license_files(dog_file_path, license_file_path):
    """
    读取授权文件
    :param dog_file_path: path of licenseDog.xml
    :param license_file_path: path of license2.xml
    :return:
    """
    if not os.path.exists(dog_file_path):
        raise FileNotFoundError(dog_file_path)
    if not os.path.exists(license_file_path):
        raise FileNotFoundError(license_file_path)

    with open(dog_file_path, 'r', encoding='utf8', newline='\r') as f:
        dog_content = f.read()
        customer_str = _parse_customer_str_from_dog_content(dog_content)
        doc = xmltodict.parse(dog_content, encoding='utf-8')
        customer = doc['CloudDog']['Customer']
        signature = doc['CloudDog']['Signature']

    with open(license_file_path, 'r', encoding='utf8', newline='\r') as f:
        license_content = f.read()
    return '%s%s' % (license_content, customer_str), signature, customer


def _parse_customer_str_from_dog_content(dog_content):
    regex = r'<Customer>.*</Customer>'
    matches = re.search(regex, dog_content, re.DOTALL)
    customer_str = matches.group()
    return re.sub(r'>(\s*\r*\n*)<', '><', customer_str)


def load_license_data(dog_data, license_data):
    doc = xmltodict.parse(dog_data, encoding='utf-8')
    customer = doc['CloudDog']['Customer']
    signature = doc['CloudDog']['Signature']
    customer_str = _parse_customer_str_from_dog_content(dog_data)
    return '%s%s' % (license_data, customer_str), signature, customer


def verify_license_file(dog_file_path, license_file_path):
    """
    license校验
    :param cert_public_key:
    :param dog_file_path: path of licenseDog.xml
    :param license_file_path: path of license2.xml
    :return:
    """
    license_data, _signature, customer = read_license_files(dog_file_path, license_file_path)
    return verify_license_data(license_data, _signature)


def verify_license_data(license_data, _signature):
    cert = crypto.load_certificate(crypto.FILETYPE_PEM, cert_public_key.strip().encode('utf-8'))
    public_key = crypto.dump_publickey(crypto.FILETYPE_PEM, cert.get_pubkey())
    _rsa = RSA.importKey(public_key)
    data_hash = SHA.new(license_data.encode('utf-8'))
    _signer = PKCS1_v1_5.new(_rsa)
    return _signer.verify(data_hash, base64.decodebytes(_signature.encode('utf-8')))


def gen_key_pair(passpharse=None):
    """
    生成key
    eg:
        pem, pub = gen_key_pair('my password')
        print('Private Key:\n%s\n' % pem)
        print('Public Key:\n%s\n' % pub)
    :param passpharse:
    :return:
    """
    random_generator = Random.new().read
    key = RSA.generate(2048, random_generator)
    return key.exportKey(passphrase=passpharse), key.publickey().exportKey()


def rsa_encrypt(message, pub):
    """
    加密数据
    eg:
        message = 'To be encrypted'
        encdata = rsa_encrypt(message.encode('utf-8'), pub)
        print('Encrypted Message:\n', encdata)
    :param message: 字符串
    :param pub:公钥
    :return:
    """
    keystream = io.BytesIO(pub)
    pubkey = RSA.importKey(keystream.read())
    cipher = PKCS1_OAEP_Cipher.new(pubkey)
    return base64.b64encode(cipher.encrypt(message))


def rsa_decrypt(ciphertext, pem, passphrase=None):
    """
    解密数据
    eg:
        decdata = rsa_decrypt(encdata, pem, passphrase)
        print('Decrypted Message:\n', decdata)
    :param ciphertext:
    :param pem: 私钥
    :param passphrase:
    :return:
    """
    ciphertext = base64.b64decode(ciphertext)
    keystream = io.BytesIO(pem)
    pemkey = RSA.importKey(keystream.read(), passphrase=passphrase)
    cipher = PKCS1_OAEP_Cipher.new(pemkey)
    return cipher.decrypt(ciphertext)


def sign(message, pem, passphrase=None):
    keystream = io.BytesIO(pem)
    pub_key = RSA.importKey(keystream.read(), passphrase)
    data_hash = SHA.new(message.encode('utf-8'))
    _signer = PKCS1_v1_5.new(pub_key)
    return base64.b64encode(_signer.sign(data_hash))


def verify_sign(message, signature, pub, passphrase=None):
    """
    验证签名
    eg:     result = verify_sign('haha', signature, pub)
            print(result)
    :param message:
    :param signature:
    :param pub:
    :param passphrase:
    :return:
    """
    keystream = io.BytesIO(pub)
    pub_key = RSA.importKey(keystream.read(), passphrase)
    signature = base64.b64decode(signature)
    data_hash = SHA.new(message.encode('utf-8'))
    _signer = PKCS1_v1_5.new(pub_key)
    return _signer.verify(data_hash, signature)


def _write_file():
    pem, pub = gen_key_pair()
    with open('/tmp/rsa.pem', 'wb') as f:
        f.write(pem)
    with open('/tmp/rsa.pub', 'wb') as f:
        f.write(pub)


def _read_rsa():
    with open('/tmp/rsa.pem', 'rb') as f:
        pem = f.read()
    with open('/tmp/rsa.pub', 'rb') as f:
        pub = f.read()
    return pem, pub
