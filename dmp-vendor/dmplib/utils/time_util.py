#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import datetime
import functools

import logging

import time


def time_me(info="耗时："):
    def _time_me(fn):
        @functools.wraps(fn)
        def _wrapper(*args, **kwargs):
            start = datetime.datetime.now()
            fn(*args, **kwargs)
            end = datetime.datetime.now()
            logging.error("函数：%s %s %s 秒" % (fn.__name__, info, (end - start).seconds))

        return _wrapper

    return _time_me


@time_me()
def test():
    time.sleep(2)


if __name__ == '__main__':
    test()
