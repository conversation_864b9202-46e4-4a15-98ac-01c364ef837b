# -*- coding: utf-8 -*-
"""
    for description
"""

import re
import time


def dist_parse(data):
    new_data = []
    for item in data:
        for key, value in item.items():
            if value == None:
                item[key] = ''
        new_data.append(item)
    return new_data


def list_page(list_data, page=None, page_size=None):
    if page is None:
        return list_data
    else:
        page = max(1, int(page))
        page_size = int(page_size) if not page_size is None else 15
        if (page - 1) * page_size - len(list_data) > 0:
            return []
        else:
            start = (page - 1) * page_size
            end = page * page_size
            return list_data[start:end]


def list_search(list_data, keyword=None, field='name'):
    if keyword is None:
        return list_data
    else:
        new_list_data = []
        for item in list_data:
            if field in item and not re.search(keyword, item[field], re.I) is None:
                new_list_data.append(item)
        return new_list_data


def strtotime(time_string, format_string='%Y-%m-%d %H:%M:%S'):
    """
   :格式化字符串时间成时间戳
   :date 2017/6/17
   :param str time_string:
   :param str format_string :
   :return :
   """

    time_string = str(time_string)

    tuple = time.strptime(time_string, format_string)
    return int(time.mktime(tuple))
