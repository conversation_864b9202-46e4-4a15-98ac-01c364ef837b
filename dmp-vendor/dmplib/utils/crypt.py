#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/3/18.
"""
from Crypto.Cipher import AES
from binascii import b2a_hex, a2b_hex
from .. import config


class AESCrypt:
    def __init__(self, key=None):
        self.key = key or config.get('Security.dmp_crypt_key')
        if self.key:
            self.key = self.key.encode('utf-8')
        self.key_length = len(self.key)
        self.mode = AES.MODE_CBC

    def encrypt(self, text):
        """
        加密
        :param str text:
        :return str:
        """
        cipher = AES.new(self.key, mode=self.mode, IV=self.key)
        text = text.encode('utf-8')
        add = self.key_length - (len(text) % self.key_length)
        text = text + b'\0' * add
        cipher_text = cipher.encrypt(text)
        return b2a_hex(cipher_text).decode('ASCII')

    def decrypt(self, text):
        """
        解密
        :param str text:
        :return str:
        """
        cipher = AES.new(self.key, mode=self.mode, IV=self.key)
        plain_text = cipher.decrypt(a2b_hex(text))
        return plain_text.rstrip(b'\0').decode('utf-8')
