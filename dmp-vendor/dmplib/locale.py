# --coding:utf-8
import gettext
import os
from loguru import logger
import json

from dmplib.hug import g
from dmplib.components.auth_util import is_env_enable_skyline_auth
from dmplib.components.ingrate_platform import IngratePlatformApi
from dmplib import nacos_client


language_domain = 'dmp'

locale_dir = os.environ.get('DMP_LOCALE_FILE_PATH') or '/home/<USER>/webapp/locale'


# 加载中文和英文的翻译资源文件
zh_trans = gettext.translation(language_domain, localedir=locale_dir, languages=['zh_CN'], fallback=True)
en_trans = gettext.translation(language_domain, localedir=locale_dir, languages=['en_US'], fallback=True)
ja_trans = gettext.translation(language_domain, localedir=locale_dir, languages=['ja_JP'], fallback=True)
zh_cht_trans = gettext.translation(language_domain, localedir=locale_dir, languages=['zh-CHT'], fallback=True)


def trans_msg(msg, language=None):
    # 基础数据的设置zh-CHS（Simplified Chinese）、zh-CHT（Traditional Chinese）、en（English）、、
    language = language or getattr(g, 'language', 'zh-CN')

    if not language:
        language = set_language()

    if language == 'en-US':
        return en_trans.gettext(msg)
    elif language in ('ja-JP', 'ja'):
        return ja_trans.gettext(msg)
    # 这里的编码还要和建模平台统一下，先取一个方便测试.看老的文档是zh-CHT
    # https://open.mingyuanyun.com/developer/knowledge/docView?id=f6de0c86-d91a-4e55-a0b2-f06c62386792
    elif language in ('zh-CHT', 'zh-HK'):
        return zh_cht_trans.gettext(msg)
    else:
        return zh_trans.gettext(msg)


def safe_get_language_from_db(code):
    """从数据库安全地获取语言设置"""
    from dmplib.db.mysql_wrapper import get_db

    try:
        with get_db() as db:
            language = db.query_scalar(
                "select `value` from dap_bi_environment_common_setting where category='locale' and item=%(item)s",
                {"item": code}
            )
            return language or 'zh-CN'
    except Exception as e:
        logger.error(f"查询数据库失败: {e}")
        return 'zh-CN'  # 默认返回值，可以根据实际情况调整


def safe_get_from_cache_or_db(code):
    """从缓存或数据库中安全地获取语言设置"""
    from dmplib.redis import RedisCache

    try:
        key = f"dmp:{code}:language"
        value = RedisCache().get(key)
        if value:
            return value.decode() if isinstance(value, bytes) else value
    except Exception as e:
        logger.error(f"从缓存中获取数据失败: {e}")

    # 如果从缓存中没有获取到，那么从数据库中获取
    language = safe_get_language_from_db(code)

    # 更新缓存
    try:
        key = f"dmp:{code}:language"
        RedisCache().set(key, language, 300)
    except Exception as e:
        logger.error(f"更新缓存失败: {e}")

    return language


def _default_language(code):
    """
    获取默认语言
    :return:
    """
    language = safe_get_language_from_db(code)

    name_map ={
        'zh-CN': '简体中文',
        'en-US': 'English',
        'ja-JP': 'Japanese',
        'ja': 'Japanese',
        'zh-CHT': '繁体中文',
        'zh-HK': '繁体中文'
    }
    value = {
        'Language': {
            'Code': language,
            'Name': name_map.get(language, '简体中文')
        }
    }
    return value


def get_language(code='', user_id='') -> dict:
    """
    获取语言
    :param code: 语言代码
    :param user_id: 用户id
    :return:
    """
    from dmplib.redis import RedisCache

    account = getattr(g, 'account', '')
    if not account:
        logger.error("当前没有account")
    # 默认使用account进行缓存,使用user_id进行查询
    key = 'locale:{}:{}'.format(code, account or user_id)
    cache = RedisCache(key_prefix='language')
    exp = 300
    try:
        value = cache.get(key)
        if value:
            if isinstance(value, bytes):
                value = value.decode()
            if isinstance(value, str):
                value = json.loads(value)
            return value
        if is_env_enable_skyline_auth():
            logger.info(f"IngratePlatformApi before. code:{code}.user_id:{user_id}")
            value = IngratePlatformApi(code=code, timeout=5).query_user_international_config(user_id=user_id)
            logger.info("IngratePlatformApi value:%s" % value)
            # i18n_enable = nacos_client.NacosClient().get('i18n_enable')
            # if i18n_enable is True or i18n_enable == 'true':
            #     language = 'en-US'
            # elif i18n_enable is False or i18n_enable == 'false':
            #     language = 'zh-CN'
            # else:
            #     language = None
            # if language:
            #     value = {
            #         'Language': {
            #             'Code': language,
            #             'Name': '简体中文' if language == 'zh-CN' else 'English'
            #         }
            #     }
        if not value or not value.get('Language', None):
            value = _default_language(code)
    except Exception as e:
        logger.exception(f"get language error: {e}")
        value = _default_language(code)
        exp = 120
    cache.set(key, json.dumps(value), exp)
    return value


def set_language(code='', user_id='', language=None):
    user_id = user_id or getattr(g, 'user_id', '')
    code = code or getattr(g, 'code', '') or ''
    result = get_language(code=code, user_id=user_id)
    if result:
        language = result.get('Language').get('Code')
    # 检查输入值
    if not code and not language:
        language = 'zh-CN'

    # 尝试从缓存或数据库中获取语言设置
    if not language:
        language = safe_get_from_cache_or_db(code)

    # 设置language
    setattr(g, 'language', language)

    return language


__all__ = [
    'set_language',
    'zh_trans',
    'en_trans'
]
