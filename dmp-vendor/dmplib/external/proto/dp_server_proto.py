import json

from ...utils.model import BaseModelEncoder, BaseModel

class DpServerResponse(BaseModel):
    __slots__ = ['result', 'msg', 'data']

    def __init__(self, result, msg, data, **kwargs):
        self.result: bool = result
        self.msg: str = msg
        self.data = data
        super().__init__(**kwargs)

    def __str__(self):
        value = json.dumps(self.get_dict(), cls=BaseModelEncoder, ensure_ascii=False)
        return f'{__class__}-{hex(id(self))} <{value}>'