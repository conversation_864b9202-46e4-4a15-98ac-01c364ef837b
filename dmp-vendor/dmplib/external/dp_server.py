import logging
from typing import List

import requests

from ..hug import g
from .proto.dp_server_proto import DpServerResponse

logger = logging.getLogger(__name__)

dp_host = "http://dp-server:9999"
admin_account = "skylineadmin"

def refresh_project_user_permission_cache(project: str = None, user_ids: List[str] = None) -> bool:
    try:
        if not project:
            project = getattr(g, 'code', None)

        rsp = requests.get(f"{dp_host}/api/common/priv/user/refresh_user_menu_permission", params={
            'project': project,
            'user_ids': user_ids,
        }, headers={"token":f'{{"account":"{admin_account}"}}'})
        if rsp.status_code != requests.codes.ok:
            logger.error(f"请求数芯刷新用户菜单权限接口失败, 状态码: {rsp.status_code}")
            return False
        rsp = DpServerResponse(**rsp.json())
        if not rsp.result:
            logger.error(f"请求数芯刷新用户菜单权限接口失败, result<{rsp.result}>: {rsp.msg}")
            return False
        return True
    except Exception as e:
        logger.error("请求数芯刷新用户菜单权限接口失败")
        logger.exception(e)
        return False
