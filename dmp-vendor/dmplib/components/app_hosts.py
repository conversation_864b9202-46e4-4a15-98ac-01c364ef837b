from enum import Enum, unique

from dmplib.components.enums import SkylineApps
from dmplib.components import auth_util
from dmplib import config
from dmplib.nacos_client import NacosClient
import logging


INSIDE_URL_SUFFIX = '.insideUrl'
URL_SUFFIX = '.url'
HOST_SUFFIX = '.host'
INSIDE_HOST_SUFFIX = '.insideHost'

class AppHosts(object):

    @staticmethod
    def get(app:SkylineApps, inside=True, retries=False):
        if not app:
            return None
        enabled = auth_util.is_env_enable_skyline_auth()
        #如果未开启统一认证使用容器云配置
        if not enabled:
            mks_key = app.mks_key()
            if not mks_key:
                return ''
            return config.get(app.mks_key(), '')

        if inside:
            suffix = INSIDE_URL_SUFFIX
        else:
            suffix = URL_SUFFIX
        key = app.value + suffix
        from dmplib.hug import g
        val = ''
        if g:
            code = getattr(g, 'code', None)
            if code:
                val = NacosClient.getByTenant(key, code, '')
        if not val:
            val = NacosClient.get(key, '')
        if not val:
            val = config.get(key, '')
        if not val and not retries:
            config.load_config()
            return AppHosts.get(app, inside, True)
        return val


    def get_host(self, app:SkylineApps, inside=True):
        if not app:
            return None
        enabled = auth_util.is_env_enable_skyline_auth()
        #如果未开启统一认证使用容器云配置
        if not enabled:
           return None

        if inside:
            suffix = INSIDE_HOST_SUFFIX
        else:
            suffix = HOST_SUFFIX
        key = app.value + suffix
        val = NacosClient.get(key, '')
        return val
