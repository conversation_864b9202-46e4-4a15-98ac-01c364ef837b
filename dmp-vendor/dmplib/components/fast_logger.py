#!/usr/local/bin python3
# -*- coding: utf-8 -*-
# pylint: disable=E0401
import os
import json
from loguru import logger
import traceback
import datetime
from abc import ABCMeta, abstractmethod
from functools import wraps

from dmplib.components import auth_util
from dmplib.hug import g
from dmplib import config, APP_NAME
from dmplib.utils.fast_logger import FastLoggerUtil, fast_logger_instance

from dmplib.utils.model import BaseModel

"""
天眼日志记录处理类
扩展新的日志类型：
建立一个新的日志实现类，继承 BaseFastLogger 基类。
在子类实现 set_log_name 日志类型名称定义，set_folder_name 日志类型文件夹名称定义
在子类定义字段列表，需要与天眼的自定义库的字段一一对应

操作示例：
    from components.fast_logger import FastLogger
    data = {
        'action': '测试',
        'env_code': '111',
        'org_code': '2222',
        'api_url': 'http://dmp-test5.mypaas.com.cn/dataview-mobile/design/39f47aa0-a221-6234-2d47-fcb205011251/3a04828c-ac69-bd23-6570-60f8a0e5c109',
    }
    FastLogger.ApiFastLogger(**data).record()
"""


class BaseFastLogger(BaseModel, metaclass=ABCMeta):
    def __init__(self, **kwargs):
        try:
            self._init_log()
            self._logger_instance = fast_logger_instance(self._log_name, self._log_folder_name)  # type: Logger
            super().__init__(**kwargs)
        except Exception as e:
            logger.error(f"天眼日志对象实例错误，errs：{str(e)} , traceback: \n{traceback.format_exc()}")

    def _init_log(self):
        log_name = self.set_log_name()
        self._log_name = self._add_log_name_prefix(log_name)
        self._log_folder_name = self.set_folder_name()

    @abstractmethod
    def set_log_name(self):
        raise Exception("该方法只能在子类实例上调用！")

    @abstractmethod
    def set_folder_name(self):
        raise Exception("该方法只能在子类实例上调用！")

    @staticmethod
    def _add_log_name_prefix(name):
        return FastLoggerUtil.NAME_PREFIX + '_' + name

    @staticmethod
    def _add_default_field(data):
        """
        日志通用附属字段信息
        :param data:
        :return:
        """
        # 租户编码
        if not data.get("org_code"):
            data["org_code"] = g.code if hasattr(g, 'code') else ""
        # 用户账号
        if not data.get("account"):
            data["account"] = g.account if hasattr(g, 'account') else ""
        now = datetime.datetime.now()
        # 环境名称
        data["env_code"] = auth_util.get_env_code()
        # 添加服务名称
        if not data.get("app_name"):
            data["app_name"] = APP_NAME
        # 日志写入时间戳
        data["unix_time"] = int(now.timestamp() * 1000)
        # 日志写入日期时间
        data["date_time"] = datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3]

    def record(self):
        """
        日志写入文件
        :return:
        """
        try:
            data = self.get_dict()
            self._add_default_field(data)
            data = json.dumps(data, ensure_ascii=False)
            return self._logger_instance.error(data)
        except Exception as e:
            logger.error(f"天眼日志写入错误，errs：{str(e)} , traceback: \n{traceback.format_exc()}")

    @classmethod
    def decorator(cls, func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"天眼日志实例化错误，errs：{str(e)} , traceback: \n{traceback.format_exc()}")

        return wrapper


class ApiFastLogger(BaseFastLogger):
    """
    api类日志处理
    """
    __slots__ = [
        'action',
        'org_code',
        'api_url',
        'api_param',
        'api_result',
        'is_success',
        'token',
        'start_time',
        'end_time',
        'duration',
        'account',
        'cookies',
        'dashboard_id',
        'dashboard_name',
        'trace_id',
        'dataset_id',
        'sql_from',
        'headers',
        'curl',
    ]

    @BaseFastLogger.decorator
    def __init__(self, **kwargs):
        # 操作类型，request_api
        self.action = ''
        # 租户名称
        self.org_code = ''
        # 请求url
        self.api_url = ''
        # 请求参数
        self.api_param = ''
        # 请求结果
        self.api_result = ''
        # 状态，1：成功，0：失败
        self.is_success = 0
        # token信息
        self.token = ''
        # 请求开始时间
        self.start_time = ''
        # 请求结束时间
        self.end_time = ''
        # 请求耗时
        self.duration = 0
        # 用户账号
        self.account = ''
        # cookie信息
        self.cookies = ''
        # 报告id
        self.dashboard_id = ''
        # 报告名称
        self.dashboard_name = ''
        # 同一次访问看板传相同traceId便于查询日志
        self.trace_id = ''
        # 数据集id
        self.dataset_id = ''
        # SQL执行来源（sqltest：测试执行,design_viewreport：设计时,viewreport：运行时）
        self.sql_from = ''
        # 请求headers
        self.headers = ''
        # curl
        self.curl = ''
        super().__init__(**kwargs)

    # 日志类型名称
    def set_log_name(self):
        return 'api'

    # 日志存储文件夹名称
    def set_folder_name(self):
        return 'api_data'


class BizErrorFastLogger(BaseFastLogger):
    """
    业务错误类日志处理
    """
    __slots__ = [
        'module_type',
        'biz_type',
        'biz_id',
        'biz_name',
        'exec_from',
        'error_level',
        'error_type',
        'error_lable1',
        'error_lable2',
        'error_lable3',
        'error_code',
        'error_data_id',
        'error_msg',
        'error_traceback',
        'extra_info',
        'org_code',
        'is_key',
        'is_success'
    ]

    @BaseFastLogger.decorator
    def __init__(self, **kwargs):
        # 模块类型：大小屏，数据集，简讯，用户
        self.module_type = ''
        # 业务类型：移动报表，仪表板，大屏，数据集，个人简讯，邮件简讯等
        self.biz_type = ''
        # 业务id
        self.biz_id = ''
        # 业务名称：移动报表，仪表板，大屏，数据集，个人简讯，邮件简讯名称
        self.biz_name = ''
        # 执行时态(测试执行,design_viewreport：设计时,viewreport：运行时)
        self.exec_from = ''
        # 错误等级：一般：ordinary，致命：fatal
        self.error_level = 'ordinary'
        # 异常类型：组件取数报错，简讯发送延迟
        self.error_type = ''
        # 异常错误的一级原因分类
        self.error_lable1 = ''
        # 异常错误的二级原因分类
        self.error_lable2 = ''
        # 异常错误的三级原因分类
        self.error_lable3 = ''
        # 错误编码
        self.error_code = ''
        # 导致错误类型的业务id或数据id（例如组件取数报错，就是组件id）
        self.error_data_id = ''
        # 业务异常信息
        self.error_msg = ''
        # 业务异常堆栈信息
        self.error_traceback = ''
        # 扩展信息，json
        self.extra_info = ''
        # 租户code
        self.org_code = ''
        # 是否为重点数据（重点大屏）
        self.is_key = 0
        # 是否成功，1：成功，0：失败
        self.is_success = 0
        super().__init__(**kwargs)

    # 日志类型名称
    def set_log_name(self):
        return 'biz_error'

    # 日志存储文件夹名称
    def set_folder_name(self):
        return 'biz_error_data'


class KeyDashboardFastLogger(BaseFastLogger):
    """
    重点大屏信息数据库处理
    """
    __slots__ = [
        'date',
        'key_dashboard_num',
        'org_code',
    ]

    @BaseFastLogger.decorator
    def __init__(self, **kwargs):
        # 日期
        self.date = ''
        # 重点大屏数量
        self.key_dashboard_num = ''
        # 租户编码
        self.org_code = ''
        super().__init__(**kwargs)

    # 日志类型名称
    def set_log_name(self):
        return 'key_dashboard'

    # 日志存储文件夹名称
    def set_folder_name(self):
        return 'key_dashboard_data'


class SyncDataFastLogger(BaseFastLogger):
    """
    同步数据
    """
    __slots__ = [
        'data',
        'type',
        'org_code',
    ]

    @BaseFastLogger.decorator
    def __init__(self, **kwargs):
        # 同步的数据，json字符串
        self.data = ''
        # 数据类型
        self.type = ''
        # 租户编码
        self.org_code = ''
        super().__init__(**kwargs)

    # 日志类型名称
    def set_log_name(self):
        return 'sync_data'

    # 日志存储文件夹名称
    def set_folder_name(self):
        return 'sync_data_out'


class OMPTenantsFastLogger(BaseFastLogger):
    """
    租户开户相关
    """
    __slots__ = [
        'app_code',
        'log_type',
        'log_time',
        'log_level',
        'thread',
        'traceId',
        'taskId',
        'msg',
        'stack_trace',
        'extend_json',
    ]

    @BaseFastLogger.decorator
    def __init__(self, **kwargs):
        # 产品编码：跟各BU事先约定好的固定值
        self.app_code = ''
        # 开户日志固定值：tenantlog
        self.log_type = 'tenantlog'
        # 要精确到毫秒，便于精准排序
        self.log_time = ''
        self.log_level = 'INFO'
        self.thread = None
        # 链路id：上游传递（变量名traceId）
        self.traceId = ''
        # 任务Id：上游传递（变量名taskId）
        self.taskId = ''
        self.msg = ''
        self.stack_trace = None
        self.extend_json = {'env_code': '', 'action': 'create'}
        super().__init__(**kwargs)

    # 日志类型名称
    def set_log_name(self):
        return 'omp_tenants'

    # 日志存储文件夹名称
    def set_folder_name(self):
        return 'omp_tenants_out'


class FlowOperationsFastLogger(BaseFastLogger):
    """
     流程运维
     """
    __slots__ = [
        'flow_type',
        'flow_name',
        'flow_id',
        'plan_start_time',
        'startup_time',
        'end_time',
        'run_status',
        'lable1',
        'lable2',
        'number1', # 简讯  number1表示发送用户总数
        'number2', # 简讯  number2表示发送失败的用户数
        'extra_info',
        'org_code',
    ]

    @BaseFastLogger.decorator
    def __init__(self, **kwargs):
        # 流程类型：数据集，简讯
        self.flow_type = ''
        # 业务名称：数据集的名称、简讯的名称
        self.flow_name = ''
        # 业务id
        self.flow_id = ''

        # 预计开始时间
        self.plan_start_time = ''
        # 执行启动时间
        self.startup_time = ''
        # 执行结束时间
        self.end_time = ''
        # 执行结果
        self.run_status = ''

        # 备注字段1
        self.lable1 = ''
        # 备注字段2
        self.lable2 = ''

        # 备注字段1 # 简讯  number1表示发送用户总数
        self.number1 = 0
        # 备注字段2 # 简讯  number2表示发送失败的用户数
        self.number2 = 0

        # 扩展信息，json
        self.extra_info = ''
        # 租户code
        self.org_code = ''
        super().__init__(**kwargs)

    # 日志类型名称
    def set_log_name(self):
        return 'flow_operations'

    # 日志存储文件夹名称
    def set_folder_name(self):
        return 'flow_operations_data'


class FastLogger:
    """
    日志操作入口类
    """

    ApiFastLogger = ApiFastLogger
    BizErrorFastLogger = BizErrorFastLogger
    KeyDashboardFastLogger = KeyDashboardFastLogger
    SyncDataFastLogger = SyncDataFastLogger
    OMPTenantsFastLogger = OMPTenantsFastLogger
    FlowOperationsFastLogger = FlowOperationsFastLogger

    class ModuleType:
        """
        模块类型：大小屏，数据集，简讯，用户等
        """
        SYSTEM_MANAGEMENT = '系统管理'
        DASHBOARD_MOBILE_SCREEN = '大小屏'
        SUBSCRIBE = '简讯'
        DATASET = '数据集'

    class BizType:
        """
        业务类型：移动报表，仪表板，大屏，数据集，个人简讯，邮件简讯等
        """
        ORG_USER_SYNC = '组织用户同步'
        DASHBOARD = '仪表板'
        LARGE_SCREEN = '大屏'
        MOBILE = '移动报表'
        SELF_SERVICE = '自助报表'
        EMAIL_SUBSCRIBE = '邮件简讯'
        PERSONAL_SUBSCRIBE = '个人简讯'
        DATASET_SYNC = '调度数据集清洗'
        METADATA_RELEASED = '元数据发布'
        METADATA_EDIT = '元数据编辑'
        PERSONAL_SUBSCRIBE_SEND = '个人简讯发送'

    class ErrorType:
        """
        异常类型：组件取数报错，报告编辑错误，简讯发送延迟等
        """
        CHART_DATA_ERROR = '组件取数报错'

        ERP_ORG_SYNC_ERROR = 'ERP组织机构同步异常'
        ORG_SYNC_ERROR = '组织机构同步异常'
        ROLE_SYNC_ERROR = '角色同步异常'
        USER_SYNC_ERROR = '用户同步异常'
        USER_ORG_IMPORT_ERROR = '组织用户引入异常'
        USER_ORG_SYNC_ERROR = '组织用户同步异常'
        SUBSCRIBE_ADD_ERROR = '添加错误'
        SUBSCRIBE_EDIT_ERROR = '编辑错误'
        SUBSCRIBE_PREVIEW_ERROR = '预览错误'

        DATASET_SYNC_ERROR = '调度数据集清洗异常'
