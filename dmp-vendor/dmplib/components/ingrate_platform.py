#!/usr/bin/env python
# -*- coding:utf-8 -*-
# @FileName  :ingrate_platform.py.py
# @Time      :2022/5/17 10:01
# <AUTHOR>
from typing import List, Dict
from datetime import datetime
import requests
from loguru import logger
import json
import curlify

from dmplib.components import auth_util
from dmplib.components.app_hosts import AppHosts
from dmplib import config
from dmplib.utils.errors import UserError
from dmplib.redis import RedisCache
from dmplib.saas.project import get_db
from dmplib.components.enums import IngrateAction, SkylineApps
from dmplib.hug import debugger, g


ACCESS_TOKEN_KEY = "access_token:ingrate"
_debugger = debugger.Debug(__name__)


def get_tenant_ingrate_platform_config(code=None):
    code = code if code else getattr(g, 'code', '')
    logger.info(f'get_tenant_ingrate_platform_config.code:{code}')
    with get_db(code=code) as db:
        data = db.query_scalar(
            'select `value` from dap_bi_system_setting where `category`=%(category)s and `item`=%(item)s',
                               {"category": 'ingrate_platform', "item": 'basic_data_platform'}
        )
    if data:
        try:
            json_data = json.loads(data)
        except Exception as e:
            logger.error(f'解析租户集成平台信息错误：data: {data}, error: {str(e)}')
            json_data = {}
    else:
        json_data = {}
    return json_data


class IngratePlatformApi(object):

    URI_MAPPING = {
        'USER_RIGHTS': {
            'IPAAS': '/api/basicdata/DMPCheckUserReportAuthorize',
            'APAAS': '/pub/Mysoft.PubPlatform.Organization.PublicServices.IStandardRolePublicService/DMPCheckUserReportAuthorize'
        },
        'APP_PUBLISH': {
            'IPAAS': '/api/basicdata/DMPSaveGroupAndReports',
            'APAAS': '/pub/Mysoft.PubPlatform.Organization.PublicServices.IStandardRolePublicService/DMPSaveGroupAndReports'
        },
        'APP_STATUS_SYNC': {
            'IPAAS': '/api/basicdata/DMPOperateApplication',
            'APAAS': '/pub/Mysoft.PubPlatform.Organization.PublicServices.IStandardRolePublicService/DMPOperateApplication'
        },
        'GET_AUTH_APP': {
            'IPAAS': '/api/basicdata/DMPQueryUseApplications',
            'APAAS': '/pub/Mysoft.PubPlatform.Organization.PublicServices.IStandardRolePublicService/DMPQueryUseApplications'
        },
        'GET_ORG_LIST': {
            'IPAAS': '/api/basicdata/direct/QueryOrganizationList',
            'APAAS': '/pub/Mysoft.PubPlatform.Organization.PublicServices.IBasicDataPublicService/QueryOrganizationList'
        },
        'GET_USER_LIST': {
            'IPAAS': '/api/basicdata/direct/QueryUserList',
            'APAAS': '/pub/Mysoft.PubPlatform.Organization.PublicServices.IBasicDataPublicService/QueryUserList'
        },
        'GET_I18N_CONFIG': {
            'IPAAS': '/api/basicdata/QueryUserInternaltionalConfig',
            'APAAS': '/pub/03011801/CountryRegion/queryUserInternaltionalConfig'
        }
    }

    def __init__(self, code='', timeout=30, action=IngrateAction.Call.value):
        self.code = code
        self.timeout = timeout
        self.action = action
        self.directly_req = False
        self.__load_config()

    def __load_config(self):
        need_from_config = True
        if self.code:
            setting_conf = get_tenant_ingrate_platform_config(code=self.code)
            tenant_mip_host = str(setting_conf.get("host", "")).rstrip('/')
            if tenant_mip_host:
                self.host = tenant_mip_host
                self.client_id = setting_conf.get("client_id") or ""
                self.client_secret = setting_conf.get("client_secret") or ""
                self.report_authorize_url = setting_conf.get('report_authorize_url') or config.get(
                    'IngratePlatform.report_authorize_url')
                self.cache = RedisCache(key_prefix=f"{self.code}:IngratePlatform:{self.action}")
                need_from_config = False

        if need_from_config:
            self.directly_req = True
            self.host = AppHosts.get(SkylineApps.APAAS).rstrip('/')
            self.client_id = config.get("IngratePlatform.client_id") or ""
            self.client_secret = config.get("IngratePlatform.client_secret") or ""
            self.report_authorize_url = config.get('IngratePlatform.report_authorize_url') or ""
            self.cache = RedisCache(key_prefix=f"IngratePlatform:{self.action}")

        if self.action == IngrateAction.Register.value:
            self.client_id = config.get("IngratePlatform.client_id_of_register") or ""
            self.client_secret = config.get("IngratePlatform.client_secret_of_register") or ""

    def get_uri(self, method):
        conf = IngratePlatformApi.URI_MAPPING.get(method)
        if not conf:
            return ''
        if self.directly_req:
            return conf.get('APAAS')
        else:
            return conf.get('IPAAS')

    def portal_check_ingrate_platform_is_exist(self):
        # 基础数据平台授权的门户场景目前只检查租户配置是否存在
        try:
            host = self.host
            client_id = self.client_id
            client_secret = self.client_secret
            if all([host, client_secret, client_id]):
                return True
            else:
                return False
        except:
            return False

    def get_access_token(self):
        access_token = self.cache.get(ACCESS_TOKEN_KEY)
        if not access_token:
            result = self._do_request(
                method="post",
                url=f"{self.host}/MIPApiAuth/Token",
                params={
                    "client_id": self.client_id,
                    "client_secret": self.client_secret
                },
                is_header=False
            )
            access_token = result.get("access_token")
            if not access_token:
                raise UserError(message="基础数据平台获取access_token错误：{}".format(result))
            expires_in = int(result.get("expires_in", 3600))
            self.cache.set(ACCESS_TOKEN_KEY, access_token, int(expires_in / 2))
        else:
            access_token = access_token.decode()
        return access_token

    def get_saas_access_token(self, host=None, client_id=None, client_secret=None):
        cache_key = ACCESS_TOKEN_KEY+':saas'
        access_token = self.cache.get(cache_key)
        if not access_token:
            host = host or self.host
            client_id = client_id or self.client_id
            client_secret = client_secret or self.client_secret
            result = self._do_request(
                method="post",
                url=f"{host}/MIPApiAuth/Token",
                params={
                    "client_id": client_id,
                    "client_secret": client_secret
                },
                is_header=False
            )
            access_token = result.get("access_token")
            if not access_token:
                raise UserError(message="saas基础数据平台获取access_token错误：{}".format(result))
            expires_in = int(result.get("expires_in", 3600))
            self.cache.set(cache_key, access_token, int(expires_in / 2))
        else:
            access_token = access_token.decode()
        return access_token

    @property
    def auth2_headers(self):
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) '
                          'Chrome/********* Safari/537.36',
            'Content-Type': 'application/json',
            "_my-skyline-yunerp-tenantcode_": self.code,
        }
        headers["Authorization"] = "Bearer " + self.get_access_token()
        return headers

    @property
    def headers(self):
        #统一应用认证
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) '
                          'Chrome/********* Safari/537.36',
            'Content-Type': 'application/json',
            "_my-skyline-yunerp-tenantcode_": self.code,
            "tenantCode": self.code,
        }
        if auth_util.is_enable_skyline_auth(self.code):
            token = auth_util.gen_auth_token()
            if token:
                headers[auth_util.AUTHORIZATION_KEY] = token
        else:
            headers["Authorization"] = "Bearer " + self.get_access_token()
        return headers

    @property
    def sample_headers(self):
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) '
                          'Chrome/********* Safari/537.36',
            'Content-Type': 'application/json',
            "tenantCode": self.code,
        }
        if auth_util.is_enable_skyline_auth(self.code):
            token = auth_util.gen_auth_token()
            if token:
                headers[auth_util.AUTHORIZATION_KEY] = token
        return headers


    def _do_request(self, method, url, params, is_header=True, extra_header:dict={}):
        headers = None
        start_time = datetime.now()
        result = ''
        curl_info = ''
        is_success = 0
        try:
            headers = self.headers if is_header else self.sample_headers
            headers.update(extra_header)
            logger.error(f"基础平台请求 url: {url}")
            if method.upper() == "GET":
                res = requests.get(url, params=params, headers=headers, timeout=self.timeout)
            else:
                res = requests.post(url, json=params, headers=headers, timeout=self.timeout)
            result = res.text
            curl_info = curlify.to_curl(res.request, compressed=True)
            _debugger.log({
                '集成数据平台请求的信息': curlify.to_curl(res.request, compressed=True),
                '返回的信息': res.text
            })
            is_success = 1
            return res.json()
        except Exception as e:
            headers = ''
            is_success = 0
            message = "请求基础数据平台错误: {}".format(str(e))
            logger.error(f"基础平台请求 curl: {curl_info}, res: {result}")
            logger.error(message)
            raise UserError(message=message) from e
        finally:
            self.record_log_of_mip(url, params, headers, start_time, result, curl_info, is_success)

    def record_log_of_mip(self, url, params, headers, start_time, api_result, curl_info, is_success):
        """
        记录mip日志
        :param url:
        :param params:
        :param headers:
        :param start_time:
        :param api_result:
        :param curl_info:
        :param is_success:
        :return:
        """
        try:
            from dmplib.components.fast_logger import FastLogger

            end_time = datetime.now()

            FastLogger.ApiFastLogger(
                action="request_mip",
                org_code=getattr(g, 'code', None),
                api_url=url,
                headers=headers,
                api_param=json.dumps(params, ensure_ascii=False) if params else '',
                is_success=is_success,
                start_time=start_time,
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                duration=(end_time - start_time).total_seconds(),
                curl=curl_info,
                api_result=json.dumps(api_result, ensure_ascii=False)
            ).record()
        except Exception as e:
            logger.exception(f"记录日志失败:{e}")

    def check_report_is_grant(self, user_code: str, report_id=''):
        """
        基础数据返回数据结构
        {
            "data": {
                "ReportAuthorizes": [
                    {
                        "ReportId": "3a011791-2298-eb13-4833-b2f70ba391e1",
                        "HasViewPermission": true,
                        "HasExportPermission": true
                    }
                ]
            },
            "success": true,
            "message": null,
            "exception": null,
            "code": null
        }
        """
        if isinstance(report_id, str):
            report_id = [report_id]
        params = {
            "UserCode": get_admin_user_code(user_code),
            "ReportIds": report_id
        }
        result = self._do_request(
            method="post",
            url=f"{self.host}{self.report_authorize_url}",
            params=params
        )
        if result.get("success") is not True:
            logger.error("check error: {}".format(str(result)))
            raise UserError(message="请求基础数据平台错误:{}".format(result.get("message") or ""))

        if not result.get("data", {}).get("ReportAuthorizes"):
            raise UserError(message="从基础数据平台获取权限为空")

        data_list = result.get("data", {}).get("ReportAuthorizes", [{}])
        auth_data = {}
        for item in data_list:
            ReportId = item.get('ReportId')
            view_permission = item.get("HasViewPermission", False)
            export_permission = item.get("HasExportPermission", False)
            auth_data[ReportId] = ','.join(
                ['view' if view_permission else '', 'download' if export_permission else ''])

        return auth_data

    def portal_check_reports_is_grant(self, user_code: str = "", report_ids: list = []):
        if not report_ids:
            return {}
        params = {
            "UserCode": get_admin_user_code(user_code),
            "ReportIds": report_ids
        }
        result = self._do_request(
            method="post",
            url=f"{self.host}{self.get_uri('USER_RIGHTS')}",
            params=params
        )
        if result.get("success") is not True:
            logger.error("check error: {}".format(str(result)))
            raise UserError(message="请求基础数据平台错误:{}".format(result.get("message") or ""))

        if not result.get("data", {}).get("ReportAuthorizes"):
            logger.error(f"从基础数据平台获取<{report_ids}>权限为空")

        permission_data = {}
        for report_data in result.get("data", {}).get("ReportAuthorizes", []):
            dashboard_id = report_data.get('ReportId', '')
            auth = []
            if report_data.get("HasViewPermission", False):
                auth.append('view')
            if report_data.get("HasExportPermission", False):
                auth.append('download')
            if report_data.get("HasPrintPermission", False):
                auth.append('print')
            permission_data[dashboard_id] = ','.join(auth)
        return permission_data

    def portal_release_sync_to_mip(self, applications: List[Dict] = [], reports: List[Dict] = []):
        """
        同步门户数据到基础数据
        {
            "DMPApplications":[{
                        "Code":"0011",
                        "Name":"销售系统"
                }],
            "DMPReports":[{
                        "Id":"39f4c75f-0b83-7317-e3f4-cb10816d9966", //分组/报表ID（必须GUID）
                        "Name":"Call客意向识别", //报表名称
                        "Kind":2, //报表类型:  1目录 2报表
                        "ParentId":"", //报表父目录ID
                        "ApplicationCode":"0011",
                        "ApplicationName":"销售系统"
                }]
        }

        """
        if not applications and not reports:
            logger.error(f'同步到基础数据平台的门户数据为空，跳过同步')
            return
        params = {
            "DMPApplications": applications,
            "DMPReports": reports
        }
        result = self._do_request(
            method="post",
            url=f"{self.host}{self.get_uri('APP_PUBLISH')}",
            params=params
        )

        if not result.get("data", {}).get("IsSuccess"):
            err_msg = result.get("data", {}).get("Message")
            raise UserError(message=f"同步到基础数据平台的门户数据失败：{err_msg}")

    def portal_status_sync_to_mip(self, applications: List[Dict] = []):
        """
        同步门户状态到基础数据
        {
           "DMPApplications":[{
                    "Code":"0011",
                    "Name":"销售系统",
                    "OperationType":1, // 1=上线、2=下线、3=删除
           }]
        }
        """
        params = {
            "DMPApplications": applications
        }
        result = self._do_request(
            method="post",
            url=f"{self.host}{self.get_uri('APP_STATUS_SYNC')}",
            params=params
        )

        if result.get("success") is not True:
            raise UserError(message=f"同步到基础数据平台的门户数据失败：{result}")

    def portal_status_rank_sync_to_mip(self, data):
        """
        同步门户状态到基础数据
        {
           "DMPApplications":[{
                    "Code":"0011",
                    "Name":"销售系统",
                    "OperationType":1, // 1=上线、2=下线、3=删除
           }]
        }
        """
        if not data:
            logger.error(f'数据为空，不同步！')
            return
        params = {
            "DMPApplications": data
        }
        result = self._do_request(
            method="post",
            url=f"{self.host}{self.get_uri('APP_STATUS_SYNC')}",
            params=params
        )

        if result.get("success") is not True:
            raise UserError(message=f"同步到基础数据平台的门户数据失败：{result}")

    def mip_available_portals(self, user_code: str = ""):
        """
        获取基础平台授权的门户
        """
        params = {
            "UserCode": get_admin_user_code(user_code),
        }
        result = self._do_request(
            method="post",
            url=f"{self.host}{self.get_uri('GET_AUTH_APP')}",
            params=params
        )
        if result.get("success") is not True:
            logger.error("check error: {}".format(str(result)))
            raise UserError(message="请求基础数据平台错误:{}".format(result.get("message") or ""))

        applications = result.get("data", {}).get("DMPApplications")
        return applications

    def send_email(self, params, code):
        self.host = AppHosts.get(SkylineApps.APAAS, False)
        platform_code = config.get("ReportCenter.open_print_aid","mysoft_cy_saas")
        header = {
            "platformCode": platform_code,
            "thirdTenantCode": code,
            auth_util.AUTHORIZATION_KEY: auth_util.gen_auth_token(),
            "tenantCode": code,
        }
        result = self._do_request(
            method="post",
            url=f"{self.host}/pub/40040302/email/send",
            params=params,
            extra_header=header
        )
        logger.info(f'邮件发送结果:{result}')
        if result.get('code') not in ['0', 0]:
            logger.error(f'请求基础数据平台发送邮件失败:{result.get("msg")}')
            raise UserError(message=f'请求基础数据平台发送邮件失败:{result.get("msg")}')
        rs = result.get("data", {})
        return rs

    def get_user_group_list(self, page, page_size):
        """
        获取基础平台的组织列表
        """
        page = int(page)
        if page > 0:
            page -= 1
        params = {
            "pageIndex": page,
            "pageSize": page_size,
            "selectFields": ['BUGUID', 'BUName', 'ParentGUID', 'OrderHierarchyCode'],
            "orderBy": {
                "OrderHierarchyCode": "asc",
            },
        }
        result = self._do_request(
            method="post",
            url=f"{self.host}{self.get_uri('GET_ORG_LIST')}",
            params=params
        )
        if result.get("success") is not True:
            logger.error("check error: {}".format(str(result)))
            raise UserError(message="请求基础数据平台错误:{}".format(result.get("message") or ""))

        rs = result.get("data", {}).get("Results")
        return rs

    def get_user_list(self, params):
        """
        获取基础平台的用户列表信息
        """
        select_params = {
            "operator": "and",
            "conditions": [
                {
                    "fieldName": "IsDisabeld",
                    "operator": "eq",
                    "value": 0
                }
            ]
        }
        org_id = params.get("org_id", "")
        # 按组织id 查询
        if org_id:
            select_params = {
                "operator": "and",
                "conditions": [
                    {
                        "fieldName": "BUGUID",
                        "operator": "eq",
                        "value": org_id
                    },
                    {
                        "fieldName": "IsDisabeld",
                        "operator": "eq",
                        "value": 0
                    }
                ]
            }

        keyword = params.get("keyword", "")
        # 用户名匹配
        if keyword:
            select_params = {
                "operator": "and",
                "conditions": [
                    {
                        "fieldName": "IsDisabeld",
                        "operator": "eq",
                        "value": 0
                    }
                ],
                "conditionGroups": [{
                    "operator": "or",
                    "conditions": [
                        {
                            "fieldName": "UserCode",
                            "operator": "like",
                            "value": f"%{keyword}%"
                        },
                        {
                            "fieldName": "UserName",
                            "operator": "like",
                            "value": f"%{keyword}%"
                        }
                    ]
                }]
            }
        # 接口的分页从0开始
        page = int(params.get("page"))
        if page > 0:
            page -= 1
        params = {
            "pageIndex": page,
            "pageSize": params.get("page_size"),
            "selectFields": ['UserGUID', 'UserCode', 'UserName', 'Email'],
            "parameters": select_params
        }
        result = self._do_request(
            method="post",
            url=f"{self.host}{self.get_uri('GET_USER_LIST')}",
            params=params
        )
        if result.get("success") is not True:
            logger.error("check error: {}".format(str(result)))
            raise UserError(message="请求基础数据平台错误:{}".format(result.get("message") or ""))

        return result.get("data", {})

    def register_interface(self, name, connector_code, api_info):
        """
        通过开放API注册连接，API，事件
        https://open.mingyuanyun.com/knowledge/mip/6a675841-d2e3-4414-b5da-29083a2ca3bc
        :param name:
        :param connector_code:
        :param api_info:
        :return:
        """
        dmp_url = AppHosts.get(SkylineApps.DMP) \
            if auth_util.is_env_enable_skyline_auth() and config.get('Skyline.mip_register_intranet', 0) in [1, '1'] \
            else config.get("Domain.dmp")
        params = {
            "connectionCode": "dmp",
            'connectionName': name,
            'connectorCode': connector_code,
            "authentication": json.dumps({"secretkey": "YC2UFKz7"}),
            "productCode": "dmp-prod",
            'serverInfo': json.dumps({'url': dmp_url}),
            'apis': json.dumps({
                "openapi": "3.0.1",
                "info": {
                    "title": "dmpService",
                    "description": "dmp接口注册",
                    "version": "1.0.0",
                },
                "paths": api_info.get('path'),
                "components": api_info.get('components') or {}
            }),
            "remark": "数见",
            "tags": "数见",
            "enable": True,
            "productName": "天际-数据分析平台",
            "apitype": "数见"
        }
        result = self._do_request(
            method='post',
            url=f'{self.host}/openapi/connectivity/registerconnectionapiandevents',
            params=params,
        )
        if result.get('code') != 1:
            raise UserError(message=f"集成平台注册接口失败:{str(result)}")
        return result

    def get_fine_report_list(self, platform='pc'):
        """
        获取帆软报表列表
        :return:
        """
        params = {
            "tenantCode": g.code,
            "sysCode": "MobileCheckQuality",
            "reportMode": "FineReport",
            "reportForm": platform
        }
        result = self._do_request(
            "post",
            url=f"{self.host}/m/ReportCenter/cyjg/get-report-list",
            params=params
        )
        if not result.get("result"):
            raise UserError(message=f"获取帆软报表列表异常:{str(result)}")
        return result.get('data') or []

    def get_fine_report_view_url(self, report_id):
        """
        获取帆软报表地址
        :param report_id:
        :return:
        """
        if isinstance(report_id, str):
            report_ids = [report_id]
        else:
            report_ids = report_id
        # 获取user_id
        user_id = getattr(g, 'userid', None)
        if not user_id:
            with get_db() as db:
                user_id = db.query_scalar('select `id` from `dap_bi_user` where account=%(account)s', {'account': g.account})
        params = {
            "tenantCode": g.code,
            "userId": user_id,
            "reportNamePath": report_ids
        }
        result = self._do_request(
            'post',
            url=f"{self.host}/m/ReportCenter/cyjg/view",
            params=params
        )
        if not result.get("result"):
            raise UserError(message=f"获取帆软报表地址异常:{str(result)}")
        url_list = result.get('data') or []
        return {i.get('reportNamePath'): i.get('url') for i in url_list}

    def proxy_call(self, path, method, params):
        """
        提供给前端SDK透明调用集成平台的接口
        """
        url = f"{self.host}{path}"
        _debugger.log({
            "代理调用的url": url,
            "代理调用的method": method,
            "代理调用的data": params,
            # "代理调用的headers": headers,
        })
        return self._do_request(method, url=url, params=params)

    def query_user_international_config(self, user_id=''):
        """
        查询用户的语言设置和区域
        https://doc.weixin.qq.com/doc/w3_ACYA8QYpALYzQdtpd1XR1CgKX2F2T?scode=AIsAVQcAABIVAFqHmtACYA8QYpALY
        """
        user_id = user_id or getattr(g, 'userid', '')
        if not user_id:
            with get_db() as db:
                user_id = db.query_scalar('select `id` from `dap_bi_user`', {'account': g.account}) or ''
        params = {
            "userId": user_id
        }
        result = self._do_request(
            method="post",
            url=f"{self.host}{self.get_uri('GET_I18N_CONFIG')}?userId={user_id}",
            params=params
        )
        if result.get("success") is not True:
            logger.error("check error: {}".format(str(result)))
            raise UserError(message="请求基础数据平台错误:{}".format(result.get("message") or ""))

        return result.get("data", {})

def get_admin_user_code(user_code):
    return 'admin' if user_code == 'skylineadmin' else user_code

if __name__ == "__main__":
    from dmplib.hug.globals import _AppCtxGlobals, _app_ctx_stack
    from dmplib.hug.context import DBContext

    g = _AppCtxGlobals()
    _app_ctx_stack.push(g)
    # inject db
    db_ctx = DBContext()
    db_ctx.inject(g)
    g.code = ''

    IngratePlatformApi('').check_report_is_grant("skylineadmin", '3a02e068-0b01-a16e-cdcd-ee439f6631e0')
