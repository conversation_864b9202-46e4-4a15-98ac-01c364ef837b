from enum import Enum, unique


@unique
class DBType(Enum):

    MYSQL = 'MySQL'
    DM = 'DM'


@unique
class IngrateAction(Enum):
    """
    集成平台接口调用类型
    """
    Register = "register"   # 接口注册到集成平台
    Call = "call"       # 通过集成平台调用注册到集成平台的接口


@unique
class SkylineApps(Enum):

    DMP = 'dmp'
    DMP_ONE_SERVICE = 'dmp-one-service'
    DP = 'dp'
    DMP_ADMIN_GROUP = 'dmp-admin-group'
    DMP_ADMIN = 'dmp-admin'
    #在线报告
    DATA_REPORT = 'dmp-ppt'
    #集成平台
    IPAAS = 'ipaas'
    #超级工作台门户站点
    WORKBENCH_PORTAL = 'workbench.protal'
    #超级工作台消息应用
    WORKBENCH_MESSAGE = 'workbench.message'
    #通行证服务
    WORKBENCH_AUTH = 'workbench.auth'
    #运营平台开户
    OMP_TENANT = 'tenantProxy'
    #数芯
    DAP = 'dap'
    #套打
    PUBSERVICE_PRINT = 'pubservice.print'
    #公共服务(电签)
    PUBSERVICE_ESIGN = 'pubservice.esign'
    #建模平台
    APAAS = 'apaas'
    #数见报表(erp报表)
    SJBB = 'sjbb'
    #统计报表(复杂报表)
    DMP_REPORT = 'dmp-report'
    #文档服务
    DOCUMENT = 'pubservice.document'
    # 日志服务
    LOG = 'logserver'
    # task-group
    DMP_TASK_GROUP = 'dmp-task-group'

    def mks_key(self):
        if self == SkylineApps.IPAAS:
            return 'IngratePlatform.host'
        elif self == SkylineApps.WORKBENCH_PORTAL:
            return 'Superportal.host'
        elif self == SkylineApps.DAP:
            return 'ThirdDatasource.bigdata_api_host'
        elif self == SkylineApps.DMP:
            return 'Domain.dmp'
        elif self == SkylineApps.DMP_ADMIN_GROUP or self == SkylineApps.DMP_ADMIN:
            return 'Domain.dmp_admin'
        elif self == SkylineApps.PUBSERVICE_PRINT:
            return 'ReportCenter.open_print_url'
        elif self == SkylineApps.DMP_REPORT:
            return 'PPT.active_report_domain_front'
        elif self == SkylineApps.DATA_REPORT:
            return 'PPT.domain'
        else:
            return None

    @staticmethod
    def custom_key(key):
        class CustomKey(str):
            @staticmethod
            def mks_key():
                return '不支持自定义站点的服务内配置获取'
            @property
            def value(self):
                return self.__str__()

        return CustomKey(key)