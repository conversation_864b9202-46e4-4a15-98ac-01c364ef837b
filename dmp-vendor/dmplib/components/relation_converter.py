import json

from dmplib.utils.strings import seq_id


def convert_relation_data_to_graph(relation_content, relation_filter_content):
    """
    视图模式content转图形化模式content
    """
    node_data_array = relation_content.get('nodeDataArray')
    link_data_array = relation_content.get('linkDataArray')
    graph_nodes = []
    graph_links = []
    for node_data in node_data_array:
        node = {
            "id": node_data.get("id"),
            "parent_id": find_parent_id_from_relation_data(node_data.get("id"), link_data_array),
            "name": node_data.get("name"),
            "name_cn": node_data.get("comment"),
            "alias": node_data.get("alias_name"),
            "type": relation_node_type_to_graph(node_data.get("table_type", 'TABLE')),
            "table_type": node_data.get("table_type"),
        }
        graph_nodes.append(node)

    for link_data in link_data_array:
        join_fields = convert_relation_fields_to_graph(node_data_array, link_data)
        link = {
            "left_node_id": link_data.get("from_id"),
            "right_node_id": link_data.get("to_id"),
            "join_type": link_data.get("join_type", "").upper(),
            "combo": " and ".join(str(x) for x in range(1, len(join_fields) + 1)),
            "join_fields": join_fields,
            "join_factor": []
        }
        graph_links.append(link)

    graph_filter = convert_relation_filter_to_graph(relation_filter_content, node_data_array)

    return {
        'node': graph_nodes,
        'link': graph_links,
        'filter': graph_filter
    }


def convert_relation_filter_to_graph(relation_filter, node_data_array):
    if relation_filter is None or len(relation_filter) == 0:
        return {
            'combo': "",
            'where_factor': []
        }
    combo, condition_map = parse_relation_filter(relation_filter)
    return {
        'combo': combo,
        'where_factor': [convert_relation_condition_to_graph(index, condition, node_data_array) for index, condition in
                         condition_map.items()]
    }


def parse_relation_filter(data):
    """
    解析嵌套的条件结构，生成布尔表达式并提取条件因子
    :param data: 输入的数据结构
    :return: 表达式字符串，条件因子列表
    """
    condition_factors = []

    def recurse(node):
        if "and" in node:
            sub_conditions = [recurse(sub_node) for sub_node in node["and"]]
            return f"({' and '.join(sub_conditions)})"
        elif "or" in node:
            sub_conditions = [recurse(sub_node) for sub_node in node["or"]]
            return f"({' or '.join(sub_conditions)})"
        else:
            # 当前条件序号基于 condition_factors 的长度
            current_index = str(len(condition_factors) + 1)
            condition_factors.append(node)  # 保存条件因子
            return current_index

    expression = recurse(data)

    # 去掉最外层多余的括号
    if expression.startswith("(") and expression.endswith(")"):
        # 检查括号内的内容是否是单独的表达式
        inner_expression = expression[1:-1]
        depth = 0
        for char in inner_expression:
            if char == "(":
                depth += 1
            elif char == ")":
                depth -= 1
            if depth < 0:
                break
        else:
            if depth == 0:  # 括号匹配正确
                expression = inner_expression

    condition_factors_map = {str(index + 1): factor for index, factor in enumerate(condition_factors)}
    return expression, condition_factors_map


def build_condition_value(condition):
    print(condition)
    if condition.get('col_value_type') == '固定值':
        return condition.get('col_value')
    elif condition.get('col_value_type') == "字段":
        return condition.get('')
    elif condition.get('col_value_type') == "变量":
        return condition.get('')
    else:
        return condition.get('')


def convert_relation_condition_to_graph(index, condition, node_data_array):
    left_table = None
    left_field = None
    if not condition.get('col_name_type') or condition.get('col_name_type') == 'col':
        left_table = find_table_by_id_from_relation_node(node_data_array, condition.get("table_id"))
    if left_table:
        left_field = find_field_by_name_from_relation_node_fields(left_table.get("fields"), condition.get("col_name"))
    left_col_name = left_field.get('origin_col_name') if left_field else None
    if not left_col_name:
        left_col_name = condition.get("col_name")

    left_table_name = left_table.get("name") if left_table else None
    if not left_table_name:
        left_table_name = condition.get("table_name")

    left_field_type = 'text'
    if condition.get("col_type") == '日期':
        left_field_type = 'date'
    elif condition.get("col_type") == '数值':
        left_field_type = 'int'

    return {
        'index': index,
        'left_field': {
            "id": seq_id(),
            "table": left_table_name,
            "table_cn": left_table.get("comment") if left_table else None,
            "table_alias": left_table.get("alias_name") if left_table else None,
            "name": left_col_name,
            "name_cn": left_field.get("name_cn") if left_field else None,
            "alias": left_field.get("alias_name") if left_field else None,
            "func": "",
            "field_type": left_field_type,
            "type": convert_relation_filter_col_type_to_graph(condition.get("col_name_type", 'col')),
            "variable_id": get_variable_id_from_relation(condition.get('col_name', '')) if condition.get(
                'col_name_type') == 'var' else None
        },
        'operator': condition.get('operator'),
        'like_type': condition.get('like_type'),
        'right_value': {
            # 视图模式右边只能选变量获取固定值
            "id": seq_id(),
            "table": '',
            "table_cn": '',
            "table_alias": '',
            "name": '',
            "name_cn": '',
            "alias": '',
            "func": '',
            "field_type": '',
            "type": condition.get('col_value_type'),
            "variable_id": get_variable_id_from_relation(condition.get('col_value', '')) if condition.get(
                'col_value_type') == '变量' else None,
            "value": condition.get('col_value') if condition.get(
                'col_value_type') == '固定值' else None
        },
    }


def get_variable_id_from_relation(col_name):
    if not col_name:
        return col_name
    return col_name.replace('${', '').replace('}', '')


def convert_relation_filter_col_type_to_graph(col_type):
    if col_type == 'col':
        return "字段"
    elif col_type == 'var':
        return "变量"
    else:
        raise Exception(f"未知的col_type{col_type}")


def convert_relation_fields_to_graph(node_data_array, link_data):
    result = []
    for i in range(len(link_data.get("join_fields", []))):
        index = str(i + 1)
        join_field = link_data.get("join_fields")[i]
        left_node = next((node for node in node_data_array if node.get('id') == link_data.get('from_id')), None)
        right_node = next((node for node in node_data_array if node.get('id') == link_data.get('to_id')), None)
        left_field = find_field_by_name_from_relation_node_fields(left_node.get('fields'), join_field.get('left'))
        right_field = find_field_by_name_from_relation_node_fields(left_node.get('fields'), join_field.get('right'))
        result.append({
            "index": index,
            "left_field": {
                "id": seq_id(),
                "table": left_node.get('name'),
                "table_cn": left_node.get('name_cn'),
                "table_alias": left_node.get('alias_name'),
                "name": join_field.get('left'),
                "name_cn": left_field.get('name_cn') if left_field else None,
                "alias": left_field.get('alias_name') if left_field else None,
                "func": None,
                "field_type": left_field.get('col_type') if left_field else None,
                "type": "字段",
                "variable_id": ""
            },
            "operator": join_field.get("operator"),
            "right_value": {
                "id": seq_id(),
                "table": right_node.get('name'),
                "table_cn": right_node.get('name_cn'),
                "table_alias": right_node.get('alias_name'),
                "name": join_field.get('right'),
                "name_cn": right_field.get('name_cn') if right_field else None,
                "alias": right_field.get('alias_name') if right_field else None,
                "func": None,
                "field_type": right_field.get('col_type') if right_field else None,
                "type": "字段",
                "variable_id": "",
                "value": join_field.get('right')
            }
        })
    return result


def find_parent_id_from_relation_data(current_id, relation_data_array):
    for relation_data in relation_data_array:
        if relation_data.get("to_id") == current_id:
            return relation_data.get("from_id")
    return None


def relation_node_type_to_graph(relation_node_type):
    if relation_node_type in ('明细事实表', '维度表', '汇总事实表', '汇总视图事实表', '数据采集表') or not relation_node_type:
        return "表"
    elif relation_node_type in ('EXCEL', "SQL", "API"):
        return "数据集"
    else:
        raise Exception(f"不支持的node_type:{relation_node_type}")


def find_field_by_name_from_relation_node_fields(fields, field_name):
    field_name_lower = field_name.lower()
    return next((field for field in fields if field.get('name', '').lower() == field_name_lower), None)


def find_table_by_id_from_relation_node(nodes, table_id):
    if not table_id:
        return None
    return next((node for node in nodes if node.get('id', '') == table_id), None)


if __name__ == '__main__':
    with open('/home/<USER>/test_relation.json', 'r', encoding='utf-8') as f:
        test_relation_content = json.loads(f.read())
    with open('/home/<USER>/test_relation_filter.json', 'r', encoding='utf-8') as f:
        test_relation_filter = json.loads(f.read())
    test_graph_content = convert_relation_condition_to_graph(test_relation_content, test_relation_filter)
    print(json.dumps(test_graph_content, ensure_ascii=False))
