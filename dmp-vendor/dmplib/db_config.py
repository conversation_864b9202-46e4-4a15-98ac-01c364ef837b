

def __get_db(current_config):
    from .db.mysql_wrapper import SimpleMysql

    return SimpleMysql(
        host=current_config.get('DB.host'),
        port=int(current_config.get('DB.port')),
        db=current_config.get('DB.database'),
        user=current_config.get('DB.user'),
        passwd=current_config.get('DB.password'),
        db_type=current_config.get('DB.db_type')
    )


def get_all_data(current_config):
    with __get_db(current_config) as db:
        config_list = db.query("select * from dap_p_global_config where app_name=%(app_name)s", {'app_name': 'dmp'})
    result = {}
    for c in config_list:
        key = c.get('key')
        value = c.get('value')
        value_type = c.get('value_type')
        group_name = c.get('group_name')

        config_key = f'{group_name}.{key}'
        if value_type == '环境':
            value = c.get('env_value')
        result[config_key] = value
    return result
