# -*- coding: utf-8 -*-
"""
    data source helpers
"""
import logging
import json

from .project import get_db, get_mysoft_erp, get_system_odps, get_system_rds

logger = logging.getLogger(__name__)


def get_source_by_id(source_id):
    with get_db() as db:
        source_info = db.query_one('SELECT * FROM dap_bi_data_source WHERE id = %s', (source_id,))
        if source_info['is_buildin']:
            if source_info['type'] == 'ODPS':
                source_info['conn_str'] = get_system_odps()
            elif source_info['type'] == 'MySQL':
                source_info['conn_str'] = get_system_rds()
            else:
                logging.error('%s数据类型不存在' % source_info['type'])
        elif source_info['type'] == 'MysoftERP':
            source_info['conn_str'] = get_mysoft_erp()
        else:
            source_info['conn_str'] = json.loads(source_info['conn_str'])
        return source_info
