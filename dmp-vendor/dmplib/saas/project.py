# -*- coding: utf-8 -*-
"""
    db helpers
"""
import logging
import json

from cryptography.fernet import <PERSON><PERSON><PERSON>

from dmplib.redis import conn as conn_redis
from dmplib.nacos_client import tenant_db_get, tenant_clean_db_get
from .errors import EmptyProjectCodeError
from ..constants import ADMINISTRATORS_ID
from ..db.mysql_wrapper import SimpleMysql, get_db as _get_db
from ..hug import g
from ..hug.context import DBContext
from ..utils.errors import UserError


logger = logging.getLogger(__name__)


def get_db(code=None, db_name_suffix=''):
    """
    获取项目的db对象
    :param str code:
    :param str db_name_suffix:
    :return SimpleMysql: SimpleMysql
    """

    db_ctx = DBContext.instance(g)
    db_conn = db_ctx.get_conn(code, db_name_suffix)
    if not db_conn:
        config = get_db_config(code, db_name_suffix)
        if not config:
            raise UserError(code=404, message='Account name or login password incorrect')
        db_conn = SimpleMysql(
            host=config.get('host'),
            port=config['port'],
            db=config['database'],
            user=config['user'],
            passwd=config['password'],
            db_type=config['db_type'],
        )
        db_ctx.set_conn(code, db_name_suffix, db_conn)
    return db_conn


def get_data_db():
    """
    获取项目Data库
    :return:
    """
    return get_db(db_name_suffix='data')


def get_data_db_config():
    """
    获取项目data库配置
    :return:
    """
    return get_db_config(db_name_suffix='data')


def _db_cache_key(code, suffix = ''):
    if suffix:
        suffix = ':' + suffix
    return f'Project:DB:Config:{code}{suffix}'


def get_db_config(code=None, db_name_suffix=None):
    """
    获取DB配置
    :param code:
    :param db_name_suffix:
    :return:
    """
    if not code:
        code = getattr(g, 'code', None)

    if not code:
        raise UserError(message="缺少企业代码")

    # cache = conn_redis()
    # cache_key = _db_cache_key(code, db_name_suffix)
    # config = cache.get_data(cache_key)
    data_db_suffix = 'data'

    # data库
    if db_name_suffix == data_db_suffix:
        config = tenant_clean_db_get(code)
    else:
        config = tenant_db_get(code)

    # if config:
    #     cache.set_data(cache_key, config)

    return config


def set_correct_project_code(code=None, request=None):
    """
    设置正确租户code（mysql存在不区分大小写问题，导致用户输入的大小写不一致的code能正常查询到，使得redis存有多份不一样的同租户缓存）
    :param request:
    :param code: 租户code
    :return:
    """
    if not code:
        code = getattr(g, 'code', None)

    if not code:
        raise UserError(message='Missing tenant code')

    if request and request.cookies.get('tenant_code') == code:
        # cookie中有code并且大小写相等，不用查询db
        g.code = code
        return code

    with _get_db() as db:
        code = db.query_scalar('SELECT code FROM dap_p_tenant WHERE code=:code ', {'code': code})
        if code:
            g.code = code
        else:
            raise EmptyProjectCodeError()

    return code


def rm_db_config_cache(code=None):
    if code is None or code == '':
        code = getattr(g, 'code', None)
    if code is None:
        return True
    cache_key = _db_cache_key(code)
    cache = conn_redis()
    return cache.delete(cache_key)


def get_odps_config(code=None):
    """
    获取ODPS配置
    :param str code: 项目code
    :return:
    """
    if not code:
        code = getattr(g, 'code', None)
    with _get_db() as db:
        sql = (
            'SELECT odps_proj AS project_name,odps_access_id AS access_id,odps_access_secret AS access_key '
            'FROM dap_bi_tenant_setting WHERE code=%s '
        )
        return db.query_one(sql, (code,))


def get_default_account(code=None):
    if not code:
        code = getattr(g, 'code', None)
    with _get_db() as db:
        sql = (
            'SELECT default_account '
            'FROM dap_bi_tenant_setting WHERE code=%(code)s '
        )
        return db.query_one(sql, {'code': code})

def get_system_rds():
    with _get_db() as db:
        project = db.query_one('SELECT tenant_db_name, rds_id FROM dap_p_tenant WHERE code=%s ', (g.code,))
        if project:
            rds_info = db.query_one('SELECT host, port, account, pwd FROM dap_bi_rds WHERE id=%s', (project['rds_id'],))
            return {
                "host": rds_info['host'],
                "port": rds_info['port'],
                "username": rds_info['account'],
                "password": rds_info['pwd'],
                "database": project['tenant_db_name'] + '_data',
            }
        return None


def get_system_odps():
    with _get_db() as db:
        odps_info = db.query_one(
            'SELECT odps_endpoint, odps_access_id, odps_access_secret, odps_proj  FROM dap_bi_tenant_setting WHERE code=%s ',
            (g.code,),
        )
        if odps_info:
            return {
                "endpoint": odps_info['odps_endpoint'],
                "project_name": odps_info['odps_proj'],
                "access_id": odps_info['odps_access_id'],
                "access_key": odps_info['odps_access_secret'],
            }
        return None


def get_mysoft_erp(erp_api_info_id=None, dataset_content=None):
    if not erp_api_info_id and dataset_content:
        if isinstance(dataset_content, str):
            dataset_content = json.loads(dataset_content)
        bind_source_id = dataset_content.get("bind_source_id")
        if bind_source_id:
            with get_db(g.code) as db:
                conn_str = db.query_scalar('select conn_str from dap_bi_data_source where id=%(id)s', {'id': bind_source_id})
                if conn_str:
                    erp_api_info_id = json.loads(conn_str).get('erp_api_info_id')
    if erp_api_info_id:
        with get_db(g.code) as db:
            erp_api_info = db.query_one("select * from dap_bi_erp_api_info where id=%(id)s", {'id': erp_api_info_id}) or {}
            host = erp_api_info.get("erp_api_host")
            if host:
                return {
                    'erpapi_host': host,
                    'erpapi_access_id': erp_api_info.get('erp_api_access_id'),
                    'erpapi_access_secret': erp_api_info.get('erp_api_access_secret')
                }
    project_info = get_project_info(g.code)  # 从缓存获取项目信息
    if project_info:
        return {'erpapi_host': project_info.get('erpapi_host'),
                'erpapi_access_id': project_info.get('erpapi_access_id'),
                'erpapi_access_secret': project_info.get('erpapi_access_secret')}
    return None


def _decode_rds(rds):
    """
    ace解密rds账号和密码
    :param rds:
    :return:
    """
    account_key = 'n-fX8xJyVLA1eav_wve4enaKfc5-AvXlrI-9kFemVlY='
    pwd_key = 'kWfWIP9EoKX4-ooJ0elahC3qZCoOWZECtunoLcCIzEI='
    cipher = Fernet(account_key)
    rds['account'] = cipher.decrypt(rds['account'].encode(encoding="utf-8")).decode()
    cipher2 = Fernet(pwd_key)
    rds['pwd'] = cipher2.decrypt(rds['pwd'].encode(encoding="utf-8")).decode()
    return rds


def get_project_info(code):
    """
    获取项目信息（从缓存获取）
    :param code:
    :return:
    """
    if not code:
        code = getattr(g, 'code', None)
    if not code:
        raise UserError(message="Missing tenant code")
    cache = conn_redis()
    cache_key = 'Project:Detail:Info:' + code
    project_info = cache.get_data(cache_key)
    if not project_info:
        with _get_db() as db:
            project_info = db.query_one('SELECT t1.name,t1.customer_id,t1.rds_id,t1.tenant_db_name,t1.title,t2.* FROM dap_p_tenant t1 join dap_bi_tenant_setting t2 on t1.code = t2.code WHERE t1.code=:code ', {'code': g.code})
        if project_info:
            cache.set_data(cache_key, project_info)
    return project_info

def get_admin_account(code):
    if not code:
        code = getattr(g, 'code', None)
    if not code:
        raise UserError(message="Missing tenant code")
    cache = conn_redis()
    cache_key = 'Project:admin:account:' + code
    admin_account = cache.get_data(cache_key)
    if not admin_account:
        with get_db(code) as db:
            admin_info = db.query_one(
                'SELECT `account` FROM dap_p_user WHERE id = %(id)s ',{'id': ADMINISTRATORS_ID})
        if not admin_info:
            return None
        admin_account = admin_info.get('account')
        cache.set_data(cache_key, admin_account)
    return admin_account


def current_db_type(suffix='', project_code=''):
    return get_db_config(project_code, db_name_suffix=suffix).get('db_type')