import cProfile
import random
from builtins import ValueError

TOPIC = 'cprofile'


class Outputer:
    def send(self, profiling_funcs):
        raise NotImplementedError()


class DebugOutputer:
    def send(self, profiling_funcs):
        raise NotImplementedError()


class CProfiling:
    def __init__(self, outputer, rate=1):
        """init
            outputer (Outputer): output engine
            rate (int, optional): Defaults to 1. 采样系数, 1表示全部, 0表示不profile
        Raises:
            ValueError: init
        """

        if rate < 0:
            raise ValueError("rate参数不能小于0")
        self.rate = rate
        self.outputer = outputer
        self.pr = None
        self.target = None

    def start(self):
        # 采样
        if self.rate <= 0:
            return

        if 1 <= self.rate == random.randint(1, self.rate):
            self.pr = cProfile.Profile(builtins=False)
            self.pr.enable()

    def end(self, target):
        if self.pr is None:
            return

        self.target = target
        self.pr.disable()
        self.pr.snapshot_stats()

        self._store()

    @staticmethod
    def _ignore(filename):
        if filename.find('python3.5') > 0:
            return True

        if filename.find('frozen importlib') > 0:
            return True
        return False

    def _store(self):

        # {
        #     "callcount": 4,
        #     "taget": "http://localhost:9001//api/user/profile",
        #     "func_name": "get_db",
        #     "calls": ["153ef66fac091e792e58743950e317a7dee97ab0"],
        #     "totaltime": 0.01324,
        #     "inlinetime": 3.7999999999999995e-05,
        #     "firstlineno": 17,
        #     "reccallcount": 4,
        #     "filename": "/home/<USER>/projects/dmp/dmplib/saas/project.py",
        #     "id": "1a30c943c9b07bcbb895b09e9043d7e621a805f0",
        # }

        rows = []

        for func, items in self.pr.stats.items():
            co_filename, co_firstlineno, co_name = func

            if self._ignore(co_filename):
                continue

            callcount, reccallcount, inlinetime, totaltime, calls = items
            import hashlib

            hm = hashlib.sha1()
            key = "%s %s %s" % (co_filename, co_firstlineno, co_name)
            hm.update(key.encode('utf-8'))
            row = {
                'id': hm.hexdigest(),
                'target': self.target,  # profile的目标, web请求的uri
                'filename': co_filename,
                'firstlineno': co_firstlineno,
                'func_name': co_name,
                'calls': [],
                'callcount': callcount,
                'reccallcount': reccallcount,
                'inlinetime': inlinetime,
                'totaltime': totaltime,
            }
            for sub_func, _ in calls.items():
                hm = hashlib.sha1()
                fn, no, nm = sub_func
                if self._ignore(fn):
                    continue
                hm.update(("%s %s %s" % (fn, no, nm)).encode('utf-8'))
                row['calls'].append(hm.hexdigest())

            rows.append(row)
        self.outputer.send(rows)
