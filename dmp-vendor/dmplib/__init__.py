# -*- coding: utf-8 -*-
"""
    for description
"""
from dmplib.conf_constants import ONE_DOMAIN, APP_NAME, DP_BASE_PATH_NAME

try:
    if ONE_DOMAIN in [1, '1']:
        from hug import redirect
        import falcon
        import hug

        # 默认域名后缀配置
        domain_prefix =  f"/{DP_BASE_PATH_NAME}"

        # 重定向带上后缀
        origin_to = redirect.to

        def to(location, code=falcon.HTTP_302):
            if location.startswith('/'):
                location = f'{domain_prefix}{location}'
            origin_to(location, code)

        redirect.to = to

except Exception as e:
    print(e)


version = '1.0.1'



