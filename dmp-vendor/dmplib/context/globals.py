# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    ~~~~~~~~~~~~~
    Defines all the global objects that are proxies to the current

"""

from .werkzeug_local import LocalStack, LocalProxy

_sentinel = object()


def _lookup_object():
    return _app_ctx_stack.top


class _AppCtxGlobals(object):
    """A plain object."""

    def get(self, name, default=None):
        return self.__dict__.get(name, default)

    def pop(self, name, default=_sentinel):
        if default is _sentinel:
            return self.__dict__.pop(name)
        else:
            return self.__dict__.pop(name, default)

    def setdefault(self, name, default=None):
        return self.__dict__.setdefault(name, default)

    def __contains__(self, item):
        return item in self.__dict__

    def __iter__(self):
        return iter(self.__dict__)


# context locals
_app_ctx_stack = LocalStack()
g = LocalProxy(_lookup_object)
