#!/usr/bin/env python
"""
    wrapper for pymysql

"""
import datetime
import json
import logging
import time
import gevent.hub
from copy import deepcopy
from sqlalchemy import create_engine, text, NullPool
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import OperationalError, ProgrammingError, DatabaseError
from sqlalchemy.engine.row import RowMapping
from urllib.parse import quote

from traceback import FrameSummary
from sshtunnel import SSHTunnelForwarder, BaseSSHTunnelForwarderError

from ..hug.context import DBContext
from ..hug.globals import g
from ..utils.errors import InvalidArgumentError
from . import errors as i_errors
from ..components.enums import DBType
from .. import global_config

# import meinheld
# 定义sql profiling的trace深度
SQL_PROFILE_TRACE_DEEP = 7

logger = logging.getLogger(__name__)
json.JSONEncoder.default = lambda self, obj: (obj.isoformat() if isinstance(obj, datetime.datetime) else None)


class ConnectException(Exception):
    pass


def _gevent_waiter(fd, hub=gevent.hub.get_hub()):
    hub.wait(hub.loop.io(fd, 1))


# def _meinheld_waiter(fd):
#    meinheld.server.trampoline(fd, read=True, timeout=120)


def get_db():
    """

    :return SimpleMysql:
    """
    from .. import config
    db = config.get('DB.database')
    if not db:
        raise config.MissConfigValueException('DB.database')

    # 从上下文中获取唯一实例
    db_ctx = DBContext.instance(g)
    db_conn = db_ctx.get_conn(None, db)

    if not db_conn or not db_conn.is_open:
        db_conn = SimpleMysql(
            host=config.get('DB.host'),
            port=int(config.get('DB.port')),
            db=config.get('DB.database'),
            user=config.get('DB.user'),
            passwd=config.get('DB.password'),
            db_type=config.get('DB.db_type')
        )
        db_ctx.set_conn(None, db, db_conn)

    return db_conn


def get_mysql_source_db(host, port, db, user, passwd):
    return SimpleMysql(host=host, port=port, db=db, user=user, passwd=passwd)


class SimpleMysql:
    conn = None
    cur = None

    def __init__(self, **kwargs):
        """ construct """
        self.host = kwargs.get("host", "localhost")
        self.port = kwargs.get("port", 3306)
        self.db = kwargs.get("db")
        self.user = kwargs.get('user')
        self.passwd = kwargs.get('passwd')

        self.keep_alive = kwargs.get("keep_alive", False)
        self.charset = kwargs.get("charset", "utf8")
        self.autocommit = kwargs.get("autocommit", False)
        # 事务标记，默认False，使用begin_transaction方法可将其开启为True。不支持事务嵌套。
        self.transaction = kwargs.get("transaction", False)
        self.connect_timeout = kwargs.get('connect_timeout', 10)

        # ssh
        self.ssh = None
        self.use_ssh = kwargs.get('use_ssh', False)
        self.ssh_host = kwargs.get('ssh_host')
        self.ssh_port = kwargs.get('ssh_port')
        self.ssh_user = kwargs.get('ssh_user')
        self.ssh_password = kwargs.get('ssh_password')

        # db_type
        self.db_type = kwargs.get('db_type') or global_config.DB_TYPE or ''

        self.engine = None

        self._check_args()

    def _check_args(self):
        """ check args of structure"""
        if not self.db and self.db_type != DBType.DM.value:
            self.db = DBType.MYSQL.value
        if self.user is None or self.passwd is None:
            raise InvalidArgumentError(500, 'Incomplete configuration of db parameters')
        if self.use_ssh and (not self.ssh_host or not self.ssh_port or not self.ssh_user or not self.ssh_password):
            raise InvalidArgumentError(500, 'SSH parameter configuration incomplete')

    def connect(self):

        """Connect to the mysql server"""
        logger.debug("connect db %s - %s", self.host, self.db)
        try:
            if self.use_ssh:
                try:
                    self.ssh = SSHTunnelForwarder(
                        (self.ssh_host, int(self.ssh_port)),
                        ssh_username=self.ssh_user,
                        ssh_password=self.ssh_password,
                        remote_bind_address=(self.host, int(self.port)),
                    )
                    self.ssh.start()
                except BaseSSHTunnelForwarderError:
                    logger.error('ssh connection failed ', exc_info=True)
                    self.ssh = None

            # if self.host == "**********":
            #     self.host = "audit.mingyuanyun.com"
            #     self.port = 30491
            #     self.user = 'f764485d-c1d0-4775-a317-23e5f8a810b0'
            #     self.passwd = 'kL8OgIMAG9lEPNGv'
            # elif self.host == "rm-bp1f9pc4yqu7v84x2.mysql.rds.aliyuncs.com":
            #     self.host = 'rm-bp1f9pc4yqu7v84x2mo.mysql.rds.aliyuncs.com'
            #     self.port = 3306
            # elif self.db_type == DBType.DM.value and self.host == '************':
            #     self.host = '************'
            #     self.port = 5236
            #     self.user = 'mycloud'
            #     self.passwd = 'hVbMcpBtk93x'

            if self.db_type == DBType.DM.value:
                url = 'dm+dmPython://{user}:{passwd}@{host}:{port}/'.format(
                    user=quote(self.user),
                    passwd=quote(self.passwd),
                    host=self.host,
                    port=self.port,
                )
                connect_args = {
                    'local_code': 1,
                    'connection_timeout': 30,
                    'schema': self.db
                }
            else:
                url = 'mysql+pymysql://{user}:{passwd}@{host}:{port}/{db}?ssl_disabled=True&connect_timeout=30'.format(
                    user=quote(self.user),
                    passwd=quote(self.passwd),
                    host=self.host,
                    port=self.port,
                    db=self.db,
                )
                connect_args = {
                    'connect_timeout': 30
                }
            self.engine = create_engine(
                url=url,
                echo=int(global_config.DB_ECHO) if global_config.DB_ECHO else 0,
                # isolation_level='REPEATABLE_READ' if not self.autocommit else 'AUTOCOMMIT',
                connect_args=connect_args,
                poolclass=NullPool
            )
            self.conn = sessionmaker(bind=self.engine)()
            if self.db_type == DBType.DM.value:
                self.ping_dm_with_retry()
        except Exception as e:
            raise ConnectException(
                "%{error}. host: {host}, port: {port}, user: {user}, pwd: {pwd}".format(
                    error=str(e),
                    host=self.host,
                    port=self.port,
                    user=self.user[:1] + '***',
                    pwd=self.passwd[:1] + '***',
                )
            )

    def ping_dm_with_retry(self):
        # 解决某些场景下, 达梦数据库查询并发较高时, 连接数据库报错Create SOCKET connection failure问题
        retry = 2
        while retry >= 0:
            try:
                self.conn.execute(text("select 1"))
                return
            except DatabaseError as e:
                if str(e).startswith("(dmPython.DatabaseError) [CODE:-70028]Create SOCKET connection failure"):
                    logger.warning(f"连接达梦数据库失败, 尝试重连, 剩余尝试次数: {retry}")
                    retry -= 1
                    time.sleep(2)
                else:
                    raise e

    def _convert_to_json(self, cur_result, one=None):

        if not cur_result:
            return None if one else []
        cur_result = [cur_result] if one else cur_result

        cur = self.cur
        r = [dict((cur.description[i][0], value) for i, value in enumerate(row)) for row in cur_result]

        return (r[0] if r else None) if one else r

    @staticmethod
    def format_params(sql, params):
        copy_params = deepcopy(params)
        if isinstance(params, dict):
            for k, v in params.items():
                if isinstance(v, (list, tuple)):
                    sql = sql.replace(f"%%({k})s", f"(:{k})").replace(f"%({k})s", f"(:{k})")
                    kk_list = []
                    for index, i in enumerate(v):
                        kk = f"{k}_{index}"
                        copy_params[kk] = i
                        kk_list.append(kk)
                    del copy_params[k]
                    sql = sql.replace(f":{k}", ", ".join([f':{j}' for j in kk_list]))
                else:
                    if isinstance(v, (datetime.datetime, datetime.date)):
                        copy_params[k] = v.strftime('%Y-%m-%d %H:%M:%S')
                    sql = sql.replace(f"%({k})s", f":{k}")
        return sql, copy_params

    @staticmethod
    def adapter_dm(sql):
        sql = sql.replace('`', '"').replace('GROUP_CONCAT(', 'wm_concat(').replace('group_concat(', 'wm_concat(')
        return sql

    def query_scalar(self, sql, params=None):
        cur = self._execute(sql, params)
        result = cur.fetchone()
        result = self.row2dict(result) if result else result

        if result:
            return list(result.values())[0]
        return None

    def query_columns(self, sql, params=None):
        cur = self._execute(sql, params)
        result = [self.row2dict(row) for row in cur.fetchall()]

        if result:
            return [list(t.values())[0] for t in result]
        return None

    def query_one(self, sql, params=None):

        if hasattr(g, "profiling"):
            import time

            before = time.time()
            cur = self._execute(sql, params)
            result = cur.fetchone()
            result = self.row2dict(result) if result else result
            g.sqls[-1]['duration'] = time.time() - before
            return result
        cur = self._execute(sql, params)
        result = cur.fetchone()
        return self.row2dict(result) if result else result

    def row2dict(self, row):
        if isinstance(row, RowMapping):
            row = dict(row)
            for k, v in row.items():
                if isinstance(v, bytes):
                    row[k] = v.decode('utf-8')
                else:
                    row[k] = v
        return row

    def query(self, sql, params=None, offset=None, limit=None):
        if offset is not None or limit is not None:
            sql += " LIMIT {}, {}".format(0 if offset is None else offset, limit)

        if hasattr(g, "profiling"):
            import time

            before = time.time()
            cur = self._execute(sql, params=params)
            items = [self.row2dict(row) for row in cur.fetchall()]
            g.sqls[-1]['duration'] = time.time() - before
        else:
            cur = self._execute(sql, params=params)
            items = [self.row2dict(row) for row in cur.fetchall()]
        if isinstance(items, tuple):
            return list(items)
        return items

    def query_cursor(self, sql, params=None, offset=None, limit=None):
        if offset is not None or limit is not None:
            sql += " LIMIT {}, {}".format(0 if offset is None else offset, limit)

        if hasattr(g, "profiling"):
            import time

            before = time.time()
            cur = self._execute(sql, params=params, dict_cursor=False)
            g.sqls[-1]['duration'] = time.time() - before
        else:
            cur = self._execute(sql, params=params, dict_cursor=False)
        return cur

    def fetch_data(self, cur):
        return [self.row2dict(row) for row in cur.mappings().fetchall()]

    def insert_dm(self, table, data, commit=True, auto_audit=True):
        """
        新增一条记录
        :param auto_audit: 是否添加审计字段
        :param table: 表名
        :param data: dict()
        :param commit: bool 是否立即提交, 如果有多个处理需要事物提交，则最后调用commit()方法提交
        :return: 受影响的行数
        """
        if auto_audit and data and isinstance(data, dict):
            if not data.get('created_by'):
                data['created_by'] = getattr(g, 'account')
            if not data.get('modified_by'):
                data['modified_by'] = getattr(g, 'account')

        copy_data = deepcopy(data)
        data = self._format_data_dm(data)
        copy_data = self._format_data(copy_data)

        query = self._serialize_insert_dm(copy_data, data)

        sql = "INSERT INTO `%s` (%s) VALUES(%s)" % (table.strip('`'), query[0], query[1])

        affect_row = self._execute(sql, data, dict_cursor=False).rowcount
        if not self.transaction and commit:
            self.commit()
        return affect_row

    def insert(self, table, data, commit=True, auto_audit=True):
        """
        新增一条记录
        :param auto_audit: 是否添加审计字段
        :param table: 表名
        :param data: dict()
        :param commit: bool 是否立即提交, 如果有多个处理需要事物提交，则最后调用commit()方法提交
        :return: 受影响的行数
        """
        if self.db_type == DBType.DM.value:
            return self.insert_dm(table, data, commit, auto_audit)

        data = self._format_data(data)
        if auto_audit and data and isinstance(data, dict):
            if not data.get('created_by'):
                data['created_by'] = getattr(g, 'account')
            if not data.get('modified_by'):
                data['modified_by'] = getattr(g, 'account')

        query = self._serialize_insert(data)

        sql = "INSERT INTO `%s` (%s) VALUES(%s)" % (table.strip('`'), query[0], query[1])

        affect_row = self._execute(sql, data, dict_cursor=False).rowcount
        if not self.transaction and commit:
            self.commit()
        return affect_row

    def replace_multi_data_for_dm(self, table, list_data, fields, commit=True, condition_field=None):
        """
        替换多行数据， dm版本
        """

        if not list_data:
            return 0
        if not condition_field:
            condition_field = ['id']

        dst_table = 'dst'
        src_table = 'src'
        using_sql_row = []

        if 'created_by' not in fields:
            fields.append('created_by')
        if 'modified_by' not in fields:
            fields.append('modified_by')

        params = {}

        for i, data in enumerate(list_data):
            data = self._format_data(data)
            if not data:
                continue
            if not data.get('created_by'):
                data['created_by'] = getattr(g, 'account')
            if not data.get('modified_by'):
                data['modified_by'] = getattr(g, 'account')
            using_sql_item = []
            _update_data = []
            for k in fields:
                if k not in data:
                    continue
                params[f"{k}_{i}"] = data.get(k)
                s = " %s as %s " % (f":{k}_{i}", k)
                using_sql_item.append(s)
            using_sql_row.append('select ' + ','.join(using_sql_item) + f'from dual ')

        on_sql = ' AND '.join([f'dst.{k} = src.{k}' for k in condition_field])
        using_sql = ' union all '.join(using_sql_row)
        update_sql = 'update set ' + ', '.join([f"{dst_table}.{i}={src_table}.{i}" for i in list_data[0].keys() if i not in condition_field])
        insert_sql = 'insert ({fields}) values ({values})'.format(
            fields=','.join([f"{dst_table}.{i}" for i in list_data[0].keys()]),
            values=','.join([f"{src_table}.{i}" for i in list_data[0].keys()])
        )

        sql = """
        MERGE INTO {table} {dst_table}
        USING (
        {using_sql}
        ) {src_table}
        on ({on_sql})
        WHEN MATCHED THEN
            {update_sql}
        WHEN NOT MATCHED THEN
            {insert_sql};
        """.format(
            table=table,
            on_sql=on_sql,
            update_sql=update_sql,
            insert_sql=insert_sql,
            using_sql=using_sql,
            dst_table=dst_table,
            src_table=src_table,
        )
        affect_row = self._execute(sql, params, dict_cursor=False).rowcount
        if not self.transaction and commit:
            self.commit()
        return affect_row

    def replace_multi_data_for_dm_v2(self, table, list_data, fields, commit=True, condition_field=None, has_del=True):
        """
        替换多行数据， dm版本, 先删后插
        """

        def _delete_sql(item, _condition_field):
            _where = '1 = 1 '
            _del_params = {}
            for k in _condition_field:
                if k in item:
                    _del_params[k] = item.get(k)
                    _where += f' and "{k}" = :{k}'
            return f'delete from "{table}" where {_where}', _del_params

        def _insert_sql(copy_item, item):
            # 达梦中mode是关键字，使用参数化insert会报错，需要启个别名
            copy_item = dict(sorted(copy_item.items()))
            item = dict(sorted(item.items()))
            keys = ', '.join([f'"{i}"' for i in copy_item.keys()])
            values = ', '.join([f':{i}' for i in item.keys()])
            return 'insert into "{table}" ({keys}) values ({values})'.format(
                table=table,
                keys=keys,
                values=values
            )

        if not list_data:
            return 0
        if not condition_field:
            condition_field = ['id']

        insert_sql_list = []
        for data in list_data:
            if not data.get('created_by'):
                data['created_by'] = getattr(g, 'account')
            if not data.get('modified_by'):
                data['modified_by'] = getattr(g, 'account')

            copy_data = deepcopy(data)
            copy_data = self._format_data(copy_data)
            data = self._format_data_dm(data)

            if has_del:
                delete_sql, del_param = _delete_sql(data, condition_field)
                self._execute(delete_sql, del_param, dict_cursor=False)
            insert_sql = _insert_sql(copy_data, data)
            insert_sql_list.append((insert_sql, data))
            self._execute(insert_sql, data, dict_cursor=False)

        if not self.transaction and commit:
            self.commit()
        return len(list_data)

    def replace_multi_data_for_dm_v3(self, table, list_data, fields, commit=True, condition_field=None, has_del=True):
        """
        替换多行数据， dm版本, 批量执行sql
        """

        def _delete_sql(item, _condition_field, inx):
            _where = '1 = 1 '
            _del_params = {}
            for k in _condition_field:
                if k in item:
                    _del_params[f'{k}_{inx}'] = item.get(k)
                    _where += f' and "{k}" = :{k}_{inx}'
            return f'delete from "{table}" where {_where}', _del_params

        def _insert_sql(copy_item, item):
            # 达梦中mode是关键字，使用参数化insert会报错，需要启个别名
            copy_item = dict(sorted(copy_item.items()))
            item = dict(sorted(item.items()))
            keys = ', '.join([f'"{i}"' for i in copy_item.keys()])
            values = ', '.join([f':{i}' for i in item.keys()])
            return 'insert into "{table}" ({keys}) values ({values})'.format(
                table=table,
                keys=keys,
                values=values
            )

        if not list_data:
            return 0
        if not condition_field:
            condition_field = ['id']

        if not isinstance(condition_field, list):
            raise TypeError('condition_field must be a list')

        sql_list = []
        all_params = {}
        for index, data in enumerate(list_data):
            if not data.get('created_by'):
                data['created_by'] = getattr(g, 'account')
            if not data.get('modified_by'):
                data['modified_by'] = getattr(g, 'account')

            copy_data = deepcopy(data)
            copy_data = self._format_data(copy_data)
            data = self._format_data_dm_v3(data, index)

            if has_del:
                delete_sql, del_param = _delete_sql(copy_data, condition_field, index)
                sql_list.append(delete_sql)
                all_params.update(del_param)
            insert_sql = _insert_sql(copy_data, data)
            sql_list.append(insert_sql)
            all_params.update(data)
            if len(sql_list) > 100:
                self._execute(';'.join(sql_list), all_params, dict_cursor=False)
                sql_list = []
                all_params = {}

        if sql_list:
            self._execute(';'.join(sql_list), all_params, dict_cursor=False)

        if not self.transaction and commit:
            self.commit()
        return len(list_data)

    def replace_multi_data_for_dm_v4(self, table, list_data, fields, commit=True, condition_field=None, has_del=True):
        """
        替换多行数据
        :param str table:
        :param list list_data:
        :param list fields:
        :param bool commit:
        :param dict condition_field:
        :param dict has_del:
        :return:
        """
        params = {}
        values = []
        tmp = 0
        cur_account = getattr(g, 'account')
        for data in list_data:
            if not isinstance(data, dict):
                continue
            tmp_val = []
            fields = list(data.keys())

            for c in fields:
                v = data.get(c)
                if not isinstance(c, str) or c == 'increment_id':
                    continue
                if c in ['created_on', 'modified_on']:
                    v = self.now()
                if v and c in ['released_on', 'first_released', 'startup_time', 'end_time', 'edit_on', 'modified_on', 'created_on'] and 'T' in v:
                    v = v.replace('T', ' ')
                if v is not None and isinstance(v, object):
                    v = str(v)
                p_name = c + '_' + str(tmp)
                tmp_val.append('%(' + p_name + ')s')
                params[p_name] = v
            if 'created_by' not in fields:
                tmp_val.append('%(created_by)s')
            if 'modified_by' not in fields:
                tmp_val.append('%(modified_by)s')
            values.append('(' + ','.join(tmp_val) + ')')

            tmp += 1

        if not values:
            return 0
        if 'created_by' not in fields:
            fields.append('created_by')
            params['created_by'] = cur_account
        if 'modified_by' not in fields:
            fields.append('modified_by')
            params['modified_by'] = cur_account

        if 'increment_id' in fields:
            fields.remove('increment_id')

        sql = 'INSERT INTO /*+IGNORE_ROW_ON_DUPKEY_INDEX("{table}")*/  "{table}"({cols}) VALUES {values};'.format(
            table=table.strip('`'), cols=','.join(['"' + c + '"' for c in fields]), values=','.join(values)
        )

        affect_row = self._execute(sql, params, dict_cursor=False).rowcount
        if not self.transaction and commit:
            self.commit()
        return affect_row

    def replace_multi_data(self, table, list_data, fields, commit=True, condition_field=None, has_del=True):
        """
        替换多行数据
        :param str table:
        :param list list_data:
        :param list fields:
        :param bool commit:
        :param dict condition_field:
        :param dict has_del:
        :return:
        """
        # 达梦不支持replace into 需要换成 merge into
        if self.db_type == DBType.DM.value:
            # return self.replace_multi_data_for_dm(table, list_data, fields, commit, condition_field)
            return self.replace_multi_data_for_dm_v3(table, list_data, fields, commit, condition_field, has_del=has_del)

        params = {}
        values = []
        tmp = 0
        cur_account = getattr(g, 'account')
        for data in list_data:
            if not isinstance(data, dict):
                continue
            tmp_val = []
            for c in fields:
                if not isinstance(c, str):
                    continue
                if c in ['created_on', 'modified_on'] and not data.get(c):
                    data[c] = self.now()
                p_name = c + '_' + str(tmp)
                tmp_val.append('%(' + p_name + ')s')
                params[p_name] = data.get(c)
            if 'created_by' not in fields:
                tmp_val.append('%(created_by)s')
            if 'modified_by' not in fields:
                tmp_val.append('%(modified_by)s')
            values.append('(' + ','.join(tmp_val) + ')')

            tmp += 1

        if not values:
            return 0
        if 'created_by' not in fields:
            fields.append('created_by')
            params['created_by'] = cur_account
        if 'modified_by' not in fields:
            fields.append('modified_by')
            params['modified_by'] = cur_account

        sql = 'replace INTO `{table}`({cols}) VALUES {values};'.format(
            table=table.strip('`'), cols=','.join(['`' + c + '`' for c in fields]), values=','.join(values)
        )

        affect_row = self._execute(sql, params, dict_cursor=False).rowcount
        if not self.transaction and commit:
            self.commit()
        return affect_row

    def insert_multi_data(self, table, list_data, fields, commit=True):
        """
        添加多行数据
        :param str table:
        :param list list_data:
        :param list fields:
        :param bool commit:
        :return:
        """
        params = {}
        values = []
        tmp = 0
        cur_account = getattr(g, 'account')
        for data in list_data:
            if not isinstance(data, dict):
                continue
            tmp_val = []
            for c in fields:
                if not isinstance(c, str):
                    continue
                p_name = c + '_' + str(tmp)
                tmp_val.append('%(' + p_name + ')s')
                params[p_name] = data.get(c)
            if 'created_by' not in fields:
                tmp_val.append('%(created_by)s')
            if 'modified_by' not in fields:
                tmp_val.append('%(modified_by)s')
            values.append('(' + ','.join(tmp_val) + ')')
            tmp += 1
        if not values:
            return 0
        if 'created_by' not in fields:
            fields.append('created_by')
            params['created_by'] = cur_account
        if 'modified_by' not in fields:
            fields.append('modified_by')
            params['modified_by'] = cur_account
        sql = 'INSERT INTO `{table}`({cols}) VALUES {values};'.format(
            table=table.strip('`'), cols=','.join(['`' + c + '`' for c in fields]), values=','.join(values)
        )

        affect_row = self._execute(sql, params, dict_cursor=False).rowcount
        if not self.transaction and commit:
            self.commit()
        return affect_row

    # 批量插入executemany
    def insert_by_many(self, table_name, table_data, commit=True):
        if not table_data:
            return False, 'Data cannot be empty'
        if not self.conn:
            self.connect()
        values = ['%s'] * len(table_data[0])
        sql = 'INSERT INTO `{table_name}` values({values})'.format(table_name=table_name.strip(), values=','.join(values))
        try:
            # 批量插入
            rowcount = self.conn.executemany(sql, tuple(table_data))
            if not self.transaction and commit:
                self.commit()
            return True, rowcount
        except Exception as e:  # pylint: disable=broad-except
            if not self.transaction and commit:
                self.rollback()
            return False, str(e)

    def update_dm(self, table, data, condition=None, commit=True, with_none=False):
        """
        更新记录
        :param table: 表名
        :param data: dict()
        :param condition: dict()
        :param commit: bool 是否立即提交, 如果有多个处理需要事物提交，则最后调用commit()方法提交
        :param with_none: bool 是否把none更新到数据库
        :return:
        """
        if data and isinstance(data, dict) and not data.get('modified_by'):
            data['modified_by'] = getattr(g, 'account')

        copy_data = deepcopy(data)
        data = self._format_data_dm(data)
        copy_data = self._format_data(copy_data)

        query = self._serialize_update_dm(copy_data, data)
        sql = "UPDATE `%s` SET %s" % (table.strip('`'), query)

        params = deepcopy(data)
        if condition and len(condition) > 0:
            c = 0
            condition_list = []
            for k, v in condition.items():
                k_alias = f"{k}_{c}"
                params[k_alias] = v
                condition_list.append(f'`{k}`= :{k_alias}')
                c += 1
            sql += " WHERE %s" % ' AND '.join(condition_list)

        affect_row = self._execute(sql, params, dict_cursor=False).rowcount
        if not self.transaction and commit:
            self.commit()
        return affect_row

    def update(self, table, data, condition=None, commit=True, with_none=False):
        """
        更新记录
        :param table: 表名
        :param data: dict()
        :param condition: dict()
        :param commit: bool 是否立即提交, 如果有多个处理需要事物提交，则最后调用commit()方法提交
        :param with_none: bool 是否把none更新到数据库
        :return:
        """

        if self.db_type == DBType.DM.value:
            return self.update_dm(table, data, condition, commit, with_none)

        data = self._format_data(data, with_none)

        if data and isinstance(data, dict) and not data.get('modified_by'):
            data['modified_by'] = getattr(g, 'account')
        query = self._serialize_update(data)
        sql = "UPDATE `%s` SET %s" % (table.strip('`'), query)

        params = deepcopy(data)
        if condition and len(condition) > 0:
            c = 0
            condition_list = []
            for k, v in condition.items():
                k_alias = f"{k}_{c}"
                params[k_alias] = v
                condition_list.append(f'`{k}`= :{k_alias}')
                c += 1
            sql += " WHERE %s" % ' AND '.join(condition_list)

        affect_row = self._execute(sql, params, dict_cursor=False).rowcount
        if not self.transaction and commit:
            self.commit()
        return affect_row

    def delete_by_id(self, table, row_id, commit=True):
        """Delete rows based on a where condition"""
        sql = "DELETE FROM `%s` WHERE id='%s'" % (table.strip('`'), row_id)

        affect_row = self._execute(sql, dict_cursor=False).rowcount
        if not self.transaction and commit:
            self.commit()
        return affect_row

    def delete(self, table, condition=None, commit=True):
        """Delete rows based on a where condition"""
        sql = "DELETE FROM `%s`" % table.strip('`')
        params = condition
        if condition and len(condition) > 0:
            sql += " WHERE %s" % ' AND '.join([f'`{k}`= :{k}' for k, _ in condition.items()])
            # params = tuple(condition.values())
        affect_row = self._execute(sql, params, dict_cursor=False).rowcount
        if not self.transaction and commit:
            self.commit()
        return affect_row

    def exec_sql(self, sql, params=None, commit=True):
        affect_row = self._execute(sql, params, dict_cursor=False).rowcount
        if not self.transaction and commit:
            self.commit()
        return affect_row

    def _execute(self, sql, params=None, dict_cursor=True):
        """
        check if connection is alive. if not, reconnect
        :param sql:
        :param params:
        :param dict_cursor:
        :rtype Cursor:
        """
        logger.debug("execute sql %s %r", sql, params)
        if not self.conn:
            self.connect()
        if hasattr(g, "profiling"):
            import traceback

            extracted_list = traceback.extract_stack(limit=SQL_PROFILE_TRACE_DEEP)
            g.sqls.append(
                {
                    "sql": sql,
                    "params": params,
                    "db": self.db,
                    "stacks": [
                        "{fname} {lineno} {name}".format(fname=frame.filename, lineno=frame.lineno, name=frame.name)
                        if isinstance(frame, FrameSummary)
                        else "{fname} {lineno} {name}".format(fname=frame[0], lineno=frame[1], name=frame[2])
                        for frame in extracted_list
                        if (isinstance(frame, (list, tuple)) and not frame[0].find('site-packages') > 0)
                        or (isinstance(frame, FrameSummary) and not frame.filename.find('site-packages') > 0)
                    ],
                }
            )
        try:
            if self.db_type == DBType.DM.value:
                sql = self.adapter_dm(sql)
            sql, new_params = self.format_params(sql, params)
            cur = self.conn.execute(text(sql), new_params)
        except (OperationalError, ProgrammingError, DatabaseError) as e:  # pylint: disable=no-member
            # 转换成自定义异常类
            raise i_errors.convert(e)

        return cur.mappings() if dict_cursor else cur

    def begin_transaction(self):
        """
        开启事务
        :return:
        """
        self.transaction = True
        return self.transaction

    def commit(self):
        """Commit a transaction (transactional engines like InnoDB require this)"""
        # 提交事务后将当前事务标记还原为False
        if self.transaction:
            self.transaction = False
        return self.conn.commit()

    def rollback(self):
        # 回归事务后将当前事务标记还原为False
        if self.transaction:
            self.transaction = False
        return self.conn.rollback()

    def is_open(self):
        """Check if the connection is open"""
        return self.conn.is_active if self.conn else False

    def end(self):
        """Kill the connection"""
        if self.cur:
            self.cur.close()
        if self.conn:
            self.conn.close()
        if self.ssh:
            self.ssh.close()
        if self.engine:
            self.engine.dispose()

    def close(self):
        self.end()

    @staticmethod
    def _serialize_insert(data):
        """Format insert dict values into strings"""
        keys = f"`{'`,`'.join(data.keys())}`"
        # vals = ('%s,' * len(data))[0:-1]
        vals = ', '.join([f':{k}' for k in data.keys()])

        return [keys, vals]

    @staticmethod
    def _serialize_insert_dm(data, new_data=None):
        """Format insert dict values into strings"""
        if not new_data:
            new_data = data

        data = dict(sorted(data.items()))
        new_data = dict(sorted(new_data.items()))

        keys = f"`{'`,`'.join(data.keys())}`"
        # vals = ('%s,' * len(data))[0:-1]
        vals = ', '.join([f':{k}' for k in new_data.keys()])

        return [keys, vals]

    @staticmethod
    def _serialize_update(data):
        """Format update dict values into string"""
        return ','.join([f'`{f}`=:{f}' for f in data.keys()])

    @staticmethod
    def _serialize_update_dm(data, new_data=None):
        """Format update dict values into string"""
        if not new_data:
            new_data = data
        data = dict(sorted(data.items()))
        new_data = dict(sorted(new_data.items()))
        keys = data.keys()
        vals = new_data.keys()
        return ','.join([f'`{k}`=:{v}' for k, v in zip(keys, vals)])


    @staticmethod
    def _format_data(data, with_none=False):
        """对象需要强制转换成 str,None 转换成空字符串"""

        for k in list(data.keys()):
            v = data[k]
            if not with_none and v is None:
                data.pop(k)
                continue
            if v is not None and isinstance(v, object):
                data[k] = str(v)
        return data

    @staticmethod
    def now():
        return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    @staticmethod
    def _format_data_dm(data, with_none=False):
        """对象需要强制转换成 str,None 转换成空字符串"""
        new_data = deepcopy(data)
        for k in list(data.keys()):
            v = data[k]
            if not with_none and v is None:
                data.pop(k)
                new_data.pop(k)
                continue
            try:
                if k in ['mode', 'level']:
                    del new_data[k]
                    new_data[f'{k}_alias'] = v
                    continue
                if v and k in ['released_on', 'first_released', 'startup_time', 'end_time', 'edit_on', 'modified_on', 'created_on'] and 'T' in v:
                    v = v.replace('T', ' ')
                if k in ['modified_on', 'created_on'] and not v:
                    v = SimpleMysql.now()
                    data[k] = v
                    new_data[k] = v
            except Exception as e:
                logger.error(e)
            if v is not None and isinstance(v, object):
                data[k] = str(v)
                new_data[k] = str(v)
        return new_data

    @staticmethod
    def _format_data_dm_v3(data, index, with_none=False):
        """对象需要强制转换成 str,None 转换成空字符串"""
        value_data = {}
        for k in list(data.keys()):
            v = data[k]
            if not with_none and v is None:
                data.pop(k)
                continue
            try:
                if v and k in ['released_on', 'first_released', 'startup_time', 'end_time', 'edit_on', 'modified_on', 'created_on'] and 'T' in v:
                    v = v.replace('T', ' ')
                if k in ['modified_on', 'created_on'] and not v:
                    v = SimpleMysql.now()

                value_data[f"{k}_{index}"] = v

            except Exception as e:
                logger.error(e)
            if v is not None and isinstance(v, object):
                value_data[f"{k}_{index}"] = str(v)
        return value_data

    def __enter__(self):
        return self

    def __exit__(self, error_type, value, traceback):
        if error_type:
            print(error_type, value, traceback)
            # 这里close由http的request上下文接管
            # self.end()
