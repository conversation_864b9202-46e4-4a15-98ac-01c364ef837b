"""
异常类
"""
import pymysql
import re


r_unknown_column = re.compile(r"Unknown column '([^']+)' in 'field list'")
r_not_exist_table = re.compile(r"Table '([^']+)' doesn't exist")


class QueryException(Exception):
    pass


class ColumnNotFoundError(QueryException):
    pass


class TableNotFoundError(QueryException):
    pass


def _extract_column_of_error(errmsg):
    """
    extract the column name from text
    :param errmsg:
    :return:
    """
    if not errmsg:
        return ''
    # "Unknown column 'ZBJJ_11859770931' in 'field list'"
    m = r_unknown_column.match(errmsg)
    if not m:
        return ''
    g = m.groups()
    return g[0] if len(g) > 0 else ''


def _extract_table_of_error(errmsg):
    if not errmsg:
        return ''

    # "Table 'dmp_yc_data.dataset_9b3a6102761dc69a' doesn't exist"
    m = r_not_exist_table.match(errmsg)
    if not m:
        return ''
    g = m.groups()
    return g[0] if len(g) > 0 else ''


def convert(source_err: Exception):
    errmsg = str(source_err)
    # pylint: disable=no-member
    if isinstance(source_err, (pymysql.OperationalError, pymysql.ProgrammingError)) and len(source_err.args) > 0:
        if source_err.args[0] == 1054:
            return ColumnNotFoundError(_extract_column_of_error(errmsg), errmsg)
        elif source_err.args[0] == 1146:
            return TableNotFoundError(_extract_table_of_error(errmsg), errmsg)
    return source_err
