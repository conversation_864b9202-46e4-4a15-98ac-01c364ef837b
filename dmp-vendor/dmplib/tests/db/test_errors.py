from unittest import TestCase

from dmplib.db import errors


class TestErrors(TestCase):
    def test__extract_column_of_error(self):

        column = errors._extract_column_of_error("Unknown column 'ZBJJ_11859770931' in 'field list'")
        self.assertEqual(column, 'ZBJJ_11859770931')

    def test__extract_table_of_error(self):
        table = errors._extract_table_of_error("Table 'dmp_yc_data.dataset_9b3a6102761dc69a' doesn't exist")
        self.assertEqual(table, "dmp_yc_data.dataset_9b3a6102761dc69a")
