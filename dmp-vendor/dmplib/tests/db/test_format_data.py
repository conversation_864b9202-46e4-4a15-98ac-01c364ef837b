import unittest

from dmplib.db.mysql_wrapper import SimpleMysql


class TestFormatData(unittest.TestCase):

    def test_format_data(self):
        args = [
            # input, expect, with_none, message
            ({'a': 1}, {'a': '1'}, False, '解析int失败'),
            ({'a': 'g'}, {'a': 'g'}, False, '解析str失败'),
            ({'a': None}, {}, False, '解析None失败'),
            # with_none
            ({'a': None}, {'a': None}, True, '解析None失败 with_none'),
            ({'a': 1}, {'a': '1'}, True, '解析int失败 with_none'),
            ({'a': 'g'}, {'a': 'g'}, True, '解析str失败 with_none'),
        ]
        for (inp, expect, with_none, msg) in args:
            result = SimpleMysql._format_data(inp, with_none)
            self.assertEqual(expect, result, msg)
