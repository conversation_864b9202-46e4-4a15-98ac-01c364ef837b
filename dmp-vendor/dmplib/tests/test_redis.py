
from .. import redis
from ..tests.base_test import BaseTest


class TestRedisCache(BaseTest):
    prefix = 'unitest'
    conn = None

    def setUp(self):
        super().setUp()
        if self.conn is None:
            self.conn = redis.RedisCache(self.prefix, 10)

    def test_hashset_with_key(self):
        hash_name = 'uinitest_hash'
        self.conn.delete(hash_name)
        self.conn.hdel(hash_name, 'prop')
        data = {'name': 'zs', 'age': 1}
        self.conn.hset(hash_name, 'prop', data)
        result = self.conn.hget(hash_name, 'prop')
        self.assertEqual(result, data)
        self.conn.delete(hash_name)

        self.conn.hmset(hash_name, {'prop1': '1', 'prop2': data, 'prop3': 22})
        result = self.conn.hget(hash_name, 'prop2')
        self.assertEqual(result, data)

        result = self.conn.hgetall(hash_name)
        self.assertTrue(result['prop1'] == '1' and result['prop2'] == data and result['prop3'] == 22)

        results = self.conn.hmget(hash_name, ('prop1', 'prop2'))
        self.assertEqual(results[0], '1')
        self.assertEqual(results[1], data)

        self.conn.delete(hash_name)

    def test_wrapper_key(self):
        self.conn.set('wrapper_key', 1, 5)
        exist = self.conn._raw_key_exist(self.prefix + 'wrapper_key')
        assert exist
