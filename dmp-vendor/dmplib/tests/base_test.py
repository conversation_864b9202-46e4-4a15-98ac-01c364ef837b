# -*- coding: utf-8 -*-
"""
Created on 2017年6月16日

@author: chenc04
"""
import unittest
import os

from ..hug.globals import _AppCtxGlobals, _app_ctx_stack
from ..hug.context import DBContext


class BaseTest(unittest.TestCase):
    def __init__(self, method_name='runTest', code='', account=''):
        os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), '../../app.config')
        super().__init__(method_name)
        self.code = code
        self.account = account

    def setUp(self):
        g = _AppCtxGlobals()
        self.g = g
        g.code = self.code
        g.account = self.account
        _app_ctx_stack.push(g)
        db_ctx = DBContext()
        db_ctx.inject(g)

    def tearDown(self):

        db_ctx = DBContext.instance(self.g)
        db_ctx.close_all()
