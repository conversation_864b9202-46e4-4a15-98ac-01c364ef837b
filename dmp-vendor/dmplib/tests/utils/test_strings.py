import unittest

from dmplib.utils import strings


class TestStrings(unittest.TestCase):
    def test_is_alpha(self):
        assert strings.is_alpha('a') is True
        assert strings.is_alpha('中') is False
        assert strings.is_alpha('') is False
        assert strings.is_alpha(' ') is False
        assert strings.is_alpha('!') is False
        assert strings.is_alpha('A') is True

    def test_get_first_pinyin_hanzi(self):
        assert strings.get_first_pinyin_hanzi('哈1') == 'H'
        assert strings.get_first_pinyin_hanzi('程序员') == 'CXY'
        assert strings.get_first_pinyin_hanzi('s程序员') == 'SCXY'
        assert strings.get_first_pinyin_hanzi('s程 序员') == 'SCXY'
        assert strings.get_first_pinyin_hanzi('s程#%序员') == 'SCXY'

    def test_fletcher32(self):
        assert strings.fletcher32('a营业额') is not None
        assert strings.fletcher32('*(程序') is not None
        assert strings.fletcher32('^程序工种0') != strings.fletcher32('^程序工种')
        long_text = """
        Upload from File – click Browse to search for and upload a new image for the logo.
Upload from URL – use one of the following conventions:
A URL beginning with 'http://' or 'https://' is treated by JIRA as an absolute URL/path.
A URL beginning with a forward slash '/' is treated as a path relative to the <jira-application-dir> subdirectory of your JIRA Installation Directory.
(tick) Tip: If you use a JIRA WAR distribution, it is recommended that you add your logo images to the edit-webapp subdirectory of your JIRA Installation Directory prior to building your WAR distribution file. For details on building JIRA WAR distributions, refer to the application server-specific documentation in the Installing JIRA WAR section.

If the JIRA logo does not appear after changing it to a custom one, ensure that the URL specified uses the correct case as this may be case-sensitive.

If you don't like the change, simply click Undo.
        """
        long_text_2 = long_text + '0'
        assert strings.fletcher32(long_text) is not None
        assert strings.fletcher32(long_text) != strings.fletcher32(long_text_2)

    def test_is_ascii(self):
        assert strings.is_ascii("2") is True
        assert strings.is_ascii("a") is True
        assert strings.is_ascii("测") is False
        assert strings.is_ascii(" ") is True
        assert strings.is_ascii("!") is True
