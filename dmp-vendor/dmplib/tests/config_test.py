import os
from ..tests.base_test import BaseTest
from .. import config
from .. import constants


class TestConfig(BaseTest):
    def setUp(self):
        super().setUp()
        self.env_code_key = constants.ENV_CODE_KEY

    def test_get_env_code(self):

        default_env_code = config.get_env_code()
        self.assertEqual(default_env_code, 'default')

        os.environ[self.env_code_key] = 'unitest'
        env_code = config.get_env_code()
        self.assertEqual(env_code, 'unitest')
        del os.environ[self.env_code_key]


class TestMissConfigValueException(BaseTest):
    def test_str(self):

        err = config.MissConfigValueException("id")
        self.assertEqual(str(err), "缺少配置项'id'或值为空")
