#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from loguru import logger
from configparser import RawConfigParser as ConfigParser, NoOptionError

from .nacos_client import NacosClient
from . import constants, default_config, db_config, global_config

ALL_CONFIG = {}

class MissConfigValueException(Exception):
    def __init__(self, config_item):
        self.config_item = config_item
        super().__init__(config_item)

    def __str__(self):
        return "缺少配置项'%s'或值为空" % self.config_item


def get_env_code():
    return os.environ.get(constants.ENV_CODE_KEY, 'default')


def aliasOfHost():
    # from dmplib.components.enums import SkylineApps
    # url = NacosClient.get(SkylineApps.DP.value + '.url', '')
    url = NacosClient.get('client_domain', '')
    if url:
        return {'Domain.client_domain': url}
    else:
        return {}

class Config:
    __instance = None

    def __init__(self, config_file_path=None):
        self.config_parser = ConfigParser()
        self.config_file_path = config_file_path or os.path.join(os.path.dirname(__file__), '../app.config')
        self.config_file_path = os.path.abspath(self.config_file_path)
        self.load_config()

    @staticmethod
    def get_instance():
        if Config.__instance:
            return Config.__instance
        Config.__instance = Config(os.environ.get('DMP_CFG_FILE_PATH'))
        return Config.__instance

    def load_config(self):
        if not os.path.isfile(self.config_file_path):
            # raise FileNotFoundError('配置文件不存在：' + str(self.config_file_path))
            logger.warning(f"app.config not exist：{self.config_file_path}")
        self.config_parser.read(self.config_file_path, 'utf-8')

    def get(self, key, default=None):
        """
        获取配置
        :param str key: 格式 [section].[key] 如：app.name
        :param Any default: 默认值
        :return:
        """
        map_key = key.split('.')
        if len(map_key) < 2:
            return default
        section = map_key[0]
        if not self.config_parser:
            return default
        if not self.config_parser.has_section(section):
            return default
        option = '.'.join(map_key[1:])
        try:
            return self.config_parser.get(section, option)
        except NoOptionError:
            return default

    def get_all_data(self):
        """
        获取所有配置
        :return:
        """
        result = dict()
        if not self.config_parser:
            return result
        for section in self.config_parser.sections():
            for key, value in self.config_parser.items(section):
                # 将 section 和 key 拼接为 'section.key' 的形式
                result[f'{section}.{key}'] = value

        return result


def get(key, default=None):
    """
    获取配置
    :param str key: 格式 [section].[key] 如：app.name
    :param Any default: 默认值
    :return:
    """
    global ALL_CONFIG
    return ALL_CONFIG.get(key, default)

def load_config():
    logger.error('重新加载nacos配置')
    NacosClient.refresh()
    global ALL_CONFIG
    ALL_CONFIG = default_config.get_all_data()
    ALL_CONFIG.update(Config.get_instance().get_all_data())
    ALL_CONFIG.update(NacosClient.get_all_data())
    global_config.DB_TYPE = ALL_CONFIG.get('DB.db_type')
    global_config.DB_ECHO = ALL_CONFIG.get('DB.echo', 0)
    ALL_CONFIG.update(db_config.get_all_data(ALL_CONFIG))
    ALL_CONFIG.update(aliasOfHost())


# 默认配置(代码层面)
ALL_CONFIG = default_config.get_all_data()
# 文件配置，从app.config文件加载
ALL_CONFIG.update(Config.get_instance().get_all_data())
# Ｎacos配置
ALL_CONFIG.update(NacosClient.get_all_data())
global_config.DB_TYPE = ALL_CONFIG.get('DB.db_type')
global_config.DB_ECHO = ALL_CONFIG.get('DB.echo', 0)
# 从数据库中读取配置
ALL_CONFIG.update(db_config.get_all_data(ALL_CONFIG))
ALL_CONFIG.update(aliasOfHost())


if __name__ == '__main__':
    obj = Config(config_file_path='./app.config')
    config_data = obj.get_all_data()
    print(config_data)
    print(ALL_CONFIG)
    print(get('App.name'))
