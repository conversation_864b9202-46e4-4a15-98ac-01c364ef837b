from dmplib.nacos_utils.middleware_parser.base_parser import MiddlewareBaseParser
from dmplib.nacos_utils.enums import ResourceUsedEnum, ResourceInstanceEnum
from dmplib.utils.model import BaseModel


class StorageUsedConfigModel(BaseModel):
    __slots__ = ('instanceId', 'code', 'rootPath')

    def __init__(self, **kwargs):
        self.instanceId = kwargs.get('instanceId')
        self.code = kwargs.get('code')
        self.rootPath = kwargs.get('rootPath')
        super().__init__(**kwargs)


class StorageInstanceModel(BaseModel):
    __slots__ = ('id', 'name', 'insideUrl', 'url', 'bucket', 'type', 'accessId', 'accessKey')

    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.name = kwargs.get('name')
        self.insideUrl = kwargs.get('insideUrl')
        self.url = kwargs.get('url')
        self.bucket = kwargs.get('bucket')
        self.type = kwargs.get('type')
        self.accessId = kwargs.get('accessId')
        self.accessKey = kwargs.get('accessKey')
        super().__init__(**kwargs)


class StorageParser(MiddlewareBaseParser):

    def __init__(self, config_data):
        super().__init__(config_data)

    def parse(self):
        # 1. 获取资源配置
        used_config = self.get_used_config(ResourceUsedEnum.STORAGE.value)
        if not used_config:
            return {}
        used_config_model = StorageUsedConfigModel(**used_config)
        # 2. 根据资源的实例ID获取实例配置
        instance_config = self.get_instance_config(ResourceInstanceEnum.STORAGE.value, used_config_model.instanceId)
        if not instance_config:
            return {}
        instance_config_model = StorageInstanceModel(**instance_config)

        del self.config_data[ResourceUsedEnum.STORAGE.value]
        del self.config_data[ResourceInstanceEnum.STORAGE.value]

        # 3. 将nacos配置映射到app.config配置
        return {
            'Minio.root_path': used_config_model.rootPath,
            'Minio.endpoint': instance_config_model.url,
            'Minio.inside_endpoint': instance_config_model.insideUrl,
            'Minio.bucket': instance_config_model.bucket,
            'Minio.access_key_secret': instance_config_model.accessKey,
            'Minio.access_key_id': instance_config_model.accessId,
            'OSS_Config.service': 'Minio'
        }
