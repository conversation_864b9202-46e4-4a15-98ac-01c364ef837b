from loguru import logger

from dmplib.nacos_utils.middleware_parser.base_parser import MiddlewareBaseParser
from dmplib.nacos_utils.enums import ResourceUsedEnum, ResourceInstanceEnum, DbBusinessEnum, ResourceCode
from dmplib.utils.model import BaseModel
from dmplib.utils.errors import UserError


class DBUsedConfigModel(BaseModel):
    __slots__ = ('dbMasterInstanceId', 'dbSlaveInstanceId', 'code', 'dbName', 'dbUser', 'tenantCode', 'category')

    def __init__(self, **kwargs):
        self.dbMasterInstanceId = kwargs.get('dbMasterInstanceId')
        self.dbSlaveInstanceId = kwargs.get('dbSlaveInstanceId')
        self.code = kwargs.get('code')
        self.dbName = kwargs.get('dbName')
        self.dbUser = kwargs.get('dbUser')
        self.tenantCode = kwargs.get('tenantCode')
        self.category = kwargs.get('category')
        super().__init__(**kwargs)


class DBInstanceModel(BaseModel):
    __slots__ = ('id', 'name', 'host', 'username', 'password', 'type', 'port', 'dbUsers')

    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.name = kwargs.get('name')
        self.host = kwargs.get('host')
        self.username = kwargs.get('username')
        self.password = kwargs.get('password')
        self.type = kwargs.get('type')
        self.port = kwargs.get('port')
        self.dbUsers = kwargs.get('dbUsers')
        super().__init__(**kwargs)


class DBInfoModel(BaseModel):
    __slots__ = ('host', 'password', 'code', 'database', 'port', 'db_type', 'username')

    def __init__(self, **kwargs):
        self.host = kwargs.get('host')
        self.password = kwargs.get('password')
        self.code = kwargs.get('code')
        self.database = kwargs.get('database')
        self.port = kwargs.get('port')
        self.db_type = kwargs.get('db_type')
        self.username = kwargs.get('username')
        super().__init__(**kwargs)


class DBParser(MiddlewareBaseParser):

    def __init__(self, config_data):
        super().__init__(config_data)

    def __parse(self, db_business_enum, resource_code=None, tenant_code=None):
        # 1. 获取资源配置
        used_config = self.get_used_config(ResourceUsedEnum.DATASOURCE.value, resource_code=resource_code, db_business_enum=db_business_enum, tenant_code=tenant_code)
        if not used_config:
            raise UserError(message=f'未获取到租户资源信息, resource_used: {ResourceUsedEnum.DATASOURCE.value}, resource_code: {resource_code}, db_business_enum: {db_business_enum}, tenant_code: {tenant_code}')
        used_config_model = DBUsedConfigModel(**used_config)
        # 2. 根据资源的实例ID获取实例配置
        instance_config = self.get_instance_config(ResourceInstanceEnum.DATASOURCE.value, used_config_model.dbMasterInstanceId)
        if not instance_config:
            raise UserError(message=f'未获取到租户实例配置信息, resource_instance: {ResourceInstanceEnum.DATASOURCE.value}, instance_id: {used_config_model.dbMasterInstanceId}')
        instance_config_model = DBInstanceModel(**instance_config)
        password = instance_config_model.password
        username = instance_config_model.username
        for item in instance_config_model.dbUsers:
            if item.get('username') == used_config_model.dbUser:
                password = item.get('password')
                username = used_config_model.dbUser
        return DBInfoModel(
            host=instance_config_model.host,
            password=password,
            database=used_config_model.dbName,
            port=instance_config_model.port,
            db_type=instance_config_model.type.upper(),
            username=username
        )

    def parse(self):
        config_db_info = self.__parse(DbBusinessEnum.CONFIG.value, resource_code=ResourceCode.DP.value)
        if config_db_info:
            del self.config_data[ResourceUsedEnum.DATASOURCE.value]
        return {
            'DB.host': config_db_info.host,
            'DB.password': config_db_info.password,
            'DB.database': config_db_info.database,
            'DB.port': config_db_info.port,
            'DB.db_type': config_db_info.db_type.upper(),
            'DB.username': config_db_info.username,
            'DB.user': config_db_info.username
        }

    def tenant_clean_db_parse(self, tenant_code):
        clean_db_info = self.__parse(DbBusinessEnum.TENANT.value, tenant_code=tenant_code, resource_code=ResourceCode.DP_DATACENTER.value)
        return clean_db_info

    def tenant_db_parse(self, tenant_code):
        db_info = self.__parse(DbBusinessEnum.TENANT.value, tenant_code=tenant_code, resource_code=ResourceCode.DP.value)
        return db_info
