"""
定义和初始化监控的对象, 按规范命名
"""
import os

from prometheus_client import Histogram, Counter, Gauge

_INF = float("inf")

ENV_CODE_LABEL = 'env_code'


def is_enabled():
    enabled = os.environ.get('METRICS_ENABLE', 'False')
    return str(enabled).lower() == 'true'


# Create a metric to track time spent and requests made.
prom_request_time = Histogram(
    'http_request_duration_seconds',
    'Request duration',
    ['env_code', 'http_status', 'path'],
    buckets=tuple([0.0001, 0.001, 0.01, 0.1, 1, _INF]),
)
prom_request_counter = Counter('http_requests_total', 'Total request count', [ENV_CODE_LABEL, 'http_status', 'path'])
prom_requests_inprogress = Gauge(
    "http_requests_inprogress", "Requests in progress by method and path", [ENV_CODE_LABEL, 'path']
)

# 监控调用阿里云vpc api时长, 弃用
prom_lic_aliyun_vpc = Gauge("lic_vpc_request_duration", "Request duration for aliyun vpc api", [ENV_CODE_LABEL])

# 监控license_server接口请求时长
prom_lic_request = Gauge("lic_server_request_duration", "Request duration for license api", [ENV_CODE_LABEL])

# 监控调用阿里云vpc api时长, 弃用
prom_lic_aliyun_vpc_his = Histogram(
    'lic_vpc_request_duration_sec', 'Request duration for aliyun vpc api', [ENV_CODE_LABEL]
)

# 监控license_server接口请求时长
prom_lic_server_his = Histogram('lic_server_request_duration_sec', 'Request duration for license api', [ENV_CODE_LABEL])
