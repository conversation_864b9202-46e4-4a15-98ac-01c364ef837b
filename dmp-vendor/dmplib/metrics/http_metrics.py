"""
https://github.com/prometheus/client_python#multiprocess-mode-gunicorn

gunicorn app:__hug_wsgi__  --env prometheus_multiproc_dir=prom-metrics-dir  --config=gunicorn.py --timeout=120 \
--bind=0.0.0.0:9001 -w 4 --log-level ERROR

"""

from prometheus_client import PlatformCollector, multiprocess, generate_latest, CollectorRegistry
from prometheus_client.multiprocess import MultiProcessCollector
from . import definition

REGISTRY = CollectorRegistry(auto_describe=False)
multiprocess.MultiProcessCollector(REGISTRY)


class ExternalMetrics:
    def __init__(self, registry=None):
        if registry is not None:
            registry.register(self)

    def collect(self):  # pylint: disable=no-self-use
        return []


class HttpMetrics:
    def __init__(self, env_code=None):
        self.requests_inprogress = None
        self.request_path = ''
        self.env_code = env_code or 'dev'
        self.enabled = definition.is_enabled()

    @staticmethod
    def get_request_path(request):
        return request.path.replace('//', '/')

    def before_request(self, request):
        if not self.enabled:
            return
        self.request_path = self.get_request_path(request)
        self.requests_inprogress = definition.prom_requests_inprogress.labels(self.env_code, self.request_path)
        self.requests_inprogress.inc()

    def before_response(self, request, response, request_latency):
        if not self.enabled:
            return
        self.requests_inprogress.dec()
        if request.method.upper() == 'OPTIONS':
            return
        definition.prom_request_time.labels(self.env_code, int(response.status[:3]), self.request_path).observe(
            request_latency
        )
        definition.prom_request_counter.labels(self.env_code, int(response.status[:3]), self.request_path).inc()


def get_metrics_data():
    registry = CollectorRegistry()
    MultiProcessCollector(registry)
    PlatformCollector(registry)
    ExternalMetrics(registry)

    return generate_latest(registry)
