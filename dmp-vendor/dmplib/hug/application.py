# -*- coding: utf-8 -*-
"""
    application
"""
# pylint:disable=no-value-for-parameter
import logging
import base64
import json
import traceback

import falcon
import hug
from hug.authentication import authenticator
import jwt

from . import middlewares
from .error_handlers import (
    user_error_handle,
    unauthorized_handle,
    friendly_error_handle,
    unknown_error_handle,
    _http_status_handler,
    user_error_with_extra_info_handle, dataset_query_error_handle,
)
from .globals import g
from .middlewares import MultipartMiddleware
from .lic import Licenser
from ..conf_constants import LOG_SLOW_LOG_SECONDS, ENABLE_PROFILING
from ..umbrella.middleware import LicenseMiddleware
from ..umbrella.core.tools import dispatch_old_env
from .. import config
from ..utils.errors import UserError, FriendlyError, UserErrorWithExtraInfo, DatasetQueryError
from ..utils.jwt_login import LoginToken

logger = logging.getLogger(__name__)


def verify_token(token):
    return LoginToken().verify_token(token, auto_logout=True)


def auth_cookies_replacement(request):
    # 新集成方案的cookies处理
    # 优先规则是如果cookies存在，取cookies，不存在取token
    token = request.cookies.get('token')
    if token:
        return

    dmp_auth_token = request.headers.get("DMP-AUTH-TOKEN", "")
    if not dmp_auth_token or dmp_auth_token == 'null':
        # headers中也没有token，不处理替换
        return

    # 替换cookies
    try:
        cookies_from_token = json.loads(base64.b64decode(dmp_auth_token).decode()) # type: dict
        for key, val in cookies_from_token.items():
            request.cookies[key] = val
        logger.info(f'成功替换cookies: {cookies_from_token}, dmp_auth_token: {dmp_auth_token}')
    except:
        logger.error(f'错误的token: {dmp_auth_token}, 处理token替换cookies发生错误: {traceback.format_exc()}')


@authenticator
def _token_verify_handle(request, _response, verify_user, **_kwargs):
    """Token verification
    Checks for the Authorization header and verifies using the verify_user function
    """
    auth_cookies_replacement(request)
    cookie = request.cookies
    g.cookie = cookie
    g.group_ids = []
    g.group_id = ''
    token = request.cookies.get('token')
    if not token:
        token = request.get_header('Authorization')
        token = token.split(' ')[-1] if token else ''
    if token:
        verified_token = verify_user(token)
        if verified_token:
            request.context['jwt_data'] = verified_token
            g.userid = verified_token.get('id')
            g.account = verified_token.get('account')
            if verified_token.get('code'):
                g.code = verified_token.get('code')
            if verified_token.get('group_id'):
                g.group_id = verified_token.get('group_id')
                g.group_ids = [g.group_id]
            if verified_token.get('group_ids'):
                g.group_ids = verified_token.get('group_ids')
            # 兼容自助报表第三方登录没有dmp用户的情况
            if verified_token.get('customize_roles'):
                customize_roles = verified_token.get('customize_roles')
                setattr(g, 'customize_roles', customize_roles)
            if verified_token.get('external_user_id'):
                external_user_id = verified_token.get('external_user_id')
                setattr(g, 'external_user_id', external_user_id)
            is_developer = verified_token.get('is_developer') or int(verified_token.get('source') == 'DEVELOPER') or 0
            setattr(g, 'is_developer', is_developer)

            from dmplib.locale import set_language

            set_language()

            return verified_token

        else:
            return False
    return False


def not_found(_req, resp):
    resp.status = hug.falcon.HTTP_404
    resp.body = hug.falcon.HTTP_404
    resp.content_type = 'text/plain'


class Singleton(type):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super(Singleton, cls).__call__(*args, **kwargs)
        return cls._instances[cls]


class Application(metaclass=Singleton):
    """
    封装框架原始的Http对象,Singleton
    """

    def __init__(self, hug_http):
        self.http = hug_http
        self.secret_key = config.get('JWT.secret')
        self.slow_log = LOG_SLOW_LOG_SECONDS
        # 获取取样数, 默认不开启
        self.profile_sample = int(config.get('Log.profile_sample', 0))
        if self.secret_key is None or self.secret_key == '':
            raise ValueError('secret_key is null')

        self._init_middleware()

    def _init_middleware(self):

        self.http.add_middleware(middlewares.AttachContext(self.slow_log, self.profile_sample))
        self.http.add_middleware(middlewares.MetricsHandler())
        self.http.add_middleware(middlewares.CookieTokenMiddlewareRouter())
        self.http.add_middleware(middlewares.CrossDomain())
        self.http.add_middleware(MultipartMiddleware())
        if ENABLE_PROFILING in ('True', 'true', '1', 1):
            self.http.add_middleware(middlewares.TimingMiddleware())
            self.http.add_middleware(middlewares.PyInstrumentMiddleware())

    def add_license_middleware(self):
        if not dispatch_old_env():
            self.http.add_middleware(LicenseMiddleware())
        else:
            self.http.add_middleware(Licenser())


class APIWrapper:
    """
    Application
    """

    __slots__ = ('name', 'secret_key', 'slow_log', 'api', 'http', '_route', '_admin_route', 'app')

    def __init__(self, name):
        """
        construct
        :param name:  app name
        """
        self.name = __name__ if name is None else name

        self.api = hug.API(name)
        hug.exception(api=self.api)(unknown_error_handle)
        self.app = Application(self.api.http)
        self.http = self.api.http
        self.secret_key = self.app.secret_key
        self.slow_log = self.app.slow_log
        self._route = hug.http(api=self.api)
        self._admin_route = hug.http(api=self.api, requires=_token_verify_handle(verify_user=verify_token))
        self._init_handlers()

        self.http.base_url = '/api'

    def _init_handlers(self):
        self.http.set_not_found_handler(not_found)
        self.http.add_exception_handler(falcon.HTTPStatus, _http_status_handler)
        self.http.add_exception_handler(falcon.errors.HTTPUnauthorized, unauthorized_handle)
        self.http.add_exception_handler(DatasetQueryError, dataset_query_error_handle)
        self.http.add_exception_handler(UserError, user_error_handle)
        self.http.add_exception_handler(FriendlyError, friendly_error_handle)
        self.http.add_exception_handler(UserErrorWithExtraInfo, user_error_with_extra_info_handle)

    @property
    def route(self):
        return self._route

    @property
    def admin_route(self):
        return self._admin_route


@authenticator
def _openapi_auth_verify(request, _response, verify_user, **_kwargs):
    """

    :param request:
    :param response:
    :return:
    """
    # request.headers.get('X-CONSUMER-CUSTOM-ID')
    # account

    token = request.get_header('Authorization')
    if token:
        token = token.split(' ')[-1]
        verified_token = verify_user(token)
        if verified_token:
            g.code = verified_token.get('tenant_code')
            g.account = verified_token.get('appid')
            return verified_token
    # 兼容kong网关的监权模式
    project_code = request.headers.get('X-CONSUMER-CUSTOM-ID')
    # 粗略校验code格式
    if not project_code or len(project_code) > 255:
        return False
    g.code = project_code
    g.account = 'openapi'
    return True


def jwt_verify(token):
    try:
        openapi_secret = config.get('Open.jwt_secret')
        return jwt.decode(token, openapi_secret, algorithms=["HS256"])
    except jwt.DecodeError:
        return False


class OpenAPIWrapper(APIWrapper):
    __slots__ = ['_route', '_open_route']

    def __init__(self, name):
        super().__init__(name)
        self._route = None
        self._open_route = None
        self.api.http.base_url = '/openapi'

    @property
    def open_route(self):
        if not self._open_route:
            self._open_route = hug.http(api=self.api, requires=_openapi_auth_verify(jwt_verify))
        return self._open_route