import datetime
import json
import threading
import time
import os
import builtins
import logging
from collections import OrderedDict
from random import choice
from string import ascii_uppercase
import hug
import requests
import backoff

from .. import config, constants
from ..metrics.definition import prom_lic_aliyun_vpc_his, prom_lic_server_his
from ..utils import dog

logger = logging.getLogger(__name__)
license_errors = {500: '非法数据', 501: '授权失败', 502: '无效签名', 503: '服务异常', 504: '非法数据'}
product_map = {'可视化': 6601, '平台': 6602, 'default': 6602}


class VPCRequestException(Exception):
    def __init__(self, message):
        self.message = message
        super().__init__(message)

    def __str__(self):
        return self.message


class VPCRequestTimeout(Exception):
    # requests.exceptions.ReadTimeout
    pass


class Licenser:
    """
    license
    """

    def __init__(self):
        self._env = config.get('App.runtime')
        is_test_env = self._env in ('test', 'dev', 'unitest')
        self._license_server_url = constants.LICENSE_SERVER
        self._tmp_verify_result = '/tmp/%s_%s_dmp_lic.json' % (os.getpid(), threading.current_thread().ident)
        self._pub_key = dog.license_pub_key_test if is_test_env else dog.license_pub_key

    @staticmethod
    def _json_loads(encoding_data):
        return json.loads(encoding_data, object_pairs_hook=OrderedDict)

    def _load_resp_from_tmp(self):
        try:
            with open(self._tmp_verify_result, 'rb') as f:
                return self._json_loads(f.read().decode('utf-8'))
        except:
            return None

    def _write_resp_tmp(self, resp_dict):
        try:
            with open(self._tmp_verify_result, 'wb') as f:
                f.write(json.dumps(resp_dict, separators=(',', ':')).encode('utf-8'))
        except:
            pass

    @staticmethod
    def _get_product_code():
        """
        获取产品类型
        :return:
        """
        _type = config.get('Product.product_type', 'default')
        return product_map.get(_type)

    @staticmethod
    def on_backoff(details):
        logger.warning(
            "Backing off {wait:0.1f} seconds afters {tries} tries "
            "calling function {target} with args {args} and kwargs "
            "{kwargs}".format(**details)
        )

    @staticmethod
    def _get_server_fingerprint():
        # for test
        if config.get('App.runtime') in ('test', 'dev', 'unitest'):
            return 'ecs01', 'vpc01'

        now = datetime.datetime.now()

        @backoff.on_exception(
            backoff.expo,
            (requests.exceptions.RequestException),
            max_tries=8,
            max_time=60,
            logger=logger,
            on_backoff=Licenser.on_backoff,
        )
        def _do_request():
            r_ecs = requests.get('%s/instance-id' % constants.ALIYUN_ECS_API_URL, timeout=2)
            r_vpc = requests.get('%s/vpc-id' % constants.ALIYUN_ECS_API_URL, timeout=2)
            return r_ecs, r_vpc

        try:
            r_ecs, r_vpc = _do_request()
            prom_lic_aliyun_vpc_his.labels(config.get_env_code()).observe(
                (datetime.datetime.now() - now).total_seconds()
            )
            return r_ecs.text, r_vpc.text
        except (
            requests.exceptions.ReadTimeout,
            requests.exceptions.ConnectionError,
            requests.exceptions.ConnectTimeout,
        ):
            raise VPCRequestTimeout()
        except Exception as e:
            raise VPCRequestException(str(e))

    def _get_local_license_data(self):
        try:
            with open(self._tmp_verify_result, 'rb') as f:
                self.is_from_local = True
                return self._json_loads(f.read().decode('utf-8'))
        except:
            return None

    def _get_server_license_data(self, cst_info):
        encrypt_str = dog.rsa_encrypt(
            json.dumps(cst_info, separators=(',', ':')).encode('utf-8'), dog.license_pub_key.encode('utf-8')
        )

        lic_api_start = datetime.datetime.now()
        try:
            r_verify = requests.post(self._license_server_url, {'data': encrypt_str}, timeout=15)
            prom_lic_server_his.labels(config.get_env_code()).observe(
                (datetime.datetime.now() - lic_api_start).total_seconds()
            )
            if r_verify.status_code == 200:
                return self._json_loads(r_verify.text)
            else:
                return None
        except Exception as e:  # pylint: disable=broad-except
            logger.exception(e)
            return None

    def _verify_license_data(self, license_data, expect_nonce):
        if not license_data:
            code = 503
            return code, license_errors[code]

        meta_data = license_data['meta_data']
        signature = license_data['signature']
        code = meta_data['code']
        msg = meta_data['message']
        timestamp = meta_data['timestamp']
        nonce = meta_data['nonce']

        if isinstance(code, str):
            code = int(code)

        if code is None or msg is None or timestamp is None:
            code = 500
            msg = license_errors[code]
            return code, msg

        # 一天内有效
        if int(time.time()) - int(timestamp) > 3600 * 24:
            code = 501
            msg = license_errors[code]
            return code, msg

        if expect_nonce is not None and nonce != expect_nonce:
            code = 504
            msg = '%s. %s vs %s' % (license_errors[code], nonce, expect_nonce)
            return code, msg

        if code > 0:
            return code, msg

        # 验证签名
        rt = dog.verify_sign(json.dumps(meta_data, separators=(',', ':')), signature, self._pub_key.encode('utf-8'))
        if not rt:
            code = 502
            msg = license_errors[code]

        return code, msg

    def process_request(self, req, _):
        _check_duration_seconds = 3600
        need_check_license = False
        if not hasattr(builtins, 'license_status'):
            license_status = req.context.get('license_status')
            builtins.license_status = license_status or dict(code=0, message='', license_update_time=None)

        # 如果之前已经判定无效license, 则直接返回无效状态
        if builtins.license_status['code'] > 0:
            return

        now = datetime.datetime.now()

        # 考虑性能问题, 一定时间内内不验证
        last_time = builtins.license_status['license_update_time']
        if last_time is not None:
            _diff_seconds = (now - last_time).total_seconds()
            if _diff_seconds > _check_duration_seconds or _diff_seconds < 0:
                need_check_license = True
        else:
            need_check_license = True

        if not need_check_license:
            return
        builtins.license_status['license_update_time'] = now

        try:
            ecs_id, vpc_id = self._get_server_fingerprint()
        except (VPCRequestException, VPCRequestTimeout) as e:
            if not isinstance(e, VPCRequestTimeout):
                logger.exception(e)
            license_data = self._load_resp_from_tmp()
            # 当读取本地缓存文件时, 不验证随机数
            code, errmsg = self._verify_license_data(license_data, None)
            if code > 0:
                req.context['license_status'] = {'message': errmsg, 'code': code}
                builtins.license_status['code'] = code
                builtins.license_status['message'] = errmsg
            return

        product_code = self._get_product_code()
        expect_nonce = ''.join(choice(ascii_uppercase) for _ in range(12))
        cst_info = {
            "app": config.get('App.name') or 'dmp',
            "ecs_id": ecs_id,
            "vpc_id": vpc_id,
            "product": {"code": product_code, "functions": []},
            "nonce": expect_nonce,
        }

        license_data = self._get_server_license_data(cst_info)
        if license_data:
            self._write_resp_tmp(license_data)
            code, errmsg = self._verify_license_data(license_data, expect_nonce)
        else:
            license_data = self._get_local_license_data()
            code, errmsg = self._verify_license_data(license_data, None)

        if code > 0:
            req.context['license_status'] = {'message': errmsg, 'code': code}
            builtins.license_status['code'] = code
            builtins.license_status['message'] = errmsg

    # pylint: disable=unused-argument
    def process_response(self, req, resp, resource, process_response=True):
        if hasattr(builtins, 'license_status'):
            license_status = builtins.license_status
        if license_status and license_status['code'] > 0:
            resp.content_type = 'text/plain'
            resp.body = '%s(%d)' % (license_status['message'], license_status['code'])
            resp.status = hug.falcon.HTTP_403
