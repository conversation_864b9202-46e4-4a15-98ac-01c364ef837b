# -*- coding: utf-8 -*-
"""
    for description
"""
# pylint:disable=unused-argument
import logging

import hug

from .globals import g
from ..utils.errors import UserError, FriendlyError, DatasetQueryError

logger = logging.getLogger(__name__)


def log_exception(request, exception: Exception):
    ctx_g = request.context.get('g')
    account = '' if not ctx_g else getattr(ctx_g, 'account', '')
    project_code = '' if not ctx_g else getattr(ctx_g, 'code', '')
    logger.error(
        '',
        exc_info=True,
        extra={
            'project_code': project_code,
            'user': {'account': account},
            'request': {
                'user_agent': request.user_agent,
                'remote_addr': request.remote_addr,
                'params': request.params,
                'url': request.url,
            },
        },
    )


@hug.exception(DatasetQueryError)
def dataset_query_error_handle(request, response, exception, **kwargs):
    response.status = hug.falcon.HTTP_200
    data = dict(result=False, msg=exception.message)
    if hasattr(exception, 'code'):
        data['code'] = exception.code
    if hasattr(g, "profiling"):
        data["profiling"] = {
            "sqls": getattr(g, "sqls") if hasattr(g, "sqls") else "",
            "logs": getattr(g, "logs") if hasattr(g, "logs") else "",
        }
    response.body = hug.output_format.json(data)


@hug.exception(UserError)
def user_error_handle(request, response, exception, **kwargs):
    response.status = hug.falcon.HTTP_200
    data = dict(result=False, msg=exception.message)
    if hasattr(exception, 'code'):
        data['code'] = exception.code
    if hasattr(g, "profiling"):
        data["profiling"] = {
            "sqls": getattr(g, "sqls") if hasattr(g, "sqls") else "",
            "logs": getattr(g, "logs") if hasattr(g, "logs") else "",
        }
    response.body = hug.output_format.json(data)


@hug.exception(FriendlyError)
def friendly_error_handle(request, response, exception, **kwargs):
    log_exception(request, exception)
    response.status = hug.falcon.HTTP_200
    data = dict(result=True, msg=exception.message)
    if hasattr(exception, 'code'):
        data['code'] = exception.code
    if hasattr(g, "profiling"):
        data["profiling"] = {
            "sqls": getattr(g, "sqls") if hasattr(g, "sqls") else "",
            "logs": getattr(g, "logs") if hasattr(g, "logs") else "",
        }
    response.body = hug.output_format.json(data)


@hug.exception(UserError)
def user_error_with_extra_info_handle(request, response, exception, **kwargs):
    response.status = hug.falcon.HTTP_200
    data = dict(result=False, msg=exception.message)
    if hasattr(exception, 'code'):
        data['code'] = exception.code
    if hasattr(exception, 'extra_info'):
        data['extra_info'] = exception.extra_info
    if hasattr(g, "profiling"):
        data["profiling"] = {
            "sqls": getattr(g, "sqls") if hasattr(g, "sqls") else "",
            "logs": getattr(g, "logs") if hasattr(g, "logs") else "",
        }
    response.body = hug.output_format.json(data)


@hug.exception(Exception)
def unknown_error_handle(request, response, exception, **kwargs):
    log_exception(request, exception)
    response.status = hug.falcon.HTTP_500
    response.content_type = 'text/plain'
    # 调试模式下，仍需返回日志和sql
    if hasattr(g, "profiling"):
        http_status = int(hug.falcon.HTTP_500.split(" ")[0])
        data = dict(result=False, msg=str(exception), code=http_status)
        data["profiling"] = {
            "sqls": getattr(g, "sqls") if hasattr(g, "sqls") else "",
            "logs": getattr(g, "logs") if hasattr(g, "logs") else "",
        }
        response.body = hug.output_format.json(data)
    else:
        response.content_type = 'text/plain'
        response.body = hug.falcon.HTTP_INTERNAL_SERVER_ERROR
    return hug.falcon.HTTP_500


def _http_status_handler(request, response, exception, **kwargs):
    if exception.headers is not None:
        response.set_headers(exception.headers)
    response.body = exception.body
    response.status = exception.status


@hug.exception(hug.falcon.errors.HTTPUnauthorized)
def unauthorized_handle(request, response, exception, **kwargs):
    response.status = exception.status
    response.content_type = 'text/plain'
    response.body = exception.title
    response.set_header('Access-Control-Expose-Headers', 'Authorization-Code-Url')
    response.set_header('Authorization-Code-Url', _get_authorization_url())


def _get_authorization_url():
    auth_url = ''
    try:
        from dmplib.components import auth_util
        auth_url = auth_util.get_authorization_url()
    except Exception as e:
        logger.error(f'获取工作台登录服务地址异常:{str(e)}')
    return auth_url