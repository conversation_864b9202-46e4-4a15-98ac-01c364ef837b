"""
管理db上下文缓存
"""


class Context:
    def __init__(self):
        self.stash_data = {}

    def inject(self, http_context):
        setattr(http_context, self.__class__.__name__, self)

    @classmethod
    def instance(cls, http_context):
        """
        从上下文中获取实例
        :param cls:
        :param http_context:
        :return: DBContext
        """

        return getattr(http_context, cls.__name__, None)


class DBContext(Context):
    """
    数据库连接对象
    """

    default_project = '__config__'
    default_dbname = '__t__'

    def __init__(self):
        super().__init__()

    def get_conn(self, project, dbname):
        project = self.default_project if not project else project
        dbname = self.default_dbname if not dbname else dbname
        return self.stash_data.get('{}:{}'.format(project, dbname))

    def set_conn(self, project, dbname, conn):
        project = self.default_project if not project else project
        dbname = self.default_dbname if not dbname else dbname
        self.stash_data['{}:{}'.format(project, dbname)] = conn

    def close_all(self):
        for db in self.stash_data.values():
            if db and db.is_open():
                try:
                    db.end()
                except:
                    pass


class RedisContext(Context):
    def __init__(self, redis_conn):
        super().__init__()
        self.redis_conn = redis_conn
        self.stash_data['kv'] = {}
        self.stash_data['hash'] = {}

    def get(self, key):
        pass

    def hget(self, name, key):
        pass

    def hgetall(self, name):
        pass
