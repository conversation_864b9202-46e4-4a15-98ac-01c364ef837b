import datetime
from . import g


class Debug:
    def __init__(self, current_model=''):
        self.current_model = current_model

    def log(self, message, *args, **kwargs):
        """
        向g.logs中追加日志
        :param message:
        :return:
        """
        if not message:
            return
        # 开启调试模式，才记录日志
        if hasattr(g, "profiling") and hasattr(g, 'logs'):
            g.logs.append(
                {
                    "datetime": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f"),
                    "current_model": self.current_model,
                    "message": message.format(args, kwargs) if isinstance(message, str) else message,
                }
            )
