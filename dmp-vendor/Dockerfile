#FROM python:3.9.17-buster as builder
FROM docker-cdv5ju.swr-pro.myhuaweicloud.com/global/base/python:3.9.17-full as builder

ARG PLATFORM=x86

ENV DM_HOME="/home/<USER>/webapp/dmdbms"
ENV LD_LIBRARY_PATH="$DM_HOME/bin:${LD_LIBRARY_PATH}"
ENV PATH="$PATH:$DM_HOME/bin"

COPY requirements.txt ./

RUN pip install --upgrade pip setuptools -i https://mirrors.aliyun.com/pypi/simple/ && \
    pip install --no-cache-dir -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

RUN if [ "$PLATFORM" = "arm" ]; then \
        wget -P /dmp-agent https://mic-open.oss-cn-hangzhou.aliyuncs.com/ops/config-agent/v5/agent-arm64 && mv /dmp-agent/agent-arm64 /dmp-agent/agent && \
        wget -P /home/<USER>/webapp/dmdbms https://oss-cn-shenzhen.aliyuncs.com/dmp-test/tools/dm/arm64/dmdbms.zip && \
        cd /home/<USER>/webapp/dmdbms && unzip dmdbms.zip && rm -rf dmdbms.zip; \
    else \
        wget -P /dmp-agent -o agent http://mic-open.oss-cn-hangzhou.aliyuncs.com/ops/agent && \
        wget -P /home/<USER>/webapp/dmdbms https://oss-cn-shenzhen.aliyuncs.com/dmp-test/tools/dm/x86/dmdbms.zip && \
        cd /home/<USER>/webapp/dmdbms && unzip dmdbms.zip && rm -rf dmdbms.zip; \
    fi

COPY . /tmp/dmp-lib
RUN cd /tmp/dmp-lib && python setup.py install && \
    cd /home/<USER>/webapp/dmdbms/dmPython && python setup.py install && \
    cd /home/<USER>/webapp/dmdbms/sqlalchemy2.0.0 && python setup.py install && \
    rm -rdf /tmp/dmp-lib

#FROM python:3.9.17-slim-buster
FROM docker-cdv5ju.swr-pro.myhuaweicloud.com/global/base/python:3.9.17

ENV DM_HOME="/home/<USER>/webapp/dmdbms"
ENV LD_LIBRARY_PATH="$DM_HOME/bin:${LD_LIBRARY_PATH}"
ENV PATH="$PATH:$DM_HOME/bin"


COPY --from=builder /usr/local/lib/python3.9 /usr/local/lib/python3.9
COPY --from=builder /dmp-agent/agent /dmp-agent/agent
COPY --from=builder /usr/local/bin/gunicorn /usr/local/bin/gunicorn
COPY --from=builder /usr/local/bin/celery /usr/local/bin/celery
COPY --from=builder /home/<USER>/webapp/dmdbms /home/<USER>/webapp/dmdbms

ENV prometheus_multiproc_dir /tmp/prom-metrics-dir
COPY app.sh /tmp/app.sh
RUN pip install --upgrade --no-cache-dir pip setuptools -i https://mirrors.aliyun.com/pypi/simple/ && \
    echo "deb https://mirrors.aliyun.com/debian/ buster main non-free contrib" > /etc/apt/sources.list && \
    echo "deb-src https://mirrors.aliyun.com/debian/ buster main non-free contrib" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian-security buster/updates main" >> /etc/apt/sources.list && \
    echo "deb-src https://mirrors.aliyun.com/debian-security buster/updates main" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian/ buster-updates main non-free contrib" >> /etc/apt/sources.list && \
    echo "deb-src https://mirrors.aliyun.com/debian/ buster-updates main non-free contrib" >> /etc/apt/sources.list && \
    apt-get update && apt-get upgrade -y --allow-unauthenticated && \
    apt-get install -y --allow-unauthenticated --no-install-recommends libpq-dev default-mysql-client vim procps && apt-get upgrade debian-archive-keyring && \
    chmod +x /tmp/app.sh && \
    chmod +x /dmp-agent/agent && \
    chmod +x /home/<USER>/webapp/dmdbms/bin/dimp && chmod +x /home/<USER>/webapp/dmdbms/bin/dexp && \
    rm -rf /var/lib/apt/lists && \
    rm -rf /usr/local/lib/python3.9/site-packages/gevent/tests/server.key /usr/local/lib/python3.9/site-packages/gevent/tests/test_server.key /usr/local/lib/python3.9/site-packages/tornado/test/test.key

WORKDIR /home/<USER>/webapp

CMD ["/tmp/app.sh"]