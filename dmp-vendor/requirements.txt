aiosignal==1.3.1
alibabacloud-credentials==0.3.2
alibabacloud-dingtalk==2.0.68
alibabacloud-endpoint-util==0.0.3
alibabacloud-gateway-dingtalk==1.0.2
alibabacloud-gateway-spi==0.0.1
alibabacloud-openapi-util==0.2.2
alibabacloud-tea==0.3.5
alibabacloud-tea-openapi==0.3.8
alibabacloud-tea-util==0.3.11
alibabacloud-tea-xml==0.0.2
aliyun-python-sdk-core==2.14.0
aliyun-python-sdk-kms==2.16.2
aliyun-python-sdk-sts==3.1.2
aiohttp==3.9.0
amqp==5.2.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
async-timeout==4.0.3
attrs==23.1.0
backoff==2.2.1
bcrypt==4.1.1
billiard==4.2.0
celery==5.3.6
certifi==2023.11.17
cffi==1.16.0
chardet==5.2.0
charset-normalizer==3.3.2
click==8.1.7
click-didyoumean==0.3.0
click-plugins==1.1.1
click-repl==0.3.0
crcmod==1.7
crontab==1.0.1
cryptography==41.0.7
curlify==2.2.1
Cython==3.0.6
dateparser==1.2.0
esdk-obs-python==3.23.9.1
exceptiongroup==1.2.0
falcon==2.0.0
flashtext==2.7
frozenlist==1.4.0
future==0.18.3
greenlet==3.2.0; platform_machine == "arm64" or platform_machine == "aarch64"
gevent==25.4.2; platform_machine == "arm64" or platform_machine == "aarch64"
greenlet==3.0.2; platform_machine == "AMD64" or platform_machine == "x86_64"
gevent==23.9.1; platform_machine == "AMD64" or platform_machine == "x86_64"
gunicorn==21.2.0
hug==2.6.1
idna==3.6
iniconfig==2.0.0
jmespath==0.10.0
jsonschema==4.20.0
jsonschema-specifications==2023.11.2
kombu==5.3.4
loguru==0.7.2
marshmallow==3.20.1
minio==7.2.0
mo-dots==9.455.23316
mo-future==7.449.23304
mo-imports==7.449.23304
mo-parsing==8.465.23337
mo-sql-parsing==10.469.23343
mock==5.1.0
moment==0.12.1
multidict==6.0.4
numpy==1.24.4
oss2==2.18.3
packaging==23.2
pyodps==0.11.4.1
pandas==2.0.3
paramiko==3.4.0
pdfkit==1.0.0
pika==1.3.2
Pillow==10.1.0
pluggy==1.3.0
prometheus-client==0.19.0
prompt-toolkit==3.0.41
protobuf==3.20.3
psutil==5.9.6
psycopg2==2.9.9
pybase64==1.3.1
pycparser==2.21
#pycrypto==2.6.1
pycryptodome==3.10.1
PyHive==0.7.0
pyinstrument==4.6.1
PyJWT==2.8.0
pymssql==2.3.0
PyMySQL==1.1.0
PyNaCl==1.5.0
pyOpenSSL==23.3.0
pypinyin==0.50.0
pytest==7.4.3
python-binary-memcached==0.31.2
python-dateutil==2.8.2
python-dotenv==1.0.0
pytz==2023.3.post1
raven==6.10.0
redis==5.0.1
referencing==0.32.0
regex==2023.10.3
requests==2.31.0
rpds-py==0.13.2
rundeckrun==0.2.1
simplejson==3.19.2
six==1.16.0
sql_metadata==2.10.0
SQLAlchemy==2.0.23
sqlparse==0.4.4
sshtunnel==0.4.0
suds-py3==*******
tablib==3.5.0
timeout-decorator==0.5.0
times==0.7
tomli==2.0.1
tornado==6.4
types-python-dateutil==*********
typing_extensions==4.9.0
tzdata==2023.3
tzlocal==5.2
uhashring==2.3
ujson==5.9.0
urllib3==1.26.18
vine==5.1.0
wcwidth==0.2.12
webargs==8.3.0
xlrd==1.1.0
XlsxWriter==3.1.9
xlwt==1.3.0
xmltodict==0.13.0
yarl==1.9.4
htmlmin==0.1.12
git+https://starship-readonly:<EMAIL>/starship/nacos-sdk-python.git@main
jwcrypto==1.5.6