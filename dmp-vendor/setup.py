# pylint: skip-file
import os
from fnmatch import fnmatch
from pathlib import Path
from distutils.command.install import install
from shutil import copyfile
from distutils.core import setup
from distutils.extension import Extension
from setuptools import Extension
from Cython.Distutils import build_ext
from Cython.Build import cythonize

force_compile = True
root = os.path.abspath(os.path.dirname(__file__))
src_dir = os.path.join(root, 'dmplib')
build_ignored_files = ['.md', '.txt', '.log']

path_build = os.path.abspath(os.path.join(root, 'build'))
if not os.path.exists(src_dir):
    os.makedirs(src_dir)


def find_source_files():
    py_files = []
    for dirname, dirnames, filenames in os.walk(src_dir):
        for f in ['test', 'build', '.git']:
            if f in dirnames:
                dirnames.remove(f)

        for filename in filenames:
            f, extension = os.path.splitext(filename)
            if extension == '.py' and filename != '__init__.py' and filename != 'setup.py':
                fullpath = os.path.join(dirname, filename)
                py_files.append(fullpath)

    return py_files


def new_modules(sources):
    modules = []
    for fullpath in sources:
        relfile = os.path.relpath(fullpath, root)
        filename = os.path.splitext(relfile)[0]
        ext_name = filename.replace(os.path.sep, ".")
        extension = Extension(
            ext_name, sources=[fullpath], define_macros=[('PYREX_WITHOUT_ASSERTIONS', None)]  # ignore asserts in code
        )
        modules.append(extension)

    return modules


class MyInstall(install):
    # Calls the default run command, then deletes the build area
    # (equivalent to "setup clean --all").
    def run(self):
        super().run()


class MyBuildExt(build_ext):
    def run(self):
        super().run()
        build_dir = Path(self.build_lib)
        root_dir = Path(__file__).parent
        target_dir = build_dir if not self.inplace else root_dir
        for dirname, dirnames, filenames in os.walk(src_dir):
            for f in ['test', 'build', 'tests', '.git']:
                if f in dirnames:
                    dirnames.remove(f)

            for filename in filenames:
                fullpath = os.path.join(dirname, filename)
                relfile = os.path.relpath(fullpath, root)
                target_path = os.path.join(target_dir.absolute().__str__(), relfile)

                if filename == '__init__.py':
                    copyfile(fullpath, target_path)

                if fnmatch(filename, '*hasp*79674*'):
                    dir_name = os.path.dirname(target_path)
                    if not os.path.exists(dir_name):
                        os.makedirs(dir_name)
                    copyfile(fullpath, target_path)


cmdclass = {'build_ext': MyBuildExt, 'install': MyInstall}
pyfiles = find_source_files()
ext_modules = new_modules(pyfiles)
setup(
    name='dmplib',
    version='3.0.0',
    description='dmp web engine',
    author='Mingyuanyun',
    url='https://mingyuanyun.com',
    requires=['requests', 'redis', 'pyopenssl', 'pycryptodome'],
    cmdclass=cmdclass,
    ext_modules=cythonize(ext_modules, compiler_directives={'language_level': "3"}),
)
