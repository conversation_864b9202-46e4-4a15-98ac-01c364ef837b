#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import base64
import gzip
import json
from loguru import logger
from base import repository
from base.repository import Pagination
from components.pular15_api import Pulsar15Api
from components.utils import debugger_and_logger, timed_lru_cache
from base.enums import ApplicationType
from dmplib.utils.strings import seq_id
from feed.services import msg_get_data_service
from open_api.services import pubserver_service

local_debugger = debugger_and_logger(__name__, prefix='[更新中心] ')

DASHBOARD_TYPE = 'dashboards'
LARGE_SCREEN_TYPE = 'large_screens'
ACTIVE_REPORT_TYPE = 'active_reports'
APPLICATION_TYPE = 'applications'
DATASET_TYPE = 'datasets'
INDEPENDENT_DATASET_RELS = 'independent_datasets_rels'
INDEPENDENT_DATASET = 'independent_datasets'
DATA_REPORTING_TYPE = 'data_reporting'
FEED_TYPE = 'feeds'

PARSED_REPORT_TYPE = {
    DASHBOARD_TYPE: '仪表板',
    LARGE_SCREEN_TYPE: '酷炫大屏',
    APPLICATION_TYPE: '数据门户',
    ACTIVE_REPORT_TYPE: '复杂报表',
    DATA_REPORTING_TYPE: '数据报告',
    DATASET_TYPE: '数据集',
    FEED_TYPE: '数据订阅',
}

DEFAULT_TAG_NAME = '其他'
DEFAULT_TAG_ID = '0'


@timed_lru_cache(seconds=1 * 60 * 60, maxsize=15)
def cache_data_decompress(data):
    try:
        b64_data = base64.b64decode(data)
        g_data = gzip.decompress(b64_data)
        return g_data.decode("utf-8")
    except Exception as e:
        print(f'解析数据集字段缓存失败： {str(e)}, data: {data}')
        return ''


class ImportParser():

    def __init__(self, task_id, only_shuxin15_datasource=True):
        self.task_id = task_id
        self.page_size = 10
        self.dataset_table_cache = {}
        self.dataset_dependency_cache = {}
        self.ref_dataset_ids = []
        self.app_code = ''
        self.file_type = 'INCR'
        self.result = {}
        self.record_ids = []
        self.only_shuxin15_datasource = only_shuxin15_datasource  #
        self.package_type = '产品更新包'

    # 获取文件中的报表，并返回关联的宽表信息
    def parse_report_with_table_model(self, type: str = None, file_type='INCR',
                                      process_independent_dataset_refs: bool = False):
        self.file_type = file_type
        # 先缓存数据集表名关系
        self._parse_all_dataset_table()
        if type:
            self._parse(type)
        else:
            self._parse(DASHBOARD_TYPE)
            self._parse(LARGE_SCREEN_TYPE)
            self._parse(ACTIVE_REPORT_TYPE)
            self._parse(APPLICATION_TYPE)
            self._parse(DATA_REPORTING_TYPE)
            self._parse(FEED_TYPE)
            self._parse(INDEPENDENT_DATASET)
        if process_independent_dataset_refs:
            # 是否尝试从全量包中查找当前文件里没有任何关联的数据集，如果有会从全量包中复制关联的报告的数据(rdc_file_content)
            self._parse(INDEPENDENT_DATASET_RELS)
        return self.result

    def parse_report_dependency_merge_child_file(self, report_id_list):
        report_dependency_list = self.parse_report_dependency(report_id_list).get('items', [])
        new_result = []
        for report_dependency in report_dependency_list:
            if report_dependency.get('application_type') not in [ApplicationType.ActiveReport.value,
                                                                 ApplicationType.Dashboard.value,
                                                                 ApplicationType.SimpleReport.value,
                                                                 ApplicationType.HD_Dashboard.value,
                                                                 ApplicationType.LargeScreen.value,
                                                                 ApplicationType.External_Dashboard.value,
                                                                 ApplicationType.DataReporting.value,
                                                                 ApplicationType.SelfService.value,
                                                                 ApplicationType.FineReport.value]:
                new_result.append(report_dependency)
            elif (report_dependency.get('application_type') not in [ApplicationType.SimpleReport.value,
                                                                    ApplicationType.ActiveReport.value]
                  and report_dependency.get('type') != 'CHILD_FILE') or (
                    report_dependency.get('application_type') in [ApplicationType.SimpleReport.value,
                                                                  ApplicationType.ActiveReport.value]
                    and not report_dependency.get('parent_id')):
                # 只加入父报表
                new_result.append(report_dependency)
        # 加载子报表依赖
        for report_dependency in new_result:
            children = [item for item in report_dependency_list if (item.get('parent_id')
                                                                    and item.get('parent_id') == report_dependency[
                                                                        'id'])]
            while len(children) > 0:
                self._merge_dependency(report_dependency, children)
                new_parent_id_list = [item['id'] for item in children]
                children = [item for item in report_dependency_list if (item.get('parent_id')
                                                                        and item.get(
                            'parent_id') in new_parent_id_list)]

        self.result['items'] = new_result
        return self.result

    @staticmethod
    def _merge_dependency(report, children):
        dependencies = []
        for child in children:
            dependencies.extend(child.get('dependencies', []))
        for dependency in dependencies:
            dependency_id = dependency.get('id')
            report_dependencies = report.get('dependencies')
            if not any(item for item in report_dependencies if item['id'] == dependency_id):
                report['dependencies'].append(dependency)

    def parse_report_dependency(self, report_id_list):
        self._parse_report_dependency(DASHBOARD_TYPE, report_id_list)
        self._parse_report_dependency(LARGE_SCREEN_TYPE, report_id_list)
        self._parse_report_dependency(ACTIVE_REPORT_TYPE, report_id_list)
        self._parse_report_dependency(APPLICATION_TYPE, report_id_list)
        self._parse_report_dependency(DATA_REPORTING_TYPE, report_id_list)
        self._parse_report_dependency(FEED_TYPE, report_id_list)
        self._parse_report_dependency(DATASET_TYPE, report_id_list)
        return self.result

    def parse_report_mapping(self, report_id_list):
        result = {
            "subject": {
                "from": [],
                "to": []
            },
            "dashboard_datasource": {
                "from": [],
                "to": []
            }
        }
        data = self._get_reports_with_children(None, report_id_list)
        dataset_id_list = []
        tag_relation = []
        for row in data:
            content_type = row['type']
            if content_type in [DASHBOARD_TYPE, LARGE_SCREEN_TYPE]:
                mapping = self._get_mapping_from_dashboard([row])
                dataset_id_list.extend(mapping.get('dataset_id_list'))
                tag_relation.extend(mapping.get('tag_relation'))
            elif content_type == APPLICATION_TYPE:
                mapping = self._get_mapping_from_application([row])
                tag_relation.extend(mapping.get('tag_relation'))
            elif content_type == ACTIVE_REPORT_TYPE:
                mapping = self._get_mapping_from_active_reports([row], report_id_list)
                dataset_id_list.extend(mapping.get('dataset_id_list'))
                tag_relation.extend(mapping.get('tag_relation'))
            elif content_type == DATA_REPORTING_TYPE:
                mapping = self._get_mapping_from_ppt([row])
                dataset_id_list.extend(mapping.get('dataset_id_list'))
                tag_relation.extend(mapping.get('tag_relation'))
        distinct_dataset_id_list = list(set(dataset_id_list))
        from_data_source_list = self._get_data_source_list(distinct_dataset_id_list)
        result['dashboard_datasource']['from'] = [{
            'id': data_source.get('id'),
            'code': data_source.get('code'),
            'name': data_source.get('name')
        } for data_source in from_data_source_list]
        result['dashboard_datasource']['to'] = [{
            'id': data_source.get('id'),
            'code': data_source.get('code'),
            'name': data_source.get('name')
        } for data_source in repository.get_list('dap_m_data_source', {})]
        tag_relation_id_list = set()
        for tag_relation_item in tag_relation:
            if tag_relation_item.get('tag_id') not in tag_relation_id_list:
                tag_relation_id_list.add(tag_relation_item.get('tag_id'))
                result['subject']['from'].append({
                    'subject_id': tag_relation_item.get('tag_id'),
                    'subject_name': tag_relation_item.get('tag_name'),
                })
        result['subject']['to'] = self._get_subject_list()
        return result

    def _parse_report_dependency(self, type, report_id_list):
        data = self._get_reports_with_children(type, report_id_list)
        parsed = []
        if type in [DASHBOARD_TYPE, LARGE_SCREEN_TYPE]:
            parsed = self._parse_dashboards_dependency(data, type)
        elif type == APPLICATION_TYPE:
            parsed = self._parse_application_dependency(data)
        elif type == FEED_TYPE:
            parsed = self._parse_feed_dependency(data, report_id_list)
        elif type == ACTIVE_REPORT_TYPE:
            parsed = self._parse_active_reports_dependency(data, report_id_list)
        elif type == DATA_REPORTING_TYPE:
            parsed = self._parse_ppt_dependency(data, type)
        elif type == DATASET_TYPE:
            parsed = self._parse_dataset_dependency(data)
        if 'items' not in self.result:
            self.result['items'] = []
        self.result['items'].extend(parsed)

    def _get_reports_with_children(self, type, report_id_list):
        if type == FEED_TYPE:
            sql = f"""
                                select * from dap_bi_rdc_file_content where task_id = %(task_id)s 
                                and type {'=' if type else 'in'} %(type)s and file_type = %(file_type)s 
                                order by created_on desc
                            """
            datas = repository.get_data_by_sql(sql,
                                               {'task_id': self.task_id, 'file_type': self.file_type,
                                                'type': type if type else [FEED_TYPE]},
                                               from_config_db=True) or []
            result = []
            for data in datas:
                if data.get('data_id') == 'dashboard_email_subscribe':
                    return data
            return result
        else:
            sql = f"""
                                select * from dap_bi_rdc_file_content where task_id = %(task_id)s 
                                and type {'=' if type else 'in'} %(type)s and file_type = %(file_type)s 
                                and (data_id in %(report_id_list)s or type = 'active_reports')
                                order by created_on desc
                            """
        result = repository.get_data_by_sql(sql,
                                            {'task_id': self.task_id, 'file_type': self.file_type,
                                             'type': type if type else [DASHBOARD_TYPE, LARGE_SCREEN_TYPE,
                                                                        ACTIVE_REPORT_TYPE,
                                                                        APPLICATION_TYPE, DATASET_TYPE],
                                             'report_id_list': report_id_list if (
                                                     report_id_list and len(report_id_list) > 0) else ['']},
                                            from_config_db=True)
        parent_id_list = [item['data_id'] for item in result]
        while len(parent_id_list) > 0:
            children = repository.get_data_by_sql(
                "select * from dap_bi_rdc_file_content where task_id = %(task_id)s and parent_id in %(parent_id)s",
                {'task_id': self.task_id, 'parent_id': parent_id_list}, from_config_db=True)
            parent_id_list = []
            if len(children) > 0:
                result.extend(children)
                parent_id_list = [item['data_id'] for item in children]
        return result

    def _parse(self, type):
        data = []
        if type != INDEPENDENT_DATASET_RELS and type != INDEPENDENT_DATASET:
            sql = """
                select * from dap_bi_rdc_file_content where task_id = %(task_id)s and file_type = %(file_type)s and type = %(type)s order by created_on desc
            """
            data = repository.get_data_by_sql(sql, {'task_id': self.task_id, 'file_type': self.file_type, 'type': type}, from_config_db=True)
            if not data:
                return
            self.app_code = data[0].get('app_code')
            sql = """
                select * from dap_bi_datacloud_public_import_dist where rdc_task_id = %(task_id)s
            """
            importData = repository.get_data_by_sql(sql, {'task_id': self.task_id}, from_config_db=True)
            if not importData:
                return
            if importData[0].get('package_type') == '定制包':
                self.package_type = '定制包'

        parsed = []
        if type in [DASHBOARD_TYPE, LARGE_SCREEN_TYPE]:
            parsed = self._parse_dashboards(data, type)
        elif type == APPLICATION_TYPE:
            parsed = self._parse_application(data)
        elif type == ACTIVE_REPORT_TYPE:
            parsed = self._parse_active_reports(data)
        elif type == DATA_REPORTING_TYPE:
            parsed = self._parse_data_reporting(data)
        elif type == INDEPENDENT_DATASET:
            parsed = self._process_independent_datasets()
        elif type == INDEPENDENT_DATASET_RELS:
            parsed = self._process_independent_dataset_rels()
        elif type == FEED_TYPE:
            parsed = self._parse_feed(data)
        else:
            return
        self._save_result(parsed)

    def _save_result(self, parsed_data):
        for item in parsed_data:
            subject_id = item.get('subject_id')
            subject_name = item.get('subject_name')
            subject_data = self.result.get(subject_id, {'subject_id': subject_id, 'subject_name': subject_name,
                                                        'dashboard': []})
            dashboards = subject_data.get('dashboard', [])
            dashboards.append(item.get('dashboard'))
            subject_data['dashboard'] = dashboards
            self.result[subject_id] = subject_data

    def _parse_independent_datasets(self):
        if not self.ref_dataset_ids:
            return []
        sql = """
                select data_id,`content` from dap_bi_rdc_file_content where task_id = %(task_id)s and file_type=%(file_type)s and type = %(type)s and data_id not in %(dataset_ids)s order by created_on desc
        """
        # 查询没有被引用的数据集
        no_rels = repository.get_data_by_sql(sql,
                                             {'task_id': self.task_id, 'file_type': self.file_type,
                                              'type': DATASET_TYPE, 'dataset_ids': self.ref_dataset_ids},
                                             from_config_db=True)
        return no_rels

    def _process_independent_datasets(self):
        no_rels = self._parse_independent_datasets()
        if not no_rels:
            return []
        res = []
        for r in no_rels:
            content = cache_data_decompress(r.get('content'))
            if not content:
                continue
            content = json.loads(content)
            subject_id, subject_name = self._parse_tag(content)
            dataset = content.get('dataset', {})
            model = {'subject_id': subject_id, 'subject_name': subject_name,
                     'dashboard': {'id': dataset.get('id'), 'name': dataset.get('name'), 'type': PARSED_REPORT_TYPE.get(DATASET_TYPE),
                                   'change_info': self._compare_dashboard(content), 'is_individuation': False,
                                   'models': [], 'datasets': [], 'status': '更新'}}
            res.append(model)
        return res

    def _process_independent_dataset_rels(self):
        no_rels = self._parse_independent_datasets()
        if not no_rels:
            return []
        no_rel_ids = [item.get('data_id') for item in no_rels]
        # 如果存在没有关联的数据集，从全量包中查找对应报表
        data = repository.get_data('dap_bi_rdc_file_content',
                                   {'app_code': self.app_code, 'file_type': 'FULL'},
                                   ['task_id'], from_config_db=True)
        if not data:
            return []
        # 全量包解析
        parser = ImportParser(data.get('task_id'))
        parsed = parser.parse_report_with_table_model(DASHBOARD_TYPE, file_type='FULL')
        if not parsed:
            return []

        res = []
        cached_tables = set([value for values in self.dataset_table_cache.values() for value in values])
        # 从全量包中找出引用当前数据集的报表，纳入当前文件内容中
        for k, v in parsed.items():
            if not v:
                continue
            dashboards = v.get('dashboard')
            if not dashboards:
                continue
            subject_id = v.get('subject_id')
            subject_name = v.get('subject_name')
            for item in dashboards:
                ref_dataset_ids = item.get('datasets', []) or []
                if not ref_dataset_ids:
                    continue
                ids = (set(ref_dataset_ids)).intersection(set(no_rel_ids))
                if not ids:
                    continue
                tables = item.get('models') or []
                # 删除当前增量包中不存在的表名
                item['models'] = list((set(tables)).intersection(cached_tables))
                model = {'subject_id': subject_id, 'subject_name': subject_name,
                         'dashboard': item}
                res.append(model)
                self._copy_data(item.get('record_id'))
        return res

    def _copy_data(self, record_id):
        # 复制数据
        if not record_id:
            return
        record = repository.get_data('dap_bi_rdc_file_content', {'id': record_id}, from_config_db=True)
        if not record:
            return
        record['id'] = seq_id()
        record['task_id'] = self.task_id
        record['created_by'] = 'copy'
        record['modified_by'] = 'copy'
        record['created_on'] = None
        record['modified_on'] = None
        record['file_type'] = 'INCR'
        repository.add_data('dap_bi_rdc_file_content', record, from_config_db=True)

    def _parse_data_reporting(self, data):
        res = []
        for item in data:
            content = item.get('content')
            content = cache_data_decompress(content)
            if not content:
                continue
            content = json.loads(content)
            dashboards = content.get('dashboard', [])
            if not dashboards:
                continue
            dashboard = dashboards[0]
            subject_id, subject_name = self._parse_tag(content)
            name = dashboard.get('name')
            id = dashboard.get('id')
            exists = repository.data_is_exists('dap_bi_dashboard', {'id': id})
            model = {'subject_id': subject_id, 'subject_name': subject_name,
                     'dashboard': {'id': id, 'name': name, 'type': PARSED_REPORT_TYPE.get(type),
                                   'change_info': self._compare_dashboard(content) if exists else '',
                                   'is_individuation': False,
                                   'models': [], 'datasets': [], 'record_id': item.get('id'),
                                   'status': '更新' if exists else '新增'}}
            res.append(model)
            data = repository.get_one('dap_bi_rdc_file_content',
                                      {'task_id': self.task_id, 'type': 'ppt', 'data_id': id},
                                      from_config_db=True)
            dataset_ids = []
            if data:
                dataset_ids = self._parse_ppt_dataset(data.get('content'))
            table_models = self._parse_dataset_table(dataset_ids)
            model.get('dashboard')['models'] = list(set(table_models))
            model.get('dashboard')['datasets'] = dataset_ids
        return res

    def _parse_ppt_dataset(self, content):
        dataset_ids = []
        content = cache_data_decompress(content)
        content = json.loads(content)
        shapes = content.get('reportShapeList') or []
        for shape in shapes:
            pr = shape.get('properties')
            if not pr:
                continue
            pr = json.loads(pr)
            chart = pr.get('chartQuery') or {}
            dataset_id = chart.get('dataSetId')
            if dataset_id:
                dataset_ids.append(dataset_id)
        filters = content.get('globalFilterDatasetList') or []
        for filter in filters:
            dataset_id = filter.get('datasetId')
            if dataset_id:
                dataset_ids.append(dataset_id)
        return dataset_ids

    def _parse_active_reports(self, data):
        res = []
        for item in data:
            content = item.get('content')
            content = cache_data_decompress(content)
            if not content:
                return None
            item = json.loads(content)
            reports = []
            self._find_report(item, reports)
            if not reports:
                return None
            for report in reports:
                # 统计报表子报表忽略
                if report.get('parent_id'):
                    continue
                subject_id, subject_name = self._parse_tag(report)
                exists = repository.data_is_exists('dap_bi_dashboard', {'id': report.get('id')})
                model = {'subject_id': subject_id, 'subject_name': subject_name,
                         'dashboard': {'id': report.get('id'), 'name': report.get('name'),
                                       'type': PARSED_REPORT_TYPE.get(ACTIVE_REPORT_TYPE),
                                       'change_info': self._compare_report(report) if exists else '',
                                       'is_individuation': False, 'status': '更新' if exists else '新增',
                                       'parent_id': report.get('parent_id'),
                                       'models': []}}
                res.append(model)
                dataset_ids = report.get('dataset_ids')
                table_models = self._parse_dataset_table(list(set(dataset_ids)))
                model.get('dashboard')['models'] = list(set(table_models))
        return res

    def _find_report(self, data, reports):
        for report in data:
            if report.get('type') == 'FILE':
                reports.append(report)
            sub = report.get('sub')
            if sub:
                self._find_report(sub, reports)

    def _parse_application(self, data):
        res = []
        for item in data:
            content = item.get('content')
            content = cache_data_decompress(content)
            if not content:
                continue
            content = json.loads(content)
            application = content.get('application', [])
            if not application:
                continue
            application = application[0]
            subject_id, subject_name = self._parse_tag(content)
            name = application.get('name')
            id = application.get('id')
            exists = repository.data_is_exists('dap_bi_application', {'id': id})
            model = {'subject_id': subject_id, 'subject_name': subject_name,
                     'dashboard': {'id': id, 'name': name, 'type': PARSED_REPORT_TYPE.get(APPLICATION_TYPE),
                                   'change_info': self._compare_application(content) if exists else '',
                                   'is_individuation': False, 'status': '更新' if exists else '新增',
                                   'models': []}}
            res.append(model)
        return res

    def _parse_feed_tag_relation(self, data):
        """
        解析简讯的tag标签信息
        """
        for item in data:
            if item.get('data_id', '') == 'tag_relation':
                try:
                    content = item.get('content')
                    content = cache_data_decompress(content)
                    return json.loads(content) or []
                except Exception as e:
                    logger.error(f"_parse_feed_tag_relation error : {e}")
                    return []
        return []

    def _parse_feed_dataset_ids(self, feed):
        """
        从简讯数据中提取数据集ids
        """
        try:
            dataset_ids = json.loads(feed.get('dataset_ids'))
        except Exception as e:
            logger.error(f"loads feed dataset_ids error : {e}")
            dataset_ids = []
        return dataset_ids

    def _parse_feed(self, data):
        res = []
        tag_list = self._parse_feed_tag_relation(data)
        for item in data:
            if item.get('data_id', '') != 'dashboard_email_subscribe':
                continue
            content = item.get('content')
            content = cache_data_decompress(content)
            if not content:
                continue
            feeds = json.loads(content) or []
            # feeds = content.get('feeds', {})
            # if not feeds:
            #     continue
            for feed in feeds:
                # feed = feeds[0]
                id = feed.get('id')
                feed['tag_relation'] = [t for t in tag_list if t.get('relation_id') == id]
                subject_id, subject_name = self._parse_tag(feed)
                name = msg_get_data_service.clear_title_html(feed.get('subject_email', ''))
                # name = feed.get('name')
                exists = repository.data_is_exists('dap_bi_dashboard_email_subscribe', {'id': id})

                dataset_ids = self._parse_feed_dataset_ids(feed)
                dataset_ids = list(set(dataset_ids))
                table_models = self._parse_dataset_table(dataset_ids)
                table_models = list(set(table_models))

                model = {
                    'subject_id': subject_id, 'subject_name': subject_name,
                    'dashboard': {
                        'id': id, 'name': name, 'type': PARSED_REPORT_TYPE.get(FEED_TYPE),
                        'change_info': '', 'is_individuation': False,
                        'status': '更新' if exists else '新增',
                        'models': table_models, 'dataset_ids': dataset_ids
                    }
                }
                res.append(model)
        return res

    def _get_feed_tag(self):
        tag_record = repository.get_one('dap_bi_rdc_file_content', {'task_id': self.task_id,
                                                                    'type': FEED_TYPE, 'data_id': 'tag_relation',
                                                                    'file_type': self.file_type},
                                        from_config_db=True) or {}
        try:
            c = cache_data_decompress(tag_record['content'])
            tag_list = json.loads(c)
        except:
            tag_list = []

        return tag_list

    def _parse_feed_dependency(self, data, report_id_list):
        res = []
        if not data or not report_id_list:
            return res
        content = data.get('content')
        content = cache_data_decompress(content)
        if not content:
            return res
        feeds = json.loads(content) or []
        # feeds = content.get('feeds', {})
        # if not feeds:
        #     continue
        tags = self._get_feed_tag()

        for feed in feeds:
            # feed = feeds[0]
            # subject_id, subject_name = self._parse_tag(feed)
            name = msg_get_data_service.clear_title_html(feed.get('subject_email', ''))
            # name = feed.get('name')
            id = feed.get('id')
            if report_id_list and id in report_id_list:
                item = {
                    'id': id,
                    "name": name,
                    "platform": PARSED_REPORT_TYPE.get(FEED_TYPE),
                    # "type": application.get("type"),
                    "created_on": feed.get("created_on"),
                    "modified_on": feed.get("modified_on"),
                    "created_by": feed.get("created_by"),
                    "modified_by": feed.get("modified_by"),
                    "application_type_name": PARSED_REPORT_TYPE.get(FEED_TYPE),
                    "tag_list": [t for t in tags if id == t.get('relation_id')],
                    # "dependencies": [],
                }
                dataset_ids = self._parse_feed_dataset_ids(feed)
                self._load_dashboard_dependencies(item, dataset_ids)
                res.append(item)

            # exists = repository.data_is_exists('dap_bi_dashboard_email_subscribe', {'id': id})
            # model = {'subject_id': subject_id, 'subject_name': subject_name,
            #          'dashboard': {'id': id, 'name': name, 'type': PARSED_REPORT_TYPE.get(FEED_TYPE),
            #                        'change_info': '', 'is_individuation': False,
            #                        'status': '更新' if exists else '新增',
            #                        'models': []}}
            # res.append(model)
        return res

    def _parse_application_dependency(self, data):
        res = []
        for item in data:
            content = item.get('content')
            content = cache_data_decompress(content)
            if not content:
                continue
            content = json.loads(content)
            applications = content.get('application', [])
            for application in applications:
                res.append({
                    'id': application.get('id'),
                    "name": application.get("name"),
                    "platform": application.get("platform"),
                    "type": application.get("type"),
                    "created_on": application.get("created_on"),
                    "modified_on": application.get("modified_on"),
                    "created_by": application.get("created_by"),
                    "modified_by": application.get("modified_by"),
                    "application_type_name": PARSED_REPORT_TYPE.get(APPLICATION_TYPE),
                    "tag_list": content.get("tag_relation", []),
                    "dependencies": [],
                })
        return res

    def _parse_dataset_dependency(self, data):
        res = []
        for item in data:
            content = item.get('content')
            content = cache_data_decompress(content)
            if not content:
                continue
            content = json.loads(content)
            dataset = content.get('dataset')
            subject_id, subject_name = self._parse_tag(content)
            obj = {'id': dataset.get('id'), 'name': dataset.get('name'), 'platform': '数据集', 'application_type_name': '数据集', 'tag_list': [{'tag_name': subject_name, 'tag_id': subject_id}]}
            self._load_dashboard_dependencies(obj, [dataset.get('id')])
            res.append(obj)
        return res

    def _parse_ppt_dependency(self, data, data_type):
        res = []
        for item in data:
            content = item.get('content')
            content = cache_data_decompress(content)
            if not content:
                continue
            content = json.loads(content)
            dashboards = content.get('dashboard', [])
            if not dashboards:
                continue
            dashboard = dashboards[0]
            dashboard_item = {
                "id": dashboard.get("id"),
                "name": dashboard.get("name"),
                "platform": dashboard.get("platform"),
                "level_code": dashboard.get("level_code"),
                "type": dashboard.get("type"),
                "parent_id": dashboard.get("parent_id"),
                "created_on": dashboard.get("created_on"),
                "modified_on": dashboard.get("modified_on"),
                "created_by": dashboard.get("created_by"),
                "modified_by": dashboard.get("modified_by"),
                "application_type": dashboard.get("application_type"),
                "new_layout_type": dashboard.get("new_layout_type"),
                "status": dashboard.get("status"),
                "tag_list": content.get('tag_relation'),
                "application_type_name": PARSED_REPORT_TYPE.get(data_type)
            }
            dataset_ids = []
            if data:
                dataset_ids = self._parse_ppt_dataset(item.get('content'))
            self._load_dashboard_dependencies(dashboard, dataset_ids)
            res.append(dashboard_item)
        return res

    def _parse_dashboards_dependency(self, data, data_type):
        res = []
        for item in data:
            content = item.get('content')
            content = cache_data_decompress(content)
            if not content:
                continue
            content = json.loads(content)
            dashboards = content.get('dashboard', [])
            charts = content.get('dashboard_chart')
            if not dashboards:
                continue
            dashboard = dashboards[0]
            dashboard_item = {
                "id": dashboard.get("id"),
                "name": dashboard.get("name"),
                "platform": dashboard.get("platform"),
                "level_code": dashboard.get("level_code"),
                "type": dashboard.get("type"),
                "parent_id": dashboard.get("parent_id"),
                "created_on": dashboard.get("created_on"),
                "modified_on": dashboard.get("modified_on"),
                "created_by": dashboard.get("created_by"),
                "modified_by": dashboard.get("modified_by"),
                "application_type": dashboard.get("application_type"),
                "new_layout_type": dashboard.get("new_layout_type"),
                "status": dashboard.get("status"),
                "tag_list": content.get('tag_relation'),
                "application_type_name": PARSED_REPORT_TYPE.get(data_type)
            }
            if not charts:
                continue
            dataset_ids = []
            for chart in charts:
                dataset_id = chart.get('source')
                if not dataset_id:
                    continue
                dataset_ids.append(dataset_id)
            self._load_dashboard_dependencies(dashboard_item, dataset_ids)
            res.append(dashboard_item)
        return res

    def _parse_active_reports_dependency(self, data, report_id_list):
        res = []
        file_list = self._get_active_reports_file_list(data, report_id_list)
        for file_item in file_list:
            dashboard_item = {
                "id": file_item.get("id"),
                "name": file_item.get("name"),
                "type": file_item.get("type"),
                "parent_id": file_item.get("parent_id"),
                "created_on": file_item.get("created_on"),
                "modified_on": file_item.get("modified_on"),
                "created_by": file_item.get("created_by"),
                "modified_by": file_item.get("modified_by"),
                "application_type": ApplicationType.ActiveReport.value,
                "status": 1 if file_item.get("publish_status") in (1, '1', 2, '2') else 0,
                "tag_list": file_item.get('tag_relation'),
                "application_type_name": PARSED_REPORT_TYPE.get(ACTIVE_REPORT_TYPE)
            }
            self._load_dashboard_dependencies(dashboard_item, file_item.get('dataset_ids', []))
            res.append(dashboard_item)
        return res

    def _parse_dashboards(self, data, type):
        res = []
        for item in data:
            content = item.get('content')
            content = cache_data_decompress(content)
            if not content:
                continue
            content = json.loads(content)
            dashboards = content.get('dashboard', [])
            if not dashboards:
                continue
            dashboard = dashboards[0]
            subject_id, subject_name = self._parse_tag(content)
            name = dashboard.get('name')
            id = dashboard.get('id')
            parent_id = dashboard.get('parent_id')
            dashboard_type = dashboard.get('type')
            if dashboard_type == 'CHILD_FILE':
                # 子报告不返回
                repository.update_data('dap_bi_rdc_file_content', {'parent_id': parent_id}, {'id': item.get('id')},
                                       from_config_db=True)
                continue
            exists = repository.data_is_exists('dap_bi_dashboard', {'id': id})
            info = repository.get_one('dap_bi_dashboard', {'id': id})
            # 如果存在 且 是系统分发 则直接跳过更新勾选
            status = '新增'
            is_individuation = False
            if exists:
                status = '更新'
                if self.package_type == '定制包' and info.get('distribute_type') == 1: # 只有定制包才有这个逻辑
                    status = '不支持更新'
                    is_individuation = True
            model = {'subject_id': subject_id, 'subject_name': subject_name,
                     'dashboard': {'id': id, 'name': name, 'type': PARSED_REPORT_TYPE.get(type),
                                   'change_info': self._compare_dashboard(content) if exists else '',
                                   'is_individuation': is_individuation,
                                   'models': [], 'datasets': [], 'record_id': item.get('id'), 'parent_id': parent_id,
                                   'status': status}}
            charts = content.get('dashboard_chart')
            res.append(model)
            if not charts:
                continue
            dataset_ids = []
            for chart in charts:
                dataset_id = chart.get('source')
                if not dataset_id:
                    continue
                dataset_ids.append(dataset_id)
            dataset_ids = list(set(dataset_ids))
            table_models = self._parse_dataset_table(dataset_ids)
            model.get('dashboard')['models'] = list(set(table_models))
            model.get('dashboard')['datasets'] = dataset_ids
        return res

    def _compare_dashboard(self, content):
        # TODO
        return '样式变化'

    def _compare_application(self, content):
        # TODO
        return '样式变化'

    def _compare_report(self, content):
        # TODO
        return '样式变化'

    def _parse_all_dataset_table(self):
        data = repository.get_data('dap_bi_rdc_file_content',
                                   {'task_id': self.task_id, 'type': DATASET_TYPE, 'file_type': self.file_type},
                                   from_config_db=True, multi_row=True)
        if not data:
            return
        for item in data:
            table_names = []
            content = item.get('content')
            content = cache_data_decompress(content)
            if not content:
                continue
            item = json.loads(content)
            if (item.get('data_source', {}) or {}).get('type') != 'MysoftShuXin15':
                continue
            used_tables = item.get('dataset_used_table', [])
            if not used_tables:
                continue
            for used_table in used_tables:
                table_name = used_table.get('table_name')
                if not table_name:
                    continue
                table_names.append(table_name)
            self.dataset_table_cache[item.get('dataset_id')] = table_names

    def _parse_dataset_table(self, dataset_ids):
        if not dataset_ids:
            return []
        table_models = []
        self.ref_dataset_ids += dataset_ids
        for dataset_id in dataset_ids:
            table_name = self.dataset_table_cache.get(dataset_id)
            if table_name:
                table_models += table_name
        return table_models

    def _parse_tag(self, data):
        tag = data.get('tag_relation') or []
        subject_id = DEFAULT_TAG_ID
        subject_name = DEFAULT_TAG_NAME
        if tag:
            tag = tag[0]
            subject_id = tag.get('tag_id')
            subject_name = tag.get('tag_name')
        return subject_id, subject_name

    def _load_dashboard_dependencies(self, dashboard_item, dataset_id_list):
        datasource_dependencies = {}
        dataset_dependencies = {}
        for dataset_id in dataset_id_list:
            dataset_dependency_info = self.dataset_dependency_cache.get(dataset_id)
            if dataset_dependency_info:
                datasource = dataset_dependency_info.get('datasource', {})
                dataset = dataset_dependency_info.get('dataset', {})
                datasource_id = datasource.get('id')
                dataset_id = dataset.get('id')
                if datasource_id and datasource_id not in datasource_dependencies:
                    datasource_dependencies[datasource_id] = datasource
                if dataset_id not in dataset_dependencies:
                    dataset_dependencies[dataset_id] = dataset
                dashboard_item['dependencies'] = list(dataset_dependencies.values()) + list(
                    datasource_dependencies.values())
                continue

            data = repository.get_data('dap_bi_rdc_file_content',
                                       {'task_id': self.task_id, 'file_type': self.file_type, 'type': DATASET_TYPE,
                                        'data_id': dataset_id},
                                       from_config_db=True)
            if not data:
                continue

            content = data.get('content')
            content = cache_data_decompress(content)
            if not content:
                continue
            data = json.loads(content)
            datasource = data.get('data_source', {}) or {}
            # if self.only_shuxin15_datasource:
            #     if datasource.get('type') != 'MysoftShuXin15':
            #         continue
            dataset = data.get('dataset', {})
            dataset_dependency = {
                "id": dataset.get('id'),
                "name": dataset.get('name'),
                "modified_by": dataset.get('modified_by'),
                "modified_on": dataset.get('modified_on'),
                "dependency_type": '数据集'
            }
            datasource_dependency = {
                "id": datasource.get('id'),
                "name": datasource.get('name'),
                "modified_by": datasource.get('modified_by'),
                "modified_on": datasource.get('modified_on'),
                "dependency_type": '数据源'
            }
            if dataset.get('id') not in dataset_dependencies:
                dataset_dependencies[dataset.get('id')] = dataset_dependency
            if datasource.get('id') and datasource.get('id') not in datasource_dependencies:
                datasource_dependencies[datasource.get('id')] = datasource_dependency
            self.dataset_dependency_cache[dataset.get('id')] = {
                'dataset': dataset_dependency,
                'datasource': datasource_dependency
            }

        dashboard_item['dependencies'] = list(dataset_dependencies.values()) + list(datasource_dependencies.values())

    def _get_mapping_from_active_reports(self, data, report_id_list):
        dataset_id_list = []
        tag_relation = []
        file_list = self._get_active_reports_file_list(data, report_id_list)
        for file_item in file_list:
            dataset_id_list.extend(file_item.get('dataset_ids', []))
            tag_relation.extend(file_item.get('tag_relation'))
        return {
            'dataset_id_list': dataset_id_list,
            'tag_relation': tag_relation
        }

    def _get_mapping_from_ppt(self, data):
        dataset_id_list = []
        tag_relation = []
        for item in data:
            content = item.get('content')
            content = cache_data_decompress(content)
            if not content:
                continue
            content = json.loads(content)
            dataset_ids = []
            if data:
                dataset_ids = self._parse_ppt_dataset(item.get('content'))
            dataset_id_list.extend(dataset_ids)
            tag_relation.extend(content.get('tag_relation', []))
        return {
            'dataset_id_list': dataset_id_list,
            'tag_relation': tag_relation
        }

    def _get_mapping_from_dashboard(self, data):
        dataset_id_list = []
        tag_relation = []
        for item in data:
            content = item.get('content')
            content = cache_data_decompress(content)
            if not content:
                continue
            content = json.loads(content)
            charts = content.get('dashboard_chart')
            dataset_ids = []
            if charts:
                for chart in charts:
                    dataset_id = chart.get('source')
                    if not dataset_id:
                        continue
                    dataset_ids.append(dataset_id)
            dataset_id_list.extend(dataset_ids)
            tag_relation.extend(content.get('tag_relation', []))
        return {
            'dataset_id_list': dataset_id_list,
            'tag_relation': tag_relation
        }

    def _get_mapping_from_application(self, data):
        tag_relation = []
        for item in data:
            content = item.get('content')
            content = cache_data_decompress(content)
            if not content:
                continue
            content = json.loads(content)
            tag_relation.extend(content.get('tag_relation', []))
        return {
            'dataset_id_list': [],
            'tag_relation': tag_relation
        }

    def _get_active_reports_file_list(self, data, report_id_list):
        file_list = []
        stack = []
        for item in data:
            content = item.get('content')
            content = cache_data_decompress(content)
            stack.extend(json.loads(content))
        valid_parent_id = []
        while stack:
            stack = sorted(stack, key=lambda v: item.get('parent_id', '') or '')
            item = stack.pop(0)
            if item.get('type') == 'FILE' and (
                    item.get('id') in report_id_list or item.get('parent_id') in valid_parent_id):
                if item.get('id') and item.get('id') not in valid_parent_id:
                    valid_parent_id.append(item.get('id'))
                file_list.append(item)
            elif item.get('type') == 'FOLDER' and 'sub' in item:
                stack.extend(item.get('sub', []))
        return file_list

    def _get_data_source_list(self, distinct_dataset_id_list):
        if distinct_dataset_id_list is None or len(distinct_dataset_id_list) == 0:
            return []
        data = repository.get_list('dap_bi_rdc_file_content',
                                   {'task_id': self.task_id, 'file_type': self.file_type, 'type': DATASET_TYPE,
                                    'data_id': distinct_dataset_id_list},
                                   from_config_db=True)
        res = {}
        for item in data:
            content = item.get('content')
            content = json.loads(cache_data_decompress(content))
            datasource = content.get('data_source', {})
            if datasource and datasource.get('id') not in res:
                res[datasource.get('id')] = datasource
        return list(res.values())

    def _get_subject_list(self):
        pulsar = Pulsar15Api()
        business_subject_list = pulsar.get_business_subject().get('list', [])
        result = []
        for business_unit in business_subject_list:
            subjects = business_unit.get('subjects', [])
            for subject in subjects:
                result.append({
                    "subject_id": subject.get('subject_id'),
                    "subject_name": subject.get('subject_name'),
                    "business_unit_id": business_unit.get('business_unit_id'),
                    "business_unit_name": business_unit.get('business_unit_name'),
                })
        return result
