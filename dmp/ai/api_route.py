#!/usr/bin/env python3  # 指定该文件使用 Python3 解释器运行
# -*- coding: utf-8 -*-  # 声明文件编码为 UTF-8，支持中文等非英文字符
# pylint: disable=E0401  # 禁用 pylint 的模块导入错误检查（用于避免找不到自定义模块的警告）
import json
import logging

import hug
from hug.authentication import authenticator

from ai.services import ai_service
from ai.services.ai_service import AI_TAG
from dmplib.hug import APIWrapper, g


# noinspection PyBroadException
@authenticator  # 使用 Hug 提供的认证装饰器，将下面的函数包装成一个验证器
def _verify_auth_key_handle(request, response, verify_user, **kwargs):
    if request.host == 'localhost':
        g.code = "ompdmdqlyz"
        g.account = "admin"
        return True

    logging.info(f'{AI_TAG} headers: {request.headers}')

    tenant_code = request.headers.get('TENANTCODE')
    auth_user_info = request.headers.get('AUTH-USER-INFO')
    userinfo = request.headers.get('userinfo')

    logging.info(f'{AI_TAG} auth_user_info: {auth_user_info}')
    logging.info(f'{AI_TAG} userinfo: {userinfo}')

    user_code = None

    # 优先使用 userinfo 字段
    if userinfo:
        try:
            user_code = json.loads(userinfo).get('UserCode')
        except:
            try:
                fixed = userinfo.replace('\\"', '"')
                if fixed.startswith('"') and fixed.endswith('"'):
                    fixed = fixed[1:-1]
                user_code = json.loads(fixed).get('UserCode')
            except:
                user_code = None

    # 如果 userinfo 没有获取到 user_code，则尝试使用 AUTH-USER-INFO
    if not user_code and auth_user_info:
        try:
            user_code = json.loads(auth_user_info).get('UserCode')
        except:
            try:
                fixed = auth_user_info.replace('\\"', '"')
                if fixed.startswith('"') and fixed.endswith('"'):
                    fixed = fixed[1:-1]
                user_code = json.loads(fixed).get('UserCode')
            except:
                user_code = None

    if not user_code:
        user_code = "admin"
        logging.info(f'{AI_TAG} user_code 为空，已默认赋值为 admin')

    g.code = tenant_code
    g.account = user_code

    logging.info(f'{AI_TAG} tenant_code: {tenant_code}, user_code: {user_code}')

    return True


class AIWrapper(APIWrapper):
    """继承 APIWrapper，专门用于开放 API 的路由封装"""
    __slots__ = ['_ai_route']  # 优化内存使用，限制类属性

    def __init__(self, name):
        super().__init__(name)  # 调用父类初始化方法
        self._ai_route = None  # 初始化公开路由
        self.api.http.base_url = '/openapi'  # 设置基础路径为 /openapi

    @property
    def ai_route(self):
        """延迟加载公开路由，并绑定认证处理器"""
        if not self._ai_route:
            # 创建 HTTP 路由实例，并设置认证处理器
            self._ai_route = hug.http(api=self.api, requires=_verify_auth_key_handle(None))
        return self._ai_route


# 实例化 AIWrapper，传入当前模块名称
api = AIWrapper(__name__)



@hug.get('/update_system_prompt')
def update_system_prompt(**kwargs):
    key = kwargs.get("key", "")
    tenant_code = kwargs.get("tenant_code", "")
    if not key or key != "d8f41c58-4808-11f0-84ff-0242ac110002":
        return False, "鉴权失败", ""
    if not tenant_code:
        return False, "租户标识不能为空", ""
    g.code = tenant_code
    g.account = "admin"
    return ai_service.update_system_prompt(kwargs)


@api.ai_route.post('/get_tool_list')
def get_tool_list(**kwargs):
    return ai_service.get_tool_list()


@api.ai_route.post('/get_data')
def get_tool_list(**kwargs):
    return ai_service.get_data_markdown(kwargs)





# @api.ai_route.post('/get_data_markdown')
# def get_tool_list(**kwargs):
#     return ai_service.get_data_markdown(kwargs)

@api.ai_route.post('/get_dim_tables')
def get_dim_tables(**kwargs):
    return ai_service.get_dim_tables(kwargs)

@api.ai_route.post('/get_dim_table_detail')
def get_dim_table_detail(**kwargs):
    return ai_service.get_dim_table_detail(kwargs)

@api.ai_route.post('/get_dim_table_data')
def get_dim_table_data(**kwargs):
    return ai_service.get_dim_table_data(kwargs)


@api.ai_route.post('/get_scene_dataset_info')
def get_scene_dataset_info(**kwargs):
    """
    获取报表关联的数据集描述和字段信息
    """
    from ai.services import web_service
    dashboard_id = kwargs.get('dashboard_id', '')
    if not dashboard_id:
        return False, '缺少报表ID', None

    try:
        result = web_service.get_dashboard_dataset_info(dashboard_id)
        return True, '获取成功', result
    except Exception as e:
        return False, str(e), None