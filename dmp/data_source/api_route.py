#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    API Route
    <NAME_EMAIL> on 2017/3/16.
"""
import hashlib
import logging

import requests

from base.enums import DatasetType, ApiParamSysValue, DataSourceType
from data_source.models import (
    DataSourceQueryModel,
    TableQueryModel,
    ColumnQueryModel,
    DataSourceModel,
    ColumnValueQueryModel,
    CreateTableModel,
    ApiParamModel,
)
from data_source.services import data_source_service
from dataset.services import dataset_service
from dmplib.utils.errors import UserError

from user_log.models import UserLogModel
from dmplib.hug import APIWrapper
from dmplib.hug import g
from dmplib.utils.strings import seq_id
from rbac.validator import PermissionValidator

api = APIWrapper(__name__)


@api.admin_route.post('/pulsar/get_params')
def get_params_of_pulsar(request, **kwargs):
    """
    获取数芯数据信息
    :param request:
    :param kwargs:
    :return:
    """
    cookie = request.cookies
    g.cookie = cookie
    model = DataSourceModel(**kwargs)
    model.conn_str_to_model()
    return True, '0k', data_source_service.get_params_of_pulsar(model.conn_str)


@api.admin_route.post('/test')
def test_data_source(request, **kwargs):
    """
    测试数据源
    :param kwargs:
    :return:
    """
    cookie = request.cookies
    g.cookie = cookie
    model = DataSourceModel(**kwargs)
    model.conn_str_to_model()
    data_source_service.test_connection(model)
    # # datahub 类型还需下发配置
    # if model.type == DataSourceType.DataHub.value:
    #     data_source_service.send_config(request, model)
    return True, '连接成功'


@api.admin_route.get('/check')
def check_dashboard_permission(**kwargs):
    """
    检测是否有权限查看该数据源
    :param dict kwargs:
    :return:
    """
    model = DataSourceModel(**kwargs)
    action_code = kwargs.get('action_code')
    if action_code not in ['edit']:
        raise UserError(message=u'action_code 只支持 edit')
    return True, None, data_source_service.check_can_edit_data_sourc(model)


@api.admin_route.post('/get_sys_params')
def get_sys_param_value():
    """
    获取DMP系统内置参数
    :return:
    """
    api_param_model = ApiParamModel()
    api_param_model.key = ApiParamSysValue.ProjectCode.value
    api_param_model.description = "企业代码"
    api_param_model.value = g.code
    params = [api_param_model]
    return True, '', params


@api.admin_route.post('/get_api_params')
def get_api_param_value(request, **kwargs):
    """
    获取api接口参数值
    :param kwargs:
    :return:
    """
    cookie = request.cookies
    g.cookie = cookie
    model = DataSourceModel(**kwargs)
    model.conn_str_to_model()
    return True, '连接成功', data_source_service.get_params(model)


@api.admin_route.post('/get_api_params_by_id')
def get_api_param_value_by_id(request, **kwargs):
    """
    获取api接口参数值
    :param kwargs:
    :return:
    """
    cookie = request.cookies
    g.cookie = cookie
    model = data_source_service.get_data_source(kwargs.get('id'))
    return True, 'ok', data_source_service.get_params(model)


@api.admin_route.post('/get_datahub_params')
def get_datahub_param_value(**kwargs):
    """
    获取datahub接口参数值
    :param kwargs:
    :return: [{"data_base_type": "SQL_Server", "db_code":"erp_database"},
    {"data_base_type": "MySQL", "db_code":"oa_database"},
    {"data_base_type": "Oracle", "db_code":"financial_database"}]
    """
    model = DataSourceModel(**kwargs)
    model.conn_str_to_model()
    return True, 'ok', data_source_service.get_params(model)


@api.admin_route.get('/get_erp_api_info')
def get_erp_api_info():
    """
    获取接口管家
    :return:
    """
    data_source_service.handel_history_erp_api()
    erp_api_info = data_source_service.get_erp_api_info()
    return True, 'ok', erp_api_info


@api.admin_route.post('/specifier/tenant_code')
def add_tenant_code_of_dataset_source(**kwargs):
    """
    为api数据源添加code配置
    :param kwargs:
    :return:
    """
    data_source_id = kwargs.get('id')
    if not id:
        raise UserError(message="缺少参数id")
    tenant_code = kwargs.get('tenant_code', '')
    return True, 'ok', data_source_service.edit_code_of_data_source(data_source_id, tenant_code)


@api.admin_route.post('/add', validate=PermissionValidator('add-datasource.edit'))
def add_data_source(request, **kwargs):
    """
    添加数据源
    :param dict kwargs:
    :return tuple:
    """
    try:
        cookie = request.cookies
        g.cookie = cookie
        if kwargs.get('type') == DatasetType.Api.value and not kwargs.get('code'):
            kwargs['code'] = 'api_' + hashlib.md5(seq_id().encode('utf-8')).hexdigest()[8:-8]
        if kwargs.get('type') == DataSourceType.HighData.value and data_source_service.check_high_data_source_exists():
            raise UserError(400, message='HighData数据源已存在，系统只允许添加一个HighData数据源')

        model = DataSourceModel(**kwargs)
        data_source_service.add_data_source(model, uncheck_connection=kwargs.get("uncheck_connection"))

        # datahub 类型还需下发配置
        model2 = DataSourceModel(**kwargs)
        model2.conn_str_to_model()

        UserLogModel.log_setting(
            request=request,
            log_data={
                'action': 'add_data_source',
                'id': kwargs.get('id'),
                'content': '新增数据源 [ {data_source_name} ]'.format(data_source_name=kwargs.get('name')),
            },
        )
    except Exception as e:
        logging.error(str(e))
        logging.exception(str(e))
        raise UserError(500, str(e))

    return True, '添加成功', model.id


@api.admin_route.post('/update')
def update_data_source(request, **kwargs):
    """
    修改数据源
    :param dict kwargs:
    :return tuple:
    """
    cookie = request.cookies
    g.cookie = cookie
    model = DataSourceModel(**kwargs)
    rs, data_source_name = data_source_service.update_data_source(
        model, uncheck_connection=kwargs.get("uncheck_connection")
    )

    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'update_data_source',
            'id': kwargs.get('id'),
            'content': '修改数据源 [ {data_source_name} ]'.format(data_source_name=data_source_name),
        },
    )

    return True, '修改成功', model.id


@api.admin_route.post('/delete')
def delete_data_source(request, **kwargs):
    """
    删除数据源
    :param dict kwargs:
    :return tuple:
    """
    record_count, data_source_name = data_source_service.delete_data_source(kwargs.get('id'))

    UserLogModel.log_setting(
        request=request,
        log_data={
            'action': 'delete_data_source',
            'id': kwargs.get('id'),
            'content': '删除数据源 [ {data_source_name} ]'.format(data_source_name=data_source_name),
        },
    )

    return (True, '删除成功') if record_count else (False, '删除失败')


@api.admin_route.get('/get', validate=PermissionValidator('add-datasource.view'))
def get_data_source(**kwargs):
    """
    获取数据源
    :param dict kwargs:
    :return tuple:
    """
    return (True, None, data_source_service.get_data_source(kwargs.get('id'), safe_mode=True))


@api.admin_route.get('/list')
def get_data_source_list(**kwargs):
    """
    获取数据源列表
    :param kwargs:
    :return:
    """
    return True, None, data_source_service.get_data_source_list(DataSourceQueryModel(**kwargs))


@api.admin_route.get('/tables')
def get_tables(request, **kwargs):
    """
    获取数据表
    :param kwargs:
    :return:
    """
    cookie = request.cookies
    g.cookie = cookie
    model = TableQueryModel(**kwargs)
    result = data_source_service.get_tables(model).get_result_dict()
    for i in result.get('items', []):
        if not i.get('type'):
            i['type'] = 'TABLE'
        if not i.get('name_cn'):
            i['name_cn'] = i.get('comment')

    return True, None, result


@api.admin_route.get('/table_columns')
def get_table_columns(request, **kwargs):
    """
    获取数据表字段
    :param kwargs:
    :return:
    """
    cookie = request.cookies
    g.cookie = cookie
    table_name = kwargs.get("table_name") or ''
    if table_name and "'" in table_name:
        raise UserError(message="表名中含有非法字符")
    result = data_source_service.get_table_columns(ColumnQueryModel(**kwargs)).get_dict(
        ['items', 'total', 'var_content'])
    __format_table_columns_result(result)
    return True, None, result


def __format_table_columns_result(result):
    if result and isinstance(result, dict) and result.get('items'):
        for column in result.get('items', []):
            if not column.get('col_name'):
                column['col_name'] = column.get('name')
            if not column.get('name_cn'):
                column['name_cn'] = column.get('comment')
            if not column.get('note'):
                column['note'] = column.get('comment')
            if not column.get('data_type'):
                column['data_type'] = column.get('type')
            if not column.get('field_type'):
                # 前端刘佳伟要求这么干
                if column.get('col_type') == '日期':
                    column['field_type'] = 'date'
                elif  column.get('col_type') == '数值':
                    column['field_type'] = 'int'
                else:
                    column['field_type'] = 'text'

@api.admin_route.get('/batch_table_columns')
def batch_get_table_columns(request, **kwargs):
    """
    获取数据表字段
    :param kwargs:
    :return:
    """
    cookie = request.cookies
    g.cookie = cookie
    table_name = kwargs.get("table_name") or ''
    if table_name and "'" in table_name:
        raise UserError(message="表名中含有非法字符")
    table_names = table_name.split(",")
    table_columns_map = {}
    for table_name in table_names:
        query_model = ColumnQueryModel(**kwargs)
        query_model.table_name = table_name
        result = data_source_service.get_table_columns(query_model).get_dict(['items', 'total', 'var_content'])
        __format_table_columns_result(result)
        table_columns_map[table_name] = result
    return True, None, table_columns_map


@api.admin_route.get('/table_column_values')
def get_table_column_values(**kwargs):
    """
    获取数据表字段值
    :param kwargs:
    :return:
    """
    return True, None, data_source_service.get_table_column_values(ColumnValueQueryModel(**kwargs)).get_result_dict()


@api.admin_route.post('/create_table')
def create_table(**kwargs):
    """
    创建数据表
    :param kwargs:
    :return:
    """
    return True, None, data_source_service.create_table(CreateTableModel(**kwargs))


@api.admin_route.get('/get_erp_apps')
def get_erp_apps(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {get} /api/data_source/get_erp_apps 获取erp的app list
    @apiGroup  data_source
    @apiResponse  200 {
    result: true,
    msg: null,
    data: [
            {
                AppCode: "1000",
                LevelCode: "1000",
                ApplicationName: "明源新一代云ERP",
                IsEnd: 0,
                IsSaaS: null,
                FirstLevelCode: "1000"
            },
            {
                AppCode: "0011",
                LevelCode: "1000.0011",
                ApplicationName: "销售系统",
                IsEnd: 1,
                IsSaaS: null,
                FirstLevelCode: "1000"
            }
        ]
    }
    **/
    """
    data_source_type = kwargs.get('data_source_type')
    erp_api_info_id = kwargs.get('erp_api_info_id')
    if not data_source_type:
        raise UserError(message="入参data_source_type不能为空")
    return True, None, data_source_service.get_erp_apps(data_source_type, erp_api_info_id)


@api.admin_route.get('/get_erp_env')
def get_erp_env(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {get} /api/data_source/get_erp_env 获取数据服务中心环境标识
    @apiGroup  data_source
    @apiResponse  200  {
        result: true,
        msg: null,
        data: {
            AppId: "erp60",
            EnvironmentId: "Product",
            SiteGroupKey: "v2.0"
        }
    }
    **/
    """
    data_source_type = kwargs.get('data_source_type')
    erp_api_info_id = kwargs.get('erp_api_info_id')
    if not data_source_type:
        raise UserError(message="入参data_source_type不能为空")
    data = data_source_service.get_erp_env(data_source_type, erp_api_info_id)
    return True, None, data


@api.admin_route.post('/get_saas_orgcode_and_secret', validate=PermissionValidator('add-datasource.edit'))
def get_saas_orgcode_and_secret(**kwargs):
    """
    /**
    @apiVersion 1.0.0
    @api {get} /api/data_source/get_saas_orgcode_and_secret 获取MysoftNewERP数据源三云MysoftSaaS的企业代码和秘钥
    @apiGroup  data_source
    @apiResponse  200  {
        result: true,
        msg: null,
        data: {
            "org_code": "abc",
            "secret": "mysoft"
        }
    }
    **/
    """
    model = DataSourceModel(**kwargs)
    model.conn_str_to_model()
    if not model.type or not model.conn_str:
        raise UserError(message=f'参数错误，请重试')
    data = data_source_service.get_saas_orgcode_and_secret(model)
    return True, None, data


@api.admin_route.get('/get_data_soruce_show_config')
def get_data_soruce_show_config(**kwargs):
    """
    获取当前租户数据源显示配置
    :return:
    """
    data = data_source_service.get_data_soruce_show_config()
    return True, None, data


@api.admin_route.get('/get_data_source_by_env')
def get_data_source_by_env(**kwargs):
    from data_source.services.env_data_source_service import get_env_data_source
    env_url = kwargs.get('env_url')
    code = kwargs.get('code')
    return True, None, get_env_data_source(env_url, code)


@api.admin_route.post('/import_data_source')
def import_data_source(**kwargs):
    from data_source.models import DataSourceModel
    from data_source.services.env_data_source_service import import_data_source_model
    model = DataSourceModel(**kwargs)
    code = kwargs.get('code')
    if not code:
        return False, '租户code不能为空', ''
    return True, None, import_data_source_model(model, code)


@api.admin_route.post('/get_data_source_by_code')
def get_data_source_by_code(**kwargs):
    code = kwargs.get('code')
    url = kwargs.get('url')
    if not code:
        return False, None, None
    if not url:
        return False, None, None
    from data_source.services.env_data_source_service import get_data_source_by_url
    return True, None, get_data_source_by_url(url, code)


@api.route.post('/get_data_source_list_by_code')
def get_data_source_list_by_code(**kwargs):
    if not kwargs.get('code'):
        return False, None, None
    g.code = kwargs.get('code')
    from data_source.services.env_data_source_service import get_data_source
    return True, None, get_data_source()


@api.admin_route.get('/get_datacenter_list')
def get_datacenter_list():
    """
    获取数据服务中心数据源列表
    :return:
    """
    data = data_source_service.get_datacenter_list()
    return True, None, data


@api.admin_route.get('/ds_conn_metrics')
def ds_conn_metrics(request, **kwargs):
    import app_celery
    app_celery.ds_conn_metrics.apply_async(kwargs=kwargs, queue='celery-slow')
    # app_celery.ds_conn_metrics(**kwargs)
    return True, '', {}


@api.admin_route.get('/import_tables')
def import_tables(request, **kwargs):
    """
    引入数据表
    :param kwargs:
    :return:
    """
    cookie = request.cookies
    g.cookie = cookie
    model = TableQueryModel(**kwargs)
    table_list = data_source_service.import_tables(model)
    return True, None, table_list
