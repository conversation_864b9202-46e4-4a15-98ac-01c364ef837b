[App]
disable_skin_button = 0
name = dmp
runtime = test
dmp_env_sign = hd
enable_profiling = 1
license = 0
enable_license = 0
dog_type = ProductDog
chart_batch_fetch = 1
apply_pymysql = 0
is_deploy_in_yunqing = 0
celery_broker = redis
one_domain = 0
custom_cookie_token_name = dmp_token
scheduler = xxl-job

[Cache]
close_sql_cache = 0
installed_components_cache_key = installed_components_v3
components_refresh_flag_cache_key = components_refresh_flag
released_dashboard_metadata_cache_key = dmp

[Component]
auto_upgrade = 0
component_endpoint = https://oss-cn-hangzhou.aliyuncs.com/mic-open/dmp-test/component/package/
component_menuicon_endpoint = https://oss-cn-hangzhou.aliyuncs.com/mic-open/dmp/icons/component_menus/
init_components = 0

[DB]
flow_log_database = dmp_flow_log
database = dmp_config_test
;host = **********
;host = *********
host = rm-bp10jpberdfgm88e5vo.mysql.rds.aliyuncs.com
;port=5590
port=3306
user = mic_test
password = Mic@2017

;database = dmp_config_8
;host = dmp-test5.mypaas.com.cn
;port=3309
;user = dev
;password = ******
;schedule_database = dmp_akso_config

;database = dmp_config_beta
;host = rm-bp1f9pc4yqu7v84x2mo.mysql.rds.aliyuncs.com
;;host = **********
;;port = 5589
;port = 3306
;user = mic_test
;password = *********

; bi环境
;database = dmp_config
;host = dmp-test5.mypaas.com.cn
;port=3306
;user = dev
;password = ******

; dm环境
;database = dmp_config
;host = ***********
;port= 5238
;user = ******
;password = ******
;db_type = dm
;debug = 0

[Domain]
dmp = http://dmp-test5.mypaas.com.cn
dmp_admin = https://dmp-admin-test.mypaas.com.cn
dmp_openapi = https://dmp-openapi-test.mypaas.com.cn
dmp_high_data = http://***********:8090
yl_host = https://es.yl.mycyjg.com
one_domain = 0

[Email]
account = <EMAIL>
name = 明源云大数据平台
password = Mysoft#2018!
smtp_enable_ssl = 1
smtp_port = 465
smtp_server = smtp.exmail.qq.com

[External]
api_timeout = 30
fill_h = 0
fill_m = 30
snap_h = 18
snap_m = 43

[Grayscale]
black_list =
white_list =
gray_env = 0

[JWT]
component_expire = 864000
component_secret = 0!D#8C&6
expires = 360000
secret = YC2UFKz7
sso_dashboard_secret = 0UZR4h#@
init_password_secret = XAuTsZWy

[Log]
format = %%(asctime)s %%(filename)s[line:%%(lineno)d]  %%(exc_text)s %%(message)s
handler = console
level = INFO
profile_sample = 2
sentry_dsn = http://********************************@*************:9000/2
slow_log_seconds = 1
user_action = 1

[LoginWebServer]
method = UserLoginForDomain
service = https://mdcmip.mingyuanyun.com
AppKey = e2224ebd74ec4348a8109cc2d3a13e01
AppSecret = 0a55fb5adc954700a17eede0d57ce539

[MOP]
domain = https://dmp-mop.mypaas.com.cn

[ODPS]
endpoint = http://odps-ext.aliyun-inc.com/api
tunnel_endpoint = http://dt-ext.nu16.odps.aliyun-inc.com

[Minio]
access_key_id = rO3wHnHbY81ZVCPu
access_key_secret = 2VXnWfuPuERZUjRFJke0MDOyCTZZps9K
bucket = tj-dmp
endpoint = https://minio-api.mysqlxncs.mingyuanyun.com:32789

[OSS]
access_key_id = LTAI4FmGhQ4dHdHQqyQNApFg
access_key_secret = ******************************
bucket = dmp-test
endpoint = https://oss-cn-shenzhen.aliyuncs.com

[OSS_STS]
access_key_id = LTAIoypXSUfZJoYq
access_key_secret = ******************************
bucket = dmp-test
endpoint = http://oss-cn-shenzhen.aliyuncs.com
img_domain_url =
role_arn = acs:ram::1353452237400551:role/ramossro
role_session_name = dmp_oss_session

[OSS_Config]
service = OSS

[OnlineService]
app_code = ycdmp
key = e60f953bbc8c7f9e3d0587e54ffc3c12
online_service_url = https://sol-test.fdcyun.com/web/login
tenant_id = 39e1f1e8-fa37-be9a-ede5-51bbc1a4db8c

[Open]
mreporting_api = http://whyd.mypaas.com.cn
mreporting_corp_id = corp5a45e4d9e38a0
mreporting_corp_secret = corpsecret5986be1a1ced35a302b1c50f8a51d9941979de8b
mreporting_redirect_url = http://whyd.mypaas.com.cn/

[Product]
product_type = 平台
record_getdata_api_log = 0

[RabbitMQ]
host = 127.0.0.1
password = guest
port = 5672
queue_name_datax = DataX
queue_name_datax_offline = Datax-offline
queue_name_flow = Flow
queue_name_flow_feeds = Flow-Feeds
queue_name_flow_offline = Flow
queue_name_flow_priority = Flow-priority
user = guest
body_limit = 0.5
vhost = vhost_tj_sj

[Redis]
db = 1
enable = 1
#host = *************
host = 127.0.0.1
password =
;password = Mic95938
port = 6379
;port = 5591
celery_db = 9
max_connections = 100
mode =

[Rundeck]
cmd_template = export PYTHONIOENCODING=utf8 && python3 /dmp-mq-producer/app_producer.py
port = 4441
project_name = dmp
server = dmp-test4.mypaas.com.cn
token = bf6PqvUrwDdicTlsLPUvOEp44jHwRD71
cmd_template_celery = export PYTHONIOENCODING=utf8 && python3 /dmp-mq-producer/celery_producer.py
is_new_version = true

[Security]
dmp_crypt_key = 7-J.\|S&w#1!Wc%,

[LoginConfig]
oa_login_secretkey=7onwjD

[SelfService]
allowed_project_codes = zdtest,zjdemo,yl_saas_prd_bak,V1_chenhh,test,yl_saas_prd_bak,zn_test
app_key = 2496315931721179
app_secret = aErovZEJxamlTNOaSyBFSowUuFPqbGUb
get_permission_fields_api = https://reporter.test.myspacex.cn/big-data/dmp-auth/get-auth-column
get_permission_filter_api = https://reporter.test.myspacex.cn/big-data/dmp-auth/get-user-auth
host = https://bigdata-saas-openapi.mypaas.com/
is_disable_detailreport = 0
is_disable_makewithdataset = 0
is_disable_releasebutton = 0
multi_dim_use_full_outer_join = 1
permission_type = relate_permission_api
pulsar_project_code = zn_test
pulsar_tenant_code_convert_method = no_convert
self_redirect_url = https://bigdata-test.myyscm.com/dmp/yfw/self-report-access-auth
use_dremio_project = yulian_dremio,cc_dremio,yulian_dremio_back,test202012251
use_multi_dim_project = hww_multi_dim_test,V1_chenhh,zdtest,data_asstes,zjdemo,yl_saas_prd_bak,zn_test
use_presto_project = V1_chenhh,zntest1,zdtest,zn_test,zjdemo,yl_saas_prd_bak,zjdemo,yl_saas_prd_bak
yl_api_secret = MXD@0cdU
yl_tenant_code_convert_secret = MXD@0cdU
yl_tenant_code_convert_url = https://bigdata-test.myyscm.com/dmp/yfw/data-permission-handler

[PPT]
domain = https://dmp-slide-test.mypaas.com.cn
active_report_domain = https://dmp-report-viewer-test.mypaas.com.cn
active_report_domain_front = https://dmp-report-viewer-test.mypaas.com.cn
jwt_secret = LdGskFiUsuiqWYGxYBguihXzhARzntRF
sso_secret = ******************************

[Permission]
dataset_permission_model = alone

[Yzs]
domain = https://www.fdccloud.com
api_token = 7BC0AB8C-7669-40A3-B0A9-DB55F550B2DB
msg_app_key = Mysoft5fa8ea629451c
report_app_ke = Mysoft5fa8e9eeddf05


[Akso]
flow_default_time = 10
percent = 0.9

[Yysc]
api_url = https://mpcloud.mingyuanyun.com/rdc-exchange-service
aud = https://appexchange.mingyuanyun.com
exp = 600
iss = DataPlatform
secret = 37EF41DE-CE7E-11EB-B44F-0242AC110008

[Superportal]
app_secret = OTMwNWIxNTMyZTI0N2JmNTc4NGJlOTdh
host = https://work-test.gzt.mypaas.com

[IngratePlatform]
host = https://ipaas.tj-test.mycyjg.com
client_id = dfed642e734d470ba9d97b26b60d8446
client_secret = 73bdfa53d90e477ba855a95096071088
report_authorize_url = /api/basicdata/CheckUserReportAuthorizeBypass
client_id_of_register = 42458fc5dc394cf49fcf084efacda23f
client_secret_of_register = a53551589ff54fc3afd2f59844544b81

[ThirdParty]
base_redirect_url = https://test-qmyxcg.myscrm.com.cn/api/index.php?r=bigdata/dmp-dashboard/get-dashboard-url
biz_shield_privacy = 1
message_push_api = https://api-open-test.myspacex.cn/m/ReportCenter/api/update-reporter
portal_base_redirect_url = https://bigdata-test.myyscm.com/dmp/yfw/portal-auth
screen_base_redirect_url = https://bigdata-test.myyscm.com/dmp/yfw/screen-auth
user_source_id = 39fd0b16-67ee-8bb5-6361-1b60ad040542
portal_third_way = mip


[Oss]
; AccessKey
AccessKey    = ${AccessKey}


[ThirdDatasource]
domain=bigdata-beta-openapi.mypaas.com
bigdata_appid=00000000-1111-1111-1111-000000000001
bigdata_secret=SLIkFbmRTeeJvQZszowIHguXsoPwMxzJ


[Rdc]
authority = https://auth.mingyuanyun.com
certification_types = 201,202
client_id = dmp
enable_auth = 1
rdc_url = https://rdc.mingyuanyun.com
redirect_uri = https://bi.mypaas.com/static/rdc-callback.html

[Metrics]
server_port = 8005


[XXL-JOB]
access_token=xxl-job
executor_host=dmp-task-goup.dmp-test.svc
admin_host = http://************:8080
admin_username = admin
admin_password = 123456
alarm_email = <EMAIL>
executor_timeout = 44
executor_fail_retry = 2
default_group_appname = dmp-task-executor
default_group_title = 测试创建
author = 宋浩