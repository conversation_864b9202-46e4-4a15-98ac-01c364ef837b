"""
schedule celery
"""
import sys
from components import celery_app, config, consts
import time

# 保持和dmp中 的app_celery一致
CELERY_APP_NAME = 'dmp_celery'


def producer(*argv):
    options = {
        'Redis.host': config.get('Redis.host'),
        'Redis.password': config.get('Redis.password'),
        'Redis.port': config.get('Redis.port'),
        'Redis.celery_db': config.get('Redis.celery_db') or 5,
        'Redis.username': config.get('Redis.username') or '',
        'RabbitMQ.user': config.get('RabbitMQ.user'),
        'RabbitMQ.password': config.get('RabbitMQ.password'),
        'RabbitMQ.host': config.get('RabbitMQ.host'),
        'RabbitMQ.port': config.get('RabbitMQ.port'),
        'RabbitMQ.type': config.get('RabbitMQ.type'),
        'App.celery_broker': config.get('App.celery_broker'),
        'RabbitMQ.vhost': config.get('RabbitMQ.vhost', 'vhost_tj_sj'),
        
    }

    project_code = argv[0]
    task_name = argv[1]
    data_id = argv[2]
    queue_name = argv[3]
    args = argv[4:]

    capp = celery_app.CeleryAPP(CELERY_APP_NAME, options)
    time_now = time.strftime("%Y-%m-%d %H:%M:00", time.localtime())
    capp.app.send_task(
        task_name,
        args=tuple(args),
        kwargs={'project_code': project_code, 'data_id': data_id, 'is_rundeck': 1, 'created_time': time_now},
        countdown=None,
        eta=None,
        task_id=None,
        producer=None,
        connection=None,
        router=None,
        result_cls=None,
        expires=None,
        publisher=None,
        link=None,
        link_error=None,
        add_to_parent=True,
        group_id=None,
        retries=0,
        chord=None,
        reply_to=None,
        time_limit=None,
        soft_time_limit=None,
        root_id=None,
        parent_id=None,
        route_name=None,
        shadow=None,
        chain=None,
        task_type=None,
        queue=queue_name
    )
