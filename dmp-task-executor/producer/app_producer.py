# -*- coding: UTF-8 -*-
import sys
import datetime
import json
import random
import logging

import math
import traceback

from components import config
from components.consts import RET_STATE, SUBSCRIBE_MSG_USED_DATASET_ID_SET_KEY, QUEUE_OF_DATASET
from components.message_queue import MessageQueue
from components.redis import conn_custom_prefix, conn
from components import db


def get_project_info(code):
    with db.get_master_db() as d:
        return d.query_one('select flow_queue_name, flow_priority from dap_bi_tenant_setting where code=%(code)s', params={'code': code}) or {}


def run(project_code, flow_id, queue_name, test_run="0", dataset_subject_id="", force_update=0, download=0, new_version=0, priority=1, is_priority=False):
    """
    消息发送入口
    :param project_code: 项目编码
    :param flow_id: 流程ID
    :param queue_name: 消息队列名称
    :param test_run: 测试运行
    :param dataset_subject_id: 主题包ID
    :param force_update: 强制更新
    :param download: 下载
    :param new_version: 主题包新版本
    :param is_priority: 主题包新版本
    :return:
    """
    if not check_message(project_code, flow_id):
        logging.error("消息内容不合法，项目code或流程id错误，项目code：" + project_code + ",流程id：" + flow_id)
        return

    flow_instance_id = create_flow_instance(project_code, flow_id)
    if flow_instance_id:
        data = {
            'project_code': project_code,
            'flow_id': flow_id,
            'flow_instance_id': flow_instance_id,
            'test_run': test_run,
            'subject_id': dataset_subject_id,
            'force_update': int(force_update),
            'download': int(download),
            'new_version': int(new_version),
            'schedule_task': 1              # rundeck触发的任务都是定时任务，为了区分定时任务和手动任务
        }

        if queue_name:
            if queue_name.lower().strip() == 'none' or queue_name.lower().strip() == 'null':
                send_queue_name = config.get('RabbitMQ.queue_name_flow')
            else:
                send_queue_name = queue_name
        else:
            send_queue_name = config.get('RabbitMQ.queue_name_flow')

        rabbit_mq = MessageQueue(is_priority=is_priority)
        res = rabbit_mq.send_message(
            send_queue_name, json.dumps(data), durable=False, headers={"_dmp_message_uuid": seq_id()},
            priority=priority, is_priority=is_priority
        )


def create_flow_instance(project_code, flow_id):
    """
    创建流程实例
    :param project_code:
    :param flow_id:
    :return:
    """
    try:
        # 创建流程实例，状态为： 已创建
        flow_data = db.get_data("dap_bi_flow", {"id": flow_id}, ["id", "name", "type"], code=project_code)
        flow_instance_id = seq_id()
        flow_instance_data = {
            'id': flow_instance_id,
            'flow_id': flow_data.get('id'),
            'name': flow_data.get('name'),
            'type': flow_data.get('type'),
            'status': '已创建',
            'message': '',
        }
        db.get_db(code=project_code).insert("dap_bi_instance", flow_instance_data)
        logging.error("创建流程实例id：" + flow_instance_id + ",状态为：已创建")
        return flow_instance_id

    except Exception as e:
        logging.error(traceback.format_exc())
        logging.error(str(e))
        logging.error("创建流程实例失败。")
        return None


def update_flow_instance(project_code, flow_id, flow_instance_id, data):
    """
    更新流程实例
    :param flow_instance_id:
    :return:
    """
    try:
        db.get_db(code=project_code).update("dap_bi_instance", data, {"id": flow_instance_id, "flow_id": flow_id})
    except Exception as e:
        logging.error(traceback.format_exc())
        logging.error(str(e))
        logging.error("更新流程实例失败。")


def check_message(project_code, flow_id):
    """
    校验消息是否正确
    :param project_code:
    :param flow_id:
    :return:
    """
    flag = True

    try:
        flow_data = db.get_data("dap_bi_flow", {"id": flow_id}, ["id", "name", "type"], code=project_code)
        if not flow_data:
            flag = False

    except Exception as e:
        logging.error(traceback.format_exc())
        logging.error(str(e))
        logging.error("消息内容错误项目code：{project_code}，流程id：{flow_id}".format(project_code=project_code, flow_id=flow_id))
        flag = False
    return flag


def _get_random_chars(char_length):
    chars = 'abcdef0123456789'
    i = 0
    res = ''
    while i < char_length:
        idx = math.floor(1 + random.random() * 16)
        res += chars[idx - 1 : idx]
        i += 1
    return res


def seq_id():
    """
    获取有序GUID与 db中fn_newSeqId 算法保持一致
    :return:str
    """
    now = datetime.datetime.utcnow().timestamp()
    ticks = hex(round(now * 1000000))[2:]
    old_ticks = hex(round(now * 1000 + 62135625600000))[2:]
    return '%s-%s-%s%s-%s-%s' % (
        old_ticks[:8],
        old_ticks[8:12],
        ticks[10:13],
        _get_random_chars(1),
        _get_random_chars(4),
        _get_random_chars(12),
    )


def _get_subscribe_redis():
    return conn_custom_prefix('subscribe:')


def _subscribe_dataset_priority(project_code, flow_id, queue_name, is_priority, priority):
    """
    从redis中检查简讯使用的调度数据集。判断数据集id是否为简讯调度数据集，如果是则优先清洗
    @param project_code:
    @param flow_id:
    @param queue_name:
    @param is_priority:
    @param priority:
    @return:
    """
    try:
        key = SUBSCRIBE_MSG_USED_DATASET_ID_SET_KEY
        val = f"{project_code}_{flow_id}"

        conn_redis = _get_subscribe_redis()
        is_exists = conn_redis.sismember(key, val)
        if is_exists:
            new_queue_name = config.get("RabbitMQ.dmp_flow_erp_op", 'Flow-erp-op') or 'Flow-erp-op'
            new_is_priority = True
            new_priority = 10
            logging.error(f"project_code: {project_code} flow_id: {flow_id} 被简讯引用，优先清洗")
            return new_queue_name, new_is_priority, new_priority
    except Exception as e:
        logging.error(f"简讯优先清洗数据集判断错误，errs：{str(e)}")
    return queue_name, is_priority, priority


def _data_center_dataset_queue(project_code, flow_id, queue_name):
    """
    数据服务中心数据集放到优先队列中
    :param project_code:
    :param flow_id:
    :param queue_name:
    :return:
    """
    def __get_datasource_id(dataset_id):
        try:
            # 获取数据源id
            content = db.get_data_scalar("dap_bi_dataset", {"id": dataset_id}, 'content', code=project_code)
            content_dict = json.loads(content)
            data_source_id = content_dict.get('data_source_id')
            if not data_source_id:
                dataset_ids = content_dict.get('source_dataset_ids')
                return __get_datasource_id(dataset_ids[0])
            else:
                return data_source_id
        except Exception as e:
            return ''

    try:
        cache = conn()
        key = QUEUE_OF_DATASET.format(code=project_code, dataset_id=flow_id)
        queue = cache.get(key)
        if not queue:
            data_source_id = __get_datasource_id(flow_id)
            _type = db.get_data_scalar('dap_m_data_source', {'id': data_source_id}, col_name='type', code=project_code)
            if _type == "MysoftNewERP":
                queue_name = config.get("RabbitMQ.dmp_flow_erp_op", 'Flow-erp-op') or 'Flow-erp-op'
            cache.set(key, queue_name, 2592000)
        else:
            queue_name = queue.decode() if isinstance(queue, bytes) else queue
        return queue_name
    except Exception as e:
        traceback.print_exc()
        logging.error(f"get queue name error: {e}")
        return queue_name


def producer(*args):
    project_code = args[0]
    flow_id = args[1]
    queue_name = args[2] if len(args) >= 3 else ''
    dataset_subject_id = args[3] if len(args) >= 4 else ''
    download = args[4] if len(args) >= 5 else False
    force_update = args[5] if len(args) >= 6 else False
    new_version = args[6] if len(args) >= 7 else 1

    is_priority = False
    priority = 1
    # # flow 队列进入优先级处理逻辑
    # if queue_name in [config.get('RabbitMQ.queue_name_flow', 'dmp_flow'), '']:
    #     # 设置优先队列
    #     proj = get_project_info(project_code)
    #     flow_erp_op = config.get("RabbitMQ.dmp_flow_erp_op", 'dmp_flow_erp_op') or 'dmp_flow_erp_op'
    #     if proj.get('flow_queue_name') == flow_erp_op:
    #         queue_name = flow_erp_op
    #         is_priority = True
    #     priority = proj.get('flow_priority', 1) or 1
    #     # 简讯调度数据集优先清洗判断
    #     queue_name, is_priority, priority = _subscribe_dataset_priority(
    #         project_code, flow_id, queue_name, is_priority, priority
    #     )
    #     if queue_name != flow_erp_op:
    #         queue_name = _data_center_dataset_queue(project_code, flow_id, queue_name)
    #         is_priority = bool(queue_name == flow_erp_op)

    # task-group 去掉 flow-erp-op服务
    if queue_name == config.get("RabbitMQ.dmp_flow_erp_op", 'dmp_flow_erp_op'):
        queue_name = config.get("RabbitMQ.queue_name_flow", 'dmp_flow')

    logging.info("项目code：" + project_code)
    logging.info("流程id：" + flow_id)
    logging.error("queue name: " + queue_name)
    run(
        project_code,
        flow_id,
        queue_name,
        dataset_subject_id=dataset_subject_id,
        force_update=force_update,
        download=download,
        new_version=new_version,
        priority=priority,
        is_priority=is_priority
    )
