#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    数据库基本操作
    <NAME_EMAIL> on 2017/3/14.
"""
import json
import logging
import socket
import struct

import pymysql
import builtins
from sshtunnel import BaseSSHTunnelForwarderError, SSHTunnelForwarder

from components import config


class SimpleMysql:
    conn = None
    cur = None

    def __init__(self, **kwargs):
        """ construct """
        self.host = kwargs.get("host", "localhost")
        self.port = kwargs.get("port", 3306)
        self.database = kwargs.get("database")
        self.user = kwargs.get('user')
        self.password = kwargs.get('password')

        self.keep_alive = kwargs.get("keep_alive", False)
        self.charset = kwargs.get("charset", "utf8")
        self.autocommit = kwargs.get("autocommit", False)
        self.connect_timeout = kwargs.get('connect_timeout', 3)

        # ssh
        self.ssh = None
        self.use_ssh = kwargs.get('use_ssh', False)
        self.ssh_host = kwargs.get('ssh_host')
        self.ssh_port = kwargs.get('ssh_port', 22)
        self.ssh_user = kwargs.get('ssh_user')
        self.ssh_password = kwargs.get('ssh_password')

        self._check_args()
        self.connect()

    def _check_args(self):
        """ check args of structure"""
        if self.database is None or self.user is None or self.password is None:
            raise Exception('db参数配置不完整')
        if self.use_ssh and (not self.ssh_host or not self.ssh_port or not self.ssh_user or not self.ssh_password):
            raise Exception('ssh参数配置不完整')

    @property
    def _loopback_address(self):
        return socket.inet_ntoa(struct.pack('!I', socket.INADDR_LOOPBACK))

    def connect(self):
        """Connect to the mysql server"""

        try:
            if self.use_ssh:
                try:
                    self.ssh = SSHTunnelForwarder(
                        (self.ssh_host, int(self.ssh_port)),
                        ssh_username=self.ssh_user,
                        ssh_password=self.ssh_password,
                        remote_bind_address=(self.host, int(self.port)))
                    self.ssh.start()
                except BaseSSHTunnelForwarderError as e:
                    logging.error('ssh connection failed ' + str(e))
                    self.ssh = None
            # if self.host == "rm-bp10jpberdfgm88e5.mysql.rds.aliyuncs.com":
            #     self.host = "rm-bp10jpberdfgm88e5vo.mysql.rds.aliyuncs.com"
            #     # self.host = "*********"
            #     self.port = 3306
            self.conn = pymysql.connect(db=self.database,
                                        host=self.host if not self.ssh else self._loopback_address,
                                        port=int(self.port) if not self.ssh else int(self.ssh.local_bind_port),
                                        user=self.user,
                                        passwd=self.password,
                                        charset=self.charset,
                                        connect_timeout=self.connect_timeout,
                                        ssl_disabled=True
                                        )
            self.cur = self.conn.cursor()
            self.conn.autocommit(self.autocommit)
        except:
            logging.error("MySQL connection failed, host: %s" % self.host)
            raise

    def _convert_to_json(self, cur_result, one=None):

        if not cur_result:
            return None if one else []
        cur_result = [cur_result] if one else cur_result

        cur = self.cur
        r = [dict((cur.description[i][0], value) for i, value in enumerate(row)) for row in cur_result]

        return (r[0] if r else None) if one else r

    def query_scalar(self, sql, params=None):
        cur = self._execute(sql, params)
        result = cur.fetchone()
        if result:
            return result[0]
        return None

    def query_columns(self, sql, params=None):
        cur = self._execute(sql, params)
        result = cur.fetchall()
        if result:
            return [t[0] for t in result]
        return None

    def query_one(self, sql, params=None):

        cur = self._execute(sql, params)
        result = cur.fetchone()

        return self._convert_to_json(result, True)

    def query(self, sql, params=None, offset=None, limit=None):
        if offset is not None or limit is not None:
            sql += " LIMIT {}, {}".format(0 if offset is None else offset, limit)

        cur = self._execute(sql, params=params)
        result = cur.fetchall()
        return self._convert_to_json(result, False)

    def insert(self, table, data, commit=True):
        """
        新增一条记录
        :param table: 表名
        :param data: dict()
        :param commit: bool 是否立即提交, 如果有多个处理需要事物提交，则最后调用commit()方法提交
        :return: 受影响的行数
        """
        if data and isinstance(data, dict):
            if not data.get('created_by'):
                data['created_by'] = 'dmp-flow'
            if not data.get('modified_by'):
                data['modified_by'] = 'dmp-flow'
        query = self._serialize_insert(data)

        sql = "INSERT INTO %s (%s) VALUES(%s)" % (table, query[0], query[1])
        affect_row = self._execute(sql, tuple(data.values())).rowcount
        if commit:
            self.commit()
        return affect_row

    def insert_multi_data(self, table, list_data, fields, commit=True):
        """
        添加多行数据
        :param str table:
        :param list list_data:
        :param list fields:
        :param bool commit:
        :return:
        """
        params = {}
        values = []
        tmp = 0
        cur_account = 'dmp-flow'
        for data in list_data:
            if not isinstance(data, dict):
                continue
            tmp_val = []
            for c in fields:
                if not isinstance(c, str):
                    continue
                p_name = c + str(tmp)
                tmp_val.append('%(' + p_name + ')s')
                params[p_name] = data.get(c)
            if 'created_by' not in fields:
                tmp_val.append('%(created_by)s')
            if 'modified_by' not in fields:
                tmp_val.append('%(modified_by)s')
            values.append('(' + ','.join(tmp_val) + ')')
            tmp += 1
        if not values:
            return 0
        if 'created_by' not in fields:
            fields.append('created_by')
            params['created_by'] = cur_account
        if 'modified_by' not in fields:
            fields.append('modified_by')
            params['modified_by'] = cur_account
        sql = 'INSERT INTO {table}({cols}) VALUES {values};'.format(table=table,
                                                                    cols=','.join(['`' + c + '`' for c in fields]),
                                                                    values=','.join(values))
        affect_row = self._execute(sql, params).rowcount
        if commit:
            self.commit()
        return affect_row

    def update(self, table, data, condition=None, commit=True):
        """
        更新记录
        :param table: 表名
        :param data: dict()
        :param condition: dict()
        :param commit: bool 是否立即提交, 如果有多个处理需要事物提交，则最后调用commit()方法提交
        :return:
        """
        if data and isinstance(data, dict) and not data.get('modified_by'):
            data['modified_by'] = 'dmp-flow'
        query = self._serialize_update(data)
        sql = "UPDATE %s SET %s" % (table, query)
        params = tuple(data.values())
        if condition and len(condition) > 0:
            sql += " WHERE %s" % ' AND '.join(['`%s`= %%s' % k for k, _ in condition.items()])
            params = params + tuple(condition.values())
        affect_row = self._execute(sql, params).rowcount
        if commit:
            self.commit()
        return affect_row

    def delete_by_id(self, table, row_id, commit=True):
        """Delete rows based on a where condition"""
        sql = "DELETE FROM %s WHERE id='%s'" % (table, row_id)
        affect_row = self._execute(sql).rowcount
        if commit:
            self.commit()
        return affect_row

    def delete(self, table, condition=None, commit=True):
        """Delete rows based on a where condition"""
        sql = "DELETE FROM %s" % table
        params = None
        if condition and len(condition) > 0:
            sql += " WHERE %s" % ' AND '.join(['`%s`= %%s' % k for k, _ in condition.items()])
            params = tuple(condition.values())
            logging.debug("execute sql: {}, with: \n ({})".format(sql, json.dumps(params)))
        affect_row = self._execute(sql, params).rowcount
        if commit:
            self.commit()
        return affect_row

    def exec(self, sql, params=None):
        affect_row = self._execute(sql, params).rowcount
        self.commit()
        return affect_row

    def execute(self, sql, params=None):
        """
        check if connection is alive. if not, reconnect
        :param sql:
        :param params:
        :rtype Cursor:
        """
        logging.debug("execute sql: {}, with: \n ({})".format(sql, json.dumps(params)))
        self.cur.execute(sql, params)
        return self.commit()

    def _execute(self, sql, params=None):
        """
        check if connection is alive. if not, reconnect
        :param sql:
        :param params:
        :rtype Cursor:
        """
        try:
            logging.debug(params)
            logging.debug("execute sql: {}, with: \n ({})".format(sql, json.dumps(params)))
            self.cur.execute(sql, params)
        except pymysql.Error as e:
            logging.exception(e)
            raise e
        return self.cur

    def commit(self):
        """Commit a transaction (transactional engines like InnoDB require this)"""
        return self.conn.commit()

    def is_open(self):
        """Check if the connection is open"""
        return self.conn.open

    def end(self):
        """Kill the connection"""
        self.cur.close()
        self.conn.close()
        if self.ssh:
            self.ssh.close()

    @staticmethod
    def _serialize_insert(data):
        """Format insert dict values into strings"""
        keys = ','.join(data.keys())
        vals = ('%s,' * len(data))[0:-1]

        return [keys, vals]

    @staticmethod
    def _serialize_update(data):
        """Format update dict values into string"""
        fields = map(lambda k: '`%s`' % k, data.keys())
        return "=%s,".join(fields) + "=%s"

    def __enter__(self):
        return self

    def __exit__(self, error_type, value, traceback):
        if error_type:
            print(error_type, value, traceback)
        self.end()


def get_master_db():
    """

    :return SimpleMysql:
    """
    return SimpleMysql(host=config.get('DB.host'),
                       port=int(config.get('DB.port')),
                       database=config.get('DB.database'),
                       user=config.get('DB.user'),
                       password=config.get('DB.password'))


def get_rundeck_db(database=None):
    """
    获取rundeck数据库连接
    :return SimpleMysql:
    """
    return SimpleMysql(host=config.get('DB.host'),
                       port=int(config.get('DB.port')),
                       database=database or "rundeck",
                       user=config.get('DB.user'),
                       password=config.get('DB.password'))


def get_db(code=None, db_name_suffix=None):
    """
    获取项目的db对象
    :param str code:
    :param str db_name_suffix:
    :return SimpleMysql: SimpleMysql
    """
    db_config = get_db_config(code, db_name_suffix)
    if not db_config:
        return Exception('%s的rds配置不存在' % code)
    return SimpleMysql(host=db_config.get('host'), port=db_config['port'], database=db_config['database'],
                       user=db_config['user'], password=db_config['password'])


def get_data_db():
    """
    获取项目Data库
    :return:
    """
    return get_db(db_name_suffix='data')


def get_db_config(code=None, db_name_suffix=None):
    """
    获取DB配置
    :param code:
    :param db_name_suffix:
    :return:
    """
    if code is None or code == '':
        code = getattr(builtins, 'code')
    with get_master_db() as db:
        sql = 'SELECT r.host,r.port,p.db_name AS `database`,r.account AS `user` ,r.pwd AS `password` ' \
              'FROM project AS p ' \
              'LEFT JOIN rds AS r ON p.rds_id=r.id ' \
              'WHERE p.code=%s '
        db_config = db.query_one(sql, (code,))
    if db_config and db_name_suffix:
        db_config['database'] = str(db_config['database']) + '_' + db_name_suffix
    return db_config


def get_data_db_config():
    """
    获取项目data库配置
    :return:
    """
    return get_db_config(db_name_suffix='data')


def get_data(table_name, conditions, fields, multi_row=None, code=None, db_name_suffix=None):
    """
    获取表数据
    :param str table_name: 表名
    :param dict conditions: 条件
    :param list fields: 列名
    :param bool multi_row: 是否返回多行，默认返回单行
    :param str code: 项目code
    :param str db_name_suffix:数据库后缀
    :return:
    """
    if not conditions:
        return None
    sql = 'SELECT {col} FROM {table_name} ' \
          ''.format(col='`' + '`,`'.join(fields) + '`' if fields else '*',
                    table_name=table_name)
    sql += 'WHERE ' + ' AND '.join(['`{col}` = %({col})s'.format(col=col) for col in conditions.keys()])
    with get_db(code, db_name_suffix) as db:
        if multi_row:
            return db.query(sql, conditions)
        else:
            sql += ' LIMIT 1 '
            return db.query_one(sql, conditions)


def get_data_scalar(table_name, conditions, col_name, code=None, db_name_suffix=None):
    """
    获取第一行第一列数据
    :param str table_name:
    :param dict conditions:
    :param str col_name:
    :param str code: 项目code
    :param str db_name_suffix:数据库后缀
    :return:
    """
    if not conditions or not col_name:
        return None
    sql = 'SELECT `{col}` FROM {table_name} '.format(col=col_name, table_name=table_name)
    sql += 'WHERE ' + ' AND '.join(['`{col}` = %({col})s'.format(col=col) for col in conditions.keys()])
    sql += ' LIMIT 1 '
    with get_db(code, db_name_suffix) as db:
        return db.query_scalar(sql, conditions)
