#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""

    <NAME_EMAIL> on 2017/8/9.
"""
import socket
import struct
from pika import spec
from pika.adapters.blocking_connection import BlockingConnection
from pika.connection import ConnectionParameters
from pika.credentials import PlainCredentials
import datetime
import time

from components import config
from components.HTPClient.tlqapi import *


class RabbitMQ:
    def __init__(self, host=None, port=None, user=None, password=None, is_priority=False):
        self.host = host or config.get('RabbitMQ.host', self._loopback_address)
        self.port = port or int(config.get('RabbitMQ.port', 5672))
        self.user = user or config.get('RabbitMQ.user', 'guest')
        self.password = password or config.get('RabbitMQ.password', 'guest')
        self.vhost = config.get('RabbitMQ.vhost', 'vhost_tj_sj')
        self.priority_args = {"x-max-priority": 10} if bool(self.vhost != '/') or is_priority else None

    @property
    def _loopback_address(self):
        return socket.inet_ntoa(struct.pack('!I', socket.INADDR_LOOPBACK))

    def get_connection(self):
        """
        获取BlockingConnection
        :return:pika.adapters.blocking_connection.BlockingConnection
        """
        credentials = PlainCredentials(self.user, self.password)
        return BlockingConnection(parameters=ConnectionParameters(self.host, self.port,
                                                                  credentials=credentials, heartbeat=0, virtual_host=self.vhost))

    def send_message(self, queue, body, durable=None, headers=None, priority=1, is_priority=False):
        """
        发送消息
        :param str queue: 队列名称
        :param str body: 消息内容
        :param bool durable: 消息是否持久
        :param headers: 消息是否持久
        :param priority: 消息是否持久
        :param is_priority: 消息是否持久
        :return:
        """
        with self.get_connection() as connection:
            channel = connection.channel()
            channel.queue_declare(queue=queue, durable=durable or False, arguments=self.priority_args)
            properties = spec.BasicProperties()
            properties.headers = headers
            if is_priority:
                properties.priority = priority
            return channel.basic_publish(exchange='', routing_key=queue, body=body, properties=properties)

    def receive_message(self, queue, consumer_callback, durable=None):
        """
        接收消息
        :param str queue: 队列名称
        :param function consumer_callback:处理消息函数
        :param bool durable: 消息是否持久
        :return:
        """
        with self.get_connection() as connection:
            channel = connection.channel()
            channel.queue_declare(queue=queue, durable=durable or False, arguments=self.priority_args)
            channel.basic_qos(prefetch_count=1)
            channel.basic_consume(on_message_callback=consumer_callback, queue=queue, auto_ack=False)
            channel.start_consuming()

    def queue_delete(self, queue):
        """
        删除队列
        :param str queue:队列名称
        :return:
        """
        with self.get_connection() as connection:
            channel = connection.channel()
            channel.queue_delete(queue=queue)

    def get_consumer_count(self, queue):
        """
        获取队列总数
        :param queue:
        :return:
        """
        with self.get_connection() as connection:
            channel = connection.channel()
            this = channel.queue_declare(queue=queue, arguments=self.priority_args)
            return this.method.consumer_count


class TongHTP:

    def __init__(self, host=None, port=None, user=None, password=None, **kwargs):
        self.host = host or config.get('RabbitMQ.host', '127.0.0.1')
        self.port = port or int(config.get('RabbitMQ.port', 9888))
        self.user = user or config.get('RabbitMQ.user', 'guest')
        self.password = password or config.get('RabbitMQ.password', 'guest')
        self.vhost = config.get('RabbitMQ.vhost', 'vhost_tj_sj')
        self.err = TLQError()
        self._config = None
        self.client_id = '123456'
        self.address = f"tcp://{self.host}:{self.port}"
        self.group = 'dmp'

    @property
    def config(self):
        if self._config is None:
            self._config = TLQConfig()
        return self._config

    def producer_destory(self, config: TLQConfig, producer: TLQLightProducer, message: TLQMessage):
        if message.pData != None:
            TLQMessageDestroy(message, self.err)
        if producer.pData != None:
            TLQLightProducerDestroy(producer, self.err)
        if config.pData != None:
            TLQConfigDestroy(self.config, self.err)

    def consumer_destory(self, config: TLQConfig, consumer: TLQLightPullConsumer, multiMessage: TLQMultiMessage):
        if multiMessage.messageCount > 0:
            TLQMultiMessageDestroy(multiMessage, self.err)
        if consumer.pData != None:
            TLQLightPullConsumerDestroy(consumer, self.err)
        if config.pData != None:
            TLQConfigDestroy(self.config, self.err)

    def send_message(self, queue, body, durable=None, headers=None, priority=1, is_priority=False):
        producer = TLQLightProducer()
        message = TLQMessage()

        if isinstance(body, str):
            body = body.encode()

        data = create_string_buffer(body)

        ret = TLQConfigInit(self.config, self.err)
        if ret != 0:
            print("TLQConfigInit error")
            return -1

        ret = TLQConfigSetClientID(self.config, f"dmp_executor_producer_{self.client_id}", self.err)
        if ret != 0:
            print("TLQConfigSetClientID error. tlq_errno=%d, sys_errno=%d, self.errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.producer_destory(self.config, producer, message)
            return -1

        ret = TLQConfigSetManager(self.config, self.address, self.err)
        if ret != 0:
            print("TLQConfigSetManager error. tlq_errno=%d, sys_errno=%d, self.errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.producer_destory(self.config, producer, message)
            return -1

        if all([self.user, self.password]):
            ret = TLQConfigSetUsernamePassword(self.config, self.user, self.password, self.err)
            if ret != 0:
                print("TLQConfigSetUsernamePassword error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                self.producer_destory(self.config, producer, message)
                return -1

        ret = TLQLightProducerInit(self.config, producer, self.err)
        if ret != 0:
            print("TLQLightProducerInit error. tlq_errno=%d, sys_errno=%d, self.errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.producer_destory(self.config, producer, message)
            return -1

        ret = TLQLightProducerSetDomain(producer, self.vhost, self.err)
        if ret != 0:
            print("TLQLightProducerSetDomain error. tlq_errno=%d, sys_errno=%d, self.errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.producer_destory(self.config, producer, message)
            return -1

        ret = TLQLightProducerSetTopic(producer, queue, self.err)
        if ret != 0:
            print("TLQLightProducerSetTopic error. tlq_errno=%d, sys_errno=%d, self.errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.producer_destory(self.config, producer, message)
            return -1

        ret = TLQLightProducerStart(producer, self.err)
        if ret != 0:
            print("TLQLightProducerStart error. tlq_errno=%d, sys_errno=%d, self.errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.producer_destory(self.config, producer, message)
            return -1

        ret = TLQMessageInit(message, self.err)
        if ret != 0:
            print("TLQMessageInit error. tlq_errno=%d, sys_errno=%d, self.errstr=%s\n" %
                  (self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.producer_destory(self.config, producer, message)
            return -1

        ret = TLQMessageSetData(message, data, len(data)+1, self.err)
        if ret != 0:
            print("TLQMessageInit error. tlq_errno=%d, sys_errno=%d, self.errstr=%s\n" %
                  (self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.producer_destory(self.config, producer, message)
            return -1

        ret = TLQLightPutMessage(producer, message, self.err)
        if ret != 0:
            print("TLQLightPutMessage error. tlq_errno=%d, sys_errno=%d, self.errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.producer_destory(self.config, producer, message)
            return -1

        print("Put message success.")

        self.producer_destory(self.config, producer, message)

    def receive_message(self, queue, consumer_callback, durable=None):

        if self.query_topic(queue) == -1:
            self.create_topic(queue)

        consumer = TLQLightPullConsumer()
        multiMessage = TLQMultiMessage()
        data = c_char_p()
        length = c_int(0)

        ret = TLQConfigInit(self.config, self.err)
        if ret != 0:
            print("TLQConfigInit error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            return -1

        ret = TLQConfigSetClientID(self.config, self.client_id, self.err)
        if ret != 0:
            print("TLQConfigSetClientID error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.consumer_destory(self.config, consumer, multiMessage)
            return -1

        ret = TLQConfigSetManager(self.config, self.address, self.err)
        if ret != 0:
            print("TLQConfigSetManager error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.consumer_destory(self.config, consumer, multiMessage)
            return -1

        if all([self.user, self.password]):
            ret = TLQConfigSetUsernamePassword(self.config, self.user, self.password, self.err)
            if ret != 0:
                print("TLQConfigSetUsernamePassword error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                self.consumer_destory(self.config, consumer, multiMessage)
                return -1

        ret = TLQLightPullConsumerInit(self.config, consumer, self.err)
        if ret != 0:
            print("TLQLightPullConsumerInit error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.consumer_destory(self.config, consumer, multiMessage)
            return -1

        ret = TLQLightPullConsumerSetDomain(consumer, self.vhost, self.err)
        if ret != 0:
            print("TLQLightPullConsumerSetDomain error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.consumer_destory(self.config, consumer, multiMessage)
            return -1

        ret = TLQLightPullConsumerSetTopic(consumer, queue, self.err)
        if ret != 0:
            print("TLQLightPullConsumerSetTopic error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.consumer_destory(self.config, consumer, multiMessage)
            return -1

        ret = TLQLightPullConsumerSetGroup(consumer, self.group, self.err)
        if ret != 0:
            print("TLQLightPullConsumerSetGroup error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            self.consumer_destory(self.config, consumer, multiMessage)
            return -1

        err_sleep = 1
        normal_sleep = 0.01

        def consume():
            ret = TLQLightPullConsumerStart(consumer, self.err)
            if ret != 0:
                print("TLQLightPullConsumerStart error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                self.consumer_destory(self.config, consumer, multiMessage)
                return err_sleep

            # ret = TLQLightPullMultiRollbackMessage(consumer, 0, 1, multiMessage, self.err)
            ret = TLQLightPullMultiMessage(consumer, 1, multiMessage, self.err)
            if (ret == -1):
                print("TLQLightPullMultiMessage error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                return err_sleep

            if multiMessage.messageCount > 0:
                ret = TLQMessageGetData(multiMessage.messageArray[0], data, length, self.err)
                if ret == -1:
                    print("TLQMessageGetData error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                        self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                    return err_sleep
                print("Message data=", data.value, ",data length=", length.value, ",message count=",
                      multiMessage.messageCount)

                consumer_callback('', '', data.value)

                TLQLightPullConsumerAckMessage(consumer, self.err)

            print("Pull message count %d." % (multiMessage.messageCount))
            return normal_sleep

        while True:
            interval = consume()
            time.sleep(interval)

    def queue_delete(self, queue):

        manager = TLQManager()

        def destory(_config: TLQConfig, _manager: TLQManager):
            if _manager.pData != None:
                TLQManagerDestroy(_manager, self.err)
            if _config.pData != None:
                TLQConfigDestroy(_config, self.err)

        ret = TLQConfigInit(self.config, self.err)
        if ret != 0:
            print("TLQConfigInit error")
            return -1

        ret = TLQConfigSetClientID(self.config, self.client_id, self.err)
        if ret != 0:
            print("TLQConfigSetClientID error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
            self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            destory(self.config, manager)
            return -1

        ret = TLQConfigSetManager(self.config, self.address, self.err)
        if ret != 0:
            print("TLQConfigSetManager error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
            self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            destory(self.config, manager)
            return -1

        if all([self.user, self.password]):
            ret = TLQConfigSetUsernamePassword(self.config, self.user, self.password, self.err)
            if ret != 0:
                print("TLQConfigSetUsernamePassword error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                destory(self.config, manager)
                return -1

        ret = TLQManagerInit(self.config, manager, self.err)
        if ret != 0:
            print("TLQManagerInit error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
            self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            destory(self.config, manager)
            return -1

        ret = TLQManagerStart(manager, self.err)
        if ret != 0:
            print("TLQManagerStart error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
            self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            destory(self.config, manager)
            return -1

        ret = TLQManagerDeleteTopic(manager, queue, self.vhost, self.err)
        if ret != 0:
            print("TLQManagerDeleteTopic error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
            self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
            destory(self.config, manager)
            return -1

        print("Delete topic success.")
        destory(self.config, manager)
        return 0

    def get_consumer_count(self, queue):
        return 0

    def get_message_count(self, queue):
        return 0

    def purge_message_of_queue(self, queue):
        pass

    def query_topic(self, topic):
        err = TLQError()
        def destory(config: TLQConfig, manager: TLQManager):
            if manager.pData != None:
                TLQManagerDestroy(manager, err)
            if config.pData != None:
                TLQConfigDestroy(config, err)

        config = TLQConfig()
        manager = TLQManager()

        ret = TLQConfigInit(config, err)
        if ret != 0:
            print("TLQConfigInit error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            return -1

        ret = TLQConfigSetClientID(config, self.client_id, err)
        if ret != 0:
            print("TLQConfigSetClientID error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        ret = TLQConfigSetManager(config, self.address, err)
        if ret != 0:
            print("TLQConfigSetManager error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        if all([self.user, self.password]):
            ret = TLQConfigSetUsernamePassword(self.config, self.user, self.password, self.err)
            if ret != 0:
                print("TLQConfigSetUsernamePassword error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                destory(config, manager)
                return -1

        ret = TLQManagerInit(config, manager, err)
        if ret != 0:
            print("TLQManagerInit error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" %
                  (err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        ret = TLQManagerStart(manager, err)
        if ret != 0:
            print("TLQManagerStart error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        ret = TLQManagerQueryTopic(manager, topic, self.vhost, err)
        if ret != 0:
            print("TLQManagerQueryTopic error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        print("Query topic success.")
        destory(config, manager)
        return 0

    def create_topic(self, _topic):
        config = TLQConfig()
        manager = TLQManager()
        err = TLQError()

        def destory(config: TLQConfig, manager: TLQManager):
            if manager.pData != None:
                TLQManagerDestroy(manager, err)
            if config.pData != None:
                TLQConfigDestroy(config, err)

        ret = TLQConfigInit(config, err)
        if ret != 0:
            print("TLQConfigInit error")
            return -1

        ret = TLQConfigSetClientID(config, self.client_id, err)
        if ret != 0:
            print("TLQConfigSetClientID error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        ret = TLQConfigSetManager(config, self.address, err)
        if ret != 0:
            print("TLQConfigSetManager error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        if all([self.user, self.password]):
            ret = TLQConfigSetUsernamePassword(self.config, self.user, self.password, self.err)
            if ret != 0:
                print("TLQConfigSetUsernamePassword error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                    self.err.tlq_errno, self.err.sys_errno, self.err.errstr))
                destory(config, manager)
                return -1

        ret = TLQManagerInit(config, manager, err)
        if ret != 0:
            print("TLQManagerInit error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" %
                  (err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        ret = TLQManagerStart(manager, err)
        if ret != 0:
            print("TLQManagerStart error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        ret = TLQManagerCreateTopic(manager, _topic, self.vhost, 0, err)
        if ret != 0:
            print("TLQManagerCreateTopic error. tlq_errno=%d, sys_errno=%d, errstr=%s\n" % (
                err.tlq_errno, err.sys_errno, err.errstr))
            destory(config, manager)
            return -1

        print("Create topic success.")
        destory(config, manager)
        return 0


class MessageQueue:

    def __init__(self, *args, **kwargs):
        queue_type = config.get('RabbitMQ.type')
        if queue_type == 'tonghtp':
            self.queue_instance = TongHTP(*args, **kwargs)
        else:
            self.queue_instance = RabbitMQ(*args, **kwargs)

    def send_message(self, queue, body, **kwargs):
        return self.queue_instance.send_message(queue, body, **kwargs)

    def receive_message(self, queue, callback, **kwargs):
        return self.queue_instance.receive_message(queue, callback, **kwargs)

    def queue_delete(self, queue):
        return self.queue_instance.queue_delete(queue)

    def get_consumer_count(self, queue):
        return self.queue_instance.get_consumer_count(queue)
