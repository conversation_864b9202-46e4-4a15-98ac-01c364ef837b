#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
from datetime import date, datetime
from redis import ConnectionPool, StrictRedis
from redis import sentinel
from decimal import Decimal
import logging
from redis.asyncio.cluster import RedisCluster

from . import config
from .consts import REDIS_SENTINEL, REDIS_SINGLE, REDIS_STANDALONE, REDIS_CLUSTER


def conn():
    """
    获取redis cache连接
    :return:
    """
    return RedisCache()


def conn_custom_prefix(prefix):
    """
    获取redis  cache连接
    :return:
    """
    return RedisCache(prefix)


g_redis_connection_pool = None
g_sentinel_obj = None
g_redis_cluster_pool = None


def redis_mode():
    try:
        if config.get('Redis.mode') == 'sentinel':
            return REDIS_SENTINEL
        if config.get('Redis.mode') == 'cluster':
            return REDIS_CLUSTER
        else:
            return REDIS_STANDALONE
    except Exception as e:
        logging.error(f"error: {e}")
        return REDIS_STANDALONE


class RedisSentinelConnection(object):

    def __init__(self, connect_timeout=None):
        self.connect_timeout = (connect_timeout or 3)*1000

    @staticmethod
    def __parse_address(address):
        if ':' not in address:
            return address
        # 按逗号分割字符串，得到多个 "ip:port" 格式的字符串
        ip_port_list = address.split(',')

        # 解析每个 "ip:port" 字符串，将其拆分为 IP 和端口
        result = []
        for item in ip_port_list:
            ip, port = item.split(':')
            result.append((ip, int(port)))  # 将端口转换为整数

        return result

    def connect(self, db=None):
        addresses = self.__parse_address(config.get("Redis.addresses"))
        db = int(config.get('Redis.db', 0)) if db is None else 0
        svc_name = config.get("Redis.svc_name") or 'mymaster'

        global g_sentinel_obj

        if g_sentinel_obj is None:
            g_sentinel_obj = sentinel.Sentinel(
                addresses,
                db=db,  # 数据库，默认为0
                socket_timeout=self.connect_timeout,  # 超时时间,单位为毫秒
                # username=config.get('Redis.username') or '',
                password=config.get('Redis.password')
            )
        master_conn = g_sentinel_obj.master_for(svc_name, socket_timeout=self.connect_timeout)
        slave_conn = g_sentinel_obj.slave_for(svc_name, socket_timeout=self.connect_timeout)

        return master_conn, slave_conn


class RedisStandalonePoolConnection(object):

    def __init__(self, connect_timeout=None):
        self.connect_timeout = connect_timeout

    def connect(self, db=None):
        db = int(config.get('Redis.db', 0)) if db is None else 0
        global g_redis_connection_pool
        if g_redis_connection_pool is None:
            g_redis_connection_pool = ConnectionPool.from_url(
                url='redis://{username}:{password}@{host}:{port}/{db}'.format(
                    host=config.get("Redis.host"),
                    port=config.get('Redis.port'),
                    username=config.get('Redis.username') or '',
                    db=db,
                    password=config.get('Redis.password')
                ),
                socket_timeout=self.connect_timeout,
                max_connections=int(config.get('Redis.max_connections', 100) or 100),
            )
        return StrictRedis(connection_pool=g_redis_connection_pool)


class RedisSingleConnection(object):
    def __init__(self, connect_timeout=None):
        self.connect_timeout = connect_timeout

    def connect(self, db=None):
        return StrictRedis.from_url(
            url='redis://{username}:{password}@{host}:{port}/{db}'.format(
                host=config.get('Redis.host'),
                port=config.get('Redis.port'),
                username=config.get('Redis.username') or '',
                db=db,
                password=config.get('Redis.password') or ''
            ),
            socket_timeout=self.connect_timeout
        )


class RedisClusterConnection(object):
    def __init__(self, connect_timeout=None):
        self.connect_timeout = connect_timeout

    def connect(self, db=None):
        host = config.get("Redis.host")
        port = int(config.get('Redis.port'))
        # db = int(config.get('Redis.db', 0)) if db is None else 0

        return RedisCluster(
            host=host,
            port=port,
            username=config.get('Redis.username') or '',
            socket_timeout=self.connect_timeout,
            max_connections_per_node=True,
            max_connections=int(config.get('Redis.max_connections', 100) or 100),
            skip_full_coverage_check=True
        )


class RedisConnectionFactory(object):

    def __init__(self, connect_timeout=None):
        self.connect_timeout = connect_timeout or 10

    def create_connection(self, mode=None, db=None):
        if not mode:
            mode = redis_mode()
        if mode == REDIS_SENTINEL:
            connection = RedisSentinelConnection
        elif mode == REDIS_SINGLE:
            connection = RedisSingleConnection
        elif mode == REDIS_CLUSTER:
            connection = RedisClusterConnection
        else:
            connection = RedisStandalonePoolConnection
        return connection(self.connect_timeout).connect(db=db)


class RedisCache:
    def __init__(self, key_prefix=None, connect_timeout=None, mode=None):
        self.key_prefix = f'{config.get("Redis.prefix", "dp")}:'
        if key_prefix:
            self.key_prefix = f'{self.key_prefix}{key_prefix}:'
        self.connect_timeout = connect_timeout or 3
        host = config.get('Redis.host')
        port = config.get('Redis.port')
        if not host or not port:
            raise ValueError('缺少Redis缓存配置')

        factory = RedisConnectionFactory(connect_timeout)
        conns = factory.create_connection(mode=mode)

        if isinstance(conns, tuple):
            self._master_conn, self._slave_conn = conns[0], conns[-1]
        else:
            self._master_conn = self._slave_conn = conns

        self._connection = self._master_conn

    def pipe(self, transaction=True, shard_hint=None):
        """
        Return a new pipeline object that can queue multiple commands for
        later execution. ``transaction`` indicates whether all commands
        should be executed atomically. Apart from making a group of operations
        atomic, pipelines are useful for reducing the back-and-forth overhead
        between the client and server.
        :param transaction:
        :param shard_hint:
        :return: redis.StrictPipeline
        """
        return self._master_conn.pipeline(transaction, shard_hint)

    def _raw_key_exist(self, raw_key):
        return self._slave_conn.exists(raw_key)

    def _wrapper_key(self, key):
        if not key:
            raise ValueError('key')

        return '%s%s' % (self.key_prefix, key) if self.key_prefix else key

    @staticmethod
    def _dumps(value):
        return json.dumps(value, cls=ObjectEncoder)

    @staticmethod
    def _loads(uvalue):
        return json.loads(uvalue.decode("UTF-8"))

    def set_data(self, key, value):
        key = self._wrapper_key(key)
        return self._master_conn.set(key, self._dumps(value))

    def get_data(self, key):
        key = self._wrapper_key(key)
        payload = self._slave_conn.get(key)
        if not payload:
            return None
        return self._loads(payload)

    def del_data(self, key):
        key = self._wrapper_key(key)
        return self._master_conn.delete(key)

    def keys(self, pattern='*'):
        return self._slave_conn.keys(pattern=pattern)

    def set(self, key, value, time):
        if isinstance(value, (dict, list)):
            value = self._dumps(value)
        key = self._wrapper_key(key)
        return self._master_conn.setex(key, time=time, value=value)

    def get(self, key):
        key = self._wrapper_key(key)
        return self._slave_conn.get(key)

    def incr(self, key, amount=1):
        key = self._wrapper_key(key)
        return self._master_conn.incr(key, amount)

    def delete(self, key):
        return self.del_data(key)

    def add(self, key, value, time=None):
        key = self._wrapper_key(key)
        if self._master_conn.setnx(key, value) and time:
            self._master_conn.expire(key, time)
        return True

    def exists(self, key):
        key = self._wrapper_key(key)
        return self._slave_conn.exists(key)

    def expire(self, key, time):
        key = self._wrapper_key(key)
        return self._master_conn.expire(key, time)

    def setnx(self, key, value):
        key = self._wrapper_key(key)
        return self._master_conn.setnx(key, value)

    def set_nx_ex(self, key, value, ex=None, px=None, nx=False, xx=False):
        """
        Set the value at key ``key`` to ``value``

        ``ex`` sets an expire flag on key ``name`` for ``ex`` seconds.

        ``px`` sets an expire flag on key ``name`` for ``px`` milliseconds.

        ``nx`` if set to True, set the value at key ``name`` to ``value`` only
            if it does not exist.

        ``xx`` if set to True, set the value at key ``name`` to ``value`` only
            if it already exists.
        """
        key = self._wrapper_key(key)
        return self._master_conn.set(key, value, ex, px, nx, xx)

    def hget(self, name, key):
        name = self._wrapper_key(name)
        data = self._slave_conn.hget(name, key)
        if not data:
            return None
        return self._loads(data)

    def hgetall(self, name):
        name = self._wrapper_key(name)
        data = self._slave_conn.hgetall(name)
        if not data:
            return None

        return {k.decode('utf-8'): self._loads(v) for k, v in data.items()}

    def hset(self, name, key, value):
        """
        设置hash set
        :param name:
        :param key:
        :param value:
        :return: Returns 1 if HSET created a new field, otherwise 0
        """
        if value is None:
            raise ValueError('value')
        name = self._wrapper_key(name)
        return self._master_conn.hset(name, key, self._dumps(value))

    def ttl(self, key):
        if not key:
            raise ValueError('key')
        name = self._wrapper_key(key)
        return self._master_conn.ttl(name)

    def hdel(self, name, key):
        name = self._wrapper_key(name)
        return self._master_conn.hdel(name, key)

    def hmset(self, name, mapping):
        """
        批量设置hash set
        :param name:
        :param mapping: dict
        :return:
        """
        if not isinstance(mapping, dict):
            raise ValueError('mapping should be dict')
        key = self._wrapper_key(name)
        mapping = {k: self._dumps(v) for k, v in mapping.items()}
        return self._master_conn.hmset(key, mapping)

    def hmget(self, name, keys):
        if not isinstance(keys, (list, tuple)):
            raise ValueError('keys should be list or tuple')
        name = self._wrapper_key(name)
        data = self._slave_conn.hmget(name, keys)
        return [self._loads(item) if item else None for item in data]

    def lpush(self, name, *value):
        name = self._wrapper_key(name)
        data = [self._dumps(item) for item in value]
        return self._master_conn.lpush(name, *data)

    def lrange(self, name, start, stop):
        name = self._wrapper_key(name)
        data = self._slave_conn.lrange(name, start, stop)
        return [self._loads(item) if item else None for item in data]

    def scan(self, cursor=0, match=None, count=None):
        """
        扫描获取存在的key
        :param cursor: 游标位置
        :param match: 可用于模糊查询或过滤的匹配模式
        :param count:
        :return:
        """
        if not isinstance(cursor, int):
            raise ValueError('cursor should be int')
        if not isinstance(match, str):
            raise ValueError('cursor should be string')
        return self._slave_conn.scan(cursor, match, count)

    def hexists(self, name, key):
        """
        检查指定hash属性key是否存在
        :param name:  hash键名
        :param key: 属性名称
        :return:
        """
        name = self._wrapper_key(name)
        return self._slave_conn.hexists(name, key)

    def sadd(self, name, *member):
        """
        将一个或多个成员元素加入到集合中，已经存在于集合的成员元素将被忽略。
        :param name:
        :param member:
        :return:
        """
        name = self._wrapper_key(name)
        return self._master_conn.sadd(name, *member)

    def sismember(self, name, member):
        """
        判断成员元素是否是集合的成员
        :param name:
        :param member:
        :return:
        """
        name = self._wrapper_key(name)
        return self._slave_conn.sismember(name, member)

    def scard(self, name):
        """
        返回集合中元素的数量
        :param name:
        :return:
        """
        name = self._wrapper_key(name)
        return self._slave_conn.scard(name)

    def smembers(self, name):
        """
        返回集合中的所有的成员
        :param name:
        :return:
        """
        name = self._wrapper_key(name)
        set_list = self._slave_conn.smembers(name)
        return {item.decode("UTF-8") if item else None for item in set_list}


class ObjectEncoder(json.JSONEncoder):
    def default(self, o):  # pylint: disable=method-hidden
        if isinstance(o, Decimal):
            return float(o)
        elif isinstance(o, datetime):
            return o.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(o, date):
            return o.strftime('%Y-%m-%d')
        else:
            return json.JSONEncoder.default(self, o)
