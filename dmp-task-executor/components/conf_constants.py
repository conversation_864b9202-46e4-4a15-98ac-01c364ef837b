# 异步服务引擎：redis、rabbitmq。默认值为：rabbitmq
APP_CELERY_BROKER = 'rabbitmq'

# local 本地部署，cloud 云端部署（默认：cloud）
APP_DEPLOYMENT = 'cloud'

# 是否关闭直连数据集sql缓存，1表示关闭，0表示不关闭
CACHE_CLOSE_DIRECT_SQL_CACHE = 0

# 是否关闭角色权限缓存（0-不关闭缓存，1-关闭缓存）
CACHE_CLOSE_ROLE_PRIVILEGE_CACHE = 0

# 直连数据集缓存前缀
CACHE_DIRECT_DATASET_CACHE_PREFIX_KEY = 'direct_sql_prefix_key'

# 默认值7, 表示直连数据集在Redis中记录sql访问次数的过期天数
CACHE_DIRECT_DATASET_CACHE_VISIT_EXPIRE = 7

# 直连DB数据集sql的缓存条件，格式count,day|count2,day2，例如3,7|1,2，表示如果在（过去7天内访问超过3次）或（过去2天内访问超过1次），则对sql进行缓存，day最大不超过10。
CACHE_DIRECT_DATASET_SQL_CACHE_CONDITION = '3,7'

# 缓存时sql执行的超时时间
CACHE_DIRECT_DATASET_SQL_TIME_OUT = 60

# 已安装组件缓存key
CACHE_INSTALLED_COMPONENTS_CACHE_KEY = 'installed_components_v30_yq'

# 已发布报告的元数据缓存key(dashboard、flow、dataset会共用此key，修改前请沟通清楚)
CACHE_RELEASED_DASHBOARD_METADATA_CACHE_KEY = 'dmp_v4'

# 调度数据集访问次数/sql文本缓存前缀，必须配置，默认值 schedule_sql_prefix_key
CACHE_SCHEDULE_DATASET_CACHE_PREFIX_KEY = 'schedule_sql_prefix_key'

# 默认值7，表示调度数据集在Redis中记录sql访问次数的过期天数
CACHE_SCHEDULE_DATASET_CACHE_VISIT_EXPIRE = 7

# 缓存时sql执行的超时时间
CACHE_SCHEDULE_DATASET_SQL_TIME_OUT = 60

# 直连的sql数据集和api数据集中表个数限制（前端编辑报告时筛选器可选数据集）
DATASETCONFIG_FILTERABLE_TABLE_LIMIT = 20

# 支持配置保留的天数
DATASETCONFIG_MAX_DAY_REPLICATIONS = 0

# 最大数据集版本数量
DATASETCONFIG_MAX_VERSION_REPLICATIONS = 5

# 默认500，api调度数据集分页取数
DATASETCONFIG_PAGESIZE_RETRIEVE_API_DATASET = 500

# datax超时
EXTERNAL_DATAX_TIMEOUT = 600

# 表格组件取数返回的列数，默认或不设置时值为100
FUNCTION_CHART_COLUMN_LIMIT_NUM = 100

# 导入/分发报表包里面的图片上传处理并行线程数
FUNCTION_DASHBOARD_IMPORT_IMAGE_THREADS = 6

# 天眼报告同步查询sql条数limit，默认值  1500
FUNCTION_FAST_SYNC_BATCH_NUM = 1500

# 天眼报告同步线程数，默认值5
FUNCTION_FAST_SYNC_THREADS = 5

# 基础数据平台获取报告权限接口
INGRATEPLATFORM_REPORT_AUTHORIZE_URL = '/api/basicdata/CheckUserReportAuthorize'

# cookie名称，防止统一域名时，不同产品之间的cookie key冲突，可以调整
# APP_CUSTOM_COOKIE_TOKEN_NAME = 'mydp-token'
APP_CUSTOM_COOKIE_TOKEN_NAME = 'dap_token'

# cookie密钥，用于cookie中user信息的加密解密
JWT_COMPONENT_SECRET = 'YC2UFKz7'

# 8002
METRICS_SERVER_PORT = 18102

# 数芯api接口密钥
PPT_JWT_SECRET = 'LdGskFiUsuiqWYGxYBguihXzhARzntRF'

# 取数秘钥
PPT_SSO_SECRET = 'S4kTQxVqoAjlAzmMN39yz6Ymo7FABA'

# body超过10M不放入队列中
RABBITMQ_BODY_LIMIT = 10

# queue_name_datax
RABBITMQ_QUEUE_NAME_DATAX = 'dmp_datax'

# queue_name_datax_offline
RABBITMQ_QUEUE_NAME_DATAX_OFFLINE = 'dmp_datax_offline'

# 消息队列queue_name
RABBITMQ_QUEUE_NAME_DOWNLOAD = 'dmp_download'

# queue_name_flow
RABBITMQ_QUEUE_NAME_FLOW = 'dmp_flow'

# queue_name_flow_feeds
RABBITMQ_QUEUE_NAME_FLOW_FEEDS = 'dmp_flow_feeds'

# queue_name_flow_offline
RABBITMQ_QUEUE_NAME_FLOW_OFFLINE = 'dmp_flow'

# Flow-priority
RABBITMQ_QUEUE_NAME_FLOW_PRIORITY = 'dmp_flow_priority'

# 新流程配置
RABBITMQ_QUEUE_NAME_WORK_FLOW = 'dmp_work_flow'

# 默认值vhost_tj_sj， 历史环境需要配 /
RABBITMQ_VHOST = 'vhost_tj_sj'

# Redis.max_connections=100
REDIS_MAX_CONNECTIONS = 100

# 链接超时时间
REPORTCENTER_OPEN_PRINT_TIME_OUT = 3600

# 打印预览超时时间（秒）
REPORTCENTER_OPEN_PRINT_VIEW_TIME_OUT = 7200

# 简讯发送进程池进程数量
SUBSCRIBE_MAX_PROCESS = 1

# 简讯发送线程池进程数量
SUBSCRIBE_MAX_WORKERS = 10

# wt加密密钥
SUPERPORTAL_APP_SECRET = 'OTMwNWIxNTMyZTI0N2JmNTc4NGJlOTdh'

# 告警emal
XXL_JOB_ALARM_EMAIL = '<EMAIL>'

# 执行器超时时间
XXL_JOB_EXECUTOR_TIMEOUT = 10

# 默认执行器名称
XXL_JOB_DEFAULT_GROUP_APPNAME = 'dmp-task-executor'

# 分组名称
XXL_JOB_DEFAULT_GROUP_TITLE = 'dmp'

# 执行失败重试次数
XXL_JOB_EXECUTOR_FAIL_RETRY = 3

# 是否开始统一域名， 1：开启
DOMAIN_ONE_DOMAIN = '1'

# expires
JWT_EXPIRES = 315360000

# secret
JWT_SECRET = 'YC2UFKz7'

# webservice服务地址
LOGINWEBSERVER_SERVICE = 'https://mdcmip.mingyuanyun.com'

# method
LOGINWEBSERVER_METHOD = 'UserLoginForDomain'
# App.runtime
DEFAULT_RUNTIME_ENV = 'test'
# Component.component_endpoint
DEFAULT_COMPONENT_ENDPOINT = 'https://dmp-open.mypaas.com.cn/dmp/component/package/'
# Component.component_menuicon_endpoint
DEFAULT_COMPONENT_ICON_ENDPOINT = 'https://dmp-open.mypaas.com.cn/dmp/icons/component_menus/'
# Domain.dmp_dashboard_template
DEFAULT_TEMPLATE_CENTER_URL = 'https://dmp-tpl.mypaas.com.cn/templet-center'
# Domain.dmp_management
DEFAULT_DEVOPS_URL = 'https://devops-ops.mypaas.com.cn'
# Function.api_sql_complex
DEFAULT_API_COMPLEX_SQL_SWITCH = 0
# Function.api_sql_complex_limit
DEFAULT_API_SOMPLEX_SQL_LIMIT = 10000*10000*10000
# Function.enable_large_screen_move
DEFAULT_ENABLE_LARGE_SCREEN_MOVE = 0
# Grayscale.gray_env
IS_GRAY_ENV = 0
# JWT.dashboard_template_secret
DEFAULT_DASHBOARD_TEMPLATE_SECRET = 'uDAFcXK0'
# JWT.dmp_management_secret
DEFAULT_DMP_MANAGEMENT_SECRET = 'TT6$YBto9kzcFXT%'
# JWT.init_password_secret
DEFAULT_INIT_PASSWORD_SECRET = 'XAuTsZWy'
# LoginWebServer.AppSecret
DEFAULT_LOGIN_SERVER_SECRET = '0a55fb5adc954700a17eede0d57ce539'
# LoginWebServer.AppKey
DEFAULT_LOGIN_SERVER_KEY = 'e2224ebd74ec4348a8109cc2d3a13e01'
# MOP.domain
DEFAULT_MOP_DOMAIN = 'https://dmp-mop.mypaas.com.cn'
# Product.dashboard_snapshot_blacklist
DISABLE_DASHBOARD_SNAPSHOT_ENV ='fangkai-dmp-saas-hw-prod,dmp_yunke,yk-hw-dap-saas-new,dmp_myfuwu,bigdata-hw-prod,cy-saas-hw-bigdata,yk-kf-saas-prod,ycg-pulsar,woxiang_ailiyun,ywy-hw-saas'
# Product.record_getdata_api_log
RECORD_API_LOG = 0
# PublishCenter.secret
DEFAULT_PUBLISHCENTER_SECRET = 'S4kTQxVqoAjlAzmMN39yz6Ymo7FABA'
# Redis.celery_db
DEFAULT_CELERY_DB = 0
# Rundeck.port
DEFAULT_RUNDECK_PORT = 4441
# Rundeck.project_name
DEFAULT_RUNDECK_PROJECT_NAME = 'dmp'
# Rundeck.server
DEFAULT_RUNDECK_SERVER_NAME = 'dmp-rundeck4'
# Rundeck.token
DEFAULT_RUNDECK_TOKEN = 'bf6PqvUrwDdicTlsLPUvOEp44jHwRD71'
# SelfService.app_key
DEFAULT_SELFSERVICE_KEY = 5.70E+15
# SelfService.app_secret
DEFAULT_SELFSERVICE_SECRET = 'exvxiSOaQBSsZEuBQiuRVyZlTLnyLmQI'
# SelfService.host
DEFAULT_SELFSERVICE_HOST = 'https://bigdata-openapi-test.mypaas.com.cn'
# SelfService.is_enable_indicator_permission
SELFSERVICE_ENABLE_INDICATOR_PERMISSION = '0'
# SelfService.pulsar_project_code
SELFSERVICE_PULSAR_PROJECT_CODE = 'data_asstes'
# SelfService.self_redirect_url
SELFSERVICE_REDIRECT_URL= 'https://bigdata-test.myyscm.com/dmp/yfw/self-report-access-auth'
# SelfService.yl_api_secret
SELFSERVICE_YL_API_SECRET = 'lsjfsldfALDdlafLGALDdlALowilxA'
# App.name
APP_NAME = 'dmp'
ONE_DOMAIN = 1
# DBInstaller.saas_env_url
DBINSTALLER_SAAS_ENV_URL = 'https://dmp-admin.tj.mycyjg.com'
LOG_SLOW_LOG_SECONDS = 3
ENABLE_PROFILING = 0
# Log.level
LOG_LEVEL = 'ERROR'
# Log.sentry_dsn
LOG_SENTRY_DSN = ''