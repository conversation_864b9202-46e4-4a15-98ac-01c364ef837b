from celery import Celery
from .redis import redis_mode
from .consts import REDIS_SENTINEL


def get_backend(options=None, is_result=False):
    if options.get('App.celery_broker') == 'rabbitmq' and options.get('RabbitMQ.type') != 'tonghtp':
        backend = "{transport}://{user}:{password}@{host}:{port}/{vhost}".format(
            transport='amqp' if not is_result else 'rpc',
            user=options.get('RabbitMQ.user'),
            password=options.get('RabbitMQ.password'),
            host=options.get('RabbitMQ.host'),
            port=options.get('RabbitMQ.port'),
            vhost=options.get('RabbitMQ.vhost')
        )
    else:
        if redis_mode() == REDIS_SENTINEL:
            backend = "sentinel://{username}:{password}@{host}:{port}/{db}".format(
                password=options.get('Redis.password') or '',
                username=options.get('Redis.username') or '',
                host=options.get('Redis.host'),
                port=options.get('Redis.port'),
                db=options.get('Redis.celery_db') or 5,
            )
        else:
            backend = 'redis://{username}:{password}@{host}:{port}/{db}'.format(
                password=options.get('Redis.password') or '',
                username=options.get('Redis.username') or '',
                host=options.get('Redis.host'),
                port=options.get('Redis.port'),
                db=options.get('Redis.celery_db') or 5,
            )
    return backend


class CeleryAPP:
    def __init__(self, name, options):
        self.name = name
        self.broker = get_backend(options)
        self.backend = get_backend(options, True)
        self.app = Celery(self.name, backend=self.backend, broker=self.broker)
        self.app.conf.update(redis_scheduler_url=self.backend)
        if options.get('App.celery_broker') != 'rabbitmq' and redis_mode() == REDIS_SENTINEL:
            svc_name = options.get("Redis.svc_name") or "mymaster"
            self.app.conf.update(
                broker_transport_options={'master_name': svc_name},
                result_backend_transport_options={'master_name': svc_name},
            )
