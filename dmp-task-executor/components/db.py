#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    数据库基本操作
    <NAME_EMAIL> on 2017/3/14.
"""
import json
import logging
import socket
import struct
from urllib.parse import quote
from copy import deepcopy
import datetime
from sqlalchemy import create_engine, text, NullPool
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import OperationalError, ProgrammingError, DatabaseError
from sqlalchemy.engine.row import RowMapping
from sshtunnel import BaseSSHTunnelForwarderError, SSHTunnelForwarder
from loguru import logger

from components.redis import RedisCache
from components import config
from components.nacos_client import tenant_db_get, tenant_clean_db_get


class SimpleMysql:
    conn = None
    cur = None

    def __init__(self, **kwargs):
        """ construct """
        self.host = kwargs.get("host", "localhost")
        self.port = kwargs.get("port", 3306)
        self.database = kwargs.get("database")
        self.user = kwargs.get('user')
        self.password = kwargs.get('password')

        self.keep_alive = kwargs.get("keep_alive", False)
        self.charset = kwargs.get("charset", "utf8")
        self.autocommit = kwargs.get("autocommit", False)
        self.connect_timeout = kwargs.get('connect_timeout', 3)

        # ssh
        self.ssh = None
        self.use_ssh = kwargs.get('use_ssh', False)
        self.ssh_host = kwargs.get('ssh_host')
        self.ssh_port = kwargs.get('ssh_port', 22)
        self.ssh_user = kwargs.get('ssh_user')
        self.ssh_password = kwargs.get('ssh_password')

        self._check_args()
        # db_type
        self.db_type = kwargs.get('db_type') or config.get('DB.db_type') or ''

        self.engine = None

        self.connect()

    def _check_args(self):
        """ check args of structure"""
        if not self.database and self.db_type != 'DM':
            self.database = 'MySQL'
        if self.user is None or self.password is None:
            raise Exception(500, 'Incomplete configuration of db parameters')
        if self.use_ssh and (not self.ssh_host or not self.ssh_port or not self.ssh_user or not self.ssh_password):
            raise Exception(500, 'SSH parameter configuration incomplete')

    @property
    def _loopback_address(self):
        return socket.inet_ntoa(struct.pack('!I', socket.INADDR_LOOPBACK))

    def connect(self):

        """Connect to the mysql server"""
        logger.debug("connect db %s - %s", self.host, self.database)
        try:
            if self.use_ssh:
                try:
                    self.ssh = SSHTunnelForwarder(
                        (self.ssh_host, int(self.ssh_port)),
                        ssh_username=self.ssh_user,
                        ssh_password=self.ssh_password,
                        remote_bind_address=(self.host, int(self.port)),
                    )
                    self.ssh.start()
                except BaseSSHTunnelForwarderError:
                    logger.error('ssh connection failed ', exc_info=True)
                    self.ssh = None

            # if self.host == "rm-bp10jpberdfgm88e5.mysql.rds.aliyuncs.com":
            #     self.host = "rm-bp10jpberdfgm88e5vo.mysql.rds.aliyuncs.com"
            #     # self.host = "*********"
            #     self.port = 3306
            # elif self.host == "rm-bp1f9pc4yqu7v84x2.mysql.rds.aliyuncs.com":
            #     self.host = 'rm-bp1f9pc4yqu7v84x2mo.mysql.rds.aliyuncs.com'
            #     self.port = 3306
            # elif self.db_type == 'DM' and self.host == '************':
            #     self.host = '***********'
            #     self.port = 5238
            #     self.user = 'SYSDBA'
            #     self.passwd = 'SYSDBA'

            if self.db_type == 'DM':
                url = 'dm+dmPython://{user}:{passwd}@{host}:{port}/'.format(
                    user=quote(self.user),
                    passwd=quote(self.password),
                    host=self.host,
                    port=self.port,
                )
                connect_args = {
                    'local_code': 1,
                    'connection_timeout': 15,
                    'schema': self.database
                }
            else:
                url = 'mysql+pymysql://{user}:{passwd}@{host}:{port}/{db}?ssl_disabled=True'.format(
                    user=quote(self.user),
                    passwd=quote(self.password),
                    host=self.host,
                    port=self.port,
                    db=self.database,
                )
                connect_args = {}
            self.engine = create_engine(
                url=url,
                echo=int(config.get('DB.echo', 0)) if config.get('DB.echo', 0) else 0,
                # isolation_level='REPEATABLE_READ' if not self.autocommit else 'AUTOCOMMIT',
                connect_args=connect_args,
                poolclass=NullPool
            )
            self.conn = sessionmaker(bind=self.engine)()

        except Exception as e:
            raise Exception(
                "%{error}. host: {host}, port: {port}, user: {user}, pwd: {pwd}".format(
                    error=str(e),
                    host=self.host,
                    port=self.port,
                    user=self.user[:1] + '***',
                    pwd=self.password[:1] + '***',
                )
            )

    def _convert_to_json(self, cur_result, one=None):

        if not cur_result:
            return None if one else []
        cur_result = [cur_result] if one else cur_result

        cur = self.cur
        r = [dict((cur.description[i][0], value) for i, value in enumerate(row)) for row in cur_result]

        return (r[0] if r else None) if one else r

    @staticmethod
    def format_params(sql, params):
        copy_params = deepcopy(params)
        if isinstance(params, dict):
            for k, v in params.items():
                if isinstance(v, (list, tuple)):
                    sql = sql.replace(f"%%({k})s", f"(:{k})").replace(f"%({k})s", f"(:{k})")
                    kk_list = []
                    for index, i in enumerate(v):
                        kk = f"{k}_{index}"
                        copy_params[kk] = i
                        kk_list.append(kk)
                    del copy_params[k]
                    sql = sql.replace(f":{k}", ", ".join([f':{j}' for j in kk_list]))
                else:
                    if isinstance(v, (datetime.datetime, datetime.date)):
                        copy_params[k] = v.strftime('%Y-%m-%d %H:%M:%S')
                    sql = sql.replace(f"%({k})s", f":{k}")
        return sql, copy_params

    @staticmethod
    def adapter_dm(sql):
        sql = sql.replace('`', '"').replace('GROUP_CONCAT(', 'wm_concat(').replace('group_concat(', 'wm_concat(')
        return sql

    def query_scalar(self, sql, params=None):
        cur = self._execute(sql, params)
        result = cur.fetchone()
        result = self.row2dict(result) if result else result

        if result:
            return list(result.values())[0]
        return None

    def query_columns(self, sql, params=None):
        cur = self._execute(sql, params)
        result = [self.row2dict(row) for row in cur.fetchall()]

        if result:
            return [list(t.values())[0] for t in result]
        return None

    def query_one(self, sql, params=None):
        cur = self._execute(sql, params)
        result = cur.fetchone()
        return self.row2dict(result) if result else result

    def row2dict(self, row):
        if isinstance(row, RowMapping):
            row = dict(row)
        return row

    def query(self, sql, params=None, offset=None, limit=None):
        if offset is not None or limit is not None:
            sql += " LIMIT {}, {}".format(0 if offset is None else offset, limit)

        cur = self._execute(sql, params=params)
        items = [self.row2dict(row) for row in cur.fetchall()]
        if isinstance(items, tuple):
            return list(items)
        return items

    def fetch_data(self, cur):
        return [self.row2dict(row) for row in cur.mappings().fetchall()]

    def insert(self, table, data, commit=True, auto_audit=True):
        """
        新增一条记录
        :param auto_audit: 是否添加审计字段
        :param table: 表名
        :param data: dict()
        :param commit: bool 是否立即提交, 如果有多个处理需要事物提交，则最后调用commit()方法提交
        :return: 受影响的行数
        """
        data = self._format_data(data)
        if auto_audit and data and isinstance(data, dict):
            if not data.get('created_by'):
                data['created_by'] = 'task-executor'
            if not data.get('modified_by'):
                data['modified_by'] = 'task-executor'

        query = self._serialize_insert(data)

        sql = "INSERT INTO `%s` (%s) VALUES(%s)" % (table.strip('`'), query[0], query[1])

        affect_row = self._execute(sql, data, dict_cursor=False).rowcount
        if commit:
            self.commit()
        return affect_row

    def update(self, table, data, condition=None, commit=True, with_none=False):
        """
        更新记录
        :param table: 表名
        :param data: dict()
        :param condition: dict()
        :param commit: bool 是否立即提交, 如果有多个处理需要事物提交，则最后调用commit()方法提交
        :param with_none: bool 是否把none更新到数据库
        :return:
        """

        data = self._format_data(data, with_none)

        if data and isinstance(data, dict) and not data.get('modified_by'):
            data['modified_by'] = 'task-executor'
        query = self._serialize_update(data)
        sql = "UPDATE `%s` SET %s" % (table.strip('`'), query)

        params = deepcopy(data)
        if condition and len(condition) > 0:
            sql += " WHERE %s" % ' AND '.join([f'`{k}`= :{k}' for k, _ in condition.items()])
            params.update(condition)

        affect_row = self._execute(sql, params, dict_cursor=False).rowcount
        if commit:
            self.commit()
        return affect_row

    def delete_by_id(self, table, row_id, commit=True):
        """Delete rows based on a where condition"""
        sql = "DELETE FROM `%s` WHERE id='%s'" % (table.strip('`'), row_id)

        affect_row = self._execute(sql, dict_cursor=False).rowcount
        if commit:
            self.commit()
        return affect_row

    def delete(self, table, condition=None, commit=True):
        """Delete rows based on a where condition"""
        sql = "DELETE FROM `%s`" % table.strip('`')
        params = condition
        if condition and len(condition) > 0:
            sql += " WHERE %s" % ' AND '.join([f'`{k}`= :{k}' for k, _ in condition.items()])
            # params = tuple(condition.values())
        affect_row = self._execute(sql, params, dict_cursor=False).rowcount
        if commit:
            self.commit()
        return affect_row

    def exec(self, sql, params=None):
        affect_row = self._execute(sql, params, dict_cursor=False).rowcount
        self.commit()
        return affect_row

    def execute(self, sql, params=None):
        """
        check if connection is alive. if not, reconnect
        :param sql:
        :param params:
        :rtype Cursor:
        """
        logging.debug("execute sql: {}, with: \n ({})".format(sql, json.dumps(params)))
        self._execute(sql, params)
        return self.commit()

    def _execute(self, sql, params=None, dict_cursor=True):
        """
        check if connection is alive. if not, reconnect
        :param sql:
        :param params:
        :param dict_cursor:
        :rtype Cursor:
        """
        logger.debug("execute sql %s %r", sql, params)
        if not self.conn:
            self.connect()
        try:
            if self.db_type == 'DM':
                sql = self.adapter_dm(sql)
            sql, new_params = self.format_params(sql, params)
            cur = self.conn.execute(text(sql), new_params)
        except (OperationalError, ProgrammingError, DatabaseError) as e:  # pylint: disable=no-member
            # 转换成自定义异常类
            raise e

        return cur.mappings() if dict_cursor else cur

    def commit(self):
        """Commit a transaction (transactional engines like InnoDB require this)"""
        return self.conn.commit()

    def is_open(self):
        """Check if the connection is open"""
        return self.conn.is_active if self.conn else False

    def end(self):
        """Kill the connection"""
        if self.cur:
            self.cur.close()
        if self.conn:
            self.conn.close()
        if self.ssh:
            self.ssh.close()
        if self.engine:
            self.engine.dispose()


    @staticmethod
    def _serialize_insert(data):
        """Format insert dict values into strings"""
        keys = f"`{'`,`'.join(data.keys())}`"
        # vals = ('%s,' * len(data))[0:-1]
        vals = ', '.join([f':{k}' for k in data.keys()])

        return [keys, vals]

    @staticmethod
    def _serialize_update(data):
        """Format update dict values into string"""
        return ','.join([f'`{f}`=:{f}' for f in data.keys()])

    @staticmethod
    def _format_data(data, with_none=False):

        for k in list(data.keys()):
            v = data[k]
            if not with_none and v is None:
                data.pop(k)
                continue
            if v is not None and isinstance(v, object):
                data[k] = str(v)
        return data

    def __enter__(self):
        return self

    def __exit__(self, error_type, value, traceback):
        if error_type:
            print(error_type, value, traceback)
        self.end()


def get_master_db():
    """

    :return SimpleMysql:
    """
    return SimpleMysql(host=config.get('DB.host'),
                       port=int(config.get('DB.port')),
                       database=config.get('DB.database'),
                       user=config.get('DB.user'),
                       password=config.get('DB.password'),
                       db_type=config.get('DB.db_type', 'mysql'),
                       )


def get_db(code=None, db_name_suffix=None):
    """
    获取项目的db对象
    :param str code:
    :param str db_name_suffix:
    :return SimpleMysql: SimpleMysql
    """
    db_config = get_db_config(code, db_name_suffix)
    if not db_config:
        return Exception('%s的rds配置不存在' % code)
    return SimpleMysql(host=db_config.get('host'), port=db_config['port'], database=db_config['database'],
                       user=db_config['user'], password=db_config['password'], db_type=db_config['db_type'])


def _db_cache_key(code, suffix=''):
    if suffix:
        suffix = ':' + suffix
    return f'Project:DB:Config:{code}{suffix}'


def get_db_config(code=None, db_name_suffix=None):
    """
    获取DB配置
    :param code:
    :param db_name_suffix:
    :return:
    """

    if not code:
        raise ValueError('code参数不能为空')

    data_db_suffix = 'data'

    # data库
    if db_name_suffix == data_db_suffix:
        db_config = tenant_clean_db_get(code)
    else:
        db_config = tenant_db_get(code)

    return db_config


def get_data_db_config():
    """
    获取项目data库配置
    :return:
    """
    return get_db_config(db_name_suffix='data')


def get_data(table_name, conditions, fields, multi_row=None, code=None, db_name_suffix=None):
    """
    获取表数据
    :param str table_name: 表名
    :param dict conditions: 条件
    :param list fields: 列名
    :param bool multi_row: 是否返回多行，默认返回单行
    :param str code: 项目code
    :param str db_name_suffix:数据库后缀
    :return:
    """
    if not conditions:
        return None
    sql = 'SELECT {col} FROM {table_name} ' \
          ''.format(col='`' + '`,`'.join(fields) + '`' if fields else '*',
                    table_name=table_name)
    sql += 'WHERE ' + ' AND '.join(['`{col}` = %({col})s'.format(col=col) for col in conditions.keys()])
    with get_db(code, db_name_suffix) as db:
        if multi_row:
            return db.query(sql, conditions)
        else:
            sql += ' LIMIT 1 '
            return db.query_one(sql, conditions)


def get_data_scalar(table_name, conditions, col_name, code=None, db_name_suffix=None):
    """
    获取第一行第一列数据
    :param str table_name:
    :param dict conditions:
    :param str col_name:
    :param str code: 项目code
    :param str db_name_suffix:数据库后缀
    :return:
    """
    if not conditions or not col_name:
        return None
    sql = 'SELECT `{col}` FROM {table_name} '.format(col=col_name, table_name=table_name)
    sql += 'WHERE ' + ' AND '.join(['`{col}` = %({col})s'.format(col=col) for col in conditions.keys()])
    sql += ' LIMIT 1 '
    with get_db(code, db_name_suffix) as db:
        return db.query_scalar(sql, conditions)
