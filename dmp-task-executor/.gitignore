*.py[cod]
.DS_Store
# C extensions

# Packages
*.egg
*.egg-info
build
eggs
parts
var
sdist
develop-eggs
.installed.cfg
lib64
MANIFEST

# Installer logs
pip-log.txt
npm-debug.log
pip-selfcheck.json

# Unit test / coverage reports
.coverage
.tox
nosetests.xml
htmlcov
.cache

# Translations
*.mo

# Mr Developer
.mr.developer.cfg
.project
.pydevproject

# SQLite
test_exp_framework

# npm
node_modules/

# dolphin
.directory
libpeerconnection.log

# setuptools
dist

# IDE Files
atlassian-ide-plugin.xml
.idea/
*.swp
*.kate-swp
.ropeproject/

# Python3 Venv Files
.venv/
bin/
include/
pyvenv.cfg
venv/
runtime/
# Cython
*.c
src/*.c
*.env
.vscode/
.python-version
