FROM docker-cdv5ju.swr-pro.myhuaweicloud.com/global/base/python:3.9.17

RUN pip install --upgrade --no-cache-dir pip setuptools -i https://mirrors.aliyun.com/pypi/simple/ && \
    pip install --no-cache-dir supervisor==4.2.4 -i https://mirrors.aliyun.com/pypi/simple/ && \
    pip uninstall py -y && \
    rm -rf /var/lib/apt/lists && \
    echo "deb https://mirrors.aliyun.com/debian/ buster main non-free contrib" > /etc/apt/sources.list && \
    echo "deb-src https://mirrors.aliyun.com/debian/ buster main non-free contrib" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian-security buster/updates main" >> /etc/apt/sources.list && \
    echo "deb-src https://mirrors.aliyun.com/debian-security buster/updates main" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian/ buster-updates main non-free contrib" >> /etc/apt/sources.list && \
    echo "deb-src https://mirrors.aliyun.com/debian/ buster-updates main non-free contrib" >> /etc/apt/sources.list && \
    apt-get update && \
    apt-get upgrade -y --allow-unauthenticated && \
    apt-get install -y --allow-unauthenticated --no-install-recommends \
    ttf-wqy-microhei \
    ttf-wqy-zenhei \
    perl \
    libpq-dev \
    gconf-service \
    libasound2 \
    libatk1.0-0 \
    libc6 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libexpat1 \
    libfontconfig1 \
    libgcc1 \
    libgconf-2-4 \
    libgdk-pixbuf2.0-0 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libstdc++6 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxss1 \
    libxtst6 \
    ca-certificates \
    fonts-liberation \
    libappindicator1 \
    libnss3 \
    chromium \
    vim \
    curl \
    wget \
    default-mysql-client \
    procps \
    gnupg && \
    echo "deb http://nginx.org/packages/debian/ buster nginx">> /etc/apt/sources.list && \
    echo "deb-src http://nginx.org/packages/debian/ buster nginx">> /etc/apt/sources.list && \
    wget http://nginx.org/keys/nginx_signing.key && apt-key add nginx_signing.key && \
    apt-get update -y && apt-get upgrade -y --allow-unauthenticated && apt-get install -y  --allow-unauthenticated --no-install-recommends nginx && \
    rm -rf /var/lib/apt/lists