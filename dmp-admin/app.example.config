[App]
name = dmp-admin
runtime = test

[DB]
host = **********
port = 3306
database = dmp_config
user = dev
password = Mysoft123
flow_log_database = dmp_flow_log

[DBInstaller]
mode = rds
project_db_prefix = dmp
project_empty_db = dmp_empty
project_data_db_suffix = data
rds_access_key_id = LTAIx7Mlk3UU6m5b
rds_access_key_secret = zfFftIWSkiDCLuYCF9SK3o0qGrON0u

[Email]
account = <EMAIL>
password = mysoft&DMP20170525!
smtp_server = smtp.exmail.qq.com
smtp_port = 465
smtp_enable_ssl = 1
name = 明源云大数据平台

[JWT]
secret = 0UZR4h5T
expires = 7200

[Log]
level = ERROR
format = %%(asctime)s %%(filename)s[line:%%(lineno)d] %%(exc_text)s %%(message)s
handler = console
slow_log_seconds = 3
sentry_dsn =

[LoginWebServer]
service = http://ekp.mingyuanyun.com/EKPWebService/AdUserService.asmx?wsdl
method = UserLoginForDomain

[OCS]
host = cca583c3937d41aa.m.cnszalist3pub001.ocs.aliyuncs.com
port = 11211
user =
password =

[ODPS]
endpoint = http://odps-ext.aliyun-inc.com/api
tunnel_endpoint = http://dt-ext.nu16.odps.aliyun-inc.com

[OSS]
endpoint = http://oss-cn-shenzhen.aliyuncs.com
access_key_id = tWm3FAtuOxwKQu8h
access_key_secret = doVpcmdr0DbKAxXnFqC1XXSmOSF6Pd
bucket = dmp-test

[RabbitMQ]
host = **********
port = 5672
user = guest
password = guest
queue_name_flow = Flow
queue_name_datax = DataX

[Security]
dmp_crypt_key = 7-J.\|S&w#1!Wc%%,

[Kong]
admin_api = http://localhost:8001

[Redis]
host=localhost
port=6379
db=0
password=
