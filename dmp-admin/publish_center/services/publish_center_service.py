import base64
import io
import json
import os
import traceback
import zipfile
from datetime import datetime

import jwt
import requests
from loguru import logger

from base import repository
from components.fast_logger import FastLogger
from components.oss import OSSFileProxy
from dmplib.components.app_hosts import AppHosts
from dmplib.components.enums import SkylineApps
from dmplib.hug import g
from dmplib.redis import RedisCache
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from issue_dashboard.services import deliver_service, import_simple_file

from urllib import parse, request

def push_product(**kwargs):
    try:
        fast_log = FastLogger.PublishCenterFastLogger(**kwargs)
    except Exception as e:
        logger.debug(str(e))
    try:
        oss_url = kwargs.get('oss_url')
        data = {}
        if oss_url:
            logger.error(f"file oss Url ： {oss_url}")
            # url = parse.quote(oss_url, safe=':/?=&')
            # temp_file = request.urlopen(url)
            # file = io.BytesIO(temp_file.read())
            file = deliver_service.get_data_from_url(oss_url)
            # file = io.BytesIO(file)
        else:
            file_data = base64.b64decode(kwargs.get('file'))
            file = io.BytesIO(file_data)
        tenant_code = kwargs.get('tenantCode')
        tenant_type = kwargs.get('tenantType')
        task_id = kwargs.get('taskId')
        app_code = kwargs.get('appCode')
        app_key = kwargs.get('appKey')
        version = kwargs.get('version')
        env_code = kwargs.get('envCode')
        customer_guid = kwargs.get('customerGuid')
        force_update = kwargs.get('force_update')
        if tenant_type == "TEMPLATE":
            logger.info("更新模版库:{0},{1},{2}".format(task_id, app_code, version))
            return save_template(file, task_id, app_code, app_key, version, env_code, customer_guid)
        elif tenant_type == "PUBLIC":
            return True, '', {}
        elif tenant_type == "TENANT":
            # 数芯定制化跳过更新
            if not force_update:
                try:
                    category = judge_tenant_is_custom(tenant_code)
                    if category == 1:
                        logger.error(f'[{tenant_code}]数芯接口返回是定制空间，不更新!')
                        return True, 'ok', '数芯接口返回是定制空间，不更新!'
                except Exception as e:
                    logger.error(f"调用数芯接口失败，使用原来的判断逻辑，原因： {str(e)}")
                    contain_sxdzyy = is_contain_sxdzyy(tenant_code)
                    if not check_tenant_app_is_1_5(app_code, tenant_code) or contain_sxdzyy:
                        msg = "租户产品非1.5版本，忽略更新：{0},{1},{2},{3}".format(task_id, app_code, tenant_code, version)
                        if contain_sxdzyy:
                            msg = "租户已包含数芯定制应用，跳过更新{0},{1},{2},{3}".format(task_id, app_code, tenant_code, version)
                        logger.error(msg)
                        set_fast_log_info(fast_log, msg, 'DEBUG')
                        return True, 'ok', msg
            logger.info("更新租户库：{0},{1},{2},{3}".format(task_id, app_code, tenant_code, version))
            return save_tenant(file, tenant_code, app_code, task_id, app_key, fast_log)

    except Exception as e:
        traceback.print_stack()
        set_fast_log_info(fast_log, traceback.format_exc(), 'ERROR')
        raise UserError(code=400, message=str(e))
    finally:
        if fast_log and fast_log.error_level:
            fast_log.record()

    return data


def judge_tenant_is_custom(tenant_code):
    """
    调用数芯的接口判断租户是否是定制空间
    """
    dap_domain  = AppHosts.get(SkylineApps.DAP, inside=True)
    secret = 'XkVKzY5tcGsCS7Au'
    token = jwt.encode({'tenant_code': tenant_code}, secret, algorithm='HS256')
    if isinstance(token, bytes):
        token = token.decode()
    headers = {
        'Authorization': f'Bearer {token}',
    }
    kwargs = {
        'url': f'{dap_domain}/api/common/publish_center/project',
        'headers': headers,
        'timeout': 10,
    }
    response = requests.get(**kwargs)
    logger.error(f"获取数芯获取空间信息： {response.content.decode()}")
    # 1： 定制空间，2：公共空间
    res = response.json()
    if not res.get('result'):
        raise UserError(res.get('msg'))
    category = (res.get('data') or {}).get('category')
    return category

if __name__ == '__main__':
    a= judge_tenant_is_custom('gzjgys')
    print(a)


def is_contain_sxdzyy(tenant_code):
    from project.services.template_sync_service import get_completed_record_appcodes
    exists_code = get_completed_record_appcodes(tenant_code)
    if not exists_code:
        return False
    return "sxdzyy" in exists_code


def set_fast_log_info(fast_log, msg, log_level, oss_url=''):
    if fast_log and msg:
        fast_log.error_traceback = msg
    if fast_log and log_level:
        fast_log.error_level = log_level
    if fast_log and oss_url:
        fast_log.extra_info = json.dumps({"oss_url": oss_url}, ensure_ascii=False)


def check_tenant_app_is_1_5(app_code, tenant_code):
    return repository.data_is_exists('dap_bi_project_tags_relation',
                                     {'project_code': tenant_code, 'tag_code': app_code,
                                      'is_data_cloud_1_5_enabled': '1'})


def save_template(all_zip_file, task_id, app_code, app_key, version, env_code, customer_guid):
    with zipfile.ZipFile(all_zip_file, "r") as zip_file:
        json_files = [name for name in zip_file.namelist() if name.endswith('.zip')]
        for file_name in json_files:
            with zip_file.open(file_name) as file:
                name = os.path.basename(file.name)
                db_type = os.path.splitext(name)[0]
                target_env_code = get_target_env_code(env_code)
                record = save_template_record(file, task_id, app_code, app_key, version,
                                              db_type, target_env_code, customer_guid, env_code)
                row = repository.data_is_exists("dap_bi_publish_center_template",
                                                {"app_code": app_code, "db_type": db_type,
                                                 "customer_guid": customer_guid, "env_code": target_env_code})
                if row:
                    repository.update_data("dap_bi_publish_center_template", {
                        'task_id': record.get("task_id"),
                        'version': record.get("version"),
                        'app_key': record.get("app_key"),
                        'file_url': record.get("file_url"),
                        'db_type': record.get("db_type"),
                        'source_env_code': record.get("source_env_code")
                    }, {'app_code': app_code, "db_type": db_type,
                        "customer_guid": customer_guid, "env_code": target_env_code})
                else:
                    repository.add_data('dap_bi_publish_center_template', record)
    return True, ''


def save_template_record(file, task_id, app_code, app_key, version, db_type, env_code, customer_guid, source_env_code):
    file_name = 'publish_center_template/{0}/{1}_{2}_{3}.zip'.format(source_env_code, app_code, db_type, version)
    oss_url = OSSFileProxy().upload(file, file_name=file_name, return_intranet_url=True)
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    record = {
        'id': seq_id(),
        'task_id': task_id,
        'app_code': app_code,
        'app_key': app_key,
        'version': version,
        'db_type': db_type,
        'created_on': current_time,
        'modified_on': current_time,
        'file_url': oss_url,
        'env_code': env_code,
        'customer_guid': customer_guid,
        'source_env_code': source_env_code
    }
    repository.add_data('dap_bi_publish_center_template_record', record)
    return record

def save_tenant(file, tenant_code, app_code, task_id, app_key, fast_log=None):

    projects = [tenant_code]

    file_name = 'publish_center_template/' + app_code + '_' + tenant_code + '_' + task_id + '.zip'
    oss_url = OSSFileProxy().upload(file, file_name=file_name, return_intranet_url=True)
    set_fast_log_info(fast_log, '', '', oss_url)

    if not repository.data_is_exists("dap_p_tenant", {'code': tenant_code}):
        msg = "更新失败，租户不存在:{0},{1},{2},{3}".format(task_id, app_code, app_key, tenant_code)
        set_fast_log_info(fast_log, msg, "DEBUG")
        logger.error(msg)
        return True, 'ok'

    file.seek(0)
    export_data = import_simple_file.parse_zip_byte(file.read())

    if not export_data:
        msg = "更新失败，数据包内容为空:{0},{1},{2},{3}".format(task_id, app_code, app_key, tenant_code)
        set_fast_log_info(fast_log, msg, "DEBUG")
        logger.error(msg)
        return True, 'ok'

    title = '更新服务推送-{0}-{1}'.format(app_code,app_key)
    description = title
    source_project = export_data.get('source_project')
    source_project_id = export_data.get('source_project_id') or ''
    version = export_data.get('version')

    # 处理历史版本问题
    if not version or (version != "v1.0.0.0" and version != "v1.5.0.0"):
        msg = "模板文件已过期，请重新导出后再导入:{0},{1},{2},{3}".format(task_id, app_code, app_key, tenant_code)
        set_fast_log_info(fast_log, msg, "DEBUG")
        logger.error(msg)
        return True, 'ok'

    # 导入数据查看
    imporotData = repository.get_data('dap_bi_datacloud_public_import_dist',{'rdc_task_id':task_id},['id','rdc_task_id','tenant_code','package_type'])
    if not imporotData:
        msg = "更新失败，没有导入记录:{0},{1},{2},{3}".format(task_id, app_code, app_key, tenant_code)
        set_fast_log_info(fast_log, msg, "DEBUG")
    is_custom_package = 0
    if imporotData and imporotData.get('package_type') == '定制包':
        is_custom_package = 1

    # 分发
    params = {
        "export_id": task_id,
        "title": title,
        "description": description,
        "source_url": oss_url,
        "source_project": source_project,
        "source_project_id": source_project_id,
        "dest_projects": projects,
        "is_all_projects": 0,
        "distribute_type": 0 if is_custom_package else 1, # 大屏、仪表板导入不锁定
        "replace_data_source": 0,
        "operate_type": 1,
        "is_lock_dataset": 0 if is_custom_package else 1, # 数据集导入定制包不锁定
        "is_publish_center": 1
    }
    deliver_id = deliver_service.insert_deliver(**params)
    export_data["app_code"] = app_code
    try:
        args = {'deliver_id': deliver_id, 'distribute_type': params.get('distribute_type'), 'is_lock_dataset': params.get('is_lock_dataset'), 'is_lock_application': 0,
                "userid": g.account, 'is_async': True, 'export_data': export_data}
        deliver_service.deliver_split(**args)
    except BaseException as e:
        traceback.print_stack()
        logger.exception(e)
        set_fast_log_info(fast_log, traceback.format_exc(), "ERROR")
        deliver_service.update_deliver_status(deliver_id, '失败', ','.join(projects), '写入队列失败')
        return False, e

    return True, 'ok'


def get_target_env_code(source_env_code):
    all_mapping = get_publish_center_env_code_mapping()
    if all_mapping.get(source_env_code):
        return all_mapping.get(source_env_code)
    else:
        return source_env_code


def get_publish_center_env_code_mapping():
    data_list = repository.get_data_by_sql("select * from dap_bi_publish_center_env_code_mapping", {})
    mapping_map = {}
    for d in data_list:
        mapping_map[d.get('source_env_code')] = d.get('target_env_code')
    return mapping_map
