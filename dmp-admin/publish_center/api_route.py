import base64
import traceback

import hug
import jwt

from dmplib.hug import APIWrapper, g
from dmplib import config
from hug.authentication import authenticator

from issue_dashboard.services.import_simple_file import parse_file_and_save_content, parse_full_file_and_save_content, has_save_file_content
from publish_center.services import publish_center_service


@authenticator
# pylint: disable=W0613
def _verify_auth_key_handle(request, response, verify_user, **kwargs):
    """

    :param request:
    :param response:
    :return:
    """
    token = request.auth if request.auth else request.get_header('Authorization')
    if not token:
        print("获取Authorization失败")
        return False

    try:
        secret = 'aijqgdgNQII2h7ZyHemQmzQ7fldsNXbVb2bMzkjT8otxRMhrI4R2BUamDn1XzM6n'
        data = jwt.decode(token, secret, algorithms='HS256', options={'verify_signature': False})
        if not data:
            return False
        if not data.get('tenant'):
            return False
    except:
        traceback.print_exc()
        return False

    g.account = 'publish_center'
    return True


class PublishCenterAPIWrapper(APIWrapper):
    __slots__ = ['_route', '_publish_center_route']

    def __init__(self, name):
        super().__init__(name)
        self._route = None
        self._publish_center_route = None
        self.api.http.base_url = '/publish_center'

    @property
    def publish_center_route(self):
        if not self._publish_center_route:
            self._publish_center_route = hug.http(api=self.api, requires=_verify_auth_key_handle(None))
        return self._publish_center_route


api = PublishCenterAPIWrapper(__name__)


@api.publish_center_route.post('/push_product')
def push_product(**kwargs):
    """
    推送产品
    """
    return publish_center_service.push_product(**kwargs)


@api.publish_center_route.post('/save_full_file_content')
def save_full_file_content(**kwargs):
    # 保存解析的文件内容
    task_id = kwargs.get('task_id')
    app_code = kwargs.get('app_code')
    return parse_full_file_and_save_content(task_id, app_code)

@api.publish_center_route.post('/save_file_content')
def save_file_content(**kwargs):
    # 保存解析的文件内容
    task_id = kwargs.get('task_id')
    file_url = kwargs.get('file_url')
    app_code = kwargs.get('app_code')
    return parse_file_and_save_content(task_id,app_code, file_url)

@api.publish_center_route.post('/has_save_file_content')
def save_file_content(**kwargs):
    # 保存解析的文件内容
    task_id = kwargs.get('task_id')
    if not task_id:
        return False, '缺少任务id', None
    return True, 'ok', has_save_file_content(task_id)


@api.publish_center_route.post('/check_tenant_appcode')
def check_tenant_appcode(**kwargs):
    # 保存解析的文件内容
    from publish_center.services.publish_center_service import check_tenant_app_is_1_5

    app_code = kwargs.get('app_code')
    tenant_code = kwargs.get('tenant_code')
    if not tenant_code or not app_code:
        return False, '缺少app_code或tenant_code', None
    if str(app_code) in ['4481', '4482']:
        # 默认放开数见、数芯两个测试应用
        return True, 'ok', 1
    else:
        return True, 'ok', check_tenant_app_is_1_5(app_code, tenant_code)
