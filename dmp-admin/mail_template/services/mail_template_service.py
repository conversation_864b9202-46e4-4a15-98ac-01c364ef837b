#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    
    <NAME_EMAIL> on 2017/12/8.
"""
from base import repository
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from mail_template.models import EmailTemplateModel
from mail_template.repositories import mail_template_repository


def get_mail_template_list(query_model):
    """
    获取邮件模板列表
    :param mail_template.models.EmailTemplateQueryModel query_model:
    :return: mail_template.models.EmailTemplateQueryModel
    """
    return mail_template_repository.get_mail_template_list(query_model)


def get_mail_template(mail_template_id):
    if not mail_template_id:
        raise UserError(message='缺少邮件模板ID')

    fields = list(EmailTemplateModel().get_dict().keys())
    data = repository.get_data('dap_bi_email_template', {'id': mail_template_id}, fields)
    if not data:
        raise UserError(message='邮件模板不存在')

    mail_template = EmailTemplateModel(**data)
    return mail_template


def add_mail_template(model):
    """
    添加邮件模板
    :param mail_template.models.EmailTemplateModel model:
    :return: mail_template.models.EmailTemplateModel
    """
    model.id = seq_id()
    model.form_mode = 'add'
    model.type = int(repository.get_data_max_rank('dap_bi_email_template', 'type'))
    model.validate()
    fields = list(EmailTemplateModel().get_dict().keys())
    result = repository.add_model('dap_bi_email_template', model, fields)
    if not result:
        raise UserError(message='添加邮件模板失败')
    return model


def _validate_key(value):
    if not value:
        return
    key_list = ['script', 'svg', 'alert', 'confirm', 'prompt', 'onload', 'onmouseover', 'onfocus', 'onerror', 'xss', 'ontoggle', ' on']
    for k in key_list:
        if k in value:
            raise UserError(message=f"不合法的脚本: {k}")


def update_mail_template(model):
    """
    更新邮件模板
    :param model:
    :return:
    """
    model.form_mode = 'update'
    model.validate()
    _validate_key(model.content)
    if not repository.data_is_exists('dap_bi_email_template', {'id': model.id}):
        raise UserError(message='邮件模板不存在')
    fields = list(EmailTemplateModel().get_dict().keys())
    return repository.update_model('dap_bi_email_template', model, {'id': model.id}, fields)
