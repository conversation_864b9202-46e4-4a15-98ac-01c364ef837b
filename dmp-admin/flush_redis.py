#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import traceback

os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), 'app.config')
os.environ['DMP_ROOT_PATH'] = os.path.dirname(__file__)

import sys
from dmplib import config
from dmplib.redis import RedisCache
import logging

logger = logging.getLogger(__name__)

FUNCTION_KEY = 'funcs'
FUNC_ACTION_KEY = 'funcs_with_actions'


class FlushRedisHandler:
    def __init__(self):
        self.cur_cache_prefix = config.get("Cache.released_dashboard_metadata_cache_key")
        self.cur_component_cache_prefix = config.get("Cache.installed_components_cache_key")
        self.version_key = "1.0.0"
        self.scan_cnt = 1000
        self.conn = RedisCache()

    def _exec_delete(self, cur, match_keys, pattern):
        logger.exception("当前缓存游标位移值: {0}，待删除键数量：{1}".format(cur, len(match_keys)))
        if match_keys:
            for key in match_keys:
                self.conn.delete(key)
        if cur:
            next_cur, next_keys = self.conn.scan(cursor=cur, match=pattern, count=self.scan_cnt)
            self._exec_delete(next_cur, next_keys, pattern)

    def _delete(self, cache_prefix):
        pattern = "*:{cache_prefix}:*".format(cache_prefix=cache_prefix)
        try:
            cur, match_keys = self.conn.scan(match=pattern, count=self.scan_cnt)
            while cur > 0:
                logger.exception("当前缓存游标位移值: {0}，待删除键数量：{1}".format(cur, len(match_keys)))
                if match_keys:
                    for key in match_keys:
                        self.conn.delete(key)
                cur, match_keys = self.conn.scan(cursor=cur, match=pattern, count=self.scan_cnt)
            # self._exec_delete(cur, match_keys, pattern)
        except Exception:
            pass

    def flush(self):

        # 报告及数据集相关缓存
        if self.cur_cache_prefix:
            self._delete(self.cur_cache_prefix)

        # 组件缓存
        if self.cur_component_cache_prefix:
            self._delete(self.cur_component_cache_prefix)

    def flush_func(self):
        # 菜单缓存
        try:
            self._delete(FUNCTION_KEY)
            self._delete(FUNC_ACTION_KEY)
        except Exception as err:
            logger.exception("清理菜单缓存异常： {}".format(str(err)))

    def get_version(self):
        return self.conn.get_data(self.version_key) or ""

    def set_version(self, version):
        return self.conn.set_data(self.version_key, version)


if __name__ == "__main__":
    cur_devops_version = ""
    args = sys.argv
    if len(args) > 0:
        cur_devops_version = args[1]
    if cur_devops_version:
        try:
            handler = FlushRedisHandler()
            handler.flush_func()
            cache_devops_version = handler.get_version()
            logger.exception(msg="清理redis缓存内容，传入版本号：{0}，缓存版本号：{1}".format(cur_devops_version, cache_devops_version))
            if cur_devops_version != cache_devops_version:
                handler.flush()
                handler.set_version(cur_devops_version)
                logger.exception(msg="完成清理redis缓存内容")
        except Exception as e:
            logger.exception(msg="清理redis缓存内容异常：{}".format(str(e)))
