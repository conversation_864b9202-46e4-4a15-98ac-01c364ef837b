# -*- coding:utf-8 -*-
# coding=UTF8


import os
from subprocess import getstatusoutput
import requests

KONG_SCRIPT_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'kong_init_script/kong_init.sh')


class KongScriptInitor:

    def __init__(self):
        self.kong_check_timeout = 60
        self.kong_host = self._get_kong_host()

    def _get_kong_host(self):
        # 现在默认使用kong2
        env_host = os.environ.get('KONG_HOST')
        if env_host:
            return env_host
        else:
            return 'dmp-kong2'

    def kong_is_ok(self):
        try:
            requests.get(f'http://{self.kong_host}:8001', timeout=self.kong_check_timeout)
        except requests.ConnectTimeout:
            raise SystemExit('未检测到Kong服务，退出注册Kong脚本')
        except Exception as e:
            raise SystemExit(f'注册Kong脚本其他未知错误，退出。 错误: {str(e)}')

    def run_with_terminal(self):
        sh = f'bash {KONG_SCRIPT_PATH}'
        status, content = getstatusoutput(sh)
        return status, content

    # def get_sh_content(self):
    #     with open(KONG_SCRIPT_PATH, 'r') as f:
    #         return f.read()

    def run(self):
        self.kong_is_ok()
        print('>>> 开始注册Kong脚本')
        status, content = self.run_with_terminal()
        print('>>> Kong脚本执行状态：', status)
        print('>>> Kong脚本执行返回：', content)
        print('>>> 完成注册Kong脚本')


if __name__ == '__main__':
    ksi = KongScriptInitor()
    ksi.run()
