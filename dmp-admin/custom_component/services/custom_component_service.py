import json
import os
import zipfile
from datetime import datetime
from hashlib import md5

from base import repository
from components.oss import OSSFileProxy
from custom_component.repositories import custom_component_repository
from custom_component.services import component_package_parser
from dmplib import config
from dmplib.components import auth_util
from dmplib.redis import RedisCache
from dmplib.utils.errors import UserError

from dmplib.redis import conn_custom_prefix as conn_redis


def get_component_list():
    data_list = repository.get_data('dap_bi_custom_component', {},
                                    ['package', 'name', 'version', 'modified_on', 'modified_by', 'created_by',
                                     'created_on'], multi_row=True)
    return {
        'list': data_list
    }


def get_component_logs(package):
    data_list = repository.get_data('dap_bi_custom_component_log', {'package': package},
                                    ['id', 'package', 'name', 'type', 'version', 'created_on', 'created_by',
                                     'modified_on',
                                     'modified_by'], order_by=[('created_on', 'desc')], multi_row=True)
    return {
        'list': data_list
    }


def recycle_component(package):
    custom_component_repository.recycle_component(package)
    clear_component_cache()


def preview_zip(file_url):
    if not file_url:
        return None

    key = md5(file_url.encode()).hexdigest()
    cache = RedisCache(key_prefix='preview_custom_component_zip')
    preview_data = cache.get(key)
    if preview_data:
        return json.loads(preview_data.decode())
    preview_data = component_package_parser.parse_zip_file(file_url)
    if not preview_data:
        raise UserError(400, '无效的数据文件格式')
    cache.set(key, json.dumps(preview_data), 3600)
    return preview_data


def upload_package(file_urls):
    if not file_urls or len(file_urls) == 0:
        raise UserError(400, '至少上传一个组件')
    # if custom_component_upload_task_repository.exist_uploading_task():
    #     raise UserError(400, '存在正在上传的组件')
    file_content_list = []
    for url in file_urls:
        file_content = preview_zip(url)
        file_content_list.append(file_content)
    # task_id = seq_id()
    # repository.add_data('dap_bi_custom_component_upload_task', {
    #     'id': task_id,
    #     'content': json.dumps(file_content_list),
    #     'file_urls': json.dumps(file_urls),
    #     'status': 0
    # })
    upload_custom_component_package(file_urls, file_content_list)
    clear_component_cache()
    # return {
    #     'task_id': task_id
    # }


def upload_custom_component_package(file_urls, file_content_list):
    custom_component_data_list = []
    for i in range(len(file_urls)):
        package_content = file_content_list[i]
        extract_upload_package(file_urls[i], package_content)
        custom_component_data = {
            'menu_id': package_content.get('menu_id', 'others'),
            'name': package_content.get('name'),
            'icon': package_content.get('icon', ''),
            'package': package_content.get('package', ''),
            'chart_type': package_content.get('chart_type', ''),
            'preview_image': package_content.get('preview_image', ''),
            'description': package_content.get('description', ''),
            'version': package_content.get('version', ''),
            'data_logic_type_code': package_content.get('data_logic_type_code', ''),
            'status': package_content.get('status', 1),
            'is_build_in': package_content.get('is_build_in', 0),
            'data_source_origin': package_content.get('data_source_origin', 'none'),
            'default_data_size': package_content.get('default_data_size', 10),
            'indicator_description': package_content.get('indicator_description', ''),
            'indicator_rules': package_content.get('indicator_rules', ''),
            'sortable': package_content.get('sortable', 0),
            'penetrable': package_content.get('penetrable', 0),
            'linkage': package_content.get('linkage', 0),
            'can_linked': package_content.get('can_linked', 0),
            'has_zaxis': package_content.get('has_zaxis', 0),
            'has_desiredvalue': package_content.get('has_desiredvalue', 0),
            'dims_report_redirect': package_content.get('dims_report_redirect', 0),
            'nums_report_redirect': package_content.get('nums_report_redirect', 0),
            'md5version': package_content.get('md5version'),
            'md5RunTimeversion': package_content.get('md5RunTimeversion'),
            'contain_css': package_content.get('contain_css', 1),
            'contain_mapgallery': package_content.get('contain_mapgallery', 0),
            'layout': package_content.get('layout', 'free,grid'),
            'layout_preview_image': package_content.get('layout_preview_image'),
            'navbar_icon': package_content.get('navbar_icon'),
            'runTerminal': package_content.get('runTerminal', ''),
            'base_chart_lib': package_content.get('base_chart_lib', ''),
            'extension': package_content.get('extension'),
            'platform_version': package_content.get('platform_version', ''),
            'distribute_version': package_content.get('distribute_version', ''),
        }
        custom_component_data_list.append(custom_component_data)
    custom_component_repository.insert_or_upgrade_components(custom_component_data_list)


def extract_upload_package(file_url, package_content):
    data = component_package_parser.read_data_from_url(file_url)
    package_root = "/custom_component/package/" + package_content.get('package') + "/" + package_content.get(
        'version') + "/"
    with zipfile.ZipFile(data, "r") as zip_file:
        file_info_list = zip_file.infolist()
        oss_proxy = OSSFileProxy()
        for zip_info in file_info_list:
            extract_path = os.path.join(package_root, zip_info.filename)
            if not zip_info.is_dir():
                with zip_file.open(zip_info) as source_file:
                    # 上传文件到OSS
                    oss_proxy.upload(source_file, root=os.path.dirname(extract_path),
                                     file_name=os.path.basename(zip_info.filename),
                                     auto_mime=True)


def clear_component_cache():
    now_time = datetime.now().strftime("%Y%m%d%H%M%S")
    update_on_key = get_component_update_on_cache_key()
    cache_key = get_installed_components_cache_key()
    redis_cache = conn_redis(auth_util.get_env_code())

    # 删除已安装组件列表缓存
    redis_cache.delete(cache_key)
    # 刷新组件最后更新时间
    redis_cache.set_data(update_on_key, now_time)


def get_installed_components_cache_key() -> str:
    """
    已安装组件缓存前缀
    :return:
    """
    installed_components_cache_key = config.get('Cache.installed_components_cache_key', 'installed_components_v1')
    return installed_components_cache_key


def get_component_update_on_cache_key() -> str:
    """
    组件刷新时间缓存key
    :return:
    """
    component_update_on_cache_key = config.get('Cache.components_refresh_flag_cache_key', 'component_update_on')
    return component_update_on_cache_key
