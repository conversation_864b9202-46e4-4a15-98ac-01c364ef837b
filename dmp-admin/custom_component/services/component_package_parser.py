import io
import json
import re
import zipfile
from urllib import parse

from base.enums import DataSourceOrigin, ComponentDataLogicType
from components.oss import OSSFileProxy
from custom_component.repositories import component_repository
from dmplib.utils.errors import UserError

COMPONENT_LAYOUTS = ['grid', 'free']


def _read_zip_file(zip_file):
    zip_sub_file_list = zip_file.namelist()
    contain_css = 0
    if "package.json" not in zip_sub_file_list:
        # 非法的包 删除临时文件
        raise UserError(message="非法的node包，package.json文件不存在！")
    for sub_file in zip_sub_file_list:
        if re.match("[^\/]+\.css$", sub_file):
            contain_css = 1
            break
    with zip_file.open("package.json") as package_file:
        #  主字段验证
        package = json.loads(str(package_file.read(), encoding="utf-8"))
        not_empty_fields = ["name", "version", "description", "platform", "config"]
        _validate_not_empty_fields(
            "package.json", package, not_empty_fields
        )
        #  platform 验证
        platform = package.get("platform")
        not_empty_platform_fileds = [
            "name",
            "icon",
            "layout",
            "previewImg",
            "md5version",
            "chartType",
            "containMapGallery",
        ]
        _validate_not_empty_fields(
            "platform", platform, not_empty_platform_fileds
        )
        data_logic_type_code = platform.get("dataLogicType")

        run_terminal = platform.get("runTerminal")
        if run_terminal and not isinstance(run_terminal, list):
            raise UserError(message="组件终端类型数据格式错误")

        # base_chart_lib 验证 新增
        base_chart_lib = platform.get("baseChartLib")
        if base_chart_lib and not isinstance(base_chart_lib, list):
            raise UserError(message="支持组件库字段数据格式错误")

        # layout 验证
        layout_configs = platform.get("layout")
        layouts = set()
        if not isinstance(layout_configs, dict):
            raise UserError(message="布局配置不正确！")
        for layout_name, confs in layout_configs.items():
            if layout_name not in COMPONENT_LAYOUTS:
                raise UserError(message="非法的布局名称！")
            if not confs.get("previewImg"):
                raise UserError(message="请指定布局[%s]的预览图！" % layout_name)
            layouts.add(layout_name)
        # pkgconfig 验证
        pkgconfig = package.get("config")
        not_empty_pkgconfig_fileds = [
            "dataSourceOrigin",
            "indicatorDescription",
            "indicatorRules",
            "sortable",
            "penetrable",
            "linkage",
            "canLinked",
            "hasZaxis",
            "hasDesiredvalue",
            "dimsReportRedirect",
            "numsReportRedirect",
        ]
        _validate_not_empty_fields(
            "config", pkgconfig, not_empty_pkgconfig_fileds
        )
        #  判断dataSourceOrigin 取值范围
        data_source_origin_range = []
        data_source_origin = pkgconfig.get("dataSourceOrigin")
        for key, item in DataSourceOrigin.__members__.items():
            data_source_origin_range.append(item.value)
        if data_source_origin not in data_source_origin_range:
            raise UserError(message="数据来源[data_source_origin_range]声明的值无效！")
        data_logic_type_code = (
                data_logic_type_code or ComponentDataLogicType.Default.value
        )  # 没有声明使用通用数据逻辑类型

        return {
            "package": package.get("name"),
            "version": package.get("version"),
            "file": zip_file,
            "name": platform.get("name"),
            "icon": str.lstrip(platform.get("icon"), "/"),
            "preview_image": str.lstrip(platform.get("previewImg"), "/"),
            "navbar_icon": str.lstrip(platform.get("navbar_icon"), "/") if platform.get("navbar_icon") else "",
            "layout_preview_image": platform.get("layout"),
            "layout": layouts,
            "description": package.get("description"),
            "md5version": platform.get("md5version"),
            "chart_type": platform.get("chartType") or "",
            "data_logic_type_code": data_logic_type_code,
            "file_list": zip_sub_file_list,
            "data_source_origin": pkgconfig.get("dataSourceOrigin"),
            "indicator_description": pkgconfig.get("indicatorDescription"),
            "indicator_rules": json.dumps(pkgconfig.get("indicatorRules")),
            "sortable": int(pkgconfig.get("sortable")),
            "penetrable": int(pkgconfig.get("penetrable")),
            "linkage": int(pkgconfig.get("linkage")),
            "can_linked": int(pkgconfig.get("canLinked")),
            "has_zaxis": int(pkgconfig.get("hasZaxis")),
            "has_desiredvalue": int(pkgconfig.get("hasDesiredvalue")),
            "dims_report_redirect": int(pkgconfig.get("dimsReportRedirect")),
            "nums_report_redirect": int(pkgconfig.get("numsReportRedirect")),
            "contain_css": int(contain_css),
            "contain_mapgallery": int(platform.get("containMapGallery")),
            "runTerminal": json.dumps(run_terminal) if run_terminal else "",
            "base_chart_lib": json.dumps(base_chart_lib) if base_chart_lib else "",
            "extension": pkgconfig.get("extension", ""),
        }


def parse_zip_file(file_url):
    data = read_data_from_url(file_url)
    with zipfile.ZipFile(data, "r") as zip_file:
        return _read_zip_file(zip_file)


def read_data_from_url(file_url):
    if not file_url:
        return None

    oss_file = OSSFileProxy()
    obj_result = oss_file.get_object(parse.unquote(file_url), True)
    if not obj_result:
        raise UserError(400, '读取文件失败')

    bytes_data = obj_result.read()
    if len(bytes_data) == 0:
        raise UserError(400, '文件为空，读取失败')

    data = io.BytesIO(bytes_data)
    return data


def _validate_not_empty_fields(data_key, data, not_empty_fields):
    for field in not_empty_fields:
        if (field not in data) or (data.get(field) is None):
            raise UserError(
                message="{data_key}缺少[{field}]字段声明".format(
                    data_key=data_key, field=field
                )
            )
