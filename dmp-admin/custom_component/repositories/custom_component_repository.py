import datetime

from dmplib.db.mysql_wrapper import get_db
from dmplib.hug import g


def insert_or_upgrade_components(component_list):
    with get_db() as db:
        db.begin_transaction()
        for component in component_list:
            component_data = db.query_one(
                "select created_on,created_by from dap_bi_custom_component where package=%(package)s", component)
            if component_data is not None:
                db.exec_sql("delete from dap_bi_custom_component where package=%(package)s", component, commit=False)
                component['created_on'] = component_data['created_on']
                component['created_by'] = component_data['created_by']
            else:
                component['created_on'] = datetime.datetime.now()
                component['created_by'] = g.account
            component['modified_on'] = datetime.datetime.now()
            component['modified_by'] = g.account
            db.insert('dap_bi_custom_component', component, commit=False)
            db.insert('dap_bi_custom_component_log', {
                'package': component.get('package'),
                'name': component.get('name'),
                'version': component.get('version'),
                'type': '升级组件',
                'created_on': datetime.datetime.now(),
                'created_by': g.account,
                'modified_on': datetime.datetime.now(),
                'modified_by': g.account
            }, commit=False)
        db.commit()


def recycle_component(package):
    with get_db() as db:
        db.begin_transaction()
        db.delete('dap_bi_custom_component_log', {'package': package}, commit=False)
        db.delete('dap_bi_custom_component', {'package': package}, commit=False)
        db.commit()
