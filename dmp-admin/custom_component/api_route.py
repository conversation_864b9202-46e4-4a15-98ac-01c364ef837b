from custom_component.services import custom_component_service
from dmplib.hug import APIWrapper

api = APIWrapper(__name__)


@api.admin_route.get('/list')
def get_component_list(**kwargs):
    """
    获取项目列表
    :param kwargs:
    :return:
    """
    return True, None, custom_component_service.get_component_list()


@api.admin_route.get('/logs')
def get_component_logs(**kwargs):
    """
    获取项目列表
    :param kwargs:
    :return:
    """
    package = kwargs.get('package', None)
    return True, None, custom_component_service.get_component_logs(package)


@api.admin_route.get('/recycle')
def recycle_component(**kwargs):
    """
    获取项目列表
    :param kwargs:
    :return:
    """
    package = kwargs.get('package', None)
    return True, None, custom_component_service.recycle_component(package)


@api.admin_route.get('/preview_zip')
def preview_zip(**kwargs):
    """
    获取项目列表
    :param kwargs:
    :return:
    """
    file_url = kwargs.get('file_url', None)
    return True, None, custom_component_service.preview_zip(file_url)


@api.admin_route.post('/upload')
def upload(**kwargs):
    """
    获取项目列表
    :param kwargs:
    :return:
    """
    file_urls = kwargs.get('file_urls', [])
    return True, None, custom_component_service.upload_package(file_urls)
