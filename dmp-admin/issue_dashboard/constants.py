# -*- coding: utf-8 -*-

DELIVER_DEFAULT_FOLDER_LEVER_CODE = "9000-"
DELIVER_DEFAULT_FOLDER_LEVER_CODE_NEW = "9100-"
DELIVER_DEFAULT_FOLDER_ID = "3a070a96-d0a3-c334-5a8c-6d601c69452f"
DELIVER_DEFAULT_FOLDER_NAME = "系统分发数据集文件夹"
DASHBOARD_DELIVER_DEFAULT_FOLDER_ID = "3a070aa4-afdf-0266-be51-e8fe2f4b0b3b"

TABLE_CONDITION_MAP = {
    'dap_bi_dashboard': ['id'],
    'dap_bi_dashboard_chart': ['id'],
    'dap_bi_dashboard_chart_dim': ['id'],
    'dap_bi_dashboard_chart_num': ['id'],
    'dap_bi_dashboard_chart_filter': ['id'],
    'dap_bi_dashboard_chart_filter_relation': ['id'],
    'dap_bi_dashboard_chart_layers': ['dashboard_chart_id', 'dim'],
    'dap_bi_dashboard_chart_markline': ['id'],
    'dap_bi_dashboard_chart_desire': ['id'],
    'dap_bi_dashboard_chart_params': ['param_id'],
    'dap_bi_dashboard_chart_params_jump': ['dashboard_chart_id', 'param_dataset_field_id', 'source_id', 'dashboard_filter_id', 'jump_config_id', 'target_filter_id', 'target_filter_field_id', 'global_params_id'],
    'dap_bi_dashboard_chart_comparison': ['id'],
    'dap_bi_dashboard_chart_penetrate_relation': ['id'],
    'dap_bi_dashboard_chart_field_sort': ['id'],
    'dap_bi_dashboard_chart_visible_triggers': ['id'],
    'dap_bi_dashboard_chart_selector': ['id'],
    'dap_bi_dashboard_chart_selector_field': ['id'],
    'dap_bi_dashboard_filter': ['id'],
    'dap_bi_dashboard_filter_vars_relation_field': ['id'],
    'dap_bi_dashboard_filter_relation': ['id'],
    'dap_bi_dashboard_dataset_field_relation': ['id'],
    'dap_bi_dashboard_released_snapshot_chart': ['snapshot_id'],
    'dap_bi_dashboard_released_snapshot_dashboard': ['snapshot_id'],
    'dap_bi_screen_dashboard': ['id'],
    'dap_bi_dashboard_filter_chart': ['id'],
    'dap_bi_dashboard_filter_chart_fixed_value': ['id'],
    'dap_bi_dashboard_filter_chart_relation': ['id'],
    'dap_bi_dashboard_linkage': ['id'],
    'dap_bi_dashboard_linkage_relation': ['id'],
    'dap_bi_dashboard_filter_chart_default_values': ['id'],
    'dap_bi_dashboard_extra': ['dashboard_id'],
    'dap_bi_dashboard_jump_config': ['id'],
    'dap_bi_dashboard_jump_relation': ['jump_config_id', 'dashboard_filter_id', 'dataset_field_id'],
    'dap_bi_dashboard_vars_jump_relation': ['jump_config_id', 'dashboard_filter_id', 'dataset_field_id', 'var_id'],
    'dap_bi_dashboard_fixed_var_jump_relation': ['jump_config_id', 'dashboard_id', 'dashboard_chart_id', 'dataset_id', 'dataset_field_id', 'var_name', 'dashboard_filter_id', 'target_filter_id', 'target_filter_field_id', 'global_params_id'],
    'dap_bi_dashboard_filter_chart_jump_relation': ['jump_config_id', 'dashboard_chart_id', 'filter_chart_id', 'dataset_field_id', 'global_params_id', 'date_filter_chart_flag'],
    'dap_bi_dashboard_global_params_jump_relation': ['jump_config_id', 'dashboard_chart_id', 'initiator_global_params_id', 'global_params_id'],
    'dap_bi_dashboard_jump_global_params': ['id'],
    'dap_bi_dashboard_global_params_2_dataset_field_relation': ['global_params_id', 'dashboard_id', 'chart_id', 'dataset_id', 'dataset_field_id'],
    'dap_bi_dashboard_global_params_2_dataset_vars_relation': ['global_params_id', 'dashboard_id', 'dataset_id', 'var_id'],
    'dap_bi_dashboard_global_params_2_filter_chart_relation': ['global_params_id', 'dashboard_id', 'filter_chart_id', 'dataset_field_id', 'date_filter_chart_flag'],
    'dap_bi_dashboard_dataset_vars_relation': ['id'],
    'dap_bi_dashboard_value_source': ['id'],
    'dap_bi_dashboard_vars_value_source_relation': ['dashboard_id', 'var_id', 'value_source_id'],
    'dap_bi_dashboard_component_filter': ['id'],
    'dap_bi_dashboard_component_filter_field_relation': ['id'],
    "dap_bi_dashboard_email_subscribe": ['id'],
    "dap_bi_dashboard_subscribe_display_format": ['subscribe_id', 'dataset_id', 'field_id'],
    "dap_bi_mobile_subscribe_filter": ['email_subscribe_id', 'dataset_id', 'dataset_field_id'],
    "dap_bi_mobile_subscribe_rules": ['email_subscribe_id', 'dataset_id'],
    "dap_bi_mobile_subscribe_chapters": ['id'],
    "dap_bi_flow": ['id'],
    "dap_bi_tag_relation": ['relation_id', 'type']
}

def get_condition_fields(table_name):
    return TABLE_CONDITION_MAP.get(table_name, ['id'])
