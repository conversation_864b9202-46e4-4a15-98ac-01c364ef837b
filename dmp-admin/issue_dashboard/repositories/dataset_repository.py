#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    Created by chenchao 2018/7/30.
"""
import copy
import json
from datetime import datetime

from dmplib.saas.project import get_db, current_db_type
from dmplib.utils.strings import seq_id
from dmplib.utils.errors import UserError
from dmplib import config
from dmplib.components.enums import DBType
from base.enums import DataSourceType, DatasetFieldType
from issue_dashboard.constants import DELIVER_DEFAULT_FOLDER_LEVER_CODE, DELIVER_DEFAULT_FOLDER_LEVER_CODE_NEW, \
    DELIVER_DEFAULT_FOLDER_ID, DELIVER_DEFAULT_FOLDER_NAME
from issue_dashboard.models import DatasetOperateRecordModel
from issue_dashboard.models import DatasetOperateRecordModel, DesignContentModel


def save_dataset_data(project_code, export_dataset_data, include_folder, replace_data_source=False, from_template=0, is_shuxin15=False):  # NOSONAR
    """
    根据租户code保存数据集相关数据
    :param project_code:
    :param export_dataset_data:
    :param include_folder:
    :return:
    """
    dataset = export_dataset_data.get("dataset")
    dataset_design = export_dataset_data.get("dataset_design") or []
    dataset_fields = export_dataset_data.get("dataset_fields")
    dataset_folders = export_dataset_data.get("dataset_folders")
    flow = export_dataset_data.get("flow")
    cache_flow = export_dataset_data.get("cache_flow")
    nodes = export_dataset_data.get("nodes")
    cache_nodes = export_dataset_data.get('cache_nodes')
    data_source = export_dataset_data.get("data_source")
    dataset_depends = export_dataset_data.get("dataset_depend")

    dataset_indexs = export_dataset_data.get("dataset_index")
    dataset_tables_collections = export_dataset_data.get("dataset_tables_collection")
    dataset_field_deletes = export_dataset_data.get("dataset_field_delete")
    dataset_filters = export_dataset_data.get("dataset_filter")
    dataset_vars = export_dataset_data.get("dataset_vars")
    dataset_field_include_vars = export_dataset_data.get("dataset_field_include_vars")
    keyword = export_dataset_data.get("keyword", [])
    keyword_details = export_dataset_data.get("keyword_details", [])
    dataset_used_table = export_dataset_data.get("dataset_used_table", [])
    dataset_schedule_page_fields = export_dataset_data.get("dataset_schedule_page_fields", [])
    dataset_graph_tables = export_dataset_data.get("dataset_graph_tables", [])
    dataset_graph_table_relations = export_dataset_data.get("dataset_graph_table_relations", [])
    dataset_graph_filters = export_dataset_data.get("dataset_graph_filters", [])
    dataset_excel_data_local_file_content = export_dataset_data.get("dataset_excel_data_local_file_content", None)
    with get_db(project_code) as db:
        if dataset:
            # 根据数据源的所属产品找到当租户租户的数据源，根据当前租户的数据库类型决定使用哪套语法
            db_type_map, math_source = {}, {}
            # 小袁说两个sql对应的数据源的app_level_code一定相同
            if len(dataset_design) == 2 and data_source.get('type') == DataSourceType.MysoftNewERP.value:
                for item in dataset_design:
                    db_type_map[item.get('db_type')] = item.get("content")
                    app_level_code = item.get("app_level_code")
                    math_source = math_source or db.query_one(
                        'select * from dap_m_data_source where app_level_code = %(app_level_code)s',
                        {'app_level_code': app_level_code}
                    )
                if not math_source:
                    raise UserError(message='多sql语法只支持mysoftnewerp数据源')
                if db_type_map and math_source:  # NOSONAR
                    c = db_type_map.get(math_source.get('db_type'))
                    if c is None:  # NOSONAR
                        raise UserError(message='下载的数据集没有对应语法的sql')
                    match_content = json.loads(c)
                    match_content['data_source_id'] = math_source.get('id')
                    dataset['content'] = json.dumps(match_content)
                    data_source = math_source or {}

            db.delete("dap_bi_dataset_design", {"dataset_id": dataset.get("id")}, commit=False)
            if dataset_design and is_shuxin15:
                # 处理moql
                design_model = DesignContentModel(**json.loads(dataset_design[0].get('content')))
                content = json.loads(dataset.get('content'))
                if current_db_type(suffix='data', project_code=project_code) == DBType.DM.value:
                    content['sql'] = design_model.dmsql
                else:
                    content['sql'] = design_model.mysql
                dataset['content'] = json.dumps(content)
                db.replace_multi_data("dap_bi_dataset_design", dataset_design, dataset_design[0].keys(), commit=False, condition_field=['id'])

            db.replace_multi_data("dap_bi_dataset", [dataset], dataset.keys(), commit=False, condition_field=['id'])
            # 添加操作日志, 数据集操作记录
            db.insert(
                'dap_bi_dataset_operate_record',
                get_operate_record_model(
                    dataset.get('id'),
                    '分发',
                    data_source=data_source.get('name') if data_source else '',
                    content=dataset.get('content'),
                ).get_dict(),
                commit=False,
            )
            db.delete("dap_bi_dataset_field", {"dataset_id": dataset.get("id"), "created_by": "celery"}, commit=False)
            db.delete("dap_bi_dataset_field", {"dataset_id": dataset.get("id"), "type": "普通"}, commit=False)
            db.delete("dap_bi_dataset_depend", {"depend_id": dataset.get("id")}, commit=False)
        if dataset_fields and len(dataset_fields) > 0:
            # 处理高级字段moql
            for field in dataset_fields:
                if field.get("type") != DatasetFieldType.Normal.value and field.get('design_content'):
                    try:
                        design_content = json.loads(field.get('design_content'))
                    except:
                        design_content = eval(field.get('design_content'))
                        field['design_content'] = json.dumps(design_content)

                    design_content = DesignContentModel(**design_content)
                    if dataset.get('connect_type') != '直连':
                        db_type = current_db_type(project_code=project_code)
                    else:
                        db_type = current_db_type(suffix='data', project_code=project_code)
                    if db_type == DBType.DM.value:
                        field['expression_advance'] = design_content.dmsql
                    else:
                        field['expression_advance'] = design_content.mysql

            db.replace_multi_data("dap_bi_dataset_field", dataset_fields, dataset_fields[0].keys(), commit=False, condition_field=['id'])
        if include_folder and dataset_folders and len(dataset_folders) > 0:
            db.replace_multi_data("dap_bi_dataset", dataset_folders, dataset_folders[0].keys(), commit=False, condition_field=['id'])
        if flow:
            # 判断是否已经存在调度 存在就不修改
            flow_id = flow.get('id') if flow else ''
            if flow_id:
                sql = "select * from dap_bi_flow where id = '{id}' ".format(id=flow_id)
                flow_one = db.query_one(sql)
                if not flow_one and dataset.get('connect_type') != '直连':
                    # 不存在调度信息就新增 同时当前数据集是调度
                    db.replace_multi_data("dap_bi_flow", [flow], flow.keys(), commit=False, condition_field=['id'])
                elif dataset.get('connect_type') != '直连':
                    export_dataset_data["flow"] = flow_one
        elif dataset.get('connect_type') != '直连':
            sql = "select * from dap_bi_flow where id = '{id}' ".format(id=dataset.get("id"))
            flow_one = db.query_one(sql)
            if flow_one:
                export_dataset_data["flow"] = flow_one

        if cache_flow:
            db.replace_multi_data("dap_bi_flow", [cache_flow], cache_flow.keys(), commit=False, condition_field=['id'])
        if nodes and len(nodes) > 0:
            flow_id = flow.get('id') if flow else ''
            if flow_id:
                db.delete('dap_bi_node', {'flow_id': flow_id, 'is_start': 1}, False)
            if dataset.get('connect_type') != '直连':
                db.replace_multi_data("dap_bi_node", nodes, nodes[0].keys(), commit=False, condition_field=['id'])
        if cache_nodes:
            db.replace_multi_data("dap_bi_node", cache_nodes, cache_nodes[0].keys(), commit=False, condition_field=['id'])
        if data_source:
            _replace_data_source(db, data_source, replace_data_source)
        if dataset_depends and len(dataset_depends) > 0:
            db.replace_multi_data("dap_bi_dataset_depend", dataset_depends, dataset_depends[0].keys(), commit=False, condition_field=['source_dataset_id', 'depend_id'])
        if dataset_tables_collections and len(dataset_tables_collections) > 0:
            db.delete("dap_bi_dataset_tables_collection", {"dataset_id": dataset.get("id")}, commit=False)
            db.replace_multi_data(
                "dap_bi_dataset_tables_collection",
                dataset_tables_collections,
                dataset_tables_collections[0].keys(),
                commit=False,
                condition_field=['id']
            )
        if from_template == 1:
            for pkg_keyword in keyword:
                # 从开户流程来的默认为系统级关键字
                pkg_keyword['is_system'] = 1
        _replace_dataset_field_deletes(dataset_field_deletes, db)
        _replace_dataset_filters(dataset_filters, db, dataset.get("id"))
        _replace_dataset_index(dataset_indexs, db)
        # 更新数据集-变量相关的表
        _replace_dataset_vars(db, dataset_vars, dataset_field_include_vars, dataset)
        # 处理关键字和关键字使用关系
        _replace_keyword_v2(db, keyword, keyword_details, data_source, project_code=project_code)
        # 处理dataset_used_table
        _replace_dataset_used_table(db, dataset_used_table, dataset.get("id"))
        # 处理dataset_schedule_page_field
        _replace_dataset_schedule_page_field(db, dataset_schedule_page_fields, dataset.get("id"))
        # 处理dataset_graph
        _replace_dataset_graph_table(db, dataset_graph_tables, dataset.get("id"))
        _replace_dataset_graph_table_relation(db, dataset_graph_table_relations, dataset.get("id"))
        _replace_dataset_graph_filter(db, dataset_graph_filters, dataset.get("id"))
        _replace_dataset_excel_data(db, dataset_excel_data_local_file_content, dataset.get("id"))
        db.commit()


def _replace_keyword(db, keyword, keyword_details, data_source):  # NOSONAR
    # system_keyword = [k for k in keyword if k.get('is_system') == 1]  # 系统级的不用重新插入，只用生成使用关系
    # custom_keyword = [k for k in keyword if k.get('is_system') == 0]
    # for c_keyword in custom_keyword:  # 有些数据源是页面上手动选择指定的， 需要替换成选择数据源
    #     c_keyword['datasource_id'] = data_source.get('id', '')

    for single_keyword in keyword:  # 无论是自定义或者是系统级的关键字都会可能有数据源，所以这里把所有的数据源id替换
        if single_keyword['datasource_id'] and len(single_keyword['datasource_id']) == 36:
            single_keyword['datasource_id'] = data_source.get('id', '')

    # # 直接把自定义的关键字插入，id也会插入
    # if custom_keyword and len(custom_keyword) > 0:
    #     db.replace_multi_data("dap_bi_keyword", custom_keyword, custom_keyword[0].keys(), commit=False)

    keyword_details_str = json.dumps(keyword_details, ensure_ascii=False)
    # 把当前有的keyword的id查询出来，并替换关键字的使用关系
    for one_keyword in keyword:
        is_system = one_keyword.get('is_system')
        old_keyword_id = one_keyword.get('id', '')
        if is_system == 1:
            keyword_id = db.query_scalar(
                'select id from dap_bi_keyword where is_system=1 and keyword_name=%(keyword_name)s',
                params={'keyword_name': one_keyword.get('keyword_name', '')}
            )
            if keyword_id and old_keyword_id:
                keyword_details_str = keyword_details_str.replace(old_keyword_id, keyword_id)
            if not keyword_id:
                # 补齐缺失的系统关键字
                db.replace_multi_data("dap_bi_keyword", [one_keyword], one_keyword.keys(), commit=False, condition_field=['id'])
        elif is_system == 0:
            keyword_id = db.query_scalar(
                'select id from dap_bi_keyword where is_system=0 and keyword_name=%(keyword_name)s and datasource_id=%(datasource_id)s',
                params={'keyword_name': one_keyword.get('keyword_name', ''), 'datasource_id': data_source.get('id', '')}
            )
            if keyword_id and old_keyword_id:
                keyword_details_str = keyword_details_str.replace(old_keyword_id, keyword_id)
            if not keyword_id:
                # 补齐缺失的自定义关键字
                db.replace_multi_data("dap_bi_keyword", [one_keyword], one_keyword.keys(), commit=False, condition_field=['id'])
    if keyword_details and len(keyword_details) > 0:
        dataset_id = keyword_details[0].get("dataset_id")
        db.delete("dap_bi_keyword_details", {"dataset_id": dataset_id}, commit=False)

    keyword_details = json.loads(keyword_details_str)
    insert_keyword_details = []
    for details in keyword_details:
        # !!!!!!!!! 这个表的唯一主键是自增id， 不能直接使用replace into，先查一遍有没有
        details.pop('id', None)
        is_existed = db.query_scalar(
            'select id from dap_bi_keyword_details where keyword_id=%(keyword_id)s and var_id=%(var_id)s and dataset_id=%(dataset_id)s',
            params={
                'keyword_id': details.get('keyword_id', ''),
                'dataset_id': details.get('dataset_id', ''),
                'var_id': details.get('var_id', '')
            }
        )
        if not is_existed:
            insert_keyword_details.append(details)

    if insert_keyword_details and len(insert_keyword_details) > 0:
        db.replace_multi_data("dap_bi_keyword_details", insert_keyword_details, insert_keyword_details[0].keys(), commit=False, condition_field=['id'])


def _replace_keyword_v2(db, keyword, keyword_details, data_source, project_code=None):  # NOSONAR
    """
    如果关键字在租户里面存在：
        1、包里面是系统关键字，这个关键字在租户里面存在，覆盖关键字的定义
        2、包里面是自定义关键字，这个关键字在租户里面存在，不覆盖关键字的定义
    如果关键字在租户里面不存在：
        1、导入关键字
    """
    for single_keyword in keyword:  # 可能是用户导入指定数据源的场景，所以这里把所有的数据源id替换
        if single_keyword['datasource_id'] and len(single_keyword['datasource_id']) == 36:
            single_keyword['datasource_id'] = data_source.get('id', '')

    keyword_details_str = json.dumps(keyword_details, ensure_ascii=False)

    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # 把当前有的keyword的id查询出来，并替换关键字的使用关系
    for pkg_keyword in keyword:
        pkg_is_system = pkg_keyword.get('is_system')
        pkg_keyword_id = pkg_keyword.get('id', '')
        pkg_keyword_name = pkg_keyword.get('keyword_name', '')
        pkg_datasource_id = pkg_keyword.get('datasource_id', '')

        if 'modified_on' in pkg_keyword and not pkg_keyword.get('modified_on'):
            pkg_keyword['modified_on'] = current_time

        if 'created_on' in pkg_keyword and not pkg_keyword.get('created_on'):
            pkg_keyword['created_on'] = current_time

        if 'modified_on' in pkg_keyword and 'T' in pkg_keyword.get('modified_on'):
            pkg_keyword['modified_on'] = pkg_keyword.get('modified_on').replace('T', ' ')

        if 'created_on' in pkg_keyword and 'T' in pkg_keyword.get('created_on'):
            pkg_keyword['created_on'] = pkg_keyword.get('modified_on').replace('T', ' ')

        existed_keywords = db.query(
            'select * from dap_bi_keyword where keyword_name=%(keyword_name)s and datasource_id=%(datasource_id)s',
            params={'keyword_name': pkg_keyword_name, 'datasource_id': pkg_datasource_id}
        ) or []

        # moql
        if data_source.get('type') == DataSourceType.MysoftShuXin15.value and pkg_keyword.get('design_content'):
            design_content = DesignContentModel(**json.loads(pkg_keyword.get('design_content')))
            if current_db_type(suffix='data', project_code=project_code) == DBType.DM.value:
                pkg_keyword['sql_text'] = design_content.dmsql
            else:
                pkg_keyword['sql_text'] = design_content.mysql

        if existed_keywords:
            # 租户存在相同关键字
            for existed_keyword in existed_keywords:
                existed_keyword_id = existed_keyword.get('id', '')
                keyword_details_str = keyword_details_str.replace(pkg_keyword_id, existed_keyword_id)
                if pkg_is_system == existed_keyword.get('is_system') == 1:
                    # 包里面和租户存在的都是系统级，覆盖租户关键字数据
                    c_pkg_keyword = copy.deepcopy(pkg_keyword)
                    c_pkg_keyword.pop('id', None)
                    db.update('dap_bi_keyword', condition={'id': existed_keyword_id}, data=c_pkg_keyword, commit=False)
                else:
                    # 其他情况跳过，不覆盖租户关键字数据，使用租户已经存在的关键字
                    pass
        else:
            # 租户不存在相同关键字，插入这个关键字
            db.replace_multi_data("dap_bi_keyword", [pkg_keyword], pkg_keyword.keys(), commit=False, condition_field=['id'])

    if keyword_details and len(keyword_details) > 0:
        dataset_id = keyword_details[0].get("dataset_id")
        db.delete("dap_bi_keyword_details", {"dataset_id": dataset_id}, commit=False)

    keyword_details = json.loads(keyword_details_str)
    insert_keyword_details = []
    for details in keyword_details:
        # !!!!!!!!! 这个表的唯一主键是自增id， 不能直接使用replace into，先查一遍有没有
        details.pop('id', None)
        is_existed = db.query_scalar(
            'select id from dap_bi_keyword_details where keyword_id=%(keyword_id)s and var_id=%(var_id)s and dataset_id=%(dataset_id)s',
            params={
                'keyword_id': details.get('keyword_id', ''),
                'dataset_id': details.get('dataset_id', ''),
                'var_id': details.get('var_id', '')
            }
        )
        if not is_existed:
            insert_keyword_details.append(details)

    if insert_keyword_details and len(insert_keyword_details) > 0:
        db.replace_multi_data("dap_bi_keyword_details", insert_keyword_details, insert_keyword_details[0].keys(), commit=False, condition_field=['keyword_id', 'var_id', 'dataset_id'])


def _replace_dataset_index(dataset_indexs, db):
    if dataset_indexs and len(dataset_indexs) > 0:
        db.replace_multi_data("dap_bi_dataset_index", dataset_indexs, dataset_indexs[0].keys(), commit=False, condition_field=['id'])


def _replace_dataset_filters(dataset_filters, db, dataset_id):
    if dataset_filters and len(dataset_filters) > 0:
        db.delete("dap_bi_dataset_filter", {"dataset_id": dataset_id}, commit=False)
        db.replace_multi_data("dap_bi_dataset_filter", dataset_filters, dataset_filters[0].keys(), commit=False, condition_field=['id'])
    else:
        #如果包里没有过滤也要删除之前的过滤配置
        db.delete("dap_bi_dataset_filter", {"dataset_id": dataset_id}, commit=False)

def _replace_dataset_field_deletes(dataset_field_deletes, db):
    if dataset_field_deletes and len(dataset_field_deletes) > 0:
        db.replace_multi_data(
            "dap_bi_dataset_field_delete", dataset_field_deletes, dataset_field_deletes[0].keys(), commit=False, condition_field=['dataset_id', 'dataset_field_id']
        )


def _replace_data_source(db, data_source, replace_data_source=False):
    """
    更新data_source表
    :param db:
    :param data_source:
    :return:
    """
    # 如果存在data_source，则不替换conn_str里面的params, 不替换user_source_id
    data_source_sql = "select * from dap_m_data_source where id = '{}'".format(data_source.get("id"))
    old_data_source = db.query_one(data_source_sql)
    if old_data_source and not replace_data_source:
        return
    if old_data_source and old_data_source.get("conn_str"):
        conn_str_dict = json.loads(data_source.get("conn_str"))
        old_conn_str_dict = json.loads(old_data_source.get("conn_str"))
        if 'params' in conn_str_dict and 'params' in old_conn_str_dict:
            conn_str_dict['params'] = old_conn_str_dict.get('params')
            data_source['conn_str'] = json.dumps(conn_str_dict)

    data_source['user_source_id'] = old_data_source.get('user_source_id') if old_data_source else ''

    scenes = data_source.get('scene') or ''
    if '数据集应用' not in scenes:
        scenes = scenes.split(',')
        scenes.append('数据集应用')
        scenes = ",".join(scenes)
        data_source['scene'] = scenes

    # 不知道这个参数的作用，数芯使用，对应工单：
    # https://www.tapd.cn/45104967/bugtrace/bugs/view?bug_id=1145104967001350711&from=wxnotification&corpid=wxfe3aa6c1dd22f053&agentid=1000048&jump_count=1
    data_source['fetch_mode'] = 'Direct'

    db.replace_multi_data("dap_m_data_source", [data_source], data_source.keys(), commit=False, condition_field=['id'])


def _replace_dataset_vars(db, dataset_vars, dataset_field_include_vars, dataset):
    """
    更新变量相关的表
    :param db:
    :param dataset_vars:
    :param dataset_field_include_vars:
    :param dataset:
    :return:
    """
    if dataset_vars and len(dataset_vars) > 0:
        db.delete("dap_bi_dataset_vars", {"dataset_id": dataset.get("id"), "created_by": "celery"}, commit=False)
        db.replace_multi_data("dap_bi_dataset_vars", dataset_vars, dataset_vars[0].keys(), commit=False, condition_field=['id'])
    if dataset_field_include_vars and len(dataset_field_include_vars) > 0:
        db.delete("dap_bi_dataset_field_include_vars", {"dataset_id": dataset.get("id"), "created_by": "celery"}, commit=False)
        db.replace_multi_data(
            "dap_bi_dataset_field_include_vars",
            dataset_field_include_vars,
            dataset_field_include_vars[0].keys(),
            commit=False,
            condition_field=['id'],
        )


def _replace_dataset_used_table(db, dataset_used_tables, dataset_id):
    """
    更新变量相关的表
    :param db:
    :param dataset_vars:
    :param dataset_field_include_vars:
    :param dataset:
    :return:
    """
    if dataset_used_tables and len(dataset_used_tables) > 0:
        db.delete("dap_bi_dataset_used_table", {"dataset_id": dataset_id}, commit=False)
        db.replace_multi_data("dap_bi_dataset_used_table", dataset_used_tables, dataset_used_tables[0].keys(), commit=False, condition_field=['id'])


def _replace_dataset_schedule_page_field(db, page_fields, dataset_id):
    if page_fields and len(page_fields) > 0:
        db.delete("dap_bi_dataset_schedule_page_field", {"dataset_id": dataset_id}, commit=False)
        db.replace_multi_data("dap_bi_dataset_schedule_page_field", page_fields, page_fields[0].keys(), commit=False)


def _replace_dataset_graph_filter(db, data, dataset_id):
    if data and len(data) > 0:
        db.delete("dap_bi_dataset_graph_filter", {"dataset_id": dataset_id}, commit=False)
        db.replace_multi_data("dap_bi_dataset_graph_filter", data, list(data[0].keys()), commit=False)


def _replace_dataset_graph_table(db, data, dataset_id):
    if data and len(data) > 0:
        db.delete("dap_bi_dataset_graph_table", {"dataset_id": dataset_id}, commit=False)
        db.replace_multi_data("dap_bi_dataset_graph_table", data, list(data[0].keys()), commit=False)


def _replace_dataset_graph_table_relation(db, data, dataset_id):
    if data and len(data) > 0:
        db.delete("dap_bi_dataset_graph_table_relation", {"dataset_id": dataset_id}, commit=False)
        db.replace_multi_data("dap_bi_dataset_graph_table_relation", data, list(data[0].keys()), commit=False)


def _replace_dataset_excel_data(db, dataset_excel_data_local_file_content, dataset_id):
    if dataset_excel_data_local_file_content:
        db.delete("dap_bi_dataset_excel_data", {"dataset_id": dataset_id}, commit=False)
        data = [{
            'id': seq_id(),
            'dataset_id': dataset_id,
            'content': dataset_excel_data_local_file_content
        }]
        db.replace_multi_data("dap_bi_dataset_excel_data", data, list(data[0].keys()), commit=False)


def create_default_folder(project_code):
    """
    创建默认分发数据集文件夹
    :param project_code:
    :return:
    """
    with get_db(project_code) as db:
        sql = "select id from dap_bi_dataset where id = '{id}' " "and type = 'FOLDER' ".format(
            id=DELIVER_DEFAULT_FOLDER_ID
        )
        parent_id = db.query_scalar(sql)
        if not parent_id:
            parent_id = DELIVER_DEFAULT_FOLDER_ID
            data = {
                "id": parent_id,
                "name": DELIVER_DEFAULT_FOLDER_NAME,
                "type": "FOLDER",
                "level_code": DELIVER_DEFAULT_FOLDER_LEVER_CODE_NEW,
                "created_by": project_code,
                "modified_by": project_code,
            }
            db.insert("dap_bi_dataset", data, commit=True)

        return parent_id


def get_operate_record_model(dataset_id, operate_mode, data_source=None, content='', fields=''):
    """
    获取数据集操作记录model
    :return:
    """
    operate_record_model = DatasetOperateRecordModel()
    operate_record_model.id = seq_id()
    operate_record_model.name = data_source
    operate_record_model.dataset_id = dataset_id
    operate_record_model.operating_mode = operate_mode
    operate_record_model.old_content = content
    operate_record_model.old_fields = fields
    return operate_record_model


def get_dataset_level_code(dataset_id, conn=None, parent_id=None):
    """
    判断数据集是否存在，存在不重新生成level_code
    :param dataset_id:
    :param conn:
    :param parent_id:
    :return:
    """
    if parent_id:
        sql = """select level_code from dap_bi_dataset where id=%(dataset_id)s and parent_id=%(parent_id)s"""
    else:
        sql = """select level_code from dap_bi_dataset where id=%(dataset_id)s"""
    params = {"dataset_id": dataset_id, "parent_id": parent_id}
    result = conn.query_one(sql, params)
    if result:
        return result.get("level_code")
    return result


def get_dataset_simple_info_by_id(dataset_id, conn=None):
    """
    判断数据集是否存在，存在不重新生成level_code
    :param dataset_id:
    :param conn:
    :param parent_id:
    :return:
    """
    sql = """select id,level_code,parent_id from dap_bi_dataset where id=%(dataset_id)s"""
    params = {"dataset_id": dataset_id}
    result = conn.query_one(sql, params) or {}
    return result


def delete_dataset_ids(project_code, dataset_ids):
    if not dataset_ids:
        return
    sql = "delete from dap_bi_dataset where id in %(dataset_ids)s"
    params = {"dataset_ids": dataset_ids}
    with get_db(project_code) as conn:
        return conn.exec_sql(sql, params)


def get_all_dataset_by_id(project_code, parent_id):
    with get_db(project_code) as conn:
        level_code = get_dataset_level_code(parent_id, conn=conn)
        # 根目录
        if not level_code:
            return {}
        child_datasets_sql = """select id, name, level_code, parent_id from dap_bi_dataset where level_code like %(level_code)s and 
                type ='FOLDER' """
        child_datasets = conn.query(child_datasets_sql, {"level_code": level_code + "%"}) or []
        child_datasets_dict = {}
        for row in child_datasets:
            if row.get("parent_id") not in child_datasets_dict.keys():
                child_datasets_dict[row.get("parent_id")] = {row.get("name"): row.get("id")}
            else:
                child_datasets_dict[row.get("parent_id")][row.get("name")] = row.get("id")
        return child_datasets_dict
