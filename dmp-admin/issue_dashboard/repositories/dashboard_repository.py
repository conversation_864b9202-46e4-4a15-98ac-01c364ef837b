#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/07/17

"""
报告分发模块之报告模块资源代码
"""


def get_son_dashboard_ids(parent_id, conn):
    """

    :param parent_id:
    :param conn:
    :return:
    """
    if not parent_id:
        return []
    sql = """select id,type from dap_bi_dashboard where parent_id=%(parent_id)s """
    params = {"parent_id": parent_id}
    return conn.query(sql, params)


def get_son_dashboard_names(parent_id, conn):
    """

    :param parent_id:
    :param conn:
    :return:
    """
    if not parent_id:
        parent_id = ''
        # 查询根目录下所有
    sql = """select name from dap_bi_dashboard where parent_id=%(parent_id)s """
    params = {"parent_id": parent_id}
    return conn.query_columns(sql, params)


def batch_get_dashboard_info(dashboard_id_list, conn):
    """

    :param dashboard_id_list:
    :param conn:
    :return:
    """
    if not dashboard_id_list:
        return []
    sql = """select id,level_code from dap_bi_dashboard where id in %(dashboard_ids)s """
    params = {"dashboard_ids": dashboard_id_list}
    return conn.query(sql, params)


def move_dashboard_tree(level_code, new_level_code, conn):
    """
    :更新level_code树
    :param :
    :return :
    """
    sql = (
        'UPDATE dap_bi_dashboard set `level_code`=replace(`level_code`,%(level_code)s,%(new_level_code)s )'
        'WHERE `level_code` like %(level_code_path)s'
    )
    params = {'level_code_path': level_code + '%', 'level_code': level_code, 'new_level_code': new_level_code}
    return conn.exec_sql(sql, params)


def get_move_list_by_level_code(level_code, conn, dashboard_type=None):
    """
    根据数据看板编码获取所有数据集
    :param str level_code:
    :param conn
    :param str dashboard_type:
    :return:
    """

    sql = (
        'SELECT id,`name`,`type`,`level_code`,`parent_id`,`created_on` FROM dap_bi_dashboard '
        'WHERE level_code like %(level_code)s'
    )
    params = {'level_code': level_code + '%'}
    if dashboard_type:
        sql += " and `type` =%(dashboard_type)s"
        params['dashboard_type'] = dashboard_type
    with conn as db:
        return db.query(sql, params)


def move_dashboard_tree_new(source_level_code, new_level_code, conn):
    import re
    # 获取对应层级下的所有子级的level_code
    old_data = get_move_list_by_level_code(source_level_code, conn)
    if not old_data:
        return
    with conn as db:
        for data in old_data:
            level_code = re.subn(r'^{}'.format(source_level_code), new_level_code, data.get('level_code'), flags=re.I)[0]
            sql = "update dap_bi_dashboard set level_code = '{}' where id = '{}';".format(level_code, data.get('id'))
            db.exec_sql(sql)


def delete_dashboard_metadata_history(dashboard_ids, conn):
    sql = "delete from dap_bi_dashboard_metadata_history where dashboard_id in %(dashboard_ids)s"
    return conn.exec_sql(sql, {'dashboard_ids': dashboard_ids})


def get_parent_dashboard_level_code(dashboard_id, conn):
    sql = "SELECT level_code from dap_bi_dashboard WHERE id = %(dashboard_id)s  LIMIT 1"
    return conn.query_scalar(sql, {'dashboard_id': dashboard_id})


def is_son_dashboard_by_dashboard_id(dashboard_id, level_code, conn):
    sql = "SELECT  id from dap_bi_dashboard WHERE id = '{}' and" \
          " level_code like '{}%' LIMIT 1".format(dashboard_id, level_code)
    return conn.query_scalar(sql)


def get_dashboard_scheduled_datasets(dashboard_id, conn):
    # 获取报告所有的调度数据集
    sql = """
    SELECT id, name from dap_bi_dataset WHERE id in (
        SELECT  source from dap_bi_dashboard_chart WHERE dashboard_id = %(dashboard_id)s
        ) and (connect_type is null or connect_type = '')
    """
    return conn.query(sql, {'dashboard_id': dashboard_id})


def update_dashboard_type_access(dashboard_id, type_access, conn):
    """
    更新报告的发布方式
    :param dashboard_id:
    :param type_access:
    :param conn:
    :return:
    """
    sql = "update dap_bi_dashboard set type_access_released = %(type_access_released)s where id = %(dashboard_id)s;"
    params = {"type_access_released": type_access, "dashboard_id": dashboard_id}
    return conn.exec_sql(sql, params)
