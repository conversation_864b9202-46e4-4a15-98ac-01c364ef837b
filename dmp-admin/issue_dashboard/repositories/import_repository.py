# -*- coding: utf-8 -*-

from dmplib.saas.project import get_db


def update_import_status(project_code: str, import_id: str, status: str, message: str):
    """
    更新导入状态
    :param project_code:
    :param import_id:
    :param status:
    :param message:
    :return:
    """
    with get_db(project_code) as db:
        db.update("dap_bi_dashboard_imports", data={"status": status, "message": message}, condition={"id": import_id})


def get_setting(project_code, item, category):
    """
    获取租户库配置信息
    :param project_code:
    :param item:
    :param category:
    :return:
    """
    with get_db(project_code) as db:
        return db.query_scalar(
            "select `value` from dap_bi_system_setting where `category`=%(category)s and `item`=%(item)s",
            {
                "category": category,
                "item": item
            }
        )


def get_project_setting(project_code, key, default_value):
    from dmplib.db.mysql_wrapper import get_db
    with get_db() as db:
        val = db.query_scalar('select `value` from `dap_bi_project_setting` where `code`=%(code)s and `key`=%(key)s', {
            'code': project_code,
            'key': key
        })
        if not val:
            return default_value
        return val


def get_dashboard_by_id(project_code: str, dashboard_id: str):
    """
    获取报告信息
    :param project_code:
    :param dashboard_id:
    :return:
    """
    sql = "select * from dap_bi_dashboard where id=%(id)s"
    with get_db(project_code) as db:
        return db.query_one(sql, {"id": dashboard_id})


def get_dashboard_released_design_data(project_code: str, dashboard_id: str):
    """
    获取报告的待还原数据
    :param project_code:
    :param dashboard_id:
    :return:
    """
    sql = "select * from dap_bi_dashboard_released_design_data where dashboard_id=%(dashboard_id)s"
    with get_db(project_code) as db:
        return db.query_one(sql, {"dashboard_id": dashboard_id})
