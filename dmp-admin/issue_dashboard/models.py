#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/07/17

"""
报告分发模块之报告模块model代码
注:表结构模型映射
"""
from base.models import BaseModel


class ProjectModel(BaseModel):
    def __init__(self, **kwargs):
        self.code = None
        self.db_name = None
        self.distribute_type = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('code', 'string', {'max': 256}))
        rules.append(('db_name', 'string', {'max': 256}))
        return rules


class DashboardModel(BaseModel):
    def __init__(self, **kwargs):
        """
        报告
        :param kwargs:
        """
        self.id = None
        self.theme = None
        self.data_report_id = None
        self.name = None
        self.type = None
        self.parent_id = None
        self.level_code = None
        self.icon = None
        self.platform = None
        self.is_multiple_screen = None
        self.status = 0
        self.default_show = None
        self.cover = None
        self.description = None
        self.layout_type = None
        self.share_secret_key = None
        self.layout = None
        self.scale_mode = None
        self.background = None
        self.rank = None
        self.build_in = None
        self.biz_code = None
        self.border = None
        self.type_access_released = None
        self.type_selector = None
        self.created_by = None
        self.modified_by = None
        self.create_type = None
        self.new_layout_type = None
        self.distribute_type = None
        self.grid_padding = None
        self.is_show_mark_img = 1
        self.application_type = 0
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        rules.append(('name', 'string', {'max': 45}))
        return rules


class DatasetOperateRecordModel(BaseModel):
    __slots__ = ['id', 'dataset_id', 'name', 'operating_mode', 'content']

    def __init__(self, **kwargs):
        self.id = None
        self.dataset_id = None
        self.name = None
        self.operating_mode = None
        self.content = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('dataset_id',))
        return rules


class LevelSequenceBaseModel(BaseModel):
    def __init__(self, **kwargs):
        self.table_name = ''
        self.table_level_id_field = 'id'
        self.table_level_code_field = 'level_code'
        self.level_id = ''
        self.attach_identify = ''
        self.unit_code_length = 4
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append((['table_name', 'table_level_id_field', 'table_level_code_field'], 'string', {'max': 100}))
        return rules


class DashboardLevelSequenceModel(LevelSequenceBaseModel):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.table_name = 'dap_bi_dashboard'
        self.max_sequence = 0


class DataSetLevelSequenceModel(LevelSequenceBaseModel):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.table_name = 'dap_bi_dataset'


class DashboardRestoreResultModel(BaseModel):
    __slots__ = ['task_id', 'status', 'error_msg', 'msg']

    def __init__(self, **kwargs):
        self.task_id = ''
        self.status = 0
        self.error_msg = ''
        self.msg = ''
        super().__init__(**kwargs)


class DesignContentModel(BaseModel):
    __slots__ = ['origin_mosql', 'origin_mysql', 'origin_dmsql', 'mysql', 'dmsql', 'dialect_setting', 'params']

    def __init__(self, **kwargs):
        self.origin_mosql = None
        self.origin_mysql = None
        self.origin_dmsql = None
        self.mysql = None
        self.dmsql = None
        self.dialect_setting = None
        self.params = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('dialect_setting', 'string', {'required': True}))
