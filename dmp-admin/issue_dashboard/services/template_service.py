#!/usr/local/bin python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------
import zipfile
import json
from urllib.request import urlretrieve
import os

# ---------------- 业务模块 ----------------
from issue_dashboard.services import import_service
from dmplib.utils.strings import seq_id
from dmplib.utils.errors import UserError
from issue_dashboard.install_handlers.dashboard_install_handler import DashboardInstallHandler
from issue_dashboard.install_handlers.dataset_install_handler import DatasetInstallHandler


def download_and_parse_zipfile(oss_url: str):
    """
    下载并解析zip压缩包
    :param oss_url:
    :return:
    """
    if not oss_url:
        raise UserError(400, 'zip包链接不存在')
    temp_file_name = seq_id()
    filename_with_path = "./{temp_file_name}.zip".format(temp_file_name=temp_file_name)
    urlretrieve(oss_url, filename_with_path)
    export_data = None
    with zipfile.ZipFile(filename_with_path, "r") as zip_file:
        for name in zip_file.namelist():
            with zip_file.open(name) as zfile:
                r_data = zfile.read()
                if r_data:
                    try:
                        export_data = json.loads(r_data.decode('utf-8'))
                    except:
                        raise UserError(400, 'zip压缩文件内容格式有误')
                    break
    if not export_data:
        raise UserError(400, '无效的数据文件格式')

    if os.path.exists(filename_with_path):
        os.remove(filename_with_path)

    return export_data


def install_template(**kwargs):
    """
    安装模板
    :param kwargs:
    :return:
    """
    oss_url = kwargs.get("file_url")
    exclude_dataset_ids = kwargs.get("exclude_dataset_ids", "")
    copy_dataset_ids = kwargs.get("copy_dataset_ids", "")
    package_data = download_and_parse_zipfile(oss_url) if oss_url else {}
    package_data = DashboardInstallHandler(package_data=package_data).execute()
    package_data = DatasetInstallHandler(
        package_data=package_data, exclude_dataset_ids=exclude_dataset_ids, copy_dataset_ids=copy_dataset_ids
    ).execute()
    kwargs["package_data"] = package_data
    import_service.import_data_for_install(**kwargs)
