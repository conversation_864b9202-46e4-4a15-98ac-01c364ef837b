#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
level_sequence
"""
from issue_dashboard.repositories import level_sequence_repository


def generate_level_code(model, separator=None, conn=None):
    """
    生成层级编码
    :param level_sequence.models.LevelSequenceBaseModel model:
    :param separator: default "-"
    :return:
    """
    model.validate()
    query_data = level_sequence_repository.get_level_sequence_record(model, conn)
    record_cnt = 0 if not query_data else query_data.get("cnt")
    if not record_cnt:
        level_sequence_repository.add_sequence(model, conn)
    cur_sequence = str(level_sequence_repository.increase_sequence(model, conn)).zfill(model.unit_code_length) + (
        separator or '-'
    )
    cur_level_code = get_cur_level_code(model, conn) or ''
    # 历史问题，可能取出的level_code已经重复
    if level_code_is_exist(cur_level_code + cur_sequence, model.table_name, conn):
        return generate_level_code(model, separator, conn)
    return cur_level_code + cur_sequence


def level_code_is_exist(level_code, table_name, conn):
    return True if level_sequence_repository.level_code_is_exist(level_code, table_name, conn) else False


def get_cur_level_code(model, conn):
    """
    获取当前层级编码
    :param level_sequence.models.LevelSequenceBaseModel model:
    :return:
    """
    return level_sequence_repository.get_cur_level_code(model, conn)
