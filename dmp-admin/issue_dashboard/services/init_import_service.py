#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
from loguru import logger

from dmplib.utils.errors import UserError
from issue_dashboard import DELIVER_STATUS_RUNNING, DELIVER_STATUS_SUCCESS, DELIVER_STATUS_FAILURE
from issue_dashboard.services import dataset_service
from ..repositories import init_import_repository
from . import dashboard_service


def update_init_import_status(project_code, init_import_id, status, msg=''):
    """
    更新初始化任务状态
    :param str project_code:
    :param str init_import_id:
    :param str status:
    :param str msg:
    :return:
    """
    return init_import_repository.update_init_import_status(project_code, init_import_id, status, msg)


def get_init_dashboard_data(db_type, dashboard_id_list):
    """
    通过查询条件获取报告信息
    :param db_type:
    :param dashboard_id_list:
    :return:
    """
    return init_import_repository.get_init_dashboard_by_db_type(db_type, dashboard_id_list)


def init_dashboard(**kwargs):
    """
    提供给celery的报告初始化服务
    :return:
    """
    project_code = kwargs.get("project_code", None)
    init_import_id = kwargs.get('init_import_id')
    if not project_code or not init_import_id:
        logger.error("[初始化报告] 租户信息为空或任务id为空")
        return

    # 更新状态为运行中
    update_init_import_status(project_code, init_import_id, DELIVER_STATUS_RUNNING)
    try:
        # 获取指定租户的初始化任务
        init_task = init_import_repository.get_init_import_task(project_code, init_import_id)
        if not init_task:
            raise Exception("[初始化报告] 任务不存在. id: '%s'" % init_import_id)

        params_json = init_task.get("params")
        params = json.loads(params_json) if params_json else {}
        db_type = params.get("db_type")

        content_json = init_task.get("content")
        dashboard_list = json.loads(content_json) if content_json else []
        dashboard_id_list = [dashboard.get('dashboard_id') for dashboard in dashboard_list]
        logger.info(f"报告数据库类型：{db_type} 初始化的报告id："+json.dumps(dashboard_id_list))
        # 依据报告id 和数据库类型查找需要初始化的报告
        dashboard_data_list = get_init_dashboard_data(db_type, dashboard_id_list)
        if not dashboard_data_list:
            raise UserError(message="[初始化报告] 未找到可初始化的报告")

        # 将报告数据合并
        all_dashboard_data = merge_dashboard_data(dashboard_data_list)
        # 报告数据执行初始化（分发逻辑）
        for data_type, dashboard_node in all_dashboard_data.items():
            if data_type == 'dashboards':
                init_dashboards(project_code, dashboard_node)
            elif data_type == 'datasets':
                init_datasets(project_code, dashboard_node)
            # todo 预留初始化手工填报的处理

        update_init_import_status(project_code, init_import_id, DELIVER_STATUS_SUCCESS, 'success')
    except Exception as e:
        err_msg = f"初始化报告异常，errs：{e.__str__()}"
        logger.exception(err_msg)
        update_init_import_status(project_code, init_import_id, DELIVER_STATUS_FAILURE, err_msg)


def merge_dashboard_data(dashboard_data_list):
    """
    将多个报告按类型合并，例如大小屏，填报
    将多个同类型的报告的数据节点合并为一个
    :param dashboard_data_list:
    :return:
    {
        "dashboards": {
            "a": {# a报告的相关节点},
            "b": {# b报告的相关节点},
        },
        "fillings": {
            "x": {# x报告的相关节点},
            "z": {# z报告的相关节点},
        },
        "datasets": [
            {# a1数据集的相关节点},
            {# a2数据集的相关节点}
        ]
    }
    """
    all_data = {
        "dashboards": {},
        "fillings": {},
        "datasets": [],
    }
    # todo 枚举类在另一个分支，待代码全部合并后改造
    data_type_map = {
        # 大小屏
        "dashboard": "dashboards",
        # 手工填报
        "filling": "fillings",
    }
    # 按报告类型数据合并
    for dashboard_item in dashboard_data_list:
        dashboard_content = dashboard_item.get("content")
        data_type = dashboard_item.get("data_type")
        if dashboard_content:
            dashboard_info = json.loads(dashboard_content)
            dashboard_key = data_type_map.get(data_type, '')
            if not dashboard_key:
                logger.info(f"不支持初始化的报告类型,data_type:{data_type}")
                continue
            # 获取报告
            dashboard_zip_data = dashboard_info.get(dashboard_key)
            if dashboard_zip_data:
                all_data[dashboard_key].update(dashboard_zip_data)
            # 获取数据集
            dataset_zip_data = dashboard_info.get('datasets')
            if dataset_zip_data:
                all_data["datasets"].extend(dataset_zip_data)

    # dataset数据集去重
    datasets = all_data.get("datasets")
    if datasets:
        all_data["datasets"] = [dict(json.loads(nd)) for nd in set([json.dumps(tuple(d.items())) for d in datasets])]
    return all_data


def init_dashboards(project_code, dashboards):
    if dashboards:
        try:
            # 初始化dashboard
            dashboard_service.deliver_dashboard_data(project_code, dashboards, 1)
        except Exception as e:
            err_msg = '初始化报告出错! error: %s' % e.__str__()
            logger.exception(err_msg)
            raise Exception(err_msg)


def init_datasets(project_code, datasets):
    if datasets:
        try:
            # 初始化dataset
            dataset_service.multi_import_dataset_data(project_code, datasets,
                                                      replace_data_source=False,
                                                      is_lock=1)
        except Exception as e:
            err_msg = '初始化数据集出错! error: %s' % e.__str__()
            logger.exception(err_msg)
            raise Exception(err_msg)
