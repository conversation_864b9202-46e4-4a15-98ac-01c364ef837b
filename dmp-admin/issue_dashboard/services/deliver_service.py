#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import datetime
import hashlib
import io
import json
import os.path
import traceback
import uuid
import zipfile
import logging
from hashlib import md5
import time

import app_celery
from components.fast_logger import FastLogger
from components.qw_message import send_publish_center_error
from dmplib import config
from dmplib.components import auth_util
from dmplib.db import mysql_wrapper
from dmplib.saas.project import get_db
from dmplib.utils.strings import seq_id
from dmplib.utils.errors import UserError
from base import repository
from base.db_wrap import DbWrap
from base.enums import ProjectValueAddedFunc, DeliverStatus, DistributeType, ProjectEnabled, ApplicationDistributeType
from issue_dashboard import DELIVER_STATUS_RUNNING, DELIVER_STATUS_SUCCESS, DELIVER_STATUS_FAILURE
from issue_dashboard.services import dataset_service, level_sequence_service, import_simple_file, tag_relation_service
from user.services.user_service import DEFAULT_USER_ID
from ..models import DashboardLevelSequenceModel, DashboardModel
from ..repositories import deliver_repository
from components.oss import OSSFileProxy
from urllib import parse
from dmplib.components.enums import DBType
from . import dashboard_service, application_service, filling_service
from .biz_link_service import BizLinkService
from dmplib.redis import RedisCache
from components.file_cache import FileCache
from issue_dashboard.services import feed_service
from issue_dashboard.repositories import import_repository

logger = logging.getLogger(__name__)


class ErrorDeliverDataset(Exception):
    pass


class ErrorDeliverDashboard(Exception):
    pass


class ErrorDeliverFeed(Exception):
    pass


def insert_deliver(
        **kwargs
):
    export_id = kwargs.get("export_id")
    title = kwargs.get("title")
    description = kwargs.get("description")
    source_url = kwargs.get("source_url")
    source_project = kwargs.get("source_project")
    dest_projects = kwargs.get("dest_projects")
    is_all_projects = kwargs.get("is_all_projects")
    distribute_type = kwargs.get("distribute_type")
    replace_data_source = kwargs.get("replace_data_source")
    is_lock_dataset = kwargs.get("is_lock_dataset")
    is_lock_application = kwargs.get("is_lock_application")
    operate_type = kwargs.get("operate_type")
    recovery_type = kwargs.get("recovery_type")
    is_publish_center = kwargs.get("is_publish_center") or "0"

    db = mysql_wrapper.get_db()
    deliver_id = seq_id()
    data_row = {
        'id': deliver_id,
        'export_id': export_id,
        'title': title,
        'description': description,
        'source_url': source_url,
        'source_project': source_project,
        'dest_projects': ','.join(dest_projects),
        'is_all_projects': is_all_projects,
        'distribute_type': distribute_type,
        'replace_data_source': replace_data_source,
        'is_lock_dataset': is_lock_dataset,
        'is_lock_application': is_lock_application,
        "operate_type": operate_type,
        "recovery_type": recovery_type,
        "is_publish_center": is_publish_center
    }
    db.insert('dap_bi_deliver_dashboard', data_row)
    return deliver_id


def update_deliver_status(deliver_id, status, failed_projects, result):
    """
    更新分发状态
    :param str deliver_id:
    :param str status:
    :param str failed_projects:
    :param result:
    :return:
    """
    db = mysql_wrapper.get_db()
    row = {'status': status, 'result': result, 'failed_projects': failed_projects}
    if status in (DELIVER_STATUS_SUCCESS, DELIVER_STATUS_FAILURE):
        row['completed_on'] = time.strftime('%Y-%m-%d %H:%M:%S')

    return db.update('dap_bi_deliver_dashboard', row, condition={'id': deliver_id})


def list_deliver(keywords, start_time, end_time, operate_type, page=1, pagesize=20):
    return deliver_repository.list_delivers(keywords, start_time, end_time, operate_type, page, pagesize)


def read_file_from_oss(file_url):
    if not file_url:
        return None

    oss_file = OSSFileProxy()
    obj_result = oss_file.get_object(parse.unquote(file_url), True)
    if not obj_result:
        raise UserError(400, '读取文件失败')

    bytes_data = obj_result.read()
    if len(bytes_data) == 0:
        raise UserError(400, '文件为空，读取失败')
    return bytes_data


def parse_data(file_url):
    if not file_url:
        return None

    key = md5(file_url.encode()).hexdigest()
    cache = FileCache()
    export_data = cache.get(key)
    if export_data:
        export_data = json.loads(export_data)
        # return json.loads(export_data)
    else:
        try:
            export_data = import_simple_file.parse_zip_file(file_url)
            if not export_data:
                raise UserError(400, '无效的数据文件格式')
        except Exception as e:
            logger.error(f'通过文件客户端下载失败，尝试从URL直接下载:{file_url}')
            prev = file_url
            for i in range(5):
                file_url = parse.unquote(file_url)
                if prev == file_url:
                    break
                else:
                    prev = file_url
            export_data = import_simple_file.parse_zip_url(file_url)
            if not export_data:
                raise e

        cache.set(key, json.dumps(export_data))

    # 处理dist目录资源文件
    export_data = import_simple_file.process_dist_data(export_data, file_url)
    return export_data

def get_data_from_url(file_url):
    from urllib import request
    oss = OSSFileProxy()
    try:
        try:
            temp_file = oss.get_object(file_url.split('?')[0], is_url=True)
            data = io.BytesIO(temp_file.read())
            return data
        except Exception as e:
            logger.error(f"oss客户端获取文件失败： {str(e)}，尝试直接网络下载：{file_url}")
            url = parse.quote(file_url, safe=':/?=&')
            temp_file = request.urlopen(url)
            data = io.BytesIO(temp_file.read())
            return data
    except Exception as e:
        raise UserError(message=str(e))

def extract_files_from_zip(file_url):
    """
    将所有的文件解压到固定文件夹
    """
    if not file_url:
        return None

    # oss_file = OSSFileProxy()
    # obj_result = oss_file.get_object(parse.unquote(file_url), True)
    # if not obj_result:
    #     raise UserError(400, '读取文件失败')
    #
    # bytes_data = obj_result.read()
    # if len(bytes_data) == 0:
    #     raise UserError(400, '文件为空，读取失败')
    # data = io.BytesIO(bytes_data)
    data = get_data_from_url(file_url)
    folder = md5(file_url.encode()).hexdigest()
    zip_extract_path = os.path.join('/tmp/', folder)
    with zipfile.ZipFile(data, "r") as zip_file:
        zip_file.extractall(zip_extract_path)
    return zip_extract_path


def upload_file_to_oss(file_path, folder='data'):
    if not file_path:
        return None

    key = md5(file_path.encode()).hexdigest()
    cache = RedisCache(key_prefix='upload_file_oss_cache:')
    upload_oss_url = cache.get(key)
    if isinstance(upload_oss_url, bytes):
        upload_oss_url = upload_oss_url.decode()
    if upload_oss_url:
        return upload_oss_url

    with open(file_path, 'rb') as file:
        oss_url = OSSFileProxy().upload(file, file_name=os.path.basename(file_path), root=f'upload-file/{folder}')
    cache.set(key, oss_url, 3600)
    return oss_url


def upload_file_to_oss_by_md5(file_path, folder='data'):
    if not file_path:
        return None

    key = calculate_md5(file_path)
    file_name_key = key+"_file_name"
    cache = RedisCache(key_prefix='upload_file_oss_cache:')
    upload_oss_url = cache.get(key)
    upload_oss_filename = cache.get(file_name_key)
    if isinstance(upload_oss_url, bytes):
        upload_oss_url = upload_oss_url.decode()
    if isinstance(upload_oss_filename, bytes):
        upload_oss_filename = upload_oss_filename.decode()
    if upload_oss_url and upload_oss_filename:
        return upload_oss_url, upload_oss_filename

    with open(file_path, 'rb') as file:
        _, file_extension = os.path.splitext(os.path.basename(file_path))
        new_file_name = str(uuid.uuid4()).replace('-', '') + file_extension
        oss_url = OSSFileProxy().upload(file, file_name=new_file_name, root=f'upload-file/{folder}', return_intranet_url=True)
    cache.set(key, oss_url, 3600)
    cache.set(file_name_key, new_file_name, 3600)
    return oss_url, new_file_name


def calculate_md5(file_path):
    md5_hash = hashlib.md5()
    with open(file_path, "rb") as file:
        # 逐块读取文件并更新MD5哈希值
        for chunk in iter(lambda: file.read(4096), b""):
            md5_hash.update(chunk)
    return md5_hash.hexdigest()


def deliver_single(
        deliver_id="",  # NOSONAR
        project_code="",  # NOSONAR
        source_project="",  # NOSONAR
        dashboards=None,  # NOSONAR
        large_screens=None,  # NOSONAR
        distribute_type=None,  # NOSONAR
        failed_projects=None,  # NOSONAR
        datasets=None,  # NOSONAR
        applications=None,  # NOSONAR
        fillings=None,  # NOSONAR
        replace_data_source=None,  # NOSONAR
        is_lock_dataset=None,  # NOSONAR
        is_lock_application=None,  # NOSONAR
        is_new_jump=None,   # NOSONAR
        error_list: list = None,  # NOSONAR
        data_reporting=None,
        feeds=None,
        active_reports=None,
        is_report_center=False,
        ppts=None,
        userid=None,
        oss_file='',
        version='',
        is_async=True,
        export_excel_data=[],
        from_template=0
):  # NOSONAR

    repository.update_data('dap_bi_deliver_dashboard_log', {'status': DeliverStatus.RUNNING.value}, {'target_project_code': project_code, 'deliver_id': deliver_id})
    start_time = int(time.time())
    deliver_data = deliver_repository.get_deliver_meta(deliver_id)
    try:

        if project_code == source_project:
            update_deliver_to_ignore(project_code, deliver_id, '原始租户不用分发')
            return

        # 租户禁用了，则状态标记为“忽略”
        from project.services import project_service
        project_enabled = project_service.get_project_is_enabled(project_code)
        if project_enabled is not None and project_enabled == ProjectEnabled.Disable.value:
            update_deliver_to_ignore(project_code, deliver_id)
            return

        if dashboards:
            st1 = int(time.time())
            try:
                # 分发dashboard
                dashboard_service.deliver_dashboard_data(project_code, dashboards, distribute_type, is_new_jump=is_new_jump)
            except BaseException as dae:
                logging.exception(dae)
                raise ErrorDeliverDashboard('分发报告出错! error: %s' % dae.__str__())
            ed1 = int(time.time())
            logger.error(f'报表分发耗时： {ed1-st1}s')

        if large_screens:
            st2 = int(time.time())
            try:
                # 分发酷炫大屏
                dashboard_service.deliver_large_screen_data(project_code, large_screens, distribute_type, is_new_jump=is_new_jump)
            except BaseException as dae:
                logging.exception(dae)
                raise ErrorDeliverDashboard('分发酷炫大屏出错! error: %s' % dae.__str__())
            ed2 = int(time.time())
            logger.error(f'大屏分发耗时： {ed2-st2}s')

        if data_reporting:
            st3 = int(time.time())
            try:
                # 分发数据报表
                dashboard_service.deliver_data_reporting_data(project_code, data_reporting, distribute_type, is_new_jump=is_new_jump)
            except BaseException as dae:
                logging.exception(dae)
                raise ErrorDeliverDashboard('分发数据报表出错! error: %s' % dae.__str__())
            ed3 = int(time.time())
            logger.error(f'数据报告分发耗时： {ed3 - st3}s')

        if datasets:
            st4 = int(time.time())
            try:
                # 分发dataset
                dataset_service.multi_import_dataset_data(project_code, datasets,
                                                          replace_data_source=replace_data_source,
                                                          is_lock=is_lock_dataset, oss_file=oss_file,
                                                          export_excel_data=export_excel_data,from_template=from_template),
            except BaseException as dse:
                logging.exception(dse)
                raise ErrorDeliverDataset('分发数据集出错! error: %s' % dse.__str__())
            ed4 = int(time.time())
            logger.error(f'数据集报告分发耗时： {ed4 - st4}s')

        if applications:
            st5 = int(time.time())
            try:
                # 分发门户时过滤租户中已经存在的门户
                filter_app = filter_application(applications, project_code)
                is_lock_application = ApplicationDistributeType.DistributeAndNotLock.value if is_lock_application is None else is_lock_application
                application_service.ApplicationDeliverHandler(project_code, filter_app, is_lock_application).deliver()
            except BaseException as dse:
                logging.exception(dse)
                raise ErrorDeliverDataset('分发门户出错! error: %s' % dse.__str__())
            ed5 = int(time.time())
            logger.error(f'门户分发耗时： {ed5 - st5}s')

        if fillings:
            st6 = int(time.time())
            try:
                # 分发填报
                filling_service.deliver_filling_data(project_code, fillings)
            except BaseException as dae:
                logging.exception(dae)
                raise ErrorDeliverDashboard('分发数据填报出错! error: %s' % dae.__str__())
            ed6 = int(time.time())
            logger.error(f'手工填报分发耗时： {ed6 - st6}s')

        if feeds:
            st7 = int(time.time())
            try:
                feeds = filter_feeds(feeds, project_code)
                feed_service.FeedDeliverHandler(project_code, feeds, []).import_feed()
            except Exception as e:
                logging.exception(e)
                raise ErrorDeliverFeed('简讯分发出错! error: %s' % e.__str__())
            ed7 = int(time.time())
            logger.error(f'简讯分发耗时： {ed7 - st7}s')

        post_process(project_code)
        # 关闭连接
        if is_async:
            conn = get_db(project_code)
            conn.end()

        if active_reports:
            begin = time.time()
            #统计报表同步分发
            import_res = import_active_report(deliver_data, active_reports, userid, project_code, is_report_center, True)
            logger.error(f'同步分发统计报表耗时{(time.time() - begin) * 1000}s 分发结果:{import_res}')
            update_active_report_deliver_status(import_res, project_code)
        else:
            end_time = int(time.time())
            repository.update_data(
                'dap_bi_deliver_dashboard_log',
                {'status': DeliverStatus.FINISH.value, 'log_info': 'success', 'run_time': end_time - start_time},
                {'target_project_code': project_code, 'deliver_id': deliver_id}
            )
            update_deliver_dashboard_status(deliver_id)

        if ppts:
            import_ppt(deliver_data, ppts, userid, project_code)

    except BaseException as e:
        logger.exception(e)
        # 添加到失败projects
        failed_projects.append(project_code)
        error_list.append(e.__str__())
        end_time = int(time.time())
        repository.update_data(
            'dap_bi_deliver_dashboard_log',
            {'status': DeliverStatus.FAIL.value, 'log_info': e.__str__(), 'run_time': end_time - start_time},
            {'target_project_code': project_code, 'deliver_id': deliver_id}
        )
        update_deliver_dashboard_status(deliver_id)
        raise e
    finally:
        logger.error(f'总分发耗时： {int(time.time()) - start_time}s')

def update_active_report_deliver_status(res, project_code):
    if not res:
        raise UserError(message=f'统计报表分发异常:请求结果为空')
    res = res.get('deliver_result', {})
    deliver_res = res.get(project_code)
    if not deliver_res:
        raise UserError(message=f'统计报表分发异常:{res}')
    is_success, error = import_callback_deliver_simple(**deliver_res)
    if not is_success:
        raise UserError(message=f'统计报表分发失败:{error}')

def update_deliver_to_ignore(project_code, deliver_id, ignore_info='', run_time=0):
    """
    更新分发明细记录状态为忽略
    :param project_code:
    :param deliver_id:
    :param ignore_info:
    :param run_time:
    :return:
    """
    ignore_info = ignore_info if ignore_info else '租户被禁用'
    repository.update_data(
        'dap_bi_deliver_dashboard_log',
        {'status': DeliverStatus.IGNORE.value, 'log_info': ignore_info, 'run_time': run_time},
        {'target_project_code': project_code, 'deliver_id': deliver_id}
    )
    update_deliver_dashboard_status(deliver_id)


def import_active_report(deliver_data, active_reports, userid, tenant_code, is_report_center, is_sync=False):
    """
    业务系统报告的分发报告接口请求
    :param deliver_data:
    :param ppts:
    :param active_reports:
    :param userid:
    :return:
    """
    is_all_projects = deliver_data.get("is_all_projects")
    import_id = deliver_data.get("id")

    import_data = {
        "tenant_code_list": [tenant_code],
        "file_url": deliver_data.get("source_url"),
        "target_dashboard_folder_id": "",
        "source_project": deliver_data.get("source_project"),
        "is_report_center": is_report_center
    }

    if active_reports:
        active_report_ids = active_reports.get("ids")
        biz_link_service = BizLinkService(ProjectValueAddedFunc.ACTIVE_REPORT.value, None, userid)
        return biz_link_service.import_report(active_report_ids, "deliver", import_id, import_data, bool(int(is_all_projects)), is_sync)
    return {}

def import_ppt(deliver_data, ppts, userid, tenant_code):
    """
    业务系统报告的分发报告接口请求
    :param deliver_data:
    :param ppts:
    :param active_reports:
    :param userid:
    :return:
    """
    import_id = deliver_data.get("id")
    import_data = {
        "tenant_code_list": [tenant_code],
        "file_url": deliver_data.get("source_url"),
        "target_dashboard_folder_id": "",
        "source_project": deliver_data.get("source_project"),
    }
    if ppts:
        ppt_ids = ppts.get("ids")
        biz_link_service = BizLinkService(ProjectValueAddedFunc.PPT.value, None, userid)
        biz_link_service.import_report(ppt_ids, "deliver", import_id, import_data, False)


def custom_import_valid_for_yk(import_data: dict):
    """
    特殊为云客定制导入的数据校验
    https://tapd.cn/38229611/prong/stories/view/1138229611001597936
    仅在包含数据集的情况下触发
    """
    from_env = import_data.get("env") or ""
    curr_env = config.get('App.dmp_env_code', '') or auth_util.get_env_code()

    yk_ali_envs_str = config.get('External.yunke_aliyun_env_code', 'yumke_dmp,yunke_aliyun_prod_gray,dmp_yunke')
    yk_ali_envs = [s.strip() for s in yk_ali_envs_str.split(',')]
    yk_hw_envs_str = config.get('External.yunke_huawei_env_code', 'yk-hw-dap-saas-new,yk-hw-dap-test')
    yk_hw_envs = [s.strip() for s in yk_hw_envs_str.split(',')]
    # yk_ali_envs = ['yumke_dmp', 'yunke_aliyun_prod_gray', 'dmp_yunke']
    # yk_hw_envs = ['yk-hw-dap-saas-new', 'yk-hw-dap-test']

    if not from_env:
        # 2024.08.22 如果是来自华为环境的导出历史包，标记为阿里导出的（不让导入历史的包）
        from_env = yk_ali_envs[0]

    if from_env in yk_hw_envs and curr_env in yk_ali_envs:
        # 从华为云客导入阿里云客
        raise UserError(message="导入文件与当前环境数据引擎不同，不支持导入。")

    if not import_data.get("datasets", []):
        return

    if from_env in yk_ali_envs and curr_env in yk_hw_envs:
        # 从阿里云客导入华为云客
        raise UserError(message="导入文件与当前环境数据引擎不同，不支持直接导入。如需导入可参考如下文档进行转换操作。<a href='https://doc.weixin.qq.com/doc/w3_ACkAGQYnALYCW1zG93vSpKPIvHNVD?scode=AIsAVQcAABIZeLjggxAAYAmgafAB0'>操作指引</a>")


def deliver_split(deliver_id=None, distribute_type=0, is_lock_dataset=0, is_lock_application=None, userid=None, is_async=True, skip_feeds = 0, from_template = 0, export_data = None):
    """
    提供给celery的报告分发服务
    :param deliver_id:
    :param distribute_type:
    :param is_lock_dataset:
    :param userid: 当前用户id
    :return:
    """
    if not deliver_id:
        logger.error("[分发报告] deliver_id为空")
        return
    # 控制门户是否锁定，默认跟distribute_type保持一致
    is_lock_application = ApplicationDistributeType.DistributeAndNotLock.value if is_lock_application is None else is_lock_application

    # 更新状态
    update_deliver_status(deliver_id, DELIVER_STATUS_RUNNING, '', '')
    failed_projects = []
    error_list = []

    try:
        deliver_data = deliver_repository.get_deliver_meta(deliver_id)
        if not deliver_data:
            raise Exception("[分发报告] deliver数据不存在. id: '%s'" % deliver_id)

        if export_data:
            zip_meta = export_data
        else:
            zip_meta = parse_data(deliver_data['source_url'])

        if not zip_meta:
            raise Exception("[分发报告] 没有读取到zip包的数据, zip文件地址: %s" % deliver_data['source_url'])

        dest_projects = deliver_data['dest_projects'].split(',') if deliver_data['dest_projects'] else []
        is_all_projects = deliver_data['is_all_projects']
        source_project = deliver_data['source_project']
        ppts = zip_meta.get(ProjectValueAddedFunc.PPT.value, {})
        active_reports = zip_meta.get(ProjectValueAddedFunc.ACTIVE_REPORT.value, {})
        report_centers = zip_meta.get(ProjectValueAddedFunc.REPORT_CENTER.value)
        is_report_center = False
        if report_centers:
            is_report_center = True

        if is_all_projects:
            from project.services import project_service

            dest_projects = project_service.get_all_project_codes()

        if len(dest_projects) > int(config.get('Function.backup_project_limits') or 10):
            is_backup_dashboard = False
        else:
            is_backup_dashboard = True

        # 将分发的租户记录到日志表中
        init_deliver_dashboard_log(deliver_id, dest_projects, source_project)

        for project_code in dest_projects:
            args = dict(
                deliver_id=deliver_id,
                project_code=project_code,
                distribute_type=distribute_type,
                failed_projects=failed_projects,
                is_lock_dataset=is_lock_dataset,
                is_lock_application=is_lock_application,
                error_list=error_list,
                active_reports=active_reports,
                is_report_center=is_report_center,
                ppts=ppts,
                userid=userid,
                is_async=is_async,
                skip_feeds=skip_feeds,
                from_template=from_template,
                is_backup_dashboard=is_backup_dashboard,
            )
            if export_data and export_data.get("app_code"):
                    args["is_publish_center"] = True
                    args["app_code"] = export_data.get("app_code")
                    args["task_id"] = deliver_data.get("export_id")
            if is_async:
                app_celery.deliver_single_dashboard.apply_async(kwargs=args)
            else:
                args["export_data"] = export_data
                app_celery.deliver_single_dashboard(**args)

    except BaseException as e:
        logger.exception(e)
        if export_data and export_data.get("app_code"):
            logger.error('正在发送开户失败告警邮件')
            log_dict = {
                'deliver_id': deliver_id
            }
            fast_log = FastLogger.PublishCenterFastLogger(**log_dict)
            fast_log.error_traceback = traceback.format_exc()
            fast_log.error_level = 'ERROR'
            fast_log.record()
            task_id = deliver_data.get("export_id") if deliver_data else ""
            code = deliver_data.get("dest_projects") if deliver_data else deliver_id
            send_publish_center_error(task_id, code, export_data.get("app_code"), str(e), fast_log.error_traceback)
        try:
            update_deliver_status(deliver_id, DELIVER_STATUS_FAILURE, ','.join(failed_projects), e.__str__())
        except Exception as ee:
            logger.error(f'修改分发记录状态出错:{str(ee)}')
        raise e


def auto_deliver_split(kwargs):
    from user.services.user_service import DEFAULT_USER_ID
    deliver_id = kwargs.get('deliver_id')
    deliver(deliver_id, userid=DEFAULT_USER_ID)


def deliver(deliver_id, distribute_type=0, is_lock_dataset=0, userid=None):  # NOSONAR
    """
    提供给celery的报告分发服务
    :param deliver_id:
    :param distribute_type:
    :param is_lock_dataset:
    :param userid: 当前用户id
    :return:
    """
    if not deliver_id:
        logger.error("[分发报告] deliver_id为空")
        return

    # 更新状态
    update_deliver_status(deliver_id, DELIVER_STATUS_RUNNING, '', '')
    failed_projects = []

    try:
        deliver_data = deliver_repository.get_deliver_meta(deliver_id)
        if not deliver_data:
            raise Exception("[分发报告] deliver数据不存在. id: '%s'" % deliver_id)
        zip_meta = parse_data(deliver_data['source_url'])

        if not zip_meta:
            raise Exception("[分发报告] 没有读取到zip包的数据, zip文件地址: %s" % deliver_data['source_url'])

        dashboards = zip_meta.get('dashboards')
        large_screens = zip_meta.get('large_screens')
        datasets = zip_meta.get('datasets')
        applications = zip_meta.get('applications')
        is_new_jump = zip_meta.get('is_new_jump')
        export_excel_data = zip_meta.get('export_excel_data') or []
        dest_projects = deliver_data['dest_projects'].split(',') if deliver_data['dest_projects'] else []
        is_all_projects = deliver_data['is_all_projects']
        source_project = deliver_data['source_project']
        replace_data_source = bool(deliver_data['replace_data_source'])
        ppts = zip_meta.get(ProjectValueAddedFunc.PPT.value, {})
        # 手工填报
        fillings = zip_meta.get('fillings', {})
        feeds = zip_meta.get('feeds', [])

        if is_all_projects:
            from project.services import project_service

            dest_projects = project_service.get_all_project_codes()

        for project_code in dest_projects:

            # skip 源项目
            if project_code == source_project:
                continue

            if dashboards:
                try:
                    # 分发dashboard
                    dashboard_service.deliver_dashboard_data(project_code, dashboards, distribute_type, is_new_jump=is_new_jump)
                except BaseException as dae:
                    logging.exception(dae)
                    # 添加到失败projects
                    failed_projects.append(project_code)
                    raise ErrorDeliverDashboard('分发报告出错! error: %s' % dae.__str__())

            if large_screens:
                try:
                    # 分发dashboard
                    dashboard_service.deliver_large_screen_data(project_code, large_screens, distribute_type, is_new_jump=is_new_jump)
                except BaseException as dae:
                    logging.exception(dae)
                    # 添加到失败projects
                    failed_projects.append(project_code)
                    raise ErrorDeliverDashboard('分发酷炫大屏出错! error: %s' % dae.__str__())

            if datasets:
                try:
                    # 分发dataset
                    dataset_service.multi_import_dataset_data(project_code, datasets,
                                                              replace_data_source=replace_data_source,
                                                              is_lock=is_lock_dataset,
                                                              oss_file=deliver_data['source_url'],
                                                              export_excel_data=export_excel_data)
                except BaseException as dse:
                    logging.exception(dse)
                    # 添加到失败projects
                    failed_projects.append(project_code)
                    raise ErrorDeliverDataset('分发数据集出错! error: %s' % dse.__str__())

            if applications:
                try:
                    filter_app = filter_application(applications, project_code)
                    application_service.ApplicationDeliverHandler(project_code, filter_app, distribute_type).deliver()
                except BaseException as dse:
                    logging.exception(dse)
                    # 添加到失败projects
                    failed_projects.append(project_code)
                    raise ErrorDeliverDataset('分发门户出错! error: %s' % dse.__str__())

            if fillings:
                try:
                    # 分发填报
                    filling_service.deliver_filling_data(project_code, fillings)
                except BaseException as dae:
                    logging.exception(dae)
                    # 添加到失败projects
                    failed_projects.append(project_code)
                    raise ErrorDeliverDashboard('分发数据填报出错! error: %s' % dae.__str__())

            if feeds:
                try:
                    feeds = filter_feeds(feeds, project_code)
                    feed_service.FeedDeliverHandler(project_code, feeds, []).import_feed()
                except Exception as e:
                    logging.exception(e)
                    raise ErrorDeliverFeed('简讯分发出错! error: %s' % e.__str__())

            post_process(project_code)
            # 关闭连接
            conn = get_db(project_code,)
            conn.end()

        update_deliver_status(deliver_id, DELIVER_STATUS_SUCCESS, '', 'success')
        # 请求业务系统的导入接口，分发在线报告，一次请求分发接口。在线报告分发的异步结果更新到分发记录即可，不论导入是否成功，数据集都会继续导入。
        import_report(deliver_data, ppts, userid)

    except BaseException as e:
        logger.exception(e)
        update_deliver_status(deliver_id, DELIVER_STATUS_FAILURE, ','.join(failed_projects), e.__str__())

def post_process(project_code):
    # 执行指定SQL
    logging.error('分发SQL后置执行')
    data = repository.get_columns('dap_bi_delivery_post_process_sql', None, 'sql')
    if not data:
        logging.error('分发SQL后置执行查询无数据')
        return
    logging.error(f'分发后待执行SQL共[{len(data)}]条')
    for sql in data:
        logging.error(f'开始执行SQL:{sql}')
        try:
            affect = get_db(project_code, ).exec_sql(sql, params=None, commit=True)
            logging.error(f'执行完成,影响行数[{affect}]行')
        except Exception as e:
            logging.error(f'执行失败:{str(e)}')


def import_report(deliver_data, ppts, userid):
    """
    业务系统报告的分发报告接口请求
    :param deliver_data:
    :param ppts:
    :param active_reports:
    :param userid:
    :return:
    """
    dest_projects = deliver_data['dest_projects'].split(',') if deliver_data['dest_projects'] else []
    is_all_projects = deliver_data.get("is_all_projects")
    import_id = deliver_data.get("id")

    import_data = {
        "tenant_code_list": dest_projects,
        "file_url": deliver_data.get("source_url"),
        "target_dashboard_folder_id": "",
        "source_project": deliver_data.get("source_project"),
    }
    if ppts:
        ppt_ids = ppts.get("ids")
        biz_link_service = BizLinkService(ProjectValueAddedFunc.PPT.value, None, userid)
        biz_link_service.import_report(ppt_ids, "deliver", import_id, import_data, bool(int(is_all_projects)))


def get_deliver_log(deliver_id):
    if not deliver_id:
        raise UserError(400, '无效的deliver_id')

    return repository.get_data_scalar_by_sql('select result from dap_bi_deliver_dashboard where id=%(id)s', {'id': deliver_id})


def get_deliver_detail(deliver_id):
    if not deliver_id:
        raise UserError(400, '无效的deliver_id')

    select_fields = [
        'id',
        'title',
        'export_id',
        'description',
        'source_project',
        'source_url',
        'dest_projects',
        'is_all_projects',
        'status',
        'distribute_type',
        'replace_data_source',
        'is_lock_dataset',
        'operate_type',
        'recovery_type'
    ]
    return repository.get_data('dap_bi_deliver_dashboard', {'id': deliver_id}, select_fields, multi_row=False)


def get_deliver_dashboard_by_id(deliver_id: str):
    """
    获取分发记录-配置库表
    :param deliver_id:
    :return:
    """
    if not deliver_id:
        return {}
    return repository.get_data('dap_bi_deliver_dashboard', {'id': deliver_id}, multi_row=False, fields=None)


def update_deliver_dashboard_by_id(deliver_id, data_row):
    """
    更新分发记录-配置库表
    :param deliver_id:
    :param data_row:
    :return:
    """
    if not deliver_id:
        return False
    return repository.update_data('dap_bi_deliver_dashboard', data_row, {'id': deliver_id})


def deliver_callback(**kwargs):
    """
    统计报表将不在使用该回调
    原有分发回调逻辑，一起返回所有的分发记录，会导致日志记录和分发行为不匹配（现有分发逻辑，每个分发租户一个异步任务）
    新的回调将使用import_callback_deliver_simple,每个租户分发成功后，都将接收一个自己分发结果的回调
    在线、明细报告分发任务的回调处理逻辑
    :param kwargs:
    :return:
    """
    try:
        logger.info("报告分发回调请求参数：" + json.dumps(kwargs, ensure_ascii=False))
        # 操作类型进行区分，不同类型处理的逻辑不同 import：导入，deliver：分发
        import_id = kwargs.get("import_id")
        action_type = kwargs.get("action_type")
        if action_type == 'deliver':
            import_callback_deliver(**kwargs)
        else:
            raise UserError(message="报告回调类型错误")

        logger.error(f"分发任务回调完成！import_id:{import_id}")
        return True
    except Exception as e:
        raise UserError(message=f"报告分发回调接口异常，{str(e)}")


def import_callback_deliver(**kwargs):
    """
    统计报表将不在使用该回调
    原有分发回调逻辑，一起返回所有的分发记录，会导致日志记录和分发行为不匹配（现有分发逻辑，每个分发租户一个异步任务）
    新的回调将使用import_callback_deliver_simple,每个租户分发成功后，都将接收一个自己分发结果的回调
    报告分发的回调处理逻辑
    判断分发的结果，更新分发记录的信息和失败租户
    :param kwargs:
    :return:
    """
    deliver_id = kwargs.get("import_id")
    deliver_data = get_deliver_dashboard_by_id(deliver_id)
    if not deliver_data:
        raise UserError(message="分发任务不存在")

    if not deliver_data.get("callback_result"):
        import_result = kwargs.get("import_result")
        status = DELIVER_STATUS_SUCCESS if import_result else DELIVER_STATUS_FAILURE

        deliver_import_data = kwargs.get("import_data")
        deliver_failure_list = deliver_import_data.get("failure_list")

        # 分发信息合并
        deliver_msg = kwargs.get("msg")
        deliver_result = deliver_data.get("result") if deliver_data.get("result") else "结果为空"
        deliver_msg = '业务报告分发：' + deliver_msg + ';' + '数据集分发：' + deliver_result

        # 业务系统报告分发完成
        deliver_row = {"callback_result": json.dumps(kwargs, ensure_ascii=False), "result": deliver_msg,
                       "status": status}

        # 合并失败的租户
        if deliver_failure_list:
            failed_projects = deliver_data.get("failed_projects")
            if failed_projects:
                logger.error("数据集分发失败租户：" + json.dumps(failed_projects))
            failed_projects_list = failed_projects.split(',') if failed_projects else []
            failed_projects_list.extend(deliver_failure_list)
            failed_projects_list = list(set(failed_projects_list))
            deliver_row["failed_projects"] = ','.join(failed_projects_list)

        update_deliver_dashboard_by_id(deliver_id, deliver_row)
        deliver_data["callback_result"] = deliver_row.get("callback_result")
    if "active_reports" == kwargs.get("biz_type"):
        import_active_report_to_dashboard(deliver_data)


def import_active_report_to_dashboard(deliver_data):
    callback_result_str = deliver_data.get("callback_result")
    if not callback_result_str:
        return
    callback_result = json.loads(callback_result_str)
    import_data = callback_result.get("import_data")
    if not import_data:
        return
    failure_list = import_data.get("failure_list") or []
    zip_meta = parse_data(deliver_data['source_url'])
    active_reports = zip_meta.get("active_reports")
    report_center = zip_meta.get("report_center")

    select = """select target_project_code from dap_bi_deliver_dashboard_log 
        where deliver_id = %(deliver_id)s """
    target_code_rows = repository.get_data_by_sql(select, {"deliver_id": deliver_data.get("id")})
    target_code_list = [row.get("target_project_code") for row in target_code_rows] or []
    if not target_code_list or len(target_code_list) <= 0:
        return
    target_code_list = list(set(target_code_list) - set(failure_list))
    for code in target_code_list:
        conn = get_db(code)
        try:
            if active_reports and report_center:
                # TODO 目前报表中心只有复杂报表，后期上了简单报表后，这个地方要过滤掉简单报表
                active_report_tree = active_reports.get("data")
                deliver_root = get_active_report_deliver_root(conn, code, DEFAULT_USER_ID);
                for active_report in active_report_tree:
                    create_or_replace_active_report_dashboard(conn, active_report, deliver_root, code)
        except Exception as e:
            logging.exception(e)


def deliver_simple_callback(**kwargs):
    """
    复杂报表分发任务的回调处理逻辑
    :param kwargs:
    :return:
    """
    try:
        logger.info("报告分发回调请求参数：" + json.dumps(kwargs, ensure_ascii=False))
        # 操作类型进行区分，不同类型处理的逻辑不同 import：导入，deliver：分发
        import_id = kwargs.get("import_id")
        import_callback_deliver_simple(**kwargs)
        logger.error(f"分发任务回调完成！import_id:{import_id}")
        return True
    except Exception as e:
        raise UserError(message=f"报告分发回调接口异常，{str(e)}")


def import_callback_deliver_simple(**kwargs):
    """
    报告分发的回调处理逻辑
    判断分发的结果，更新分发记录的信息和失败租户
    :param kwargs:
    :return:
    """
    deliver_id = kwargs.get("import_id")
    deliver_data = get_deliver_dashboard_by_id(deliver_id)
    if not deliver_data:
        raise UserError(message="分发任务不存在")

    result_data = kwargs.get("data")
    tenant_code = result_data.get("tenant_code")
    import_result = result_data.get("import_result")
    if import_result:
        if kwargs.get("biz_type") and kwargs.get("biz_type") == 'ppt':
            update_deliver_dashboard_log('success', DeliverStatus.FINISH.value, deliver_id, tenant_code)
            update_deliver_dashboard_status(deliver_id)
        else:
            import_active_report_to_dashboard_simple(deliver_data, tenant_code)
    else:
        deliver_msg = result_data.get("msg")
        update_deliver_dashboard_log(deliver_msg, DeliverStatus.FAIL.value, deliver_id, tenant_code)
        update_deliver_dashboard_status(deliver_id, deliver_data.get("is_publish_center"),
                                        tenant_code,deliver_msg,deliver_data.get("title"),deliver_data.get("export_id"))
    return import_result, result_data.get("msg")

def update_deliver_dashboard_log(msg, status, deliver_id, target_project_code):
    sql = (
        'update `dap_bi_deliver_dashboard_log` '
        "set log_info = CONCAT(ifnull(log_info,''), %(msg)s), status=if(status = '失败', status, %(status)s) "
        'WHERE deliver_id=%(deliver_id)s and target_project_code = %(target_project_code)s'
    )
    return repository.execute_sql(sql, params={
        'msg': msg,
        'status': status,
        'deliver_id': deliver_id,
        'target_project_code': target_project_code
    })


def import_active_report_to_dashboard_simple(deliver_data, target_code):
    try:
        deliver_id = deliver_data.get("id")
        conn = get_db(target_code)
        zip_meta = parse_data(deliver_data['source_url'])
        active_reports = zip_meta.get("active_reports")
        is_report_center = True if zip_meta.get("report_center") else False
        active_report_tree = active_reports.get('data')

        if is_report_center:
            deliver_root = get_active_report_deliver_root(conn, target_code, DEFAULT_USER_ID)
            for active_report in active_report_tree:
                create_or_replace_active_report_dashboard(conn, active_report, deliver_root, target_code)
        update_deliver_dashboard_log('success', DeliverStatus.FINISH.value, deliver_id, target_code)
        update_deliver_dashboard_status(deliver_id)
    except Exception as e:
        logging.exception(e)
        update_deliver_dashboard_log(e.__str__(), DeliverStatus.FAIL.value, deliver_id, target_code)
        update_deliver_dashboard_status(deliver_id, deliver_data.get("is_publish_center"),
                                        target_code, e.__str__(),deliver_data.get("title"),
                                        deliver_data.get("export_id"), traceback.format_exc())


def get_active_report_deliver_root(conn, code, userid):
    # 获取并创建根目录，如果根目录位置发生移动，则不还原位置
    biz_link_service = BizLinkService(ProjectValueAddedFunc.ACTIVE_REPORT.value, code, userid)
    root_deliver_folder = biz_link_service.get_deliver_folder()
    root_id = root_deliver_folder.get("group_id")
    deliver_root = conn.query_one("select id,parent_id,level_code from dap_bi_dashboard where id = %(dashboard_id)s", {"dashboard_id": root_id})
    if not deliver_root:
        data = get_active_report_dashboard_folder(code)
        operate_model = DashboardLevelSequenceModel(level_id="")
        data["level_code"] = level_sequence_service.generate_level_code(operate_model, conn=conn)
        data["parent_id"] = ""
        data["name"] = "系统分发"
        data["id"] = root_id
        conn.insert('dap_bi_dashboard', data)
        return data
    # else:
    #     if deliver_root.get("parent_id") or deliver_root.get("parent_id") != '':
    #         move_active_report_dashboard(conn, root_id, "", deliver_root.get("level_code"))
    return deliver_root


def create_or_replace_active_report_dashboard(conn, active_report, parent, code):
    report_type = active_report.get("type")
    dashboard_id = active_report.get("id")
    if report_type == "FOLDER":
        # 创建或复原文件夹位置
        data = conn.query_one("select id,name,level_code,parent_id from dap_bi_dashboard where id = %(dashboard_id)s", {"dashboard_id": dashboard_id})
        if not data:
            data = get_active_report_dashboard_folder(code)
            operate_model = DashboardLevelSequenceModel(level_id=parent.get("id"))
            data["level_code"] = level_sequence_service.generate_level_code(operate_model, conn=conn)
            data["parent_id"] = parent.get("id")
            data["name"] = active_report.get("name")
            data["id"] = dashboard_id
            conn.insert('dap_bi_dashboard', data)
        else:
            if data.get("parent_id") != parent.get("id"):
                data = move_active_report_dashboard(conn, dashboard_id, parent.get("id"), data.get("level_code"))
            if data.get("name") != active_report.get("name"):
                update_active_report_dashboard_name(conn, dashboard_id, active_report.get("name"))
        sub = active_report.get("sub")
        for s in sub:
            create_or_replace_active_report_dashboard(conn, s, data, code)
    else:
        # 创建或复原文件夹位置
        row_state = active_report.get("publish_status") or 0
        type_access_released = 4
        status = 0
        if row_state in [1, 2, '1', '2']:
            status = 1
        if active_report.get("release_type") and active_report.get("release_type") in ['3', 3]:
            type_access_released = 3

        data = conn.query_one("select id,name,level_code,parent_id from dap_bi_dashboard where id = %(dashboard_id)s",
                              {"dashboard_id": dashboard_id})
        if not data:
            logger.debug('execute active_report %r', active_report)
            data = get_active_report_dashboard_file(code)
            if active_report.get("parent_id"):
                data["parent_id"] = active_report.get("parent_id")
                data["type"] = "CHILD_FILE"
            else:
                data["parent_id"] = parent.get("id")
                data["type"] = "FILE"
            data["status"] = status
            data["type_access_released"] = type_access_released
            operate_model = DashboardLevelSequenceModel(level_id=data.get("parent_id"))
            data["level_code"] = level_sequence_service.generate_level_code(operate_model, conn=conn)
            data["name"] = active_report.get("name")
            data["id"] = dashboard_id
            data["layout"] = active_report.get("is_screen_rpt", None)
            conn.insert('dap_bi_dashboard', data)
            insert_dashboard_extra(active_report, row_state, dashboard_id, code, conn)
        else:
            # 如果文件存在，且是非子文件的时候则需要判断是否需要位置复原
            if data.get("parent_id") != parent.get("id") and not active_report.get("parent_id"):
                move_active_report_dashboard(conn, dashboard_id, parent.get("id"), data.get("level_code"))
            if data.get("name") != active_report.get("name"):
                update_active_report_dashboard_name(conn, dashboard_id, active_report.get("name"))
            # update_active_report_dashboard_status(conn, dashboard_id, status, type_access_released)

        tag_relation_list = active_report.get("tag_relation")
        if tag_relation_list:
            for tag_relation in tag_relation_list:
                tag_relation['type'] = '1'
                tag_relation['relation_id'] = dashboard_id
        tag_relation_service.update_tag_relation(conn, dashboard_id, '1', tag_relation_list)


def move_active_report_dashboard(conn, dashboard_id, parent_id, old_level_code):
    # 目录或者文件位置复原，先修改自身parent_id 和level_code，再修改其子节点的level_code
    sql = '''update dap_bi_dashboard set parent_id=%(parent_id)s,level_code=%(level_code)s
                              where id=%(dashboard_id)s and application_type in %(application_types)s '''
    operate_model = DashboardLevelSequenceModel(level_id=parent_id)
    level_code = level_sequence_service.generate_level_code(operate_model, conn=conn)
    params = {
        "parent_id": parent_id,
        "application_types": ['5', '6'],
        "level_code": level_code,
        "dashboard_id": dashboard_id
    }
    conn.exec_sql(sql, params)

    sql = '''update dap_bi_dashboard set level_code=CONCAT(%(new_level_code)s,substring(level_code,{}))
                                          where application_type in %(application_types)s 
                                          and level_code like %(old_level_code)s'''.format(len(old_level_code) + 1)
    params = {
        "application_types": ['5', '6'],
        "new_level_code": level_code,
        "old_level_code": old_level_code + "%"
    }
    conn.exec_sql(sql, params)
    return {"level_code": level_code, "id": dashboard_id, "parent_id": parent_id}


def update_active_report_dashboard_name(conn, dashboard_id, name):
    sql = '''update dap_bi_dashboard set name = %(name)s where id = %(dashboard_id)s '''
    params = {
        "name": name,
        "dashboard_id": dashboard_id
    }
    conn.exec_sql(sql, params)


def update_active_report_dashboard_status(conn, dashboard_id, status, type_access_released):
    sql = '''update dap_bi_dashboard set status = %(status)s,type_access_released = %(type_access_released)s where id = %(dashboard_id)s '''
    params = {
        "dashboard_id": dashboard_id,
        "type_access_released": type_access_released,
        "status": status
    }
    conn.exec_sql(sql, params)


def insert_dashboard_extra(active_report, row_state, dashboard_id, code, conn):
    modified_time = active_report.get('modified_time') or datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    if row_state in [1, '1']:
        conn.delete('dap_bi_dashboard_extra', {"dashboard_id": dashboard_id})
        sql = """insert into dap_bi_dashboard_extra (dashboard_id, edit_on, released_on, modified_by, modified_on, created_by, created_on) values
                                      (%(dashboard_id)s, %(edit_on)s, %(released_on)s, %(modified_by)s, now(), %(created_by)s, now())"""
        conn.exec_sql(
            sql,
            {"dashboard_id": dashboard_id, "edit_on": modified_time, "released_on": modified_time,
             "created_by": code, "modified_by": code},
        )
    elif row_state in [2, '2']:
        conn.delete('dap_bi_dashboard_extra', {"dashboard_id": dashboard_id})
        sql = """insert into dap_bi_dashboard_extra (dashboard_id, released_on, modified_by, modified_on, created_by, created_on) values
                                      (%(dashboard_id)s, %(released_on)s, %(modified_by)s, now(), %(created_by)s, now())
                                      """
        conn.exec_sql(
            sql,
            {"dashboard_id": dashboard_id, "released_on": modified_time, "created_by": code,
             "modified_by": code},
        )
    else:
        sql = """update dap_bi_dashboard_extra set edit_on=null,released_on=null, modified_on=now(), modified_by=%(modified_by)s
                                            where dashboard_id=%(dashboard_id)s """
        conn.exec_sql(sql, {"dashboard_id": dashboard_id, "modified_by": code})


def get_active_report_dashboard_folder(code):
    model = DashboardModel(
        application_type=6, platform="pc", theme="tech_blue",
        is_show_mark_img=1, new_layout_type=1, create_type=1, type="FOLDER",
    )
    model.created_by = code
    model.modified_by = code
    model.biz_code = uuid.uuid4().__str__().replace('-', '')
    model.distribute_type = DistributeType.Distribute.value
    return model.get_dict(get_active_report_dashboard_fields())


def get_active_report_dashboard_file(code):
    model = DashboardModel(
        application_type=5, platform="pc", theme="colorful_white",
        is_show_mark_img=1, new_layout_type=1, create_type=1
    )
    model.created_by = code
    model.modified_by = code
    model.biz_code = uuid.uuid4().__str__().replace('-', '')
    return model.get_dict(get_active_report_dashboard_fields())


def get_active_report_dashboard_fields():
    return [
        'id',
        'theme',
        'name',
        'platform',
        'user_group_id',
        'description',
        'type',
        'parent_id',
        'level_code',
        'is_multiple_screen',
        'cover',
        'layout',
        'background',
        'type_selector',
        'created_by',
        'modified_by',
        'create_type',
        'new_layout_type',
        'application_type',
    ]

def init_deliver_dashboard_log(deliver_id: str, project_codes: list, source_project: str):
    if not deliver_id:
        return
    data_list = []
    for code in project_codes:
        if code == source_project:
            continue
        fields = ['id', 'deliver_id', 'target_project_code', 'status']
        data = {'id': seq_id(), 'deliver_id': deliver_id, 'target_project_code': code, 'status': DeliverStatus.CREATE.value}
        data_list.append(data)
    if data_list:
        repository.add_list_data('dap_bi_deliver_dashboard_log', data_list, fields)


def update_deliver_dashboard_status(deliver_id: str, is_report_center=False,
                                    project_code="", msg="", title="", task_id="", error=""):
    status = [DeliverStatus.CREATE.value, DeliverStatus.RUNNING.value]
    # 查询分发中正在执行的租户
    sql = "select id from dap_bi_deliver_dashboard_log where deliver_id = %(deliver_id)s and status in %(status)s limit 1"
    params = {"deliver_id": deliver_id, "status": status}
    data = repository.get_data_by_sql(sql, params) or None
    if not data:
        # 查询分发中失败的租户
        fail_data = repository.get_data_scalar(
            'dap_bi_deliver_dashboard_log',
            {'deliver_id': deliver_id, 'status': DeliverStatus.FAIL.value},
            'id'
        ) or None
        if fail_data:
            update_row = repository.update_data('dap_bi_deliver_dashboard', {'status': DELIVER_STATUS_FAILURE}, {'id': deliver_id})
            if update_row > 0 and is_report_center:
                send_publish_center_error(task_id, project_code, title, msg, error or msg)
        else:
            repository.update_data('dap_bi_deliver_dashboard', {'status': DELIVER_STATUS_SUCCESS}, {'id': deliver_id})


def deliver_log_list(deliver_id, status, page, pagesize):
    total, item = deliver_repository.deliver_log_list(deliver_id, status, page, pagesize)
    group_info = deliver_repository.deliver_log_group_count(deliver_id)
    return {'total': total, 'items': item, 'group_count': group_info}


def get_code_by_source_project_id(source_project_id):
    return repository.get_data_scalar('dap_p_tenant', {'id': source_project_id}, 'code') or -1


def filter_application(application: dict, project_code):
    new_application = {}
    # 查询租户code中所有的门户信息
    application_ids = list(application.keys()) or []
    if not application_ids:
        return application
    db = DbWrap(project_code)
    has_ids = db.get_column('dap_bi_application', {'id': application_ids}, 'id') or []
    if not has_ids:
        return application
    import_ids = list(set(application_ids).difference(set(has_ids))) or []
    for has_id in import_ids:
        if application.get(has_id):
            new_application[has_id] = application[has_id]
    return new_application


def filter_feeds(feeds: dict, project_code=None):
    feed_data = feeds.get('dashboard_email_subscribe') or []
    all_feeds_list = [feed.get('id') for feed in feed_data] or []
    if not all_feeds_list:
        return {}
    table_field_config = {
        'dashboard_email_subscribe': 'id', 'dashboard_subscribe_display_format': 'subscribe_id', 'mobile_subscribe_filter': 'email_subscribe_id',
        'mobile_subscribe_rules': 'email_subscribe_id', 'mobile_subscribe_chapters': 'email_subscribe_id', 'flow': 'id'
    }
    new_feeds = {}
    db = DbWrap(project_code)
    has_ids = db.get_column('dap_bi_dashboard_email_subscribe', {'id': all_feeds_list}, 'id') or []
    if not has_ids:
        return feeds
    for table, feed in feeds.items():
        if not new_feeds.get(table):
            new_feeds.update({table: []})
        field_key = table_field_config.get(table)
        if not field_key:
            continue
        for row in feed:
            if row.get(field_key) not in has_ids:
                new_feeds[table].append(row)
    return new_feeds
