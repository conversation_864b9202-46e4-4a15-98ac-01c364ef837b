import logging

from components.pular15_api import Pulsar15Api

logger = logging.getLogger(__name__)


def try_get_business_tag_map(tenant_code):
    try:
        pulsar = Pulsar15Api(tenant_code=tenant_code)
        data = pulsar.get_business_subject()
    except Exception as e:
        message = '获取业务主题异常:' + str(e) + ',租户code:' + str(tenant_code)
        logger.error(message)
        data = {'list': []}

    data_list = data.get('list', [])

    current_tenant_tag_map = {}
    for business_unit in data_list:
        subjects = business_unit.get('subjects', [])
        for subject in subjects:
            current_tenant_tag_map[subject.get('subject_name')] = subject.get('subject_id')
    return current_tenant_tag_map


def update_tag_relation(conn, relation_id, business_type, tag_relation_list):
    conn.delete('dap_bi_tag_relation', {
        'relation_id': relation_id,
        'type': business_type
    }, False)
    insert_business_tags = []
    if tag_relation_list:
        for tag_item in tag_relation_list:
            insert_business_tags.append({
                'relation_id': tag_item.get('relation_id'),
                'tag_name': tag_item.get('tag_name'),
                'tag_id': tag_item.get('tag_id'),
                'type': tag_item.get('type'),
                'created_on': tag_item.get('created_on'),
                'created_by': tag_item.get('created_by'),
                'modified_by': tag_item.get('modified_by'),
                'modified_on': tag_item.get('modified_on'),
            })
    if insert_business_tags and len(insert_business_tags) > 0:
        conn.insert_multi_data('dap_bi_tag_relation', insert_business_tags,
                               ['relation_id', 'type', 'tag_id', 'created_on', 'created_by', 'modified_by',
                                'modified_on', 'tag_name'], False)
    conn.commit()
