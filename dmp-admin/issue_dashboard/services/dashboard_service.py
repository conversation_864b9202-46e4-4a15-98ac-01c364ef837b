#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# <NAME_EMAIL> on 2018/07/17

"""
报告分发模块之报告模块service代码
"""
import json
# ---------------- 标准模块 ----------------
import logging
import re
import traceback
import uuid
from datetime import datetime
import traceback

from base.enums import (
    DashboardType, DistributeType, DashboardTypeAccessReleased, ApplicationType,
    FunctionReportType, ApplicationReportType
)
from components.pular15_api import Pulsar15Api
from dmplib import config
from dmplib.redis import conn as conn_redis
from dmplib.saas.project import get_db
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from issue_dashboard.models import *
from issue_dashboard.models import ProjectModel, DashboardLevelSequenceModel
from collections import defaultdict
from issue_dashboard.repositories import dashboard_repository, level_sequence_repository
from issue_dashboard.services import level_sequence_service, tag_relation_service
from issue_dashboard.constants import DASHBOARD_DELIVER_DEFAULT_FOLDER_ID, get_condition_fields
from copy import deepcopy
from issue_dashboard.services import dashboard_validators
from components.dmp_api import DMPAPI
from base.db_wrap import DbWrap
from base.sql_adapter import adapter_sql

DEFAULT_FOLDER_NAME = '系统分发报告文件夹'
UNIT_CODE_LENGTH = 4
LARGE_DEFAULT_FOLDER_NAME = '大屏系统分发报告文件夹'
LARGE_DEFAULT_FOLDER_ID = '00000000-1111-0000-2222-02420a0c0002'
DATA_REPORTING_DEFAULT_FOLDER_NAME = '数据报表系统分发文件夹'
DATA_REPORTING_DEFAULT_FOLDER_ID = '00000000-1111-2222-3333-02420a0c0003'

logger = logging.getLogger(__name__)


class QueryBuilder:
    def __init__(self, model):
        """
        :param issue_dashboard.models.ProjectModel model:
        """
        self.model = model
        self.rds = get_db(self.model.code)

    def replace_data(self, **kwargs):
        pass

    def replace_multi_data(self, data):
        pass

    def insert_data(self, data):
        pass

    def insert_multi_data(self, data):
        pass

    def update_data(self, data):
        pass


class ModelFactory:
    """
    模型工厂
    """

    def __init__(self, table_name='', data=None, project_model=None):
        # delete_type 区分删除原表数据的方式 ignore: 不需要处理 union: 根据id联表删除 direct: 直接根据id删除
        self.valid_table_name_dict = {
            'dap_bi_dashboard_folders': {'delete_type': 'direct'},
            'dap_bi_dashboard': {'delete_type': 'direct'},
            'dap_bi_dashboard_chart': {'delete_type': 'direct'},
            'dap_bi_dashboard_chart_dim': {'delete_type': 'union'},
            'dap_bi_dashboard_chart_num': {'delete_type': 'union'},
            'dap_bi_dashboard_chart_desire': {'delete_type': 'union'},
            'dap_bi_dashboard_chart_layers': {'delete_type': 'union'},
            'dap_bi_dashboard_chart_markline': {'delete_type': 'union'},
            'dap_bi_dashboard_chart_params': {'delete_type': 'direct'},
            'dap_bi_dashboard_chart_visible_triggers': {'delete_type': 'direct'},
            'dap_bi_dashboard_chart_params_jump': {'delete_type': 'direct'},
            'dap_bi_dashboard_fixed_var_jump_relation': {'delete_type': 'direct'},
            'dap_bi_dashboard_filter_chart_jump_relation': {'delete_type': 'direct'},
            'dap_bi_dashboard_global_params_jump_relation': {'delete_type': 'direct'},
            'dap_bi_dashboard_jump_global_params': {'delete_type': 'direct'},
            'dap_bi_dashboard_global_params_2_dataset_field_relation': {'delete_type': 'direct'},
            'dap_bi_dashboard_global_params_2_dataset_vars_relation': {'delete_type': 'direct'},
            'dap_bi_dashboard_global_params_2_filter_chart_relation': {'delete_type': 'direct'},
            'dap_bi_dashboard_chart_selector': {'delete_type': 'direct'},
            'dap_bi_dashboard_chart_selector_field': {'delete_type': 'direct'},
            'dap_bi_dashboard_filter': {'delete_type': 'direct'},
            'dap_bi_dashboard_dataset_field_relation': {'delete_type': 'direct'},
            'dap_bi_dashboard_chart_filter': {'delete_type': 'union'},
            'dap_bi_screen_dashboard': {'delete_type': 'direct'},
            'dap_bi_dashboard_released_snapshot_chart': {'delete_type': 'ignore'},
            'dap_bi_dashboard_released_snapshot_dashboard': {'delete_type': 'ignore'},
            'dap_bi_dashboard_jump_config': {'delete_type': 'direct'},
            'dap_bi_dashboard_jump_relation': {'delete_type': 'direct'},
            'dap_bi_dashboard_vars_jump_relation': {'delete_type': 'direct'},
            'dap_bi_dashboard_component_filter': {'delete_type': 'union'},
            'dap_bi_dashboard_component_filter_field_relation': {'delete_type': 'union'},
            'dap_bi_dashboard_chart_comparison': {'delete_type': 'union'},
            'dap_bi_dashboard_chart_penetrate_relation': {'delete_type': 'union'},
            'dap_bi_dashboard_filter_relation': {'delete_type': 'direct'},
            'dap_bi_dashboard_chart_filter_relation': {'delete_type': 'union'},
            'dap_bi_dashboard_filter_chart': {'delete_type': 'direct'},
            'dap_bi_dashboard_filter_chart_relation': {'delete_type': 'direct'},
            'dap_bi_dashboard_filter_chart_default_values': {'delete_type': 'direct'},
            'dap_bi_dashboard_filter_chart_fixed_value': {'delete_type': 'direct'},
            'dap_bi_dashboard_linkage': {'delete_type': 'direct'},
            'dap_bi_dashboard_linkage_relation': {'delete_type': 'direct'},
            'dap_bi_dashboard_dataset_vars_relation': {'delete_type': 'direct'},
            'dap_bi_dashboard_extra': {'delete_type': 'direct'},
            'dap_bi_dashboard_chart_field_sort': {'delete_type': 'direct'},
            'dap_bi_dashboard_value_source': {'delete_type': 'direct'},
            'dap_bi_dashboard_vars_value_source_relation': {'delete_type': 'direct'},
        }
        self.table_name = table_name
        self.data = data
        self.project_model = project_model

    def get_table_operations(self):
        """
        获取操作项
        :return:
        """
        return self.valid_table_name_dict.get(self.table_name, {})

    @staticmethod
    def _create_instance(field_dict):
        """
        实例化
        :param field_dict:
        :return:
        """
        model = BaseModel()
        for field, field_data in field_dict.items():
            # 优先取字段默认值
            default = field_data.get("Default")
            field_type = field_data.get("Type")
            # 字段值不能为空且没有设置默认值的情况
            if field_data.get("Null") == "NO" and default is None:
                if "int" in field_type:
                    default = 0
                elif "char" in field_type or "time" in field_type or "date" in field_type or "enum" in field_type:
                    default = ""
            if field in ["created_on", "modified_on"]:
                default = ""
            model.__setattr__(field, default)
        return model

    def initialize_table_model(self):
        """
        利用目标租户的数据表字段来实例化model
        :return:
        """
        table_model_dict = dict()
        if not self.project_model or not isinstance(self.project_model, ProjectModel):
            raise UserError(message='当前租户信息初始化异常，请重启分发任务')
        if not self.valid_table_name_dict:
            return table_model_dict
        conn = get_db(self.project_model.code)
        if not conn:
            return table_model_dict
        self._create_instance_for_table(conn, table_model_dict)
        return table_model_dict

    def _create_instance_for_table(self, conn, table_model_dict):
        """
        _create_instance_for_table
        :param conn:
        :param table_model_dict:
        :return:
        """
        for table_name in list(self.valid_table_name_dict.keys()):
            cur = None
            try:
                if table_name in ["dap_bi_dashboard_folders"]:
                    continue
                # 获取表字段
                field_dict = dict()
                column_of_sql = adapter_sql('table_columns', conn.db_type).format(
                    owner=conn.db,
                    table_name=table_name
                )
                query_data = conn.query(column_of_sql)
                if not query_data:
                    continue
                for single_field in query_data:
                    field = single_field.get("Field")
                    if not field:
                        continue
                    field_dict[field] = single_field
                table_model_dict[table_name] = self._create_instance(field_dict)
            except BaseException as e:
                if cur:
                    cur.close()
                raise UserError(message="获取目标租户表信息异常：{}".format(e))


class DashboardQueryBuilder(QueryBuilder):
    def __init__(self, model):
        self.top_parent_id = ''
        self.top_level_code = '9000-'
        self.dashboard_level_code_dict = defaultdict(list)
        self.orig_dashboard_level_code_dict = dict()
        self.orig_top_parent_id = ""
        self.orig_custom_dashboard_list = list()
        self.include_folder = True
        self.is_import = False
        self.extra_info = {}
        super().__init__(model)

    @staticmethod
    def get_validator(table_name):
        """
        获取表数据校验类，如果匹配不到校验类则返回None
        :param table_name:
        :return:
        """
        class_name = ""
        name_arr = table_name.split("_")
        for item in name_arr:
            class_name += item.capitalize()
        class_name += "Validator"
        try:
            validator = getattr(dashboard_validators, class_name)()
            return validator
        except AttributeError:
            return None

    def validate_table_data(self, table_name, table_data):
        """
        校验表数据
        :param table_name:
        :param table_data:
        :return:
        """
        validator = self.get_validator(table_name)
        if validator and isinstance(validator, dashboard_validators.Validator):
            validator.table_name = table_name
            validator.table_data = table_data
            validator.validate()

    @staticmethod
    def get_default_dashbaord_id(conn, top_level_code):
        """
        获取默认文件夹id
        :param conn:
        :return:
        """
        sql = '''select id from dap_bi_dashboard where level_code='{0}' and type='{1}' limit 1
              '''.format(
            top_level_code, DashboardType.Folder.value
        )
        data = conn.query_one(sql)
        return data

    @staticmethod
    def reset_default_folder_info(conn, top_parent_id, folder_name):
        """
        重置默认文件夹的信息
        :param conn:
        :param top_parent_id:
        :return:
        """
        # 更新distribute_type字段
        sql = '''update dap_bi_dashboard set distribute_type=%(distribute_type)s
              where id=%(dashboard_id)s and distribute_type=%(default_type)s limit 1'''
        params = {
            "distribute_type": DistributeType.Distribute.value,
            "dashboard_id": top_parent_id,
            "default_type": DistributeType.Default.value,
        }
        conn.exec_sql(sql, params)

        # 更新文件夹名称
        sql_two = '''update dap_bi_dashboard set name=%(name)s
              where id=%(dashboard_id)s and name!=%(name)s limit 1'''
        params_two = {"dashboard_id": top_parent_id, "name": folder_name}
        conn.exec_sql(sql_two, params_two)

    def _pre_collect_level_code_data(self, data):
        """
        收集需要处理的文件夹数据
        :param data:
        :return:
        """
        for dashboard_id, dashboard_table in data.items():
            if not dashboard_table:
                continue
            for dashboard_name, table_data in dashboard_table.items():
                if dashboard_name not in ["dap_bi_dashboard", "dap_bi_dashboard_folders"]:
                    continue
                self._collect_dashboard(table_data)

        self._re_collect_valid_dashboard()
        # 收集被分发报告在当前目标租户下的level_code
        self._collect_orig_dashboard_data()

    def _collect_dashboard(self, table_data):
        """
        收集子级报告id
        :param table_data:
        :return:
        """
        if not table_data or not isinstance(table_data, list):
            return []
        for idx, item in enumerate(table_data):
            self._assign_level_code_dict(item)
            self._assign_custom_dashboard_list(item)

    def _assign_level_code_dict(self, item):
        """
        组装level_code_dict
        :param item:
        :return:
        """
        dashboard_id = item.get('id')
        parent_id = item.get('parent_id')
        dashboard_type = item.get('type')
        level_code = item.get('level_code')
        # if not parent_id and level_code == self.top_level_code:
        if (
                (not parent_id and level_code == self.top_level_code) # 仪表板固定level_code的系统分发
                or (dashboard_id == LARGE_DEFAULT_FOLDER_ID)  # 大屏固定id的系统分发
        ):
            self.orig_top_parent_id = dashboard_id

        if self.top_parent_id and self.top_parent_id not in self.dashboard_level_code_dict.keys():
            self.dashboard_level_code_dict[self.top_parent_id] = []

        # 父级id为文件夹
        if parent_id and parent_id not in self.dashboard_level_code_dict.keys():
            self.dashboard_level_code_dict[parent_id] = []

        # 本身也为文件夹
        if dashboard_type == DashboardType.Folder.value and (
                dashboard_id and dashboard_id not in self.dashboard_level_code_dict.keys()
        ):
            self.dashboard_level_code_dict[dashboard_id] = []

        # 不收集不满足的文件类型
        if parent_id and dashboard_type in [e.value for e in DashboardType.__members__.values()]:
            self.dashboard_level_code_dict[parent_id].append(dashboard_id)

        # 没有父级id的则挂靠在默认分发主目录下
        if not parent_id and dashboard_id:
            self.dashboard_level_code_dict[self.top_parent_id].append(dashboard_id)

    def _assign_custom_dashboard_list(self, item):
        """
        组装custom_dashboard_list
        :param item:
        :return:
        """
        dashboard_id = item.get('id')
        parent_id = item.get('parent_id')
        level_code = item.get('level_code')
        if level_code != self.top_level_code and dashboard_id not in self.orig_custom_dashboard_list:
            self.orig_custom_dashboard_list.append(dashboard_id)
        if parent_id and level_code != self.top_level_code and parent_id not in self.orig_custom_dashboard_list:
            self.orig_custom_dashboard_list.append(parent_id)

    def _re_collect_valid_dashboard(self):
        """
        需要将目标租户自己创建的文件也算进来
        :return:
        """
        # 兼容从9000-文件夹分发报告的情况
        if self.orig_top_parent_id and self.orig_top_parent_id != self.top_parent_id and self.dashboard_level_code_dict[
            self.orig_top_parent_id]:
            self.dashboard_level_code_dict[self.top_parent_id].extend(
                self.dashboard_level_code_dict[self.orig_top_parent_id]
            )
            self.dashboard_level_code_dict.pop(self.orig_top_parent_id)
        for k, v in self.dashboard_level_code_dict.items():
            new_v = [i for i in v if i != self.orig_top_parent_id] if v and self.orig_top_parent_id else v
            self.dashboard_level_code_dict[k] = new_v

        if not self.dashboard_level_code_dict or not isinstance(self.dashboard_level_code_dict, dict):
            return
        orig_dashboard_level_code_dict = deepcopy(self.dashboard_level_code_dict)
        self._deal_with_level_code_dict(orig_dashboard_level_code_dict)

    def _deal_with_level_code_dict(self, orig_dashboard_level_code_dict):
        """
        处理level_code_dict
        :param orig_dashboard_level_code_dict:
        :return:
        """
        for parent_id, son_dashboard_list in orig_dashboard_level_code_dict.items():
            valid_son_dashboard_id_list = list()
            valid_son_custom_folder_list = list()
            son_dashboard_ids = dashboard_repository.get_son_dashboard_ids(parent_id, self.rds)
            if son_dashboard_ids:
                for i in son_dashboard_ids:
                    valid_son_dashboard_id_list.append(i.get("id"))
                    if parent_id in self.orig_custom_dashboard_list and i.get("type") == DashboardType.Folder.value:
                        valid_son_custom_folder_list.append(i.get("id"))
            self.dashboard_level_code_dict[parent_id] = list(
                set(son_dashboard_list).union(set(valid_son_dashboard_id_list))
            )
            # 处理用户自定义的文件夹或报告
            if valid_son_custom_folder_list:
                self._deal_with_custom_folder_list(valid_son_custom_folder_list)

    def _deal_with_custom_folder_list(self, valid_son_custom_folder_list):
        """
        递归处理用户自己的目录文件
        :param valid_son_custom_folder_list:
        :return:
        """
        for custom_parent_id in list(set(valid_son_custom_folder_list)):
            if custom_parent_id:
                self._recusion_get_custom_tree_dashboard(custom_parent_id)

    def _recusion_get_custom_tree_dashboard(self, parent_id):
        """
        递归获取文件夹层级内的各级文件
        :param parent_id:
        :return:
        """
        son_dashboard_list = dashboard_repository.get_son_dashboard_ids(parent_id, self.rds)
        if son_dashboard_list:
            if parent_id not in self.dashboard_level_code_dict.keys():
                self.dashboard_level_code_dict[parent_id] = list()
            for i in son_dashboard_list:
                self.dashboard_level_code_dict[parent_id].append(i.get("id"))
                self._recusion_get_custom_tree_dashboard(i.get("id"))

    def _collect_orig_dashboard_data(self):
        """
        收集被分发报告在当前目标租户下的原数据
        :return:
        """
        operate_dashboard_id_list = list()
        if self.dashboard_level_code_dict:
            for k, v in self.dashboard_level_code_dict.items():
                operate_dashboard_id_list.append(k)
                if v and isinstance(v, list):
                    operate_dashboard_id_list.extend(v)
        query_data = (
            dashboard_repository.batch_get_dashboard_info(list(set(operate_dashboard_id_list)), self.rds)
            if operate_dashboard_id_list
            else []
        )
        if query_data:
            for item in query_data:
                self.orig_dashboard_level_code_dict[item.get("id")] = item.get("level_code", "")

    def _batch_deal_with_level_code(self):
        """
        处理被分发报告的level_code
        :return:
        """
        # 从默认分发主目录下的子级id开始
        top_dashboard_id_list = self.dashboard_level_code_dict.get(self.top_parent_id)
        if not top_dashboard_id_list:
            return
        for operate_dashboard_id in list(set(top_dashboard_id_list)):
            self._recursion_generate_new_level_code(self.top_parent_id, operate_dashboard_id)

    def _recursion_generate_new_level_code(self, parent_id, operate_dashboard_id):
        """
        递归生成level_code
        :param parent_id:
        :param operate_dashboard_id:
        :return:
        """
        operate_model = DashboardLevelSequenceModel(level_id=parent_id)
        new_level_code = level_sequence_service.generate_level_code(operate_model, conn=self.rds)
        # 如果当前操作的报告之前已存在目标租户下，则需要修改存在的其他子级文件的level_code
        orig_level_code = self.orig_dashboard_level_code_dict.get(operate_dashboard_id)
        if orig_level_code:
            dashboard_repository.move_dashboard_tree_new(orig_level_code, new_level_code, self.rds)
        level_sequence_repository.update_level_code(new_level_code, operate_dashboard_id, self.rds)
        operate_son_dashboard_list = self.dashboard_level_code_dict.get(operate_dashboard_id)
        if operate_son_dashboard_list:
            for son_operate_dashboard_id in list(set(operate_son_dashboard_list)):
                self._recursion_generate_new_level_code(operate_dashboard_id, son_operate_dashboard_id)

    def create_default_folder(self, conn):
        """
        创建报告分发默认文件夹
        :return:
        """
        dashboard = conn.query_one('select id from dap_bi_dashboard where id=%(id)s', params={'id': DASHBOARD_DELIVER_DEFAULT_FOLDER_ID})
        if not dashboard:
            dashboard_id = DASHBOARD_DELIVER_DEFAULT_FOLDER_ID
            kwargs = {
                'id': dashboard_id,
                'name': DEFAULT_FOLDER_NAME,
                'type': DashboardType.Folder.value,
                'parent_id': '',
                'created_by': self.model.code,
                'modified_by': self.model.code,
            }
            model = DashboardModel(**kwargs)
            data = model.get_dict(
                [
                    'id',
                    'theme',
                    'name',
                    'platform',
                    'user_group_id',
                    'description',
                    'type',
                    'parent_id',
                    'level_code',
                    'is_multiple_screen',
                    'cover',
                    'layout',
                    'background',
                    'type_selector',
                    'created_by',
                    'modified_by',
                    'create_type',
                    'new_layout_type',
                ]
            )
            data['level_code'] = self.top_level_code
            data['user_group_id'] = '00000000-0000-0000-1111-000000000000'
            data['biz_code'] = uuid.uuid4().__str__().replace('-', '')
            data['distribute_type'] = DistributeType.Distribute.value
            conn.insert('dap_bi_dashboard', data)
        else:
            dashboard_id = dashboard.get('id')
        default_dashboard_id_data = {'id': dashboard_id}
        return default_dashboard_id_data

    @staticmethod
    def update_increment_table(table_name, table_data, model, conn, commit):
        """
        更新自增长主键的表格
        :param table_name:
        :param table_data:
        :param model:
        :param conn:
        :param commit:
        :return:
        """
        snapshot_id_list = list()
        # 先删除原数据表缓存
        snapshot_id_list = [
            item.get('snapshot_id')
            for item in table_data
            if item.get('snapshot_id') and item.get('snapshot_id') not in snapshot_id_list
        ]
        if len(snapshot_id_list):
            for snapshot_id in snapshot_id_list:
                if not snapshot_id:
                    continue
                sql = '''delete from {0} where snapshot_id='{1}' '''.format(table_name, snapshot_id)
                conn.exec_sql(sql)

        # 批量插入数据
        for item in table_data:
            primary_id = item.get('id', '')
            snapshot_id = item.get('snapshot_id', '')
            if not primary_id or not snapshot_id:
                continue
            conn.replace_multi_data(table_name, [item], list(model.get_dict().keys()), commit=commit, condition_field=get_condition_fields(table_name), has_del=False)

    def rename_dashboard(self, dashboard_name):
        is_exist_names = dashboard_repository.get_son_dashboard_names(self.top_parent_id, self.rds) or []
        if is_exist_names:
            return self.generate_new_dashboard_name(dashboard_name, is_exist_names)
        return dashboard_name

    def generate_new_dashboard_name(self, name, is_exist_names, i=1):
        if name in is_exist_names:
            # 判断是否以 (1) 这种结尾
            int_list = re.findall(r'\((.*?)\)', name)
            if not int_list:
                return self.generate_new_dashboard_name(name + "(1)", is_exist_names)
            # 判断最后一个数字
            else:
                try:
                    end_num = int(int_list[-1])
                except ValueError:
                    return name + "(1)"
                # 如果最后一位比当前循环大，从名称中重新获取i
                i = (end_num if i < end_num else i) + 1
                new_name = name[:-3] + "(%s)" % i
                return self.generate_new_dashboard_name(new_name, is_exist_names, i=i)
        return name

    def pre_deal_with_table_data(self, table_name, table_data):
        """
        预处理表格数据
        :param table_name:
        :param table_data:
        :return:
        """
        result_table_data = list()
        for index, item in enumerate(table_data):
            self._deal_with_single_table_data(table_name, table_data, item, result_table_data)
        return result_table_data

    def _change_field(self, old_field_name, new_field_name, item):
        if old_field_name in item:
            item[new_field_name] = item.get(old_field_name)
            item.pop(old_field_name)

    def support_table_field_name_change(self, table_name, item):
        """
        处理旧的字段名称
        :return:
        """
        if table_name == "dap_bi_dashboard_chart_params_jump":
            self._change_field("dataset_field_id", "source_id", item)
        if table_name == "dap_bi_dashboard_jump_config":
            self._change_field("dataset_field_id", "source_id", item)
            self._change_field("dataset_field_type", "source_type", item)

    def deal_import_diff_dashboard_auth(self, table_name, table_data):
        if table_name != 'dap_bi_dashboard' or not table_data:
            return
        for item in table_data:
            self._deal_import_diff_dashboard_auth(table_name, item)

    def _deal_import_diff_dashboard_auth(self, table_name, item):
        """
        酷炫大屏拆分后，仪表版导入迁移过的大屏后，这个大屏会重新回到仪表板下，这里重新处理下权限
        """
        if table_name != 'dap_bi_dashboard':
            return

        sql = 'select platform,new_layout_type,type,application_type from dap_bi_dashboard where id = :dashboard_id limit 1'
        dashboard_id = item.get('id', '')
        curr_dashboard = self.rds.query_one(sql, {'dashboard_id': dashboard_id})
        if not curr_dashboard:
            return

        old_screen_filter = lambda x: (
                x.get('platform') == 'pc'
                and x.get('new_layout_type') == 0
                and x.get('type') == 'FILE'
                and x.get('application_type') == 0
        )
        large_screen_filter = lambda x: (
                x.get('platform') == 'pc'
                and x.get('new_layout_type') == 0
                and x.get('type') == 'FILE'
                and x.get('application_type') == ApplicationType.LargeScreen.value
        )
        if list(filter(large_screen_filter, [curr_dashboard])) and list(filter(old_screen_filter, [item])):
            # 当前报告在酷炫大屏下，然而导入的这个大屏是仪表板的下旧大屏
            logger.error(f'检测到迁移过的大屏被仪表版导入，将权限和菜单变更回仪表板，id：<{dashboard_id}>')
            # 1. 如果对这个大屏有报告级的权限设置，讲这个权限变更回仪表版的权限
            self.__fix_removed_screen_permissions(dashboard_id, to_change_type='dashboard')
            # 2.变更门户菜单挂接
            self.__fix_application_function_links(dashboard_id, to_change_type='dashboard')
        elif list(filter(old_screen_filter, [curr_dashboard])) and list(filter(large_screen_filter, [item])):
            logger.error(f'检测到仪表板中的大屏被酷炫大屏导入，id：<{dashboard_id}>')
            # 1. 如果有报告级的权限设置，讲这个权限变更回大屏的权限
            self.__fix_removed_screen_permissions(dashboard_id, to_change_type='large_screen')
            # 2.变更门户菜单挂接
            self.__fix_application_function_links(dashboard_id, to_change_type='large_screen')

    def __fix_application_function_links(self, dashboard_id, to_change_type='dashboard'):
        """
        已经是大屏的报表移动回仪表板的时候，门户菜单挂接变更回去
        """
        if to_change_type == 'large_screen':
            table_list = [
                {'table': 'dap_bi_application', 'type': ApplicationReportType.LargeScreen.value},
                {'table': 'dap_bi_release_application', 'type': ApplicationReportType.LargeScreen.value},
                {'table': 'dap_bi_function', 'type': FunctionReportType.LargeScreen.value},
                {'table': 'dap_bi_release_function', 'type': FunctionReportType.LargeScreen.value},
            ]
        else:
            table_list = [
                {'table': 'dap_bi_application', 'type': ApplicationReportType.DASHBOARD.value},
                {'table': 'dap_bi_release_application', 'type': ApplicationReportType.DASHBOARD.value},
                {'table': 'dap_bi_function', 'type': FunctionReportType.DASHBOARD.value},
                {'table': 'dap_bi_release_function', 'type': FunctionReportType.DASHBOARD.value},
            ]
        condition = {'url': dashboard_id}
        for data in table_list:
            table = data['table']
            update_data = {'report_type': data['type']}
            self.rds.update(table, data=update_data, condition=condition, commit=False)

    def __fix_removed_screen_permissions(self, dashboard_id, to_change_type='dashboard'):
        """
        已经是大屏的报表移动回仪表板的时候，如果对这个大屏有报告级的权限设置，权限变回仪表板的权限
        """
        if to_change_type == 'large_screen':
            to_data_type = 'large_screen'
        else:
            to_data_type = 'dashboard'
        sql = 'select * from dap_p_user_role_data_permission where data_id = :dashboard_id'
        permissions = self.rds.query(sql, {'dashboard_id': dashboard_id})
        fields = ['data_id', 'data_type', 'role_id', 'data_action_code']
        for permission in permissions:
            data_type = permission.get('data_type')
            if data_type == to_data_type:
                continue
            old_permission = {f: permission.get(f) for f in fields}
            logger.info(f'将会对报告权限进行修复，权限信息: {old_permission}')
            new_permission = deepcopy(old_permission)
            new_permission['data_type'] = to_data_type
            # 处理异常数据，可能出现一个报表id在两边出现的权限设置，这里如果要切换的数据已经存在，就删除当前的准备切换的权限设置！！
            # 这个表['data_id', 'data_type', 'role_id', 'data_action_code']是联合唯一索引！！！！！！！！！！！！！！！
            has_existed = self.rds.query_one(
                'select * from dap_p_user_role_data_permission where data_id = %(data_id)s and  data_type = %(data_type)s and  role_id = %(role_id)s and  data_action_code = %(data_action_code)s',
                new_permission.copy()
            )
            if has_existed:
                # 删除当前的这个配置
                self.rds.delete('dap_p_user_role_data_permission', old_permission.copy())
                logger.info(f'发现要变更的权限已经存在，删除准备变更的权限: {old_permission}')
            else:
                # 变更当前的这个配置
                self.rds.update('dap_p_user_role_data_permission', data=new_permission, condition=old_permission, commit=False)
                logger.info(f'权限变更成为: {new_permission}')

    # def deal_clob_file_for_dm(self, table, item):
    #     """
    #     处理clob字段
    #     :param table:
    #     :param item:
    #     :return:
    #     """
    #     if config.get('DB.db_type', '') != "dm":
    #         return
    #     if table in ['dashboard_chart']:
    #         item['config'] =

    def _deal_with_single_table_data(self, table_name, table_data, item, result_table_data):
        """
        预处理表格的单条记录
        :param table_name:
        :param table_data:
        :param item:
        :param result_table_data:
        :return:
        """
        if not isinstance(table_data, list):
            return
        # 平铺的情况不处理文件夹
        if not self.include_folder and table_name == 'dap_bi_dashboard_folders':
            return
        # 处理level_code
        if table_name in ['dap_bi_dashboard_folders', 'dap_bi_dashboard', 'dap_bi_dashboard_released_snapshot_dashboard']:
            flag = self._deal_with_parent_id_level_code(item)
            if not flag:
                return

        # 兼容更改过数据库字段名
        self.support_table_field_name_change(table_name, item)

        # 平铺的情况下，还需要处理报告重名 + 1问题
        if not self.include_folder and table_name == 'dap_bi_dashboard':
            item['name'] = self.rename_dashboard(item.get("name"))

        # 分发的报告是否锁定由用户选择的状态来决定 # 更新：导入报告，不改变报告原始的分发锁定状态；分发报告，报告则锁定
        if table_name in ['dap_bi_dashboard', 'dap_bi_dashboard_folders'] and not self.is_import:
            item['distribute_type'] = self.model.distribute_type

        # 处理created_by and modified_by
        # 如果是报告还原场景，不要覆盖创建者，更新修改者
        if self.extra_info.get("is_restore", None):
            modified_account = self.extra_info.get("modified_account", None)
            item['modified_by'] = modified_account if modified_account else self.model.code
        else:
            item['created_by'] = self.model.code
            item['modified_by'] = self.model.code
        # 处理created_on and modified_on
        item.update(
            {
                'created_on': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'modified_on': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            }
        )
        # 处理自增长id
        if item.get('increment_id', None):
            item.pop('increment_id')

        result_table_data.append(item)

    def _deal_with_parent_id_level_code(self, item):
        # 平铺的情况，parent_id为顶层目录，根据parent_id重新生成level_code
        if not self.include_folder:
            if 'parent_id' in item:
                item['parent_id'] = item.get('parent_id') \
                    if item.get('type') and item.get('type') == DashboardType.CHILD_FILE.value else self.top_parent_id
                operate_model = DashboardLevelSequenceModel(level_id=item.get("parent_id"))
                item["level_code"] = level_sequence_service.generate_level_code(operate_model, conn=self.rds)

        else:
            orig_level_code = item['level_code']
            item['level_code'] = (
                self.top_level_code + item.get('level_code')
                if not item.get('level_code').startswith(self.top_level_code)
                else item.get('level_code')
            )

            # 原文件夹是系统分发文件夹则不处理
            if (
                    (not item.get('parent_id') and orig_level_code == self.top_level_code)
                    or (item.get('id') == LARGE_DEFAULT_FOLDER_ID)
            ):
                return False

            # 处理parent_id
            self.__deal_parent_id(item)

        return True

    def __deal_parent_id(self, item):
        if 'parent_id' in item and item.get('level_code') and len(item.get('level_code')) <= 10:
            item['parent_id'] = self.top_parent_id
            # 替换parent_id后需要加入到需要收集的层级列表中
            if item['parent_id'] in self.dashboard_level_code_dict.keys():
                self.dashboard_level_code_dict[self.top_parent_id].append(item.get('id'))

    @staticmethod
    def delete_multi_data(table_name, dashboard_id, table_operations, conn):
        """
        按条件删除旧数据
        :param table_name:
        :param dashboard_id:
        :param table_operations:
        :param conn:
        :return:
        """
        sql = ''
        if not table_name or not dashboard_id or not table_operations:
            return False
        delete_type = table_operations.get('delete_type', 'ignore')
        if delete_type == 'ignore':
            return True
        elif delete_type == 'union':
            sql = '''delete from {0} where dashboard_chart_id in 
                  (select id from dap_bi_dashboard_chart where dashboard_id='{1}')
                  '''.format(
                table_name, dashboard_id
            )
            if table_name == 'dap_bi_dashboard_component_filter':
                sql = '''delete from {0} where chart_initiator_id in 
                      (select id from dap_bi_dashboard_chart where dashboard_id='{1}')
                      '''.format(
                    table_name, dashboard_id
                )
            elif table_name == 'dap_bi_dashboard_component_filter_field_relation':
                sql = '''delete from {0} where chart_id in 
                      (select id from dap_bi_dashboard_chart where dashboard_id='{1}')
                      '''.format(
                    table_name, dashboard_id
                )
        elif delete_type == 'direct':
            sql = '''delete from {0} where dashboard_id='{1}' '''.format(table_name, dashboard_id)
            if table_name == 'dap_bi_dashboard':
                sql = '''delete from {0} where id='{1}' '''.format(table_name, dashboard_id)

            if table_name == 'dap_bi_dashboard_folders':
                sql = ""
        if sql:
            conn.exec_sql(sql)
        return True

    def delete_dashboard_child_file(self, table_name, dashboard_id, conn):
        """
        删除报告可能存在的子报告
        :param table_name:
        :param dashboard_id:
        :param conn:
        :return:
        """
        if table_name == 'dap_bi_dashboard':
            child_id_list = []
            try:
                times = 10
                self.recursive_get_dashboard(conn, [dashboard_id], child_id_list, times)
            except Exception as e:
                logger.error("报告的子报告删除失败，errs:"+str(e))
            if child_id_list:
                ids = ','.join(["'%s'" % i for i in child_id_list])
                del_sql = "delete from dap_bi_dashboard where id in (%s) and type = 'CHILD_FILE'" % ids
                conn.exec_sql(del_sql)

    def recursive_get_dashboard(self, conn, parent_id_list: list, result_id_list: list, times):
        """
        通过父级id递归获取报告
        :param conn:
        :param parent_id_list:
        :param result_id_list:
        :param times: 最多查找层数
        :return:
        """
        if times <= 0:
            return
        times -= 1
        ids = ','.join(["'%s'" % i for i in parent_id_list])
        sql = "select id from dap_bi_dashboard where parent_id in (%s) and type = 'CHILD_FILE'" % ids
        child_id_list = conn.query_columns(sql)
        if child_id_list:
            result_id_list.extend(child_id_list)
            self.recursive_get_dashboard(conn, child_id_list, result_id_list, times)

    def create_top_folder(self):
        # 默认文件夹id
        default_dashboard_id_data = self.get_default_dashbaord_id(self.rds, self.top_level_code)
        # 没有则创建
        if not default_dashboard_id_data:
            default_dashboard_id_data = self.create_default_folder(self.rds)
            top_parent_id = default_dashboard_id_data.get("id")
        else:
            top_parent_id = default_dashboard_id_data.get("id")
            # 已经存在的分发文件夹需要重新设置下文件夹名称
            self.reset_default_folder_info(self.rds, top_parent_id, DEFAULT_FOLDER_NAME)

        return top_parent_id

    @staticmethod
    def _assign_field_for_insert(table_data: list, model):
        """
        表字段
        :param table_data:
        :param model:
        :return:
        """
        if not table_data or not model:
            return table_data
        model_dict = model.get_dict()
        if not model_dict:
            return table_data
        for item in table_data:
            # 添加缺少的字段
            add_keys = set(list(model_dict.keys())) - set(list(item.keys()))
            if add_keys:
                for x in add_keys:
                    item[x] = model_dict[x]
            # 删除多余的导入字段
            diff_keys = set(list(item.keys())) - set(list(model_dict.keys()))
            if diff_keys:
                for y in diff_keys:
                    item.pop(y)

    def replace_data(self, dashboard_id, dashboard_table, retry=3):
        """
        执行更新
        :param dashboard_id:
        :param dashboard_table:
        :return:
        """
        commit = False
        factory = ModelFactory(project_model=self.model)
        valid_table_name_list = list(factory.valid_table_name_dict.keys())
        table_model_dict = factory.initialize_table_model()
        if not table_model_dict:
            return False, "表字段初始化失败"
        # rds连接实例
        conn = self.rds
        if dashboard_table is None:  # 新增对None的检查
            logger.error(f"传入的数据为None，租户code: {self.model.code}")
            return True, ""

        if not isinstance(dashboard_table, dict):
            return True, ""
        try:
            for table_name in sorted(list(dashboard_table.keys())):
                table_data = dashboard_table.get(table_name, [])
                # 兼容错误的历史代码
                if table_name in ['screen_dashboard', 'dashboard_screen_dashboard']:
                    table_name = 'screen_dashboard'
                if not table_name.startswith('dap_bi_'):
                    table_name = 'dap_bi_' + table_name
                if table_name not in valid_table_name_list:
                    continue
                # 处理导入大屏报告位置移动的权限问题，放在报告删除前面，里面需要查询报告数据
                self.deal_import_diff_dashboard_auth(table_name, table_data)
                # 先删除其他表格的旧数据
                table_operations = ModelFactory(table_name).get_table_operations()
                self.delete_multi_data(table_name, dashboard_id, table_operations, conn)
                # 删除报告的子报告
                self.delete_dashboard_child_file(table_name, dashboard_id, conn)
                # 没有数据则不需要更新
                if not table_data:
                    continue
                # 预处理
                table_data_v2 = self.pre_deal_with_table_data(table_name, table_data)
                real_table_name = 'dap_bi_dashboard' if table_name == 'dap_bi_dashboard_folders' else table_name
                model = table_model_dict.get(real_table_name)
                if not model:
                    continue

                # 待导入表数据的校验入口
                self.validate_table_data(real_table_name, table_data_v2)

                # 兼容场景：导入的数据缺少某些表字段
                self._assign_field_for_insert(table_data_v2, model)

                # 更新主键是自增长id的快照数据表
                if 'released_snapshot' in real_table_name:
                    self.update_increment_table(real_table_name, table_data_v2, model, conn, commit)
                    continue
                conn.replace_multi_data(real_table_name, table_data_v2, list(model.get_dict().keys()), commit=commit, condition_field=get_condition_fields(real_table_name))

            # 统一commit更改
            conn.commit()
            return True, ""
        except Exception as e:
            traceback.print_exc()  # 直接打印堆栈到标准错误（stderr）
            conn.close()
            conn.rollback()
            # conn.commit()
            message = '分发报告到指定租户失败:' + str(e) + ',租户code:' + str(self.model.code) + ',table_name:' + table_name
            logger.error(message)
            if retry > 0:
                logger.error(f'－－－分发报告失败重试。retry:{retry}')
                retry -= 1
                return self.replace_data(dashboard_id, dashboard_table, retry=retry)
            else:
                raise UserError(message=message)

    def _check_table_data_exist(self, dashboard_table_list):
        """
        校验是否缺少表
        :param dashboard_table_list:
        :return:
        """
        valid_table_name_list = list(ModelFactory().valid_table_name_dict.keys())
        for table_name in valid_table_name_list:
            if table_name not in dashboard_table_list:
                e = '缺失应导入数据表{}'.format(table_name)
                message = '报告分发异常:' + e + ',租户code:' + str(self.model.code)
                raise UserError(message=message)

    def replace_multi_data(self, data):
        """
        批量更新报告数据
        :param data:
        :return:
        """

        if data is None:  # 新增对None的检查
            logger.error(f"传入的数据为None，租户code: {self.model.code}")
            return False

        if not isinstance(data, dict):
            return False

        # 平铺不需要再次处理
        if self.include_folder:
            # 预先收集需要处理的报告或文件夹
            try:
                self._pre_collect_level_code_data(data)
            except BaseException as e:
                message = '报告分发中预先收集报告数据异常:' + str(e) + ',租户code:' + str(self.model.code)
                logger.error(message)
        # 获取当前租户的报告发布授权模式
        project_auth_mode = self.get_project_auth_mode(self.model.code)
        for dashboard_id, dashboard_table in data.items():
            if not dashboard_table:
                continue
            result, msg = self.replace_data(dashboard_id, dashboard_table)
            # 删除报告元数据
            if result and dashboard_id:
                self._delete_dashboard_metadata_cache(dashboard_id)
            if not result:
                message = '报告分发异常: ' + msg + ',租户code:' + str(self.model.code)
                logger.error(message)
                raise UserError(message=message)
            # 更改每个报告的发布方式
            self._update_dashboard_publish_way(project_auth_mode, dashboard_id, dashboard_table.get("dashboard", []))
            tag_relation_service.update_tag_relation(self.rds, dashboard_id, "1", dashboard_table.get('tag_relation'))

        self._delete_dashboard_metadata_history(list(data.keys()))

        # 平铺不需要再次处理
        if self.include_folder:
            try:
                # 批量移动报告
                self._batch_deal_with_level_code()
            except BaseException as e:
                message = '批量移动报告数据异常:' + str(e) + ',租户code:' + str(self.model.code)
                logger.error(message)

    @staticmethod
    def get_project_auth_mode(project_code):
        from base import repository
        project = repository.get_data('dap_bi_tenant_setting', {'code': project_code}, fields=['auth_mode'], multi_row=False)
        return bool(int(project.get("auth_mode")))

    def _update_dashboard_publish_way(self, project_auth_mode, dashboard_id, dashboard_list):
        """
        更新报告发布方式
        租户的授权模式是“基础数据的权限体系”，则报告导入、分发需要将“数见平台管理"修改成“第三方后台管理”
        :param project_auth_mode:
        :param dashboard_id:
        :param dashboard_list:
        :return:
        """
        try:
            dashboard_data = next((x for x in dashboard_list), {})
            if project_auth_mode \
                    and dashboard_data.get("type_access_released") == DashboardTypeAccessReleased.UserRole.value:
                type_access_released = DashboardTypeAccessReleased.ThirdParty.value
                return dashboard_repository.update_dashboard_type_access(dashboard_id, type_access_released, self.rds)
        except Exception as e:
            logger.error(f"dashboard_id：{dashboard_id} 更新报告发布方式错误，errs：{str(e)}")

    def _delete_dashboard_metadata_cache(self, dashboard_id):
        """
        1,删除dmp报告元数据缓存
        2,删除已发布报告表数据缓存
        :return:
        """
        try:
            for object_name in ['dashboard', 'dashboard_release_info']:
                cache_key = '{project_code}:{prefix_key}:{object_name}:{object_id}'.format(
                    project_code=self.model.code,
                    prefix_key=config.get('Cache.released_dashboard_metadata_cache_key', 'dmp'),
                    object_name=object_name,
                    object_id=dashboard_id,
                )
                conn_redis().delete(cache_key)
                conn_redis().delete(f"{self.model.code}:dashboard_release_data:{dashboard_id}")
        except BaseException as e:
            message = '报告分发异常:' + str(e) + ',租户code:' + str(self.model.code)
            logger.error(message)

    def _delete_dashboard_metadata_history(self, dashboard_ids):
        """删除报告上一个元数据版本"""
        if not dashboard_ids:
            return False
        return dashboard_repository.delete_dashboard_metadata_history(dashboard_ids, self.rds)

    def _try_get_business_tag_map(self):
        try:
            pulsar = Pulsar15Api(tenant_code=self.model.code)
            data = pulsar.get_business_subject()
        except Exception as e:
            message = '获取业务主题异常:' + str(e) + ',租户code:' + str(self.model.code)
            logger.error(message)
            data = {'list': []}

        data_list = data.get('list', [])

        current_tenant_tag_map = {}
        for business_unit in data_list:
            subjects = business_unit.get('subjects', [])
            for subject in subjects:
                current_tenant_tag_map[subject.get('subject_name')] = subject.get('subject_id')
        return current_tenant_tag_map

    def _update_dashboard_tag_relation(self, dashboard_id, tag_relation_list, business_tag_map):
        self.rds.delete('dap_bi_tag_relation', {
            'relation_id': dashboard_id,
            'type': '1'
        }, False)
        valid_business_tags = []
        if tag_relation_list:
            for tag_item in tag_relation_list:
                if tag_item.get('tag_name') in business_tag_map:
                    valid_business_tags.append({
                        'relation_id': tag_item.get('relation_id'),
                        'tag_name': tag_item.get('tag_name'),
                        'tag_id': business_tag_map[tag_item.get('tag_name')],
                        'type': tag_item.get('type'),
                        'created_on': tag_item.get('created_on'),
                        'created_by': tag_item.get('created_by'),
                        'modified_by': tag_item.get('modified_by'),
                        'modified_on': tag_item.get('modified_on'),
                    })
        if tag_relation_list and len(tag_relation_list) > 0:
            self.rds.insert_multi_data('dap_bi_tag_relation', valid_business_tags,
                                       ['relation_id', 'type', 'tag_id', 'created_on', 'created_by', 'modified_by',
                                        'modified_on', 'tag_name'], False)


class LargeScreenQueryBuilder(DashboardQueryBuilder):
    """
    酷炫大屏导入、分发的处理对象
    """
    def __init__(self, model):
        super().__init__(model)

    def create_top_folder(self):
        # 默认文件夹id
        default_folder_data = self.create_default_folder(self.rds)
        top_parent_id = default_folder_data.get("id")
        # 已经存在的分发文件夹需要重新设置下文件夹名称
        self.reset_default_folder_info(self.rds, top_parent_id, LARGE_DEFAULT_FOLDER_NAME)
        # 重新设置当前的分发文件夹的level_code
        self.top_level_code = default_folder_data.get('level_code', '') or ''
        if self.top_level_code.count('-') != 1:
            # 不是根目录下的文件夹
            raise UserError(message=f'大屏的系统分发文件夹level_code存在异常：{self.top_level_code}')
        return top_parent_id

    def create_default_folder(self, conn):
        """
        创建大屏分发默认文件夹
        :return:
        """
        distribute_folder = conn.query_one(
            'select * from dap_bi_dashboard where id = %(id)s', {'id': LARGE_DEFAULT_FOLDER_ID}
        ) or {}
        if distribute_folder:
            return distribute_folder
        else:
            # 创建一个大屏系统分发文件夹
            biz_code = str(uuid.uuid4()).replace('-', '')
            distribute_folder = {
                'id': LARGE_DEFAULT_FOLDER_ID,
                'name': LARGE_DEFAULT_FOLDER_NAME,
                'parent_id': '',
                'type': 'FOLDER',
                'theme': 'tech_blue',
                'application_type': ApplicationType.LargeScreen.value,
                'biz_code': biz_code
            }
            operate_model = DashboardLevelSequenceModel(level_id='')
            distribute_folder["level_code"] = level_sequence_service.generate_level_code(operate_model, conn=conn)
            distribute_folder['user_group_id'] = '00000000-0000-0000-1111-000000000000'
            distribute_folder['distribute_type'] = DistributeType.Distribute.value
            conn.insert('dap_bi_dashboard', data=distribute_folder.copy())
            return distribute_folder


def deliver_large_screen_data(
        project_code,  # NOSONAR
        large_screens_data,  # NOSONAR
        distribute_type,  # NOSONAR
        target_large_screen_folder_id=None,  # NOSONAR
        is_import=False,   # NOSONAR
        include_large_screen_folder=True,
        is_new_jump=0,
        extra_info={}
):  # NOSONAR
    """
    处理待分发大屏数据，参数参见仪表板的导入
    """
    extra_info = extra_info or {}
    extra_info['deliver_dashboard_type'] = 'large_screen'

    if target_large_screen_folder_id:
        db = DbWrap(project_code)
        folder = db.get_one(
            'dap_bi_dashboard', conditions={'id': target_large_screen_folder_id},
            fields=['application_type', 'name']
        ) or {}
        if folder.get('application_type') != ApplicationType.LargeScreen.value:
            raise UserError(message=f'目标文件夹<{folder.get("name")}>不是大屏文件夹!')

    # 1. 完全调用仪表板的导入
    deliver_dashboard_data(
        project_code=project_code, dashboard_data=large_screens_data,
        distribute_type=distribute_type, target_dashboard_folder_id=target_large_screen_folder_id,
        is_import=is_import, include_folder=include_large_screen_folder,
        is_new_jump=is_new_jump, extra_info=extra_info,
    )


class DataReportingQueryBuilder(DashboardQueryBuilder):
    """
    数据报告导入、分发的处理对象
    """
    def __init__(self, model):
        super().__init__(model)

    def create_top_folder(self):
        # 默认文件夹id
        default_folder_data = self.create_default_folder(self.rds)
        top_parent_id = default_folder_data.get("id")
        # 已经存在的分发文件夹需要重新设置下文件夹名称
        self.reset_default_folder_info(self.rds, top_parent_id, DATA_REPORTING_DEFAULT_FOLDER_NAME)
        # 重新设置当前的分发文件夹的level_code
        self.top_level_code = default_folder_data.get('level_code', '') or ''
        if self.top_level_code.count('-') != 1:
            # 不是根目录下的文件夹
            raise UserError(message=f'数据报表系统分发文件夹level_code存在异常：{self.top_level_code}')
        return top_parent_id

    def create_default_folder(self, conn):
        """
        创建大屏分发默认文件夹
        :return:
        """
        distribute_folder = conn.query_one(
            'select * from dap_bi_dashboard where id = %(id)s', {'id': DATA_REPORTING_DEFAULT_FOLDER_ID}
        ) or {}
        if distribute_folder:
            return distribute_folder
        else:
            # 创建一个数据报表系统分发文件夹
            biz_code = str(uuid.uuid4()).replace('-', '')
            distribute_folder = {
                'id': DATA_REPORTING_DEFAULT_FOLDER_ID,
                'name': DATA_REPORTING_DEFAULT_FOLDER_NAME,
                'parent_id': '',
                'type': 'FOLDER',
                'theme': 'tech_blue',
                'application_type': ApplicationType.DataReporting.value,
                'biz_code': biz_code
            }
            operate_model = DashboardLevelSequenceModel(level_id='')
            distribute_folder["level_code"] = level_sequence_service.generate_level_code(operate_model, conn=conn)
            distribute_folder['user_group_id'] = '00000000-0000-0000-1111-000000000000'
            distribute_folder['distribute_type'] = DistributeType.Distribute.value
            conn.insert('dap_bi_dashboard', data=distribute_folder.copy())
            return distribute_folder


def deliver_data_reporting_data(
        project_code,  # NOSONAR
        data_reporting_data,  # NOSONAR
        distribute_type,  # NOSONAR
        target_data_reporting_folder_id=None,  # NOSONAR
        is_import=False,   # NOSONAR
        include_data_reporting_folder=True,
        is_new_jump=0,
        extra_info={}
):  # NOSONAR
    """
    处理待分发数据报表数据，参数参见仪表板的导入
    """
    extra_info = extra_info or {}
    extra_info['deliver_dashboard_type'] = 'data_reporting'

    if target_data_reporting_folder_id:
        db = DbWrap(project_code)
        folder = db.get_one(
            'dap_bi_dashboard', conditions={'id': target_data_reporting_folder_id},
            fields=['application_type', 'name']
        ) or {}
        if folder.get('application_type') != ApplicationType.DataReporting.value:
            raise UserError(message=f'目标文件夹<{folder.get("name")}>不是数据报表文件夹!')

    # 1. 完全调用仪表板的导入
    deliver_dashboard_data(
        project_code=project_code, dashboard_data=data_reporting_data,
        distribute_type=distribute_type, target_dashboard_folder_id=target_data_reporting_folder_id,
        is_import=is_import, include_folder=include_data_reporting_folder,
        is_new_jump=is_new_jump, extra_info=extra_info,
    )


def deliver_dashboard_data(
        project_code,  # NOSONAR
        dashboard_data,  # NOSONAR
        distribute_type,  # NOSONAR
        target_dashboard_folder_id=None,  # NOSONAR
        is_import=False,   # NOSONAR
        include_folder=True,
        is_new_jump=0,
        extra_info={}
):  # NOSONAR
    """
    处理待分发报告数据
    :param project_code:
    :param dashboard_data:
    :param distribute_type:
    :param target_dashboard_folder_id:
    :param is_import:
    :param include_folder:
    :param is_new_jump: 报告中的数据是否是新跳转
    :param extra_info: 附属扩展信息
    报告还原场景时，参数结构
    extra_info = {
        'is_restore': True, # 报告还原场景
        'modified_account': modified_account # 报告还原修改者
    }
    :return:
    """
    db_name = config.get('DBInstaller.project_db_prefix', 'dmp') + '_' + project_code
    model = ProjectModel(code=project_code, db_name=db_name, distribute_type=distribute_type)
    logger.info('执行报告分发 租户code:{0} 租户数据库:{1} 分发锁定状态:{2}'.format(project_code, db_name, distribute_type))
    if extra_info.get('deliver_dashboard_type') == 'large_screen':
        query_builder = LargeScreenQueryBuilder(model)
    elif extra_info.get('deliver_dashboard_type') == 'data_reporting':
        query_builder = DataReportingQueryBuilder(model)
    else:
        query_builder = DashboardQueryBuilder(model)
    top_parent_id = target_dashboard_folder_id if target_dashboard_folder_id is not None else query_builder.create_top_folder()
    query_builder.top_parent_id = top_parent_id
    query_builder.include_folder = include_folder
    query_builder.is_import = is_import
    if extra_info:
        query_builder.extra_info = extra_info
    query_builder.replace_multi_data(dashboard_data)
    # run_flow_when_dashboard_download_from_app_mark(project_code, dashboard_data)
    upgrade_old_redirect_report(dashboard_data, project_code, is_new_jump)

    # 分发后也需要清除重点大屏缓存
    if dashboard_data:
        for dashboard_id, dashboard in dashboard_data.items():
            if dashboard.get('is_key'):
                from project.services.project_service import delete_last_version_cache

                delete_last_version_cache(dashboard_id, project_code)

    return True


def run_flow_when_dashboard_download_from_app_mark(project_code, dashboard_data):
    """
    如果是来自应用市场的报告的话，立即触发一次流程调度
    """
    dashboard_data = dashboard_data or {}
    dmp_api = DMPAPI(project_code)
    queue_name = config.get('RabbitMQ.queue_name_flow')
    conn = get_db(project_code)
    dashboard_ids = list(dashboard_data.keys())
    yysc_dashboard_folder_parent_id = '1569e1a6-e444-11eb-9287-00155d0a440c'
    # yysc_dataset_folder_id = '23292623-e444-11eb-9287-00155d0a440c'

    yysc_dashboard_parent_level_code = dashboard_repository.get_parent_dashboard_level_code(yysc_dashboard_folder_parent_id, conn)
    if not yysc_dashboard_parent_level_code:
        # 租户还没有应用市场这个文件夹
        return

    for dashboard_id in dashboard_ids:
        # 是否是来自应用市场的报告
        is_son_dashboard = dashboard_repository.is_son_dashboard_by_dashboard_id(dashboard_id, yysc_dashboard_parent_level_code, conn)
        if not is_son_dashboard:
            continue

        logger.info(f'开始处理来自应用内市场的报告[{dashboard_id}], project_code: {project_code}')
        datasets = dashboard_repository.get_dashboard_scheduled_datasets(dashboard_id, conn) or []
        for dataset in datasets:
            try:
                dmp_api.flow_run(dataset.get("id"), queue_name)
                logger.info(f'数据集触发了调度：{dataset}')
            except:
                logger.info(f'数据集触发调度失败：{traceback.format_exc()}')


def upgrade_old_redirect_report(dashboards, project_code, is_new_jump):
    from base import repository
    from upgrade.command.dashboard_redirect_params_upgrade import DashboardRedirectParamsUpgrade

    project = repository.get_data('dap_bi_tenant_setting', {'code': project_code, 'is_new_jump': 1}, fields=[], multi_row=False)
    is_new_jump_mode = bool(project)
    is_new_jump = bool(is_new_jump)

    # 如果不是旧报告导入新跳转模式，就不处理升级
    if not ((not is_new_jump) and is_new_jump_mode):
        return

    unupgrade_dashboard_ids = get_unupgrade_dashboard_ids(dashboards)
    if not unupgrade_dashboard_ids:
        return

    # 旧的导出的报告是旧的跳转关系，需要将旧的跳转关系升级到新的全局参数
    upgrader = DashboardRedirectParamsUpgrade(tenant_code=project_code)
    for dashboard_id in unupgrade_dashboard_ids:
        try:
            upgrader.exec_one(dashboard_id)
            logger.info(f'[{dashboard_id}]完成了升级旧导出报告的跳转关系')
        except:
            logger.error(f'[{dashboard_id}]升级旧导出报告的跳转关系出错：{traceback.format_exc()}')


def get_unupgrade_dashboard_ids(dashboards):
    # 获取未升级的dashboard_id
    # 判断条件：以前的导出跳转关系中没有global_params_id这个字段或者这个字段没有值
    result = set()
    for dashboard_id, data in dashboards.items():
    #     jump_config_list = []
    #     jump_config_list.extend(data.get('dashboard_chart_params_jump', []))
    #     jump_config_list.extend(data.get('dashboard_jump_relation', []))
    #     jump_config_list.extend(data.get('dashboard_vars_jump_relation', []))
    #     jump_config_list.extend(data.get('dashboard_fixed_var_jump_relation', []))
    #     for jump in jump_config_list:
    #         if not jump.get('global_params_id'):
    #             result.add(dashboard_id)
        result.add(dashboard_id)
    return result
