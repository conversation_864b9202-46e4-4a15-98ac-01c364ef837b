"""
导入报表前的备份
"""
import json
import time
import logging

import traceback

from components.dmp_api import DMPAPI
from dmplib import config
from dmplib.saas.project import get_db

logger = logging.getLogger(__name__)


def get_list_ids(datas: list):
    """
    [{'id': 1}, {'id': 2}]
    """
    return [data.get('id', '') for data in datas if data.get('id', '')]


#
# def get_list_ids(datas: list):
#     """
#     [{'id': 1}, {'id': 2}]
#     """
#     return [data.get('id', '') for data in datas if data.get('id', '')]


def build_export_kwargs(db, kwargs):
    """
    构建导出的参数
    """
    dashboards = kwargs.get('dashboards') or []
    large_screens = kwargs.get('large_screens') or []
    data_reporting = kwargs.get('data_reporting') or []
    datasets = kwargs.get('datasets') or []
    applications = kwargs.get('applications') or []
    feeds = kwargs.get('feeds') or []
    report_center = kwargs.get('report_center') or []
    ppt = kwargs.get('ppt') or []

    application_ids = get_list_ids(applications)
    dataset_ids = get_list_ids(datasets)
    dashboard_ids = get_list_ids(dashboards)
    large_screen_ids = get_list_ids(large_screens)
    data_reporting_ids = get_list_ids(data_reporting)
    ppt_ids = get_list_ids(ppt)
    report_center_ids = get_list_ids(report_center)
    feed_ids = get_list_ids(feeds)

    application_ids = filter_existed_ids(application_ids, db, table='dap_bi_application')
    dataset_ids = filter_existed_ids(dataset_ids, db, table='dap_bi_dataset')
    dashboard_ids = filter_existed_ids(dashboard_ids, db, table='dap_bi_dashboard')
    large_screen_ids = filter_existed_ids(large_screen_ids, db, table='dap_bi_dashboard')
    data_reporting_ids = filter_existed_ids(data_reporting_ids, db, table='dap_bi_dashboard')
    ppt_ids = filter_existed_ids(ppt_ids, db, table='dap_bi_dashboard')
    report_center_ids = filter_existed_ids(report_center_ids, db, table='dap_bi_dashboard')
    feed_ids = filter_existed_ids(feed_ids, db, table='dap_bi_dashboard_email_subscribe')

    title = get_title()
    data = {
        "description": title,
        "title": title,
        "is_export_excel_data": 1,
        "application_ids": application_ids,
        "dataset_ids": dataset_ids,
        "dashboard_ids": dashboard_ids,
        "large_screen_ids": large_screen_ids,
        "data_reporting_ids": data_reporting_ids,
        "ppt_ids": ppt_ids,
        "report_center_ids": report_center_ids,
        "feed_ids": feed_ids
    }
    return data


def filter_existed_ids(ids, db, table='dap_bi_dataset'):
    """
    过滤出环境上不存在id
    """
    if not ids:
        return []
    sql = f'select id from {table} where id  in  %(ids)s'
    data = db.query(sql, {'ids': ids}) or []
    str_data = str(data)
    result = [i for i in ids if i in str_data]
    return result


def get_title():
    suffix = time.strftime('%Y%m%d%H%M%S', time.localtime())
    return f"导入备份-{suffix}"


def build_export_kwargs_from_deliver(db, kwargs):
    """
    构建导出的参数
    """
    dashboards = kwargs.get('dashboards') or {}
    large_screens = kwargs.get('large_screens') or {}
    data_reporting = kwargs.get('data_reporting') or {}
    datasets = kwargs.get('datasets') or []
    applications = kwargs.get('applications') or {}
    feeds = kwargs.get('feeds') or {}
    report_center = kwargs.get('report_center') or {}
    ppt = kwargs.get('ppt') or {}

    application_ids = list(applications.keys())
    dataset_ids = _get_dataset_ids(datasets)
    dashboard_ids = list(dashboards.keys())
    large_screen_ids = list(large_screens.keys())
    data_reporting_ids = list(data_reporting.keys())
    ppt_ids = ppt.get('ids') or []
    report_center_ids = report_center.get('ids') or []
    feed_ids = _get_feed_ids(feeds)

    application_ids = filter_existed_ids(application_ids, db, table='dap_bi_application')
    dataset_ids = filter_existed_ids(dataset_ids, db, table='dap_bi_dataset')
    dashboard_ids = filter_existed_ids(dashboard_ids, db, table='dap_bi_dashboard')
    large_screen_ids = filter_existed_ids(large_screen_ids, db, table='dap_bi_dashboard')
    data_reporting_ids = filter_existed_ids(data_reporting_ids, db, table='dap_bi_dashboard')
    ppt_ids = filter_existed_ids(ppt_ids, db, table='dap_bi_dashboard')
    report_center_ids = filter_existed_ids(report_center_ids, db, table='dap_bi_dashboard')
    feed_ids = filter_existed_ids(feed_ids, db, table='dap_bi_dashboard_email_subscribe')

    title = get_title()
    data = {
        "description": title,
        "title": title,
        "is_export_excel_data": 1,
        "application_ids": application_ids,
        "dataset_ids": dataset_ids,
        "dashboard_ids": dashboard_ids,
        "large_screen_ids": large_screen_ids,
        "data_reporting_ids": data_reporting_ids,
        "ppt_ids": ppt_ids,
        "report_center_ids": report_center_ids,
        "feed_ids": feed_ids
    }
    return data


def _get_feed_ids(feeds: dict):
    dashboard_email_subscribes = feeds.get('dashboard_email_subscribe') or []
    return [feed.get('id') for feed in dashboard_email_subscribes if feed.get('id')]


def _get_dataset_ids(datasets: list):
    return [dataset.get('dataset', {}).get('id', '') for dataset in datasets if
            dataset.get('dataset', {}).get('id', '')]


def backup_import_resources(project_code, **kwargs):
    """
    kwargs:  {
            'dashboards': dashboards, 'large_screens': large_screens, 'data_reporting': data_reporting,
            'datasets': datasets, 'applications': applications, 'feeds': feeds,
        }
    """
    try:
        logger.error(f"开始处理导入之前的备份")
        db = get_db(project_code)
        # 1。构建参数
        if kwargs.get('__backup_type__') == 'deliver':
            data = build_export_kwargs_from_deliver(db, kwargs)
        else:
            # 来自dmp前台用户的导入
            data = build_export_kwargs(db, kwargs)

        # 2。触发导出
        start_time = time.time()
        dmp_api = DMPAPI(project_code, 3)
        result = dmp_api.call_export(data)
        logger.info(f"备份触发参数：{json.dumps(data, ensure_ascii=False)}")
        logger.error(f"触发备份返回： {result}")

        # 2。查询导出备份状态
        timeout = int(config.get('Function.backup_timeout') or 3 * 60)
        export_id = result.get('data') or ''
        if export_id:
            logger.error(f"开始获取导出结果，timeout：{timeout}")
            sql = 'select `status`, `url` from `dap_bi_exports` where id = %r limit 1' % export_id
            while time.time() - start_time < timeout:
                db = get_db(project_code)
                data = db.query_one(sql, ()) or {}
                db.commit()
                if data.get('status') in ['成功', '失败']:
                    cost = int(time.time() - start_time)
                    logger.error(f"备份完成，耗时：{cost}s，状态：{data.get('status')}，url: {data.get('url')}")
                    # 隐藏掉这个记录
                    update_sql = 'update `dap_bi_exports` set `is_deleted` = 1 where id = %r limit 1' % export_id
                    db.exec_sql(update_sql, ())
                    db.commit()
                    return
                time.sleep(3)
            else:
                logger.error(f"查询导出数据超时")
        else:
            logger.error(f"没有获取到导出id，退出备份！")

    except:
        logger.error(f"备份失败： {traceback.format_exc(limit=4)}")
