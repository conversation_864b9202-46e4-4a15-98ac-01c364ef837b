import base64
import concurrent.futures
import gzip
import io
import json
import logging
import os
import re
import traceback
import zipfile
from urllib import request, parse
from urllib.parse import urlparse

import xmltodict

from base import repository
from components import utils, g_handle
from components.oss import OSSFileProxy
from dmplib import config
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from issue_dashboard.services import deliver_service


def parse_zip_file(file_url):
    # 指定 base_info.json 文件路径
    base_info_file_path = 'base/info.json'

    bytes_data = deliver_service.read_file_from_oss(file_url)
    data = io.BytesIO(bytes_data)
    return parse_zip_file_data(data)


def parse_zip_byte(bytes_data):
    # 指定 base_info.json 文件路径
    base_info_file_path = 'base/info.json'
    data = io.BytesIO(bytes_data)
    return parse_zip_file_data(data)


def cache_data_set_process(data):
    # 新的缓存数据处理
    g_data = gzip.compress(json.dumps(data, ensure_ascii=False).encode())
    b64_data = base64.b64encode(g_data)
    return b64_data.decode()


def save_file_content(task_id, app_code, file_data, is_full_file:bool=False):
    try:
        if not file_data:
            return
        res = []
        file_type = 'INCR'
        if is_full_file:
            file_type = 'FULL'
        for k, v in file_data.items():
            type = k
            if not isinstance(v, dict) and not isinstance(v, list):
                continue
            if isinstance(v, dict) :
                for data_id, content in v.items():
                    if not content:
                        continue
                    c_content = cache_data_set_process(content)
                    res.append({'id': seq_id(),'task_id':task_id, 'parent_id':'', 'app_code':app_code, 'file_type': file_type, 'type': type, 'data_id': data_id, 'content': c_content})
            else:
                for content in v:
                    if not content:
                        continue
                    data_id = ''
                    if type == 'datasets':
                        data_id = content.get('dataset_id', '')
                    c_content = cache_data_set_process(content)
                    res.append({'id': seq_id(), 'task_id': task_id, 'parent_id':'', 'app_code':app_code, 'file_type': file_type, 'type': type, 'data_id': data_id, 'content': c_content})
        if res:
            condition =  {'task_id': task_id}
            if is_full_file:
                condition = {'file_type': 'FULL', 'app_code': app_code}
            exists = repository.data_is_exists('dap_bi_rdc_file_content', condition)
            if exists:
                repository.delete_data('dap_bi_rdc_file_content', condition)
            repository.add_list_data('dap_bi_rdc_file_content', res, ['id', 'task_id', 'parent_id', 'file_type', 'app_code','type','data_id','content'])
            return True, "保存成功"
    except Exception as e:
        logging.error(f'保存文件内容失败:{str(e)}')
        return False, f'保存文件内容失败:{str(e)}'


def has_save_file_content(task_id):
    exists = repository.data_is_exists('dap_bi_rdc_file_content', {'task_id': task_id, 'file_type': "INCR"})
    return exists


def parse_file_and_save_content(task_id,app_code, file_url):
    file_data = None
    try:
        # file_data:dict = parse_zip_url(file_url)
        file_url = file_url.split('?')[0]
        file_data:dict = parse_zip_file(file_url)
    except Exception as e:
        logging.error(f'解析文件内容失败:{str(e)}')
    return save_file_content(task_id,app_code, file_data)

def parse_full_file_and_save_content(task_id, app_code):
    file_data = None
    try:
        data = repository.get_data('dap_bi_publish_center_template', {'task_id': task_id, 'app_code': app_code},['file_url'])
        if not data:
            return None
        file_url = data.get('file_url')
        # file_data:dict = parse_zip_url(file_url)
        file_url = file_url.split('?')[0]
        file_data:dict = parse_zip_file(file_url)
    except Exception as e:
        logging.error(f'解析文件内容失败:{str(e)}')
    return save_file_content(task_id,app_code, file_data, True)


def parse_zip_url(url):
    # 指定 base_info.json 文件路径
    base_info_file_path = 'base/info.json'
    try:
        url = parse.quote(unquote_url(url), safe=':/?=&')
        temp_file = request.urlopen(url)
    except Exception as e:
        raise UserError(message=str(e))
    data = io.BytesIO(temp_file.read())
    return parse_zip_file_data(data)

def unquote_url(url) :
    prev = url
    try:
        for i in range(5):
            url = parse.unquote(url)
            if prev == url:
                break
            else:
                prev = url
    except Exception as e:
        pass
    return url

def parse_zip_file_data(file_data):
    # 指定 base_info.json 文件路径
    base_info_file_path = 'base/info.json'

    export_data = None
    with zipfile.ZipFile(file_data, "r") as zip_file:
        # 判断 ZIP 文件中是否存在 base_info.json 文件
        if base_info_file_path in zip_file.namelist():
            # 如果存在 base_info.json 文件，读取该文件的内容
            with zip_file.open(base_info_file_path) as base_info_file:
                try:
                    export_data = json.loads(base_info_file.read().decode('utf-8'))
                except:
                    raise UserError(400, 'zip压缩文件内容格式有误')

                read_map(export_data, 'dashboards', zip_file)
                read_list(export_data, 'datasets', zip_file)
                read_map(export_data, 'large_screens', zip_file)
                read_map(export_data, 'applications', zip_file)
                read_map(export_data, 'data_reporting', zip_file)
                read_feeds(export_data, 'feeds', zip_file)
                read_active_reports(export_data, zip_file)
                read_excel(export_data, zip_file)
                read_ppt(export_data, 'ppt', zip_file)

        else:
            # 如果不存在 base_info.json 文件，读取根目录下的 .json 文件
            json_files = [name for name in zip_file.namelist() if name.endswith('.json')]
            for file_name in json_files:
                with zip_file.open(file_name) as file:
                    r_data = file.read()
                    if r_data:
                        try:
                            export_data = json.loads(r_data.decode('utf-8'))
                        except:
                            raise UserError(400, 'zip压缩文件内容格式有误')
                        break
    return export_data


IMAGE_PATTERN = re.compile(r'http[s]?://[^"]+\.(?:png|jpg|webp|bmp|svg|ico|icon|jpeg|gif|tiff)(?:\?[^"]*)?')
TEMPLATE_URL_PATH = '/template'


def get_filename_from_url(url):
    """
    从url中提取出文件名称
    """
    from urllib.parse import urlparse

    parsed_url = urlparse(url)
    file_name = os.path.basename(parsed_url.path)
    return file_name


def upload_file(zip_file, url, fp, zip_dists):
    # 压缩包的文件存在
    if fp in zip_dists:
        root = f'{TEMPLATE_URL_PATH}/dist'
        with zip_file.open(fp, 'r') as f:
            return utils.apply_oss_file_to_own(url, f, root=root)
    else:
        # 本地的文件名称
        # fn = os.path.basename(fp)
        # url上的名字（url的文件名和包里面的文件名不一样，包里面的是url-md5算出来的）
        url_fn = get_filename_from_url(url)
        # sql = f"""
        #   select * from dap_bi_dist_upload_record where  old_url like '%/dist/{fn}%'  or old_url like '%/dist/{url_fn}%' limit 1;
        #   """
        sql = f"""
          select * from dap_bi_dist_upload_record where  old_url like '%/dist/{url_fn}%' or old_url like '%/dp/{url_fn}%' limit 1;
        """
        record = {}
        rs = repository.get_data_by_sql(sql , {}) or []
        if rs:
            record = rs[0]
        if record:
            oss_url = record.get('new_url', '')
            return oss_url
        else:
            return url


def process_dist_data(export_data, zip_url):
    """加工处理oss file"""
    try:
        bytes_data = deliver_service.read_file_from_oss(zip_url)
        file_data = io.BytesIO(bytes_data)
        # return parse_zip_file_data(data)
        with zipfile.ZipFile(file_data, "r") as zip_file:
            # if config.get('OSS_Config.service') != 'Minio':
            #     return export_data
            upload_image_threads = int(config.get('Function.dashboard_import_image_threads', 6))
            data_str = json.dumps(export_data, ensure_ascii=False)
            # 不依赖包里面的资源文件
            # 因为rdc的推过来的包会去掉之前更新的图片文件

            zip_dists = [i for i in zip_file.namelist() if i.startswith('dist/')]
            dist_mainfest = load_dist_maifest(zip_file, zip_dists)
            dist_mainfest_version = dist_mainfest.get('version') or 'v1'
            logging.info(f"图片资源版本: {dist_mainfest_version}")

            # # 上传包内的所有图片
            # for fp in zip_dists:
            #     with zip_file.open(fp, 'r') as f:
            #         fn = fp.replace('dist/', '')
            #         oss_url = OSSFileProxy().upload(f, file_name=fn, root=f'{TEMPLATE_URL_PATH}/dist', **{'auto_mime': True})
            #         ref[fn] = oss_url

            # 查找图片
            ulist = IMAGE_PATTERN.findall(data_str, re.A)
            ulist = set(ulist)
            logging.info(f"找到图片资源个数: {len(ulist)}")

            futures = []
            ref = {}
            with concurrent.futures.ThreadPoolExecutor(max_workers=upload_image_threads) as executor:
                for url in ulist:
                    # logging.info(f"url: {url}")

                    suffix = utils.get_file_extension(url)
                    # 最新使用path进行计算
                    if dist_mainfest_version == 'v2':
                        calc_url_path = urlparse(url).path
                        md5_value = utils.hash_k(calc_url_path)
                    # 兼容旧版逻辑（使用url整个进行md5计算得到文件名称）
                    # 后面要放弃这个逻辑
                    else:
                        md5_value = utils.hash_k(url)
                    # logging.info(f"md5_value: {md5_value}")
                    file_name = f'{md5_value}{suffix}'
                    fp = os.path.join('dist/', file_name)
                    # if fp not in zip_dists:

                    kwargs = {}
                    kwargs['g'] = g_handle.get_g_property()
                    # if fp in zip_dists:
                    # 提交并行任务
                    future = executor.submit(g_handle.handle_g_auto(upload_file), zip_file, url, fp, zip_dists, **kwargs)
                    futures.append(future)
                    ref[future] = url
                    #     root = f'{TEMPLATE_URL_PATH}/dist'
                    #     with zip_file.open(fp, 'r') as f:
                    #         oss_url = utils.apply_oss_file_to_own(url, f, root=root)
                    #         if oss_url:
                    #             logging.info(f"替换： {url} ==> {oss_url}")
                    #             data_str = data_str.replace(url, oss_url)

                # 等待所有任务完成并获取结果
                for future in concurrent.futures.as_completed(futures):
                    oss_url = future.result()
                    url = ref.get(future, '')
                    if oss_url and url:
                        if url.endswith('\\') and not oss_url.endswith('\\'):
                            oss_url += '\\'
                        # logging.info(f"替换图片： {url} ==> {oss_url}")
                        data_str = data_str.replace(url, oss_url)

            try:
                data_json = json.loads(data_str)
                logging.info(f"处理图片完成")
            except Exception as e:
                logging.error(f'处理图片错误: {traceback.format_exc(limit=2)}')
                with open('/tmp/backup_error_image_json.txt', 'w') as f:
                    f.write(data_str)
                raise e
            return data_json
    except:
        logging.error(f'处理图片资源失败: {traceback.format_exc()}')
        return export_data


# def get_url_filename_v2(url, zip_dists):
#     """
#     新的文件名称计算规则
#     """
#     suffix = utils.get_file_extension(url)
#     calc_url_path = urlparse(url).path
#     md5_value = utils.hash_k(calc_url_path)
#     file_name = f'{md5_value}{suffix}'
#     fp = os.path.join('dist/', file_name)
#     return fp in zip_dists, fp


def load_dist_maifest(zip_file, zip_dists):
    """
    加载导出的图片文件名称映射关系map
    """
    name = 'image_dist.json'
    fp = os.path.join('dist/', name)
    if fp not in zip_dists:
        logging.error(f"dist目录不存在{name}, 加载为空")
        return {}
    with zip_file.open(fp, 'r') as f:
        return json.loads(f.read())


def read_map(export_data, directory, zip_file):
    files_in_directory = [name for name in zip_file.namelist() if
                          name.startswith(directory + '/') and name.endswith('.json')]
    export_data[directory] = {}
    # 遍历二级目录下的文件，并读取其内容
    for file_name in files_in_directory:
        with zip_file.open(file_name) as file:
            content = file.read()
            _map = json.loads(content.decode('utf-8'))
            for key,value in _map.items():
                export_data.get(directory)[key] = value


def read_list(export_data, directory, zip_file):
    files_in_directory = [name for name in zip_file.namelist() if
                          name.startswith(directory + '/') and name.endswith('.json')]
    export_data[directory] = []
    # 遍历二级目录下的文件，并读取其内容
    for file_name in files_in_directory:
        with zip_file.open(file_name) as file:
            content = file.read()
            _list = json.loads(content.decode('utf-8'))
            export_data[directory].append(_list)


def read_feeds(export_data, directory, zip_file):
    files_in_directory = [name for name in zip_file.namelist() if
                          name.startswith(directory + '/') and name.endswith('.json')]
    export_data[directory] = {}
    export_table = {
        'dashboard_email_subscribe': 'id', 'dashboard_subscribe_display_format': 'subscribe_id',
        'mobile_subscribe_filter': 'email_subscribe_id', 'tag_relation': 'relation_id',
        'mobile_subscribe_rules': 'email_subscribe_id', 'mobile_subscribe_chapters': 'email_subscribe_id', 'flow': 'id'
    }
    export_data[directory] = {}
    # 遍历二级目录下的文件，并读取其内容
    for file_name in files_in_directory:
        with zip_file.open(file_name) as file:
            content = file.read()
            _map = json.loads(content.decode('utf-8'))
            for key, value in export_table.items():
                if not export_data.get(directory).get(key):
                    export_data.get(directory)[key] = []
                _val = _map.get(key) or []
                export_data.get(directory).get(key).extend(_val)

# def read_active_reports(export_data, zip_file):
#     is_active_reports = True
#     files_in_directory = [name for name in zip_file.namelist() if name == "active_reports/base.json"]
#     if not files_in_directory or len(files_in_directory) <= 0:
#         is_active_reports = False
#         files_in_directory = [name for name in zip_file.namelist() if name == 'report_center/base.json']
#     # 遍历二级目录下的文件，并读取其内容
#     if files_in_directory and len(files_in_directory) > 0:
#         file_name = files_in_directory[0]
#         with zip_file.open(file_name) as file:
#             content = file.read()
#             export_data["active_reports"] = json.loads(content.decode('utf-8'))
#         if not is_active_reports:
#             ids = export_data["active_reports"]["ids"]
#             export_data["active_reports"]["ids"] = []
#             export_data["report_center"] = {"data": {}, "ids": ids}


def read_active_reports(export_data, zip_file):
    def report_1_0_postprocessor(path, key, value):
        if isinstance(value, dict) and '@p4:nil' in value and value['@p4:nil'] == 'true':
            return key, None
        return key, value

    is_report_center = True
    files_in_directory = [name for name in zip_file.namelist() if
                          name.startswith('report_center/') and name.endswith('.rptx')]
    if not files_in_directory:
        is_report_center = False
        files_in_directory = [name for name in zip_file.namelist() if
                              name.startswith('active_reports/') and name.endswith('.rptx')]
    if not files_in_directory:
         return

    group_map = {}
    detail_map = {}
    detail_ids = []
    for file_name in files_in_directory:
        with zip_file.open(file_name) as file:
            content = file.read()
            report_package_dict = xmltodict.parse(content, postprocessor=report_1_0_postprocessor, encoding='utf-8')
            group_id = read_active_reports_group(report_package_dict, group_map)
            read_active_reports_detail(report_package_dict, detail_map, group_id, detail_ids)
    # form_reports_group_tree(group_map, result)
    # detail_list = format_report_detail_tree(detail_map)
    # format_report(detail_list, group_map, result)

    result = format_report_tree(list(detail_map.values()), list(group_map.values()))
    remove_file_parent_id(result, group_map.keys())
    result = sort_report_tree(result)
    export_data["active_reports"] = {"data": result, "ids": detail_ids}
    if is_report_center:
        export_data["active_reports"]["ids"] = []
        export_data["report_center"] = {"data": {}, "ids": detail_ids}


def remove_file_parent_id(tree, folder_id_list):
    def flatten_tree(file_tree):
        flat_list = []
        _stack = [file_tree]

        while _stack:
            current_node = _stack.pop()
            flat_list.append(current_node)

            # 将子节点添加到堆栈中，以便进一步处理
            if 'sub' in current_node and current_node['sub']:
                for child in reversed(current_node['sub']):
                    _stack.append(child)

            # 如果当前节点有子节点，清空它的子节点
            current_node['sub'] = []

        return flat_list

    stack = tree[:]
    file_set = set()
    new_sub_dict = {}
    while stack:
        current = stack.pop()
        if current['parent_id'] in folder_id_list and current['type'] == 'FILE' and current['id'] not in file_set:
            # 此处current一定是FILE而不是CHILD_FILE
            new_sub = flatten_tree(current)
            if current['parent_id'] not in new_sub_dict.keys():
                new_sub_dict[current['parent_id']] = []
            new_sub_dict[current['parent_id']].extend(new_sub)
            current['parent_id'] = None
            file_set.add(current['id'])
        stack.extend(current['sub'])

    stack = tree[:]
    while stack:
        current = stack.pop()
        if current["id"] in new_sub_dict.keys():
            current['sub'] = new_sub_dict[current["id"]]
        stack.extend(current['sub'])


def format_report_tree(files, folders):
    # Step 1: 过滤掉name为系统分发的folder
    folders = [folder for folder in folders if folder['name'] != '系统分发']

    # Step 2: 将所有节点放入一个字典，方便查找和构建树
    all_items = files + folders
    node_dict = {item['id']: item for item in all_items}

    # Step 3: 初始化根节点集合和子节点集合
    roots = []
    children = {}

    for item in all_items:
        if not item.get('sub'):
            item['sub'] = []  # 初始化子节点列表
        parent_id = item.get('parent_id')

        # 处理parent不存在的场景
        if parent_id is None or parent_id not in node_dict:
            item['parent_id'] = None
            roots.append(item)
        else:
            if parent_id not in children:
                children[parent_id] = []
            children[parent_id].append(item)

    # Step 4: 处理存在环的场景
    visited = set()
    stack = []

    def add_to_stack(item):
        if item['id'] in visited:
            return
        visited.add(item['id'])
        stack.append(item)

    for root in roots:
        add_to_stack(root)

    while stack:
        node = stack.pop()
        node_id = node['id']

        if node_id in children:
            sub_nodes = children[node_id]
            for sub_node in sub_nodes:
                if sub_node['id'] in visited:
                    # 如果存在环，修正parent_id
                    sub_node['parent_id'] = None
                    if sub_node not in roots:
                        roots.append(sub_node)
                else:
                    node['sub'].append(sub_node)
                    add_to_stack(sub_node)

    return roots


def sort_report_tree(tree):
    def sort_key(item):
        # 处理不存在rank字段或rank字段不是数值的情况
        try:
            rank = int(item.get('rank', 0) or 0)
        except:
            rank = 0
        return rank, item.get('name', '') or ''

    def sort_node(node):
        node['sub'].sort(key=sort_key)
        for sub_node in node['sub']:
            sort_node(sub_node)

    for root in tree:
        sort_node(root)

    return tree


def read_active_reports_group(report_package_dict, group_map):
    report_package_group = report_package_dict["ReportPackage"].get("Groups", {})
    report_groups = report_package_group.get("ReportGroup", [])
    root_id = ''
    if not isinstance(report_groups, list):
        report_groups = [report_groups]
    for group in report_groups:
        if not root_id:
            root_id = group.get('MyRptGroupId')
        if group_map.get(group.get('MyRptGroupId')):
            continue
        group_map[group.get('MyRptGroupId')] = {
            "id": group.get('MyRptGroupId'),
            "name": group.get('GroupCName'),
            "type": "FOLDER",
            "parent_id": group.get('ParentId'),
            "sub":[],
            "release_type": None,
            "publish_status": None
        }
    return root_id


def form_reports_group_tree(group_map, result):
    for _id, group in group_map.items():
        parent_id = group.get('parent_id')
        if not parent_id:
            result.append(group_map.get(_id))
        else:
            parent = group_map.get(parent_id)
            if parent:
                parent['sub'].append(group_map.get(_id))
            else:
                result.append(group_map.get(id))


def read_active_reports_detail(report_package_dict, detail_map, group_id, detail_ids):
    report_package_reports = report_package_dict["ReportPackage"].get("Reports", {})
    report_details = report_package_reports.get("ReportDetail", [])
    if not isinstance(report_details, list):
        report_details = [report_details]
    for detail in report_details:
        release_type = 4
        row_state = 0
        if detail.get('PublishStatus') and detail.get('PublishStatus') in [1, 2, '1', '2']:
            row_state = 1

        if detail.get('ReleaseType') and detail.get('ReleaseType') in [3, '3']:
            release_type = 3
        report = {
            "id": detail.get('MyRptDetailId'),
            "name": detail.get('RptCName'),
            "type": "FILE",
            "parent_id": detail.get('ParentId') or group_id,
            "created_by": detail.get('CreatedName') or '',
            "modified_by": detail.get('ModifiedName') or '',
            "sub": [],
            "release_type": release_type,
            "publish_status": row_state,
            "is_screen_rpt": detail.get('IsScreenRpt'),
            "rank": detail.get('Rank'),
            "tag_relation": json.loads(detail.get('BusinessTags')) if detail.get('BusinessTags') else [],
            "dataset_ids": read_relation_datasets(detail)
        }
        detail_ids.append(report.get('id'))
        detail_map[report.get('id')] = report

def read_relation_datasets(detail):
    ids = []
    vars = detail.get('RptVar', '')
    ds_defines = detail.get('DsDefine', '')
    vars = json.loads(vars)
    ds_defines = json.loads(ds_defines)
    for var in vars:
        dataset_id = var.get('DatasetId', '')
        if dataset_id:
            ids.append(dataset_id)
        rels = var.get('ColumnRelations', []) or []
        for rel in rels:
            dataset_id = rel.get('DatasetId', '')
            if dataset_id:
                ids.append(dataset_id)
    for ds_define in ds_defines:
        dataset_id = ds_define.get('DatasetId', '')
        if dataset_id:
            ids.append(dataset_id)
    return list(set(ids))


def format_report_detail_tree(detail_map):
    root_list = []
    for key, value in detail_map.items():
        parent_id = value.get("parent_id")
        if parent_id and detail_map.get(parent_id):
            detail_map.get(parent_id).get("sub").append(value)
        else:
            root_list.append(value)
    return root_list


def format_report(detail_list, group_map, result):
    for detail in detail_list:
        new_detail_list = []
        sub = detail.get("sub")
        detail["sub"] = ""
        parent_id = detail.get("parent_id")
        new_detail_list.append(detail)
        format_report_detail_list(new_detail_list, sub)
        if parent_id and group_map.get(parent_id):
            detail["parent_id"] = ""
            group_map.get(parent_id).get("sub").extend(new_detail_list)
        else:
            result.extend(new_detail_list)


def format_report_detail_list(new_detail_list, detail_list):
    if not detail_list:
        return
    for detail in detail_list:
        sub = detail.get("sub")
        detail["sub"] = ""
        new_detail_list.append(detail)
        format_report_detail_list(new_detail_list, sub)


def read_excel(export_data, zip_file):
    files_in_directory = [name for name in zip_file.namelist() if
                          name.startswith('excel/') and (name.endswith('.csv') or name.endswith('.txt'))]
    # 遍历二级目录下的文件，并读取其内容
    export_data["export_excel_data"] = []
    for file_name in files_in_directory:
        # f'excel_data_{id}.csv'
        name = os.path.basename(file_name)
        id = extract_id_from_excel_file_name(name)
        export_data["export_excel_data"].append({
             'id': id,
             'name': id,
             'table_name': '',
             'type': 'EXCEL',
             'zip_path': file_name
        })


def extract_id_from_excel_file_name(file_name):
    if file_name.endswith(".txt"):
        pattern = r'excel_data_(.*?)_(.*?).txt'
    else:
        pattern = r'excel_data_(.*).csv'
    match = re.match(pattern, file_name)
    if match:
        id_value = match.group(1)
        return id_value
    else:
        return None

def read_ppt(export_data, directory, zip_file):
    files_in_directory = [name for name in zip_file.namelist() if name == (directory + "/base.json")]
    if not files_in_directory or len(files_in_directory) <= 0:
        return

    file_name = files_in_directory[0]
    with zip_file.open(file_name) as file:
        content = file.read()
        export_data[directory] = json.loads(content.decode('utf-8'))

    # 以下是因为rdc更新中心需要每个ppt文件内容
    files_in_directory = [name for name in zip_file.namelist() if name.startswith(directory+'/') and name != (directory + "/base.json")]
    if not files_in_directory or len(files_in_directory) <= 0:
        return

    for name in files_in_directory:
        k = extract_uuid(name)
        if not k:
            continue
        with zip_file.open(name) as file:
            content = file.read()
            export_data[directory][k] = json.loads(content.decode('utf-8'))


def extract_uuid(filename):
    import re
    match = re.search(r'/([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})\.', filename)
    if match:
        return match.group(1)
    else:
        return None

def read_other(export_data, directory, zip_file):
    files_in_directory = [name for name in zip_file.namelist() if name == (directory+"/base.json")]
    if files_in_directory and len(files_in_directory) > 0:
        file_name = files_in_directory[0]
        with zip_file.open(file_name) as file:
            content = file.read()
            export_data[directory] = json.loads(content.decode('utf-8'))