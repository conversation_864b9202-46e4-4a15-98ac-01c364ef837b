#!/usr/local/bin python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------
import json
import logging

# ---------------- 业务模块 ----------------
from base.models import BaseModel
from dmplib.saas.project import get_db
from dmplib.utils.errors import UserError
from base.sql_adapter import adapter_sql


def _instantiate_field_model(field_dict):
    """
    获取field model实例
    :param field_dict:
    :return:
    """
    model = BaseModel()
    for field, field_data in field_dict.items():
        # 优先取字段默认值
        default = field_data.get("Default")
        field_type = field_data.get("Type")
        # 字段值不能为空且没有设置默认值的情况
        if field_data.get("Null") == "NO" and default is None:
            if "int" in field_type:
                default = 0
            elif "char" in field_type or "time" in field_type or "date" in field_type or "enum" in field_type:
                default = ""
        if field in ["created_on", "modified_on"]:
            default = ""
        model.__setattr__(field, default)
    return model


def batch_get_table_fields_models(project_code, db_table_names):
    """
    批量获取数据表的field model
    todo: 报告分发里面可复用此方法
    :return:
    """
    target_table_field_models = {}
    if not project_code or not db_table_names:
        return target_table_field_models
    try:
        db_instance = get_db(project_code)
        if not db_instance:
            return target_table_field_models
        for table_name in db_table_names:
            field_dict = {}
            sql = adapter_sql('table_columns', db_type=db_instance.db_type).format(
                table_name=table_name,
                owner=db_instance.db
            )
            cur = db_instance._execute(sql)
            query_data = cur.fetchall()
            if not query_data:
                continue
            for single_field in query_data:
                field = single_field.get("Field")
                if not field:
                    continue
                field_dict[field] = single_field
            target_table_field_models[table_name] = _instantiate_field_model(field_dict)
        return target_table_field_models
    except BaseException as e:
        if db_instance:
            db_instance.conn.close()
        raise UserError(message="获取目标租户表信息异常：{}".format(e))
