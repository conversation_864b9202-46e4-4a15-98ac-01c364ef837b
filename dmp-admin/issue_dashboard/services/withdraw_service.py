#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
from base.enums import ProjectValueAddedFunc, DashboardType

from components.dmp_api import DMPAPI
from dmplib.saas.project import get_db
from issue_dashboard import DELIVER_STATUS_RUNNING, DELIVER_STATUS_SUCCESS, DELIVER_STATUS_FAILURE
from issue_dashboard.services import deliver_service, level_sequence_service
from .biz_link_service import BizLinkService
from user.services.user_service import DEFAULT_USER_ID
from ..models import DashboardLevelSequenceModel

logger = logging.getLogger(__name__)


def withdraw_dashboard(withdraw_id):
    """
    报告撤回
    kwargs:
    :return:
    """
    deliver = deliver_service.get_deliver_detail(withdraw_id)
    dashboard_ids = deliver.get("source_url")
    is_all_projects = deliver.get("is_all_projects")
    projects = deliver.get("dest_projects")
    # 回收的报告类型，0:仪表板，1：统计报表
    recovery_type = deliver.get("recovery_type")

    if isinstance(projects, str):
        projects = str.split(projects, ",")
    if is_all_projects:
        from project.services import project_service
        projects = project_service.get_all_project_codes()
    failure_project = []
    msg = "success"
    is_success = DELIVER_STATUS_SUCCESS
    deliver_service.update_deliver_status(withdraw_id, DELIVER_STATUS_RUNNING, '', '')
    for project in projects:
        try:
            if recovery_type == 0:
                # 仪表板删除
                dmp_api = DMPAPI(project, 3)
                dmp_api.delete_dashboard(dashboard_ids)
            elif recovery_type == 1:
                # 统计报表同步请求删除
                biz_link_service = BizLinkService(ProjectValueAddedFunc.ACTIVE_REPORT.value, None, DEFAULT_USER_ID)
                biz_link_service.delete_report(dashboard_ids, project)
                delete_report_center(dashboard_ids, project)

        except Exception as e:
            is_success = DELIVER_STATUS_FAILURE
            failure_project.append(project)
            msg = f"failure，原因：{str(e)}"

    deliver_service.update_deliver_status(withdraw_id, is_success, ",".join(failure_project), msg)


def delete_report_center(dashboard_ids, project):
    if not dashboard_ids:
        return
    dashboard_ids = str.split(dashboard_ids, ",")
    conn = get_db(project)
    sql = """
        select id,parent_id,level_code from dap_bi_dashboard where id in %(dashboard_ids)s order by level_code desc
    """
    dashboard_list = conn.query(sql, {"dashboard_ids": dashboard_ids})
    for dashboard in dashboard_list:
        if not dashboard or dashboard.get('type') == DashboardType.Folder.value:
            continue
        delete_active_report_dashboard_file_all(conn, dashboard)


def delete_active_report_dashboard_file_all(conn, dashboard):
    """
    回收父报表及其子报表
    """
    level_code = dashboard.get("level_code")
    sql = '''delete from dap_bi_dashboard 
        where level_code like %(level_code)s 
        and application_type in %(application_types)s '''
    params = {
        "level_code": level_code + "%",
        "application_types": ['5', '6']
    }
    conn.exec_sql(sql, params)


def delete_active_report_dashboard_file(conn, dashboard):
    """
    只回收对应的报表，如果是父报表，则子报表不删除，子报表变成父报表
    """
    dashboard_id = dashboard.get("id")
    child_file_list = get_active_report_dashboard_child_file(conn, dashboard_id)
    for child_file in child_file_list:
        move_active_report_dashboard(conn, child_file.get("id"), dashboard_id, child_file.get("level_code"))
    sql = '''delete from dap_bi_dashboard where id = %(dashboard_id)s and application_type in %(application_types)s '''
    params = {
        "dashboard_id": dashboard_id,
        "application_types": ['5', '6']
    }
    conn.exec_sql(sql, params)


def move_active_report_dashboard(conn, dashboard_id, parent_id, old_level_code):
    # 目录或者文件位置复原，先修改自身parent_id 和level_code，再修改其子节点的level_code
    sql = '''update dap_bi_dashboard set parent_id=%(parent_id)s,level_code=%(level_code)s
                              where id=%(dashboard_id)s and application_type in %(application_types)s '''
    operate_model = DashboardLevelSequenceModel(level_id=parent_id)
    level_code = level_sequence_service.generate_level_code(operate_model, conn=conn)
    params = {
        "parent_id": parent_id,
        "application_types": ['5', '6'],
        "level_code": level_code,
        "dashboard_id": dashboard_id
    }
    conn.exec_sql(sql, params)

    sql = '''update dap_bi_dashboard set level_code=CONCAT(%(new_level_code)s,substring(level_code,{}))
                                          where application_type in %(application_types)s 
                                          and level_code like %(old_level_code)s'''.format(len(old_level_code) + 1)
    params = {
        "application_types": ['5', '6'],
        "new_level_code": level_code,
        "old_level_code": old_level_code + "%"
    }
    conn.exec_sql(sql, params)
    return {"level_code": level_code, "id": dashboard_id, "parent_id": parent_id}


def get_active_report_dashboard_child_file(conn, dashboard_id):
    sql = '''select id,level_code from dap_bi_dashboard
                                  where parent_id=%(dashboard_id)s and application_type in %(application_types)s '''
    params = {
        "application_types": ['5', '6'],
        "dashboard_id": dashboard_id
    }
    return conn.query(sql, params)
