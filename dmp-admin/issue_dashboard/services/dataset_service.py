#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    Created by chenchao 2018/7/30.
"""
import copy
import hashlib
import json
import os.path
import traceback
from loguru import logger
import shutil
from datetime import datetime

from components import graph_upgrade_helper
from dmplib.components.relation_converter import convert_relation_data_to_graph
from dmplib import config
from components.dmp_api import DMPAPI
from dmplib.saas.project import get_db
from dmplib.utils.errors import UserError
from dmplib.utils.strings import seq_id
from issue_dashboard.repositories import dataset_repository
from dmplib.redis import conn as conn_redis
from base import repository

# 数据集字段关联变量数据缓存key前缀
from issue_dashboard.services import level_sequence_service, deliver_helper
from issue_dashboard.models import DataSetLevelSequenceModel


DATASET_INCLUDE_VARS = "dataset_include_vars"

DATASET_MULTI_FIELD = "multi_field_meta"

# 节点和表名的映射关系，配置的表会根据对应租户表的字段信息来匹配需要导入的数据
DATASET_NODE_TABLE_CONFIG = {
    'dataset': 'dap_bi_dataset', 'dataset_design': 'dap_bi_dataset_design', 'dataset_fields': 'dap_bi_dataset_field',
    'dataset_folders': 'dap_bi_dataset', 'data_source': 'dap_m_data_source', 'dataset_depend': 'dap_bi_dataset_depend',
    'dataset_index': 'dap_bi_dataset_index', 'dataset_tables_collection': 'dap_bi_dataset_tables_collection',
    'dataset_field_delete': 'dap_bi_dataset_field_delete', 'dataset_filter': 'dap_bi_dataset_filter',
    'dataset_vars': 'dap_bi_dataset_vars', 'dataset_field_include_vars': 'dap_bi_dataset_field_include_vars',
    'keyword': 'dap_bi_keyword', 'keyword_details': 'dap_bi_keyword_details',
    'dataset_used_table': 'dap_bi_dataset_used_table',
    'dataset_graph_tables': 'dap_bi_dataset_graph_table',
    'dataset_graph_table_relations': 'dap_bi_dataset_graph_table_relation',
    'dataset_graph_filters': 'dap_bi_dataset_graph_filter'
}


def get_dataset_table_field(project_code):
    # 获取所有表名，根据表名获取对应租户表中的字段信息
    try:
        table_list = list(set(list(DATASET_NODE_TABLE_CONFIG.values()))) or []
        table_field_models = deliver_helper.batch_get_table_fields_models(project_code, table_list) or {}
        return table_field_models
    except Exception as e:
        logger.error(str(e))
        return {}


def prepare_dataset_data(export_dataset_data, table_field_models):
    if not table_field_models or not export_dataset_data:
        return

    def filter_data(data: dict, fields):
        filtered_dict = {key: value for key, value in data.items() if key in fields}
        return filtered_dict

    for node, table in DATASET_NODE_TABLE_CONFIG.items():
        # 获取表字段信息
        field_model = table_field_models.get(table)
        if not field_model:
            logger.error("获取目标租户数据表`{}`异常".format(table))
            continue
        target_fields = list(field_model.get_dict().keys())
        target_node_data = export_dataset_data.get(node)
        if isinstance(target_node_data, dict) and target_node_data:
            export_dataset_data[node] = filter_data(target_node_data, target_fields)
        if isinstance(target_node_data, list) and target_node_data:
            new_target_data = []
            for target_data in target_node_data:
                new_target_data.append(filter_data(target_data, target_fields))
            if new_target_data:
                export_dataset_data[node] = new_target_data


def convert_relation_to_graph(export_dataset_data):
    dataset = export_dataset_data.get('dataset')
    if dataset.get('edit_mode') == 'relation':
        dataset_tables_collection = export_dataset_data.get('dataset_tables_collection', [])
        dataset_tables_collection = [table for table in dataset_tables_collection if
                                     table.get('dataset_id') == dataset.get('id')]

        dataset_fields = export_dataset_data.get('dataset_fields', [])
        dataset_fields = [dataset_field for dataset_field in dataset_fields if
                          dataset_field.get('dataset_id') == dataset.get('id')]

        dataset_filter = export_dataset_data.get('dataset_filter', [])
        dataset_filter = next((f for f in dataset_filter if
                               f.get('dataset_id') == dataset.get('id')), None)

        graph_content = graph_upgrade_helper.build_graph_content(dataset, dataset_fields, dataset_tables_collection,
                                                                 dataset_filter)

        # 清理视图模式数据
        dataset['edit_mode'] = 'graph'
        if dataset.get('is_import_table') == 1:
            dataset['is_import_table'] = 0

        if export_dataset_data.get('dataset_filter'):
            export_dataset_data['dataset_filter'] = [f for f in export_dataset_data.get('dataset_filter') if
                                                     f.get('dataset_id') != dataset.get('id')]

        if export_dataset_data.get('dataset_tables_collection'):
            export_dataset_data['dataset_tables_collection'] = [table for table in dataset_tables_collection if
                                                                table.get('dataset_id') != dataset.get('id')]

        # 存入新图形化数据
        if export_dataset_data.get('dataset_graph_tables') is None:
            export_dataset_data['dataset_graph_tables'] = []
        export_dataset_data['dataset_graph_tables'].extend(graph_content.get('node'))

        if export_dataset_data.get('dataset_graph_table_relations') is None:
            export_dataset_data['dataset_graph_table_relations'] = []
        export_dataset_data['dataset_graph_table_relations'].extend(graph_content.get('link'))

        if export_dataset_data.get('dataset_graph_filters') is None:
            export_dataset_data['dataset_graph_filters'] = []

        filter_data = graph_upgrade_helper.build_graph_filter_data(dataset.get('id'), graph_content, dataset_filter)

        if filter_data:
            export_dataset_data['dataset_graph_filters'].append(filter_data)


def import_dataset_data(project_code, export_dataset_data, parent_id,
                        is_import, include_folder, replace_data_source=False, is_lock=0,from_template=0,
                        table_field_models=None, is_shuxin15=False):  # NOSONAR
    """
    根据 导入数据集
    :param project_code:
    :param export_dataset_data:
    :param parent_id:
    :param is_import:
    :param include_folder:
    :param replace_data_source:
    :param is_lock:
    :param table_field_models:
    :param is_shuxin15:
    :return:
    """

    if not project_code:
        raise UserError(message="租户code不能为空")

    dataset_id = export_dataset_data.get("dataset_id")
    if not dataset_id:
        raise UserError(message="数据集ID不能为空")

    # 获取所有的表名，根据导入的租户匹配应该需要保留的字段信息（如有新增的表导入，请在处理方法中加入对应表名）
    prepare_dataset_data(export_dataset_data, table_field_models)

    # 转换历史视图模式数据
    convert_relation_to_graph(export_dataset_data)

    delete_ids = []
    need_update_parent_id = {}
    if include_folder:
        dataset_folders, dataset_folders_ids, need_update_parent_id = deal_dataset_folders(project_code, parent_id,
                                                                                           export_dataset_data.get(
                                                                                               "dataset_folders"),
                                                                                           is_import)
        delete_ids.extend(dataset_folders_ids)
        export_dataset_data["dataset_folders"] = dataset_folders

    dataset, dataset_ids = deal_dataset(project_code, parent_id, export_dataset_data.get("dataset"), is_import,
                                        need_update_parent_id, include_folder, is_lock)
    delete_ids.extend(dataset_ids)
    export_dataset_data["dataset"] = dataset

    # 修改创建人和修改人
    update_created_by(export_dataset_data, project_code)

    # 写入数据
    try:
        if not export_dataset_data.get("is_diff_excel"):
            dataset_repository.save_dataset_data(project_code, export_dataset_data, include_folder, replace_data_source, from_template, is_shuxin15=is_shuxin15)
    except:
        dataset_repository.delete_dataset_ids(project_code, delete_ids)
        raise

    # rundeck注册调度流程
    flow = export_dataset_data.get("flow")
    if flow and flow.get("status") == "启用" and flow.get("schedule"):
        dmp_api = DMPAPI(project_code)
        # 定时任务仍然走flow
        queue_name = (
            config.get('RabbitMQ.queue_name_flow')
            if flow.get("type") == "数据集"
            else config.get('RabbitMQ.queue_name_flow_offline')
        )
        dmp_api.flow_enable(flow.get("id"), queue_name)

        if is_from_dmp_import():
            # 立即运行走优先队列
            priority = config.get('RabbitMQ.queue_name_flow_priority')
            dmp_api.flow_run_ignore_error(flow.get("id"), queue_name=priority)
        # else:
        # 分发导入慢队列
        # flow_erp_op = config.get("RabbitMQ.dmp_flow_erp_op", 'Flow-erp-op')
        # dmp_api.flow_run_ignore_error(flow.get("id"), queue_name=flow_erp_op)

    if flow and dataset.get('type') == 'EXCEL':
        dmp_api = DMPAPI(project_code)
        # 立即运行走优先队列
        priority = config.get('RabbitMQ.queue_name_flow_priority')
        dmp_api.flow_run_ignore_error(flow.get("id"), queue_name=priority)

    cache_flow = export_dataset_data.get("cache_flow")
    if cache_flow and cache_flow.get("status") == "启用" and cache_flow.get("schedule"):
        dmp_api = DMPAPI(project_code)
        queue_name = config.get('RabbitMQ.queue_name_flow')
        dmp_api.flow_enable_ignore_error(cache_flow.get("id"), queue_name)

    # rundeck禁用调度流程
    if flow and flow.get("status") == "禁用":
        dmp_api = DMPAPI(project_code)
        dmp_api.flow_delete_schedule(flow.get("id"))

    if cache_flow and cache_flow.get("status") == "禁用" and cache_flow.get('id'):
        dmp_api = DMPAPI(project_code)
        dmp_api.flow_delete_schedule(cache_flow.get("id"))


def is_from_dmp_import():
    # 判断是来自dmp还是admin租户分发的， 目前来自admin的分发功能
    # 来自admin租户分发的现在不处理
    stacks = traceback.extract_stack()
    for stack in stacks[:-10]:
        if 'deliver_service.py' in stack.filename and stack.name == 'deliver':
            return False
    return True


def deal_dataset_folders(project_code, parent_id, dataset_folders, is_import):
    # 导入的时候，根据分发还是导入，走不同逻辑，分发覆盖目录，导入根据目录名称合并
    # [f1, f2, f3] 当f1存在时，删除f1列表，f2是否存在重复，重复也是删除，不重复，修改f2的parent_id和生成level_code
    _delete_ids = []
    child_folders = {}
    # 需要替换的parent_id
    need_update_parent_id = {}
    # 覆盖全目录不会走下面逻辑
    if not dataset_folders:
        return dataset_folders, _delete_ids, need_update_parent_id
    if is_import:
        child_folders = dataset_repository.get_all_dataset_by_id(project_code, parent_id)
    save_dataset_folders = copy.deepcopy(dataset_folders)
    for i, dataset_folder in enumerate(dataset_folders):
        old_dataset_folder = copy.copy(dataset_folder)
        # 先处理parent_id问题
        if not dataset_folder.get("parent_id"):
            dataset_folder["parent_id"] = parent_id
        # 名称在同级目录存在，且id不一致
        if dataset_folder.get("name") in child_folders.get(dataset_folder.get("parent_id"), {}).keys() \
                and dataset_folder.get("id") != child_folders.get(dataset_folder.get("parent_id"), {}).get(
            dataset_folder.get("name")):
            need_update_parent_id[dataset_folder.get("id")] = child_folders.get(dataset_folder.get("parent_id"),
                                                                                {}).get(
                dataset_folder.get("name"))
            save_dataset_folders.pop(i)
            continue
        # 当前parent_id需要被替换
        if dataset_folder.get("parent_id") in need_update_parent_id.keys():
            dataset_folder["parent_id"] = need_update_parent_id.get(dataset_folder.get("parent_id"))
        # 重新生成level_code
        dataset_folder["created_by"] = project_code
        dataset_folder["modified_by"] = project_code
        with get_db(project_code) as conn:
            # 当前的目录是否在该系统下存在
            old_dataset = dataset_repository.get_dataset_simple_info_by_id(dataset_folder.get("id"), conn=conn)
            new_level_code = old_dataset.get('level_code')
            if not new_level_code:
                old_dataset_parent = dataset_repository.get_dataset_simple_info_by_id(dataset_folder.get("parent_id"),
                                                                                      conn=conn)
                if not old_dataset_parent:
                    # 如果parent_id不存在，则放置分发根目录下
                    dataset_folder["parent_id"] = parent_id
                new_level_code = level_sequence_service.generate_level_code(
                    DataSetLevelSequenceModel(level_id=dataset_folder.get("parent_id")), conn=conn)
            else:
                # 如果使用数据库中的level_code那么parent_id也要使用数据库中的，以保持level_code和parent_id一致
                dataset_folder["parent_id"] = old_dataset.get("parent_id")
            dataset_folder["level_code"] = new_level_code

            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            if not dataset_folder['created_on']:
                dataset_folder['created_on'] = current_time
            if not dataset_folder['modified_by']:
                dataset_folder['modified_by'] = current_time

            conn.replace_multi_data("dap_bi_dataset", [dataset_folder], dataset_folders[0].keys(), condition_field=['id'])
            _delete_ids.append(dataset_folder.get("id"))
        save_dataset_folders.remove(old_dataset_folder)
        save_dataset_folders.append(dataset_folder)
    return save_dataset_folders, _delete_ids, need_update_parent_id


def deal_dataset(project_code, parent_id, dataset, import_data, need_update_parent_id, include_folder, is_lock=0):
    _delete_ids = []
    dataset["created_by"] = project_code
    dataset["modified_by"] = project_code
    if not import_data:
        dataset["is_lock"] = is_lock
    # 平铺的情况都使用主目录parent_id
    if not dataset.get("parent_id") or not include_folder:
        dataset['parent_id'] = parent_id
    # 导入的情况下，有可能需要更改parent_id
    if import_data and dataset['parent_id'] in need_update_parent_id.keys():
        dataset['parent_id'] = need_update_parent_id.get(dataset['parent_id'])
    # 重新生成level_code
    with get_db(project_code) as conn:
        # 当前的目录是否在该系统下存在
        # TODO 这个地方存在BUG，如果parent_id=""(导入到根目录的时候)如果数据已经存在，这个时候，level_code存在不会重写但是，parent_id会设置成空
        dataset_level_code = dataset_repository.get_dataset_level_code(dataset.get("id"),
                                                                       conn=conn, parent_id=dataset.get('parent_id'))
        # level_code不存在或者来自dmp前台用户导入根目录，修复上面的问题
        if not dataset_level_code or \
        (import_data and parent_id == '') \
        :
            dataset_level_code = level_sequence_service.generate_level_code(
                DataSetLevelSequenceModel(level_id=dataset.get("parent_id")), conn=conn)
        dataset["level_code"] = dataset_level_code

        # 导入分发时 判断数据集是否存在 存在不修改调度信息
        dataset_id = dataset.get('id')
        if dataset_id:
            sql = "select connect_type from dap_bi_dataset where id = '{id}' ".format(id=dataset_id)
            connect_type = conn.query_scalar(sql)
            if connect_type is not None:
                dataset['connect_type'] = connect_type

        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        if not dataset['created_on']:
            dataset['created_on'] = current_time
        if not dataset['modified_by']:
            dataset['modified_by'] = current_time

        conn.replace_multi_data("dap_bi_dataset", [dataset], dataset.keys(), condition_field=['id'])
        _delete_ids.append(dataset.get("id"))
    return dataset, _delete_ids


def update_created_by(export_dataset_data, project_code):
    """
    修改创建人和修改人
    :param export_dataset_data:
    :param project_code:
    :return:
    """
    if export_dataset_data.get("data_source"):
        export_dataset_data["data_source"]["created_by"] = project_code
        export_dataset_data["data_source"]["modified_by"] = project_code

    if export_dataset_data.get("dataset_fields"):
        for dataset_field in export_dataset_data.get("dataset_fields"):
            dataset_field["created_by"] = "celery"
            dataset_field["modified_by"] = "celery"

    if export_dataset_data.get("dataset_vars"):
        for dataset_var in export_dataset_data.get("dataset_vars"):
            dataset_var["created_by"] = "celery"
            dataset_var["modified_by"] = "celery"

    if export_dataset_data.get("dataset_field_include_vars"):
        for dataset_field_include_var in export_dataset_data.get("dataset_field_include_vars"):
            dataset_field_include_var["created_by"] = "celery"
            dataset_field_include_var["modified_by"] = "celery"


def check_dataset_is_shuxin15(data_source):
    try:
        return data_source.get('type') == 'MysoftShuXin15'
    except:
        return False


def multi_import_dataset_data(project_code, export_dataset_datas, parent_id=None, is_import=False,
                              include_folder=True, replace_data_source=False, is_lock=0,
                              oss_file='', export_excel_data=[], from_template=0):  # NOSONAR
    """
    多租户 导入多数据集
    :param project_code: 'code1'
    :param export_dataset_datas: [{},{}]
    :param parent_id: "39ec8814-da00-0f29-2c6b-5cdaaa798318"
    :param is_import: bool
    :param include_folder: bool 是否使用目录结构
    :param replace_data_source: bool 是否替换数据源
    :param is_lock: bool 数据集是否锁定
    :param oss_file: 整个导出压缩包的oss地址
    :param export_excel_data: 导出的Excel数据集文件信息
    :return:
    """
    # 是否有数芯指标数据集
    has_pulsar_dataset = False

    if is_import and parent_id is None:
        raise UserError(message='导入数据集需指定目录')
    # 数据导入可以放到根目录
    if parent_id is None and not is_import:
        parent_id = dataset_repository.create_default_folder(project_code)

    # 导入Excel数据集数据处理，替换导入数据中的Excel oss地址
    if export_excel_data:
        process_import_excel_data(oss_file, export_dataset_datas, export_excel_data, project_code)

    # 获取配置中表的字段信息
    table_field_models = get_dataset_table_field(project_code)

    for export_dataset_data in export_dataset_datas:

        # 数芯数据集不导入
        if isinstance(export_dataset_data, dict) and export_dataset_data.get("dataset") and export_dataset_data.get(
                "dataset").get('external_type'):
            has_pulsar_dataset = True
            # 判断是否有数芯数据源， 没有则导入
            with get_db(project_code) as db:
                if not db.query_scalar("select `id` from `dap_m_data_source` where `type`='MysoftShuXin'"):
                    data_source = export_dataset_data.get("data_source")
                    dataset_repository._replace_data_source(db, data_source, replace_data_source)
                    db.commit()

                    # 插入project_to_shuxin记录
                    update_project_to_shuxin(data_source, project_code)
            continue

        # 判断是否是数芯1.5的数据集
        is_shuxin15 = check_dataset_is_shuxin15(export_dataset_data.get("data_source"))

        import_dataset_data(project_code, export_dataset_data, parent_id, is_import=is_import,
                            include_folder=include_folder, replace_data_source=replace_data_source,
                            is_lock=is_lock,from_template=from_template, table_field_models=table_field_models,
                            is_shuxin15=is_shuxin15
                            )
        # 清空数据集元数据和结果缓存
        conn = conn_redis()
        prefix = '{project_code}:{cache_key}:{object_name}'.format(
            project_code=project_code,
            cache_key=config.get('Cache.released_dashboard_metadata_cache_key', 'dmp'),
            object_name="dataset",
        )
        result_key = '{prefix}:{object_id}'.format(
            prefix=prefix, object_id="sql_result_" + export_dataset_data.get("dataset_id")
        )
        sql_key = '{prefix}:{object_id}'.format(
            prefix=prefix, object_id="sql_keys_" + export_dataset_data.get("dataset_id")
        )
        meta_key = '{prefix}:{object_id}'.format(prefix=prefix, object_id=DATASET_MULTI_FIELD)
        meta_key_dataset = '{prefix}:{object_id}'.format(prefix=prefix, object_id=export_dataset_data.get("dataset_id"))
        var_key = '{prefix}:{dataset_include_vars}'.format(prefix=prefix, dataset_include_vars=DATASET_INCLUDE_VARS)

        conn.delete(result_key)
        conn.delete(sql_key)
        conn.delete(meta_key)
        conn.delete(meta_key_dataset)
        conn.delete(var_key)
        if replace_data_source and export_dataset_data.get('data_source', {}):
            conn.delete(f"{project_code}:data_source::{export_dataset_data.get('data_source', {}).get('id')}")

    # 触发一次指标数据集同步
    if has_pulsar_dataset:
        DMPAPI(project_code).pulsar_dataset_sync()


def process_import_excel_data(oss_file, export_dataset_datas, export_excel_data, project_code=None):
    """
    加工导入的Excel数据集信息，替换oss地址
    oss_file：压缩包地址
    export_dataset_datas：数据集信息
    export_excel_data: 压缩包内的数据集文件信息: [
        {
             'id': '3a0b9cfe-1ad0-be69-7cc4-94b9c73c5e29',
             'name': '测试excel',
             'table_name': 'dataset_dc9e2d07c2a6bd27',
             'type': 'EXCEL',
             'zip_path': 'excel_data_3a0b9cfe-1ad0-be69-7cc4-94b9c73c5e29.csv'
        }
    ]
    """

    if not export_excel_data:
        logger.error(f'需要导入Excel数据集数据，但是缺少导出的Excel文件信息，跳过导入数据!')
        return
    if not oss_file:
        logger.error('需要导入Excel数据集数据，但是缺少oss地址，跳过导入数据!')
        return
    from issue_dashboard.services.deliver_service import extract_files_from_zip, upload_file_to_oss, \
        upload_file_to_oss_by_md5
    # 1. 解压导入包
    try:
        zip_extract_path = extract_files_from_zip(oss_file)
    except:
        logger.error(f'解压Excel文件压缩包异常: {traceback.format_exc()}')
        return

    export_excel_data = copy.deepcopy(export_excel_data)
    dataset_info_map = {d.get('dataset', {}).get('id', ''): d for d in export_dataset_datas}
    add_diff_dataset(project_code, export_dataset_datas, dataset_info_map, export_excel_data)

    # 2. 处理每一个需要导入的Excel数据集
    for export_dataset_info in export_excel_data:
        dataset_id = export_dataset_info.get('id', '')
        dataset_name = export_dataset_info.get('name', '')
        zip_path = export_dataset_info.get('zip_path', '')

        local_excel_file_path = os.path.join(zip_extract_path, zip_path)
        if not os.path.exists(local_excel_file_path):
            logger.error(f'解压的Excel文件<{local_excel_file_path}>没有找到，跳过这个Excel导入!')
            continue

        import_dataset = dataset_info_map.get(dataset_id) or {}
        if not import_dataset:
            continue

        if local_excel_file_path.endswith('.txt'):
            logger.error(f"设置数据集数据文件内容:{local_excel_file_path}")
            with open(local_excel_file_path, 'r', encoding='utf-8') as excel_content_file:
                import_dataset['dataset_excel_data_local_file_content'] = excel_content_file.read()
            continue

        # 2.1 把Excel文件上传到环境的oss中
        try:
            oss_url, new_file_name = upload_file_to_oss_by_md5(local_excel_file_path, folder='import_excel')
        except:
            logger.error(f'上传Excel到oss失败，跳过这个Excel导入! 原因：{traceback.format_exc()}')
            continue

        # 2.2 替换导入Excel数据集的oss地址
        dataset = import_dataset.get('dataset') or {}
        try:
            dataset_content = dataset.get('content') or '{}'
            dataset_content = json.loads(dataset_content)
            origin_oss_url = dataset_content.get('oss_url', '')
            origin_file_name = dataset_content.get('file_name', '')
            dataset_content['oss_url'] = oss_url
            dataset_content['file_name'] = new_file_name
            dataset_content['origin_file_info'] = {
                'origin_oss_url': origin_oss_url,
                'origin_file_name': origin_file_name,
            }
            dataset['content'] = json.dumps(dataset_content)
        except Exception as e:
            logger.error(f'替换导出包内Excel的oss地址失败，跳过！原因：{e}')
            continue

        logger.info(f'完成Excel数据集的oss地址替换, id: {dataset_id}, name: {dataset_name}')

    try:
        shutil.rmtree(zip_extract_path)
    except:
        pass


def add_diff_dataset(project_code, export_dataset_datas, dataset_info_map, export_excel_data):
    if project_code and export_excel_data:
        for export_dataset_info in export_excel_data:
            dataset_id = export_dataset_info.get('id', '')
            if not dataset_info_map.get(dataset_id):
                with get_db(project_code) as db:
                    row = db.query_one("select * FROM dap_bi_dataset where id = %(dataset_id)s and type = 'EXCEL'",
                                        {'dataset_id': dataset_id}) or {}
                    if not row:
                        continue
                    flow = db.query_one("select * FROM dap_bi_flow where id = %(flow_id)s", {"flow_id": dataset_id})
                    diff_export_dataset = {
                        "dataset_id": row.get('id'),
                        "dataset": row,
                        "flow": flow,
                        "is_diff_excel": True
                    }
                    dataset_info_map[row.get('id')] = diff_export_dataset
                    export_dataset_datas.append(diff_export_dataset)



def update_project_to_shuxin(data_source, project_code):
    try:
        from dmplib.db.mysql_wrapper import get_db as config_db

        conn_str = json.loads(data_source.get('conn_str'))
        pulsar_code = conn_str.get('project_code')
        api_host = conn_str.get('api_host')
        if pulsar_code:
            with config_db() as db:
                record = db.query_one(
                    'select * from dap_bi_project_to_shuxin where code=%(code)s and shuxin_code=%(shuxin_code)s',
                    params={'code': project_code, 'shuxin_code': pulsar_code}
                )
                if record:
                    db.update(
                        'dap_bi_project_to_shuxin', data={'api_host': api_host},
                        condition={'code': project_code, 'shuxin_code': pulsar_code}
                    )
                else:
                    db.insert('dap_bi_project_to_shuxin',
                              {'code': project_code, 'shuxin_code': pulsar_code, 'api_host': api_host})
        return True
    except Exception as e:
        print("update project_to_shuxin error: ", e)
        return False


def get_dataset_list(export_dataset_datas):
    """
    获取所有的dataset集合
    :param export_dataset_datas:
    :return:
    """
    dataset_list = []

    for export_dataset_data in export_dataset_datas:
        dataset_list.append(export_dataset_data.get("dataset"))

    return dataset_list


def get_random_lever_code():
    """
    获取4位随机编码
    :return:
    """
    return hashlib.md5(seq_id().encode('utf-8')).hexdigest()[14:-14]


if __name__ == '__main__':
    with open('/home/<USER>/3a169ccc-9e4d-e4c5-1d4f-a9e40cddf192.json', 'r', encoding='utf-8') as f:
        export_dataset_data = json.loads(f.read())
    convert_relation_to_graph(export_dataset_data)
    print(json.dumps(export_dataset_data, ensure_ascii=False))
