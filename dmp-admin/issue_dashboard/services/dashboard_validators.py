#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
报告数据校验类
"""

# ---------------- 标准模块 ----------------


# ---------------- 业务模块 ----------------
from dmplib.utils.errors import UserError


class Validator:
    """
    校验基类
    """
    def __init__(self):
        self.table_name = ""
        self.table_data = ""

    def validate_dashboard_id(self):
        """
        校验字段
        :return:
        """
        if isinstance(self.table_data, list):
            for item in self.table_data:
                # 没有dashboard_id字段或值为空
                if "dashboard_id" not in item:
                    raise UserError(message="数据包版本过低，请重新制作导入数据包")
                if not item.get("dashboard_id"):
                    raise UserError(message="{}表缺少dashboard_id字段值".format(self.table_name,))

    def validate_dim_type(self):
        """

        :return:
        """
        if isinstance(self.table_data, list):
            for item in self.table_data:
                # 没有dashboard_id字段或值为空
                if "dim_type" not in item:
                    raise UserError(message="数据包版本过低，请重新制作导入数据包")

    def validate(self):
        """
        执行校验入口
        :return:
        """
        pass


class DashboardChartDimValidator(Validator):
    """
    dim
    """
    def __init__(self):
        super().__init__()

    def validate(self):
        """

        :return:
        """
        self.validate_dashboard_id()
        self.validate_dim_type()


class DashboardChartComparisonValidator(Validator):
    """
    comparison
    """
    def __init__(self):
        super().__init__()

    def validate(self):
        """

        :return:
        """
        self.validate_dashboard_id()


class DashboardChartDesireValidator(Validator):
    """
    desire
    """
    def __init__(self):
        super().__init__()

    def validate(self):
        """

        :return:
        """
        self.validate_dashboard_id()


class DashboardChartFilterValidator(Validator):
    """
    filter
    """
    def __init__(self):
        super().__init__()

    def validate(self):
        """

        :return:
        """
        self.validate_dashboard_id()


class DashboardChartMarklineValidator(Validator):
    """
    markline
    """
    def __init__(self):
        super().__init__()

    def validate(self):
        """

        :return:
        """
        self.validate_dashboard_id()


class DashboardChartNumValidator(Validator):
    """
    num
    """
    def __init__(self):
        super().__init__()

    def validate(self):
        """

        :return:
        """
        self.validate_dashboard_id()
