#!/usr/local/bin python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------
import logging
from datetime import datetime

# ---------------- 业务模块 ----------------
from dmplib.saas.project import get_db
from dmplib.utils.errors import UserError
from issue_dashboard.constants import get_condition_fields


logger = logging.getLogger(__name__)


class FeedDeliverHandler:

    def __init__(self, project_code, feeds, feed_list):
        self.project_code = project_code
        self.feeds = feeds
        self.feed_list = feed_list

    @staticmethod
    def _pre_handle_single_table_data(single_table_data):
        """
        插入表前预处理一些字段值
        :return:
        """
        for item in single_table_data:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            item["created_by"] = "系统管理员"
            item["modified_by"] = "系统管理员"
            item["created_on"] = current_time
            item["modified_on"] = current_time

    def import_feed(self):
        """
        简讯分发导入执行入口
        :return:
        """
        if not self.feeds or not isinstance(self.feeds, dict):
            return False
        # target_table_field_models = deliver_helper.batch_get_table_fields_models(self.project_code, self.op_table_names)
        db = get_db(self.project_code)
        try:
            db.begin_transaction()
            self.flush_old_feed_data(db)
            for table, table_data in self.feeds.items():
                if not table_data:
                    continue
                self._pre_handle_single_table_data(table_data)
                if not table.startswith('dap_bi_'):
                    table = 'dap_bi_' + table
                db.replace_multi_data(table, table_data, list(table_data[0].keys()), commit=False, condition_field=get_condition_fields(table))
            db.commit()
        except UserError as e:
            db.rollback()
            message = "简讯导入失败，异常信息:{}".format(str(e))
            raise UserError(message=message)
        except Exception as e:
            db.rollback()
            logger.error(e)
            raise e
        return True

    def flush_old_feed_data(self, db):
        table_field_config = {
            'dap_bi_dashboard_email_subscribe': 'id', 'dap_bi_dashboard_subscribe_display_format': 'subscribe_id', 'dap_bi_mobile_subscribe_filter': 'email_subscribe_id',
            'dap_bi_mobile_subscribe_rules': 'email_subscribe_id', 'dap_bi_mobile_subscribe_chapters': 'email_subscribe_id', 'dap_bi_flow': 'id', 'dap_bi_mobile_subscribe_role': 'email_subscribe_id'
        }
        if self.feed_list:
            for table, field in table_field_config.items():
                for feed_id in self.feed_list:
                    db.delete(table, {field: feed_id}, commit=False)
