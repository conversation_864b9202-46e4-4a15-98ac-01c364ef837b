#!/usr/local/bin python3
# -*- coding: utf-8 -*-

"""
数据集相关配置表对应的ReplaceKeyModel类，用于记录每个表需要做id替换的字段名称
"""

# ---------------- 标准模块 ----------------

# ---------------- 业务模块 ----------------
from dmplib.utils.model import BaseModel
from issue_dashboard.replace_key_models.base_model import ReplaceKeyBaseModel


class DatasetReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dataset
    """
    def _get_keys(self):
        self.dataset_id_keys = ["id"]
        self.dataset_table_name_keys = ["table_name"]


class DatasetFieldsReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dataset_fields
    """
    def _get_keys(self):
        self.dataset_field_id_keys = ["id"]
        self.dataset_id_keys = ["dataset_id"]


class DataSourceReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: datasource
    """
    def _get_keys(self):
        pass


class DatasetDependReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dataset_depend
    """
    def _get_keys(self):
        self.dataset_id_keys = ["source_dataset_id", "depend_id"]


class DatasetFieldDeleteReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dataset_field_delete
    """
    def _get_keys(self):
        self.dataset_field_id_keys = ["dataset_field_id"]
        self.dataset_id_keys = ["dataset_id"]


class DatasetFieldIncludeVarsReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dataset_field_include_vars
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dataset_field_id_keys = ["field_id"]
        self.dataset_id_keys = ["dataset_id"]
        self.var_id_keys = ["var_id"]


class DatasetFilterReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dataset_filter
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dataset_id_keys = ["dataset_id"]


class DatasetFoldersReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dataset_folders
    """
    def _get_keys(self):
        self.dataset_id_keys = ["id"]

class DatasetIndexReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dataset_index
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dataset_id_keys = ["dataset_id"]


class DatasetTablesCollectionReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dataset_tables_collection
    """
    def _get_keys(self):
        self.primary_keys = ["id"]
        self.dataset_id_keys = ["dataset_id"]

class DatasetVarsReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: dataset_vars
    """
    def _get_keys(self):
        self.var_id_keys = ["id"]
        self.dataset_id_keys = ["dataset_id"]


class FlowReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: flow
    """
    def _get_keys(self):
        self.flow_id_keys = ["id"]


class NodesReplaceKeyModel(ReplaceKeyBaseModel):
    """
    table name: nodes
    """
    def _get_keys(self):
        self.node_id_keys = ["id"]
        self.flow_id_keys = ["flow_id"]