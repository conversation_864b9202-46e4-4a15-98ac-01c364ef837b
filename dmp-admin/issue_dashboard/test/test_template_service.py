#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

"""
test template service

"""

import logging
from tests.base import BaseTest
from issue_dashboard.services import template_service
import unittest

logger = logging.getLogger(__name__)


class TestTemplate(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='jifenglin', account='admin')

    def test_install_template(self):
        oss_url = "https://dmp-test.oss-cn-shenzhen.aliyuncs.com/tmp/%E6%A8%A1%E6%9D%BF-%E5%85%A8%E5%9C%BA%E6%99%AF_20191217163819.zip"
        kwargs = {
            'project_code': "jifenglin",
            'file_url': oss_url,
            'target_dashboard_folder_id': "39f17a0d-3f62-5739-774f-6aac1b0272a8",
            'target_dataset_folder_id': "39f17a0d-4d36-9dc9-fe73-8c4a146c85af",
            "exclude_dataset_ids": "",
        }
        template_service.install_template(**kwargs)


if __name__ == '__main__':
    unittest.main()
