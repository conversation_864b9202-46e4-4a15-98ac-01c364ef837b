#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file

import logging
from tests.base import BaseTest
import unittest
from dmplib.hug import g
from issue_dashboard.api_route import *

logger = logging.getLogger(__name__)


class TestDeliverService(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='uitest', account='admin')

    def test_deliver(self):
        oss_url = "https://dmp-test.oss-cn-shenzhen.aliyuncs.com/tmp/111_20200309164734.zip"
        kwargs = {
            'projects': ["ycm1"],
            'file_url': oss_url,
            'is_all_projects': 0,
            'distribute_type': 1,
            "replace_source": 0,
        }
        deliver(**kwargs)

    def test_deliver_filling(self):
        g.userid = '39f32579-f2cc-d498-3661-3bd01a724e4a'
        oss_url = "https://dmp-test.oss-cn-shenzhen.aliyuncs.com/tmp/AA%E6%89%8B%E5%B7%A5%E5%A1%AB%E6%8A%A5%E5%AF%BC%E5%87%BA2_20220610114432.zip"
        kwargs = {
            'projects': ["qy1701"],
            'file_url': oss_url,
            'is_all_projects': 0,
            "replace_source": 0,
        }
        deliver(**kwargs)


if __name__ == '__main__':
    # unittest.main()
    s = unittest.TestSuite()
    s.addTest(TestDeliverService("test_deliver"))
    runner = unittest.TextTestRunner()
    runner.run(s)
