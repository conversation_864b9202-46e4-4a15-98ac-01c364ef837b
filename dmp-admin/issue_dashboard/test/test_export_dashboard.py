#!/usr/bin/env python3
# -*- coding: utf-8 -*-


from tests.base import BaseTest
from issue_dashboard.services import dashboard_service


class TestProject(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='cmsk', account='cmsk')

    def test_export_dashboard(self):

        project_code = "cmsk"
        distribute_type = 1
        dashboards = {
                "39ec64d8-7b52-7f98-d6eb-c09d94a8ebd1": {
                    "dashboard_chart_filter": [],
                    "dashboard_component_filter_field_relation": [],
                    "screen_dashboard": [
                        {
                            "id": "39ec64d8-feb6-8247-ba53-0e1b0dc16557",
                            "screen_id": "39ec64d8-7b52-7f98-d6eb-c09d94a8ebd1",
                            "modified_by": "ycm2",
                            "type": 1,
                            "dashboard_id": "39ec64d8-7b52-7f98-d6eb-c09d94a8ebd1",
                            "created_by": "ycm2",
                            "rank": 1,
                        }
                    ],
                    "dashboard_filter_chart_default_values": [],
                    "dashboard_filter": [],
                    "dashboard_chart": [
                        {
                            "layout": None,
                            "chart_code": "table",
                            "data_logic_type_code": None,
                            "sort_method": None,
                            "dashboard_id": "39ec64d8-7b52-7f98-d6eb-c09d94a8ebd1",
                            "created_by": "ycm2",
                            "data_modified_on": None,
                            "refresh_rate": "",
                            "column_order": None,
                            "percentage": None,
                            "content": None,
                            "modified_on": "2019-03-06T19:03:22",
                            "level_code": "",
                            "config": "[{\"title\":\"全局样式\",\"field\":\"global\",\"spread\":false,\"items\":[{\"field\":\"scroll\",\"label\":\"滚动设置\",\"show\":{\"field\":\"checked\",\"data\":false},\"items\":[{\"field\":\"interVal\",\"label\":\"间隔时间\",\"data\":5},{\"field\":\"ln\",\"label\":\"锁定行数\",\"data\":0},{\"field\":\"scrollMode\",\"data\":{\"mode\":\"page\",\"rows\":1}}]},{\"field\":\"cell\",\"label\":\"单元格\",\"items\":[{\"field\":\"fontSize\",\"label\":\"字号\",\"data\":14},{\"field\":\"color\",\"label\":\"颜色\",\"data\":\"RGBA(46,46,46,1)\",\"theme_controled\":true},{\"field\":\"lineHeight\",\"label\":\"行高\",\"data\":24},{\"field\":\"paddingX\",\"label\":\"水平内边距\",\"data\":6},{\"field\":\"paddingY\",\"label\":\"垂直内边距\",\"data\":6},{\"field\":\"textAlign\",\"label\":\"文本对齐\",\"data\":\"center\"},{\"field\":\"iswhiteSpace\",\"label\":\"自行换行\",\"data\":false},{\"field\":\"rowspan\",\"label\":\"合并同类单元格\",\"data\":false}]},{\"field\":\"qianN\",\"label\":\"前N行设置\",\"show\":{\"field\":\"checked\",\"data\":false},\"items\":[{\"field\":\"end\",\"label\":\"行数\",\"data\":3},{\"field\":\"fontSize\",\"label\":\"字号\",\"data\":14},{\"field\":\"color\",\"label\":\"颜色\",\"data\":\"#FFFFFF\"},{\"field\":\"lineHeight\",\"label\":\"行高\",\"data\":24},{\"field\":\"paddingX\",\"label\":\"水平内边距\",\"data\":6},{\"field\":\"paddingY\",\"label\":\"垂直内边距\",\"data\":6},{\"field\":\"textAlign\",\"label\":\"文本对齐\",\"data\":\"center\"},{\"field\":\"background\",\"label\":\"背景颜色\",\"data\":\"\"}]}]},{\"title\":\"表头\",\"field\":\"tableHeader\",\"show\":true,\"spread\":false,\"items\":[{\"field\":\"fontSize\",\"label\":\"字号\",\"data\":16},{\"field\":\"color\",\"label\":\"颜色\",\"data\":\"RGBA(46,46,46,1)\",\"theme_controled\":true},{\"field\":\"lineHeight\",\"label\":\"行高\",\"data\":24},{\"field\":\"paddingX\",\"label\":\"水平内边距\",\"data\":6},{\"field\":\"paddingY\",\"label\":\"垂直内边距\",\"data\":6},{\"field\":\"fontStyle\",\"label\":\"样式\",\"data\":{\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"textDecoration\":\"none\"}},{\"field\":\"textAlign\",\"label\":\"对齐\",\"data\":\"center\"},{\"field\":\"iswhiteSpace\",\"label\":\"自行换行\",\"data\":false},{\"field\":\"background\",\"label\":\"背景颜色\",\"data\":\"RGBA(250,250,250,1)\",\"theme_controled\":true}]},{\"title\":\"行\",\"field\":\"rows\",\"spread\":false,\"items\":[{\"field\":\"splitLine\",\"label\":\"分割线\",\"show\":{\"field\":\"checked\",\"data\":true},\"items\":[{\"field\":\"border\",\"data\":{\"borderColor\":\"RGBA(238,238,238,1)\",\"borderStyle\":\"solid\",\"borderWidth\":1},\"theme_controled\":true}]},{\"field\":\"oddEven\",\"label\":\"区分奇偶行\",\"show\":{\"field\":\"checked\",\"data\":true},\"items\":[{\"field\":\"oddBackgroundColor\",\"label\":\"奇行背景色\",\"data\":\"TRANSPARENT\",\"theme_controled\":true},{\"field\":\"evenBackgroundColor\",\"label\":\"偶行背景色\",\"data\":\"RGBA(250,250,250,1)\",\"theme_controled\":true}]}]},{\"title\":\"列\",\"field\":\"cols\",\"spread\":false,\"items\":[{\"field\":\"list\",\"data\":{\"cellTextStyle\":{\"checked\":false,\"fontSize\":14,\"color\":\"RGBA(46,46,46,1)\",\"fontStyle\":{\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"textDecoration\":\"normal\"},\"textAlign\":\"center\",\"background\":\"RGBA(250,250,250,1)\"},\"cellOther\":{\"cellWidth\":100,\"contentType\":\"文字\",\"imageWidth\":100}},\"theme_controled\":true}]},{\"title\":\"序号列\",\"field\":\"indexCol\",\"show\":false,\"spread\":false,\"items\":[{\"field\":\"header\",\"label\":\"表头内容\",\"data\":\"序号\"},{\"field\":\"fontSize\",\"label\":\"字号\",\"data\":14},{\"field\":\"color\",\"label\":\"颜色\",\"data\":\"#FFFFFF\"},{\"field\":\"fontStyle\",\"label\":\"样式\",\"data\":{\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"textDecoration\":\"normal\"}},{\"field\":\"colWidth\",\"label\":\"列宽占比\",\"data\":50},{\"field\":\"radius\",\"label\":\"半径占比\",\"data\":60},{\"field\":\"background\",\"label\":\"序号背景\",\"data\":\"#345A8A\"}]},{\"title\":\"字段备注\",\"field\":\"colNote\",\"spread\":false,\"show\":false,\"items\":[{\"field\":\"fontSize\",\"label\":\"字号\",\"data\":14},{\"field\":\"color\",\"label\":\"颜色\",\"data\":\"RGBA(46,46,46,1)\",\"theme_controled\":true},{\"field\":\"lineHeight\",\"label\":\"行高\",\"data\":24},{\"field\":\"paddingX\",\"label\":\"水平内边距\",\"data\":6},{\"field\":\"paddingY\",\"label\":\"垂直内边距\",\"data\":6},{\"field\":\"fontStyle\",\"label\":\"样式\",\"data\":{\"fontWeight\":\"normal\",\"fontStyle\":\"italic\",\"textDecoration\":\"none\"}},{\"field\":\"textAlign\",\"label\":\"对齐\",\"data\":\"center\"},{\"field\":\"background\",\"label\":\"背景颜色\",\"data\":\"RGBA(250,250,250,1)\",\"theme_controled\":true},{\"field\":\"showInReport\",\"label\":\"默认展开备注\",\"data\":true}]},{\"title\":\"标题\",\"field\":\"containerTitle\",\"spread\":false,\"scope\":\"container.title\",\"items\":[{\"field\":\"text\",\"label\":\"标题\",\"data\":\"表格-1\",\"scope\":\"container.title.text\"},{\"field\":\"fontSize\",\"label\":\"字号\",\"data\":16,\"scope\":\"container.title.fontSize\"},{\"field\":\"color\",\"label\":\"文本颜色\",\"data\":\"#000\",\"scope\":\"container.title.color\"},{\"field\":\"fontStyle\",\"label\":\"样式\",\"data\":{\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"textDecoration\":\"normal\"},\"scope\":\"container.title.fontStyle\"},{\"field\":\"distance\",\"label\":\"上下间距\",\"data\":10,\"scope\":\"container.title.distance\"},{\"field\":\"textAlign\",\"label\":\"显示位置\",\"data\":\"left\",\"scope\":\"container.title.textAlign\"},{\"field\":\"hideTitle\",\"label\":\"隐藏标题\",\"scope\":\"container.title.hideTitle\",\"show\":{\"field\":\"show\",\"data\":false},\"items\":[]}]},{\"title\":\"数据更新时间\",\"field\":\"containerUpdateTime\",\"spread\":false,\"show\":false,\"scope\":\"container.updateTime\",\"items\":[{\"field\":\"type\",\"label\":\"\",\"scope\":\"container.updateTime.type\",\"items\":[{\"field\":\"icon\",\"label\":\"时间展示方式\",\"data\":true}]},{\"field\":\"position\",\"label\":\"显示位置\",\"data\":\"follow-title\",\"scope\":\"container.updateTime.position\"}]},{\"title\":\"背景\",\"field\":\"containerBackground\",\"spread\":false,\"show\":true,\"scope\":\"container.background\",\"items\":[{\"field\":\"backgroundColor\",\"label\":\"背景颜色\",\"data\":\"#FFFFFF\",\"scope\":\"container.background.backgroundColor\"}]},{\"title\":\"边框\",\"field\":\"containerBorder\",\"spread\":false,\"show\":false,\"scope\":\"container.border\",\"items\":[{\"field\":\"border\",\"label\":\"\",\"data\":{\"borderColor\":\"\",\"borderStyle\":\"solid\",\"borderWidth\":0},\"scope\":\"container.border.borderStyle\"}]}]",
                            "penetrate": 0,
                            "page_size": 0,
                            "created_on": "2019-03-06T19:03:08",
                            "id": "b2a7c2ed-3fff-11e9-8335-753e026b4c17",
                            "name": "表格-1",
                            "layers": None,
                            "layout_extend": None,
                            "filter_relation": 0,
                            "modified_by": "ycm2",
                            "position": "{\"i\": \"b2a7c2ed-3fff-11e9-8335-753e026b4c17\", \"size_x\": 6, \"row\": 0, \"col\": 0, \"z\": \"auto\", \"size_y\": 6}",
                            "default_value": "",
                            "source": "39ea8f24-da7a-ddde-ee4d-299cb1f25cc9",
                            "style_type": None,
                            "desired_value": 0,
                            "parent_id": "",
                            "chart_type": "chart",
                            "filter_config": None,
                            "display_item": "",
                        }
                    ],
                    "dashboard_jump_relation": [],
                    "dashboard_folders": [
                        {
                            "layout": None,
                            "created_on": "2019-03-06T19:30:47",
                            "cover": "",
                            "build_in": 0,
                            "type": "FOLDER",
                            "border": None,
                            "layout_type": "标准布局",
                            "created_by": "ycm2",
                            "platform": "pc",
                            "biz_code": "eb988758985a4321940338e8c81662dc",
                            "theme": "tech_blue",
                            "id": "39ec64f2-0348-f4e8-d3a6-44030227670f",
                            "create_type": 0,
                            "default_show": 0,
                            "modified_on": "2019-03-06T19:30:52",
                            "level_code": "0035-",
                            "user_group_id": "",
                            "data_report_id": None,
                            "refresh_rate": None,
                            "parent_id": "",
                            "is_multiple_screen": 0,
                            "icon": "",
                            "new_layout_type": 0,
                            "type_access_released": 4,
                            "modified_by": "ycm2",
                            "description": "",
                            "background": None,
                            "status": 0,
                            "grid_padding": None,
                            "share_secret_key": "",
                            "type_selector": 1,
                            "rank": 0,
                            "name": "分发文件夹",
                            "distribute_type": 0,
                            "scale_mode": 0,
                            "is_show_mark_img": 0,
                        }
                    ],
                    "dashboard_chart_selector_field": [],
                    "dashboard_chart_filter_relation": [],
                    "dashboard_jump_config": [],
                    "dashboard_released_snapshot_chart": [
                        {
                            "filters": "[]",
                            "chart_params": "[]",
                            "nums": "[]",
                            "dashboard_id": "39ec64d8-7b52-7f98-d6eb-c09d94a8ebd1",
                            "created_by": "ycm2",
                            "data_modified_on": None,
                            "chart_linkage": "[]",
                            "source_type": "API",
                            "content": None,
                            "level_code": None,
                            "chart_params_jump": "[]",
                            "label_id": None,
                            "penetrates": "[]",
                            "layers": "[]",
                            "filter_relation": 0,
                            "dims": "[{\"sort\": \"\", \"field_group\": \"\\u7ef4\\u5ea6\", \"formula_mode\": \"\", \"dashboard_chart_id\": \"b2a7c2ed-3fff-11e9-8335-753e026b4c17\", \"content\": null, \"origin_col_name\": \"col1\", \"col_name\": \"COL_11467822118\", \"chart_params_jump\": [], \"data_type\": \"\\u5b57\\u7b26\\u4e32\", \"rank\": 0, \"dataset_id\": \"39ea8f24-da7a-ddde-ee4d-299cb1f25cc9\", \"alias\": \"col1\", \"note\": null, \"alias_name\": \"col1\", \"visible\": 1, \"dim\": \"39eaf078-8bd0-5211-28d8-a7d55b1e0786\", \"dashboard_jump_config\": null}, {\"sort\": \"\", \"field_group\": \"\\u7ef4\\u5ea6\", \"formula_mode\": \"\", \"dashboard_chart_id\": \"b2a7c2ed-3fff-11e9-8335-753e026b4c17\", \"content\": null, \"origin_col_name\": \"col2\", \"col_name\": \"COL_11467887655\", \"chart_params_jump\": [], \"data_type\": \"\\u5b57\\u7b26\\u4e32\", \"rank\": 1, \"dataset_id\": \"39ea8f24-da7a-ddde-ee4d-299cb1f25cc9\", \"alias\": \"col2\", \"note\": null, \"alias_name\": \"col2\", \"visible\": 1, \"dim\": \"39ea8f25-4157-a6ff-c84a-4050ea03495e\", \"dashboard_jump_config\": null}]",
                            "label_org_id": None,
                            "position": "{\"i\": \"b2a7c2ed-3fff-11e9-8335-753e026b4c17\", \"size_x\": 6, \"row\": 0, \"col\": 0, \"z\": \"auto\", \"size_y\": 6}",
                            "source": "39ea8f24-da7a-ddde-ee4d-299cb1f25cc9",
                            "parent_id": "",
                            "source_content": "{\"data_source_id\": \"39e50cb4-829c-322d-63ee-6c8f814a72ae\", \"sql\": \"select name,b,c from a_copy\", \"tmp_table_name\": \"dataset_tmp_5c2056d5c97e4\", \"params\": [], \"create_table_sql\": \"create table if not exists dataset_tmp_5c2056d5c97e4 (`COL_11467822118` varchar(500) ,`COL_11467887655` varchar(500) ,`COL_11467953192` varchar(500) ,`COL_11468018729` varchar(500) ,`COL_11468084266` varchar(500) ,`COL_11468149803` varchar(500) ,`COL_11468215340` varchar(500) ,`COL_11468280877` varchar(500) ,`COL_11468346414` datetime ,`COL_11809002582` datetime ,`COL_11809068119` datetime ,`COL_11809133656` datetime ,`COL_11809199193` datetime ,`COL_11809264730` decimal(30,10) ,`COL_11809330267` varchar(500) ,`COL_11809395804` decimal(30,10) ,`COL_11809461341` varchar(500) ,`COL_11809526878` decimal(30,10) ,`COL_11809592415` decimal(30,10) ,`COL_11809133655` decimal(30,10) ,`COL_11809199192` decimal(30,10) ,`COL_11809264729` decimal(30,10) ,`COL_11809330266` decimal(30,10) ,`COL_11809395803` decimal(30,10) ,`COL_11809461340` decimal(30,10) ,`COL_11809526877` decimal(30,10) ,`COL_11809592414` decimal(30,10) ,`COL_10950349711` varchar(20) ,`COL_10950415248` varchar(12) ,`COL_10950480785` varchar(12) ,`COL_10950546322` varchar(8) ,`COL_10950611859` varchar(52) ,`COL_10950677396` varchar(60) ,`COL_10950742933` varchar(24) ,`COL_10950808470` varchar(16) ,`COL_10950874007` datetime ,`COL_11281634239` datetime ,`COL_11281699776` datetime ,`COL_11281765313` datetime ,`COL_11281830850` datetime ,`COL_11281896387` decimal(30,9) ,`COL_11281961924` varchar(16) ,`COL_11282027461` decimal(30,0) ,`COL_11282092998` varchar(172) ,`COL_11282158535` decimal(30,0) ,`COL_11282224072` decimal(30,5) ,`COL_11281765312` decimal(30,6) ,`COL_11281830849` decimal(30,4) ,`COL_11281896386` decimal(30,6) ,`COL_11281961923` decimal(30,3) ,`COL_11282027460` decimal(30,3) ,`COL_11282092997` decimal(30,4) ,`COL_11282158534` decimal(30,3) ,`COL_11282224071` decimal(30,0) )\", \"count\": 367}",
                            "filter_config": None,
                            "layout": None,
                            "name": "表格-1",
                            "chart_code": "table",
                            "data_logic_type_code": None,
                            "sort_method": "",
                            "chart_filter": "[]",
                            "penetrate_filter_relation": "[]",
                            "refresh_rate": "",
                            "column_order": None,
                            "jump": "[]",
                            "display_item": "",
                            "config": "[{\"title\":\"全局样式\",\"field\":\"global\",\"spread\":false,\"items\":[{\"field\":\"scroll\",\"label\":\"滚动设置\",\"show\":{\"field\":\"checked\",\"data\":false},\"items\":[{\"field\":\"interVal\",\"label\":\"间隔时间\",\"data\":5},{\"field\":\"ln\",\"label\":\"锁定行数\",\"data\":0},{\"field\":\"scrollMode\",\"data\":{\"mode\":\"page\",\"rows\":1}}]},{\"field\":\"cell\",\"label\":\"单元格\",\"items\":[{\"field\":\"fontSize\",\"label\":\"字号\",\"data\":14},{\"field\":\"color\",\"label\":\"颜色\",\"data\":\"RGBA(46,46,46,1)\",\"theme_controled\":true},{\"field\":\"lineHeight\",\"label\":\"行高\",\"data\":24},{\"field\":\"paddingX\",\"label\":\"水平内边距\",\"data\":6},{\"field\":\"paddingY\",\"label\":\"垂直内边距\",\"data\":6},{\"field\":\"textAlign\",\"label\":\"文本对齐\",\"data\":\"center\"},{\"field\":\"iswhiteSpace\",\"label\":\"自行换行\",\"data\":false},{\"field\":\"rowspan\",\"label\":\"合并同类单元格\",\"data\":false}]},{\"field\":\"qianN\",\"label\":\"前N行设置\",\"show\":{\"field\":\"checked\",\"data\":false},\"items\":[{\"field\":\"end\",\"label\":\"行数\",\"data\":3},{\"field\":\"fontSize\",\"label\":\"字号\",\"data\":14},{\"field\":\"color\",\"label\":\"颜色\",\"data\":\"#FFFFFF\"},{\"field\":\"lineHeight\",\"label\":\"行高\",\"data\":24},{\"field\":\"paddingX\",\"label\":\"水平内边距\",\"data\":6},{\"field\":\"paddingY\",\"label\":\"垂直内边距\",\"data\":6},{\"field\":\"textAlign\",\"label\":\"文本对齐\",\"data\":\"center\"},{\"field\":\"background\",\"label\":\"背景颜色\",\"data\":\"\"}]}]},{\"title\":\"表头\",\"field\":\"tableHeader\",\"show\":true,\"spread\":false,\"items\":[{\"field\":\"fontSize\",\"label\":\"字号\",\"data\":16},{\"field\":\"color\",\"label\":\"颜色\",\"data\":\"RGBA(46,46,46,1)\",\"theme_controled\":true},{\"field\":\"lineHeight\",\"label\":\"行高\",\"data\":24},{\"field\":\"paddingX\",\"label\":\"水平内边距\",\"data\":6},{\"field\":\"paddingY\",\"label\":\"垂直内边距\",\"data\":6},{\"field\":\"fontStyle\",\"label\":\"样式\",\"data\":{\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"textDecoration\":\"none\"}},{\"field\":\"textAlign\",\"label\":\"对齐\",\"data\":\"center\"},{\"field\":\"iswhiteSpace\",\"label\":\"自行换行\",\"data\":false},{\"field\":\"background\",\"label\":\"背景颜色\",\"data\":\"RGBA(250,250,250,1)\",\"theme_controled\":true}]},{\"title\":\"行\",\"field\":\"rows\",\"spread\":false,\"items\":[{\"field\":\"splitLine\",\"label\":\"分割线\",\"show\":{\"field\":\"checked\",\"data\":true},\"items\":[{\"field\":\"border\",\"data\":{\"borderColor\":\"RGBA(238,238,238,1)\",\"borderStyle\":\"solid\",\"borderWidth\":1},\"theme_controled\":true}]},{\"field\":\"oddEven\",\"label\":\"区分奇偶行\",\"show\":{\"field\":\"checked\",\"data\":true},\"items\":[{\"field\":\"oddBackgroundColor\",\"label\":\"奇行背景色\",\"data\":\"TRANSPARENT\",\"theme_controled\":true},{\"field\":\"evenBackgroundColor\",\"label\":\"偶行背景色\",\"data\":\"RGBA(250,250,250,1)\",\"theme_controled\":true}]}]},{\"title\":\"列\",\"field\":\"cols\",\"spread\":false,\"items\":[{\"field\":\"list\",\"data\":{\"cellTextStyle\":{\"checked\":false,\"fontSize\":14,\"color\":\"RGBA(46,46,46,1)\",\"fontStyle\":{\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"textDecoration\":\"normal\"},\"textAlign\":\"center\",\"background\":\"RGBA(250,250,250,1)\"},\"cellOther\":{\"cellWidth\":100,\"contentType\":\"文字\",\"imageWidth\":100}},\"theme_controled\":true}]},{\"title\":\"序号列\",\"field\":\"indexCol\",\"show\":false,\"spread\":false,\"items\":[{\"field\":\"header\",\"label\":\"表头内容\",\"data\":\"序号\"},{\"field\":\"fontSize\",\"label\":\"字号\",\"data\":14},{\"field\":\"color\",\"label\":\"颜色\",\"data\":\"#FFFFFF\"},{\"field\":\"fontStyle\",\"label\":\"样式\",\"data\":{\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"textDecoration\":\"normal\"}},{\"field\":\"colWidth\",\"label\":\"列宽占比\",\"data\":50},{\"field\":\"radius\",\"label\":\"半径占比\",\"data\":60},{\"field\":\"background\",\"label\":\"序号背景\",\"data\":\"#345A8A\"}]},{\"title\":\"字段备注\",\"field\":\"colNote\",\"spread\":false,\"show\":false,\"items\":[{\"field\":\"fontSize\",\"label\":\"字号\",\"data\":14},{\"field\":\"color\",\"label\":\"颜色\",\"data\":\"RGBA(46,46,46,1)\",\"theme_controled\":true},{\"field\":\"lineHeight\",\"label\":\"行高\",\"data\":24},{\"field\":\"paddingX\",\"label\":\"水平内边距\",\"data\":6},{\"field\":\"paddingY\",\"label\":\"垂直内边距\",\"data\":6},{\"field\":\"fontStyle\",\"label\":\"样式\",\"data\":{\"fontWeight\":\"normal\",\"fontStyle\":\"italic\",\"textDecoration\":\"none\"}},{\"field\":\"textAlign\",\"label\":\"对齐\",\"data\":\"center\"},{\"field\":\"background\",\"label\":\"背景颜色\",\"data\":\"RGBA(250,250,250,1)\",\"theme_controled\":true},{\"field\":\"showInReport\",\"label\":\"默认展开备注\",\"data\":true}]},{\"title\":\"标题\",\"field\":\"containerTitle\",\"spread\":false,\"scope\":\"container.title\",\"items\":[{\"field\":\"text\",\"label\":\"标题\",\"data\":\"表格-1\",\"scope\":\"container.title.text\"},{\"field\":\"fontSize\",\"label\":\"字号\",\"data\":16,\"scope\":\"container.title.fontSize\"},{\"field\":\"color\",\"label\":\"文本颜色\",\"data\":\"#000\",\"scope\":\"container.title.color\"},{\"field\":\"fontStyle\",\"label\":\"样式\",\"data\":{\"fontWeight\":\"normal\",\"fontStyle\":\"normal\",\"textDecoration\":\"normal\"},\"scope\":\"container.title.fontStyle\"},{\"field\":\"distance\",\"label\":\"上下间距\",\"data\":10,\"scope\":\"container.title.distance\"},{\"field\":\"textAlign\",\"label\":\"显示位置\",\"data\":\"left\",\"scope\":\"container.title.textAlign\"},{\"field\":\"hideTitle\",\"label\":\"隐藏标题\",\"scope\":\"container.title.hideTitle\",\"show\":{\"field\":\"show\",\"data\":false},\"items\":[]}]},{\"title\":\"数据更新时间\",\"field\":\"containerUpdateTime\",\"spread\":false,\"show\":false,\"scope\":\"container.updateTime\",\"items\":[{\"field\":\"type\",\"label\":\"\",\"scope\":\"container.updateTime.type\",\"items\":[{\"field\":\"icon\",\"label\":\"时间展示方式\",\"data\":true}]},{\"field\":\"position\",\"label\":\"显示位置\",\"data\":\"follow-title\",\"scope\":\"container.updateTime.position\"}]},{\"title\":\"背景\",\"field\":\"containerBackground\",\"spread\":false,\"show\":true,\"scope\":\"container.background\",\"items\":[{\"field\":\"backgroundColor\",\"label\":\"背景颜色\",\"data\":\"#FFFFFF\",\"scope\":\"container.background.backgroundColor\"}]},{\"title\":\"边框\",\"field\":\"containerBorder\",\"spread\":false,\"show\":false,\"scope\":\"container.border\",\"items\":[{\"field\":\"border\",\"label\":\"\",\"data\":{\"borderColor\":\"\",\"borderStyle\":\"solid\",\"borderWidth\":0},\"scope\":\"container.border.borderStyle\"}]}]",
                            "penetrate": 0,
                            "page_size": 0,
                            "comparisons": "[]",
                            "increment_id": 3254,
                            "percentage": None,
                            "component_filter": "[]",
                            "desires": "[]",
                            "layout_extend": None,
                            "id": "b2a7c2ed-3fff-11e9-8335-753e026b4c17",
                            "chart_type": "chart",
                            "penetrate_relation": "[]",
                            "marklines": "[]",
                            "zaxis": "[]",
                            "default_value": "",
                            "label_tmpl_id": None,
                            "snapshot_id": "39ec64d8-7b52-7f98-d6eb-c09d94a8ebd1",
                            "label_name": None,
                            "chart_default_value": "[]",
                            "modified_by": "ycm2",
                        }
                    ],
                    "dashboard_chart_params_jump": [],
                    "dashboard_chart_selector": [],
                    "dashboard_filter_chart": [],
                    "dashboard_linkage_relation": [],
                    "dashboard_chart_dim": [
                        {
                            "dashboard_chart_id": "b2a7c2ed-3fff-11e9-8335-753e026b4c17",
                            "dim": "39eaf078-8bd0-5211-28d8-a7d55b1e0786",
                            "sort": "",
                            "created_by": "ycm2",
                            "note": None,
                            "id": "bbecf5fc-3fff-11e9-8335-753e026b4c17",
                            "dashboard_jump_config": None,
                            "content": None,
                            "formula_mode": "",
                            "rank": 0,
                            "alias": "col1",
                            "modified_by": "ycm2",
                        },
                        {
                            "dashboard_chart_id": "b2a7c2ed-3fff-11e9-8335-753e026b4c17",
                            "dim": "39ea8f25-4157-a6ff-c84a-4050ea03495e",
                            "sort": "",
                            "created_by": "ycm2",
                            "note": None,
                            "id": "bc5358ec-3fff-11e9-8335-753e026b4c17",
                            "dashboard_jump_config": None,
                            "content": None,
                            "formula_mode": "",
                            "rank": 1,
                            "alias": "col2",
                            "modified_by": "ycm2",
                        },
                    ],
                    "dashboard_released_snapshot_dashboard": [
                        {
                            "layout": "{\"lattice\": 10, \"mode\": \"grid\", \"ratio\": \"16:9\", \"toolbar\": \"show\", \"platform\": \"pc\", \"height\": 1080, \"width\": 1920}",
                            "cover": "",
                            "type": "FILE",
                            "created_by": "ycm2",
                            "biz_code": "5e1c6653437043bdafc8aafabd349713",
                            "theme": "colorful_white",
                            "id": "39ec64d8-7b52-7f98-d6eb-c09d94a8ebd1",
                            "create_type": 1,
                            "is_multiple_screen": 0,
                            "dashboard_filters": "[]",
                            "level_code": "9000-0985-0284-0001-",
                            "user_group_id": "",
                            "refresh_rate": "",
                            "increment_id": 438,
                            "platform": "pc",
                            "new_layout_type": 1,
                            "selectors": "{}",
                            "type_access_released": 4,
                            "modified_by": "ycm2",
                            "data_type": 0,
                            "background": "{\"color\": \"#EBEDF2\", \"show\": true, \"size\": \"stretch\", \"image\": \"\"}",
                            "status": 1,
                            "grid_padding": "{\"chart_background\":\"#FFFFFF\",\"chart_margin\":[10,10],\"chart_padding\":[15,15,15,15],\"container_padding\":[10,10]}",
                            "share_secret_key": "",
                            "snapshot_id": "39ec64d8-7b52-7f98-d6eb-c09d94a8ebd1",
                            "rank": 0,
                            "name": "哈哈-分发用",
                            "scale_mode": 0,
                            "is_show_mark_img": 0,
                        },
                        {
                            "layout": "{\"lattice\": 10, \"mode\": \"grid\", \"ratio\": \"16:9\", \"toolbar\": \"show\", \"platform\": \"pc\", \"height\": 1080, \"width\": 1920}",
                            "cover": "",
                            "type": "FILE",
                            "created_by": "ycm2",
                            "biz_code": "5e1c6653437043bdafc8aafabd349713",
                            "theme": "colorful_white",
                            "id": "39ec64d8-7b52-7f98-d6eb-c09d94a8ebd1",
                            "create_type": 1,
                            "is_multiple_screen": 0,
                            "dashboard_filters": "[]",
                            "level_code": "9000-0985-0284-0001-",
                            "user_group_id": "",
                            "refresh_rate": "",
                            "increment_id": 439,
                            "platform": "pc",
                            "new_layout_type": 1,
                            "selectors": "{}",
                            "type_access_released": 4,
                            "modified_by": "ycm2",
                            "data_type": 1,
                            "background": "{\"color\": \"#EBEDF2\", \"show\": true, \"size\": \"stretch\", \"image\": \"\"}",
                            "status": 1,
                            "grid_padding": "{\"chart_background\":\"#FFFFFF\",\"chart_margin\":[10,10],\"chart_padding\":[15,15,15,15],\"container_padding\":[10,10]}",
                            "share_secret_key": "",
                            "snapshot_id": "39ec64d8-7b52-7f98-d6eb-c09d94a8ebd1",
                            "rank": 0,
                            "name": "哈哈-分发用",
                            "scale_mode": 0,
                            "is_show_mark_img": 0,
                        },
                    ],
                    "dashboard_dataset_field_relation": [],
                    "dashboard_chart_desire": [],
                    "dashboard_chart_penetrate_relation": [],
                    "dashboard_filter_chart_relation": [],
                    "dashboard_chart_comparison": [],
                    "dashboard_chart_markline": [],
                    "dashboard_chart_params": [],
                    "dashboard_component_filter": [],
                    "dashboard_linkage": [],
                    "dashboard_chart_num": [],
                    "dashboard_chart_layers": [],
                    "dashboard": [
                        {
                            "layout": "{\"lattice\":10,\"mode\":\"grid\",\"ratio\":\"16:9\",\"toolbar\":\"show\",\"platform\":\"pc\",\"height\":1080,\"width\":1920}",
                            "created_on": "2019-03-06T19:02:54",
                            "cover": "",
                            "build_in": 0,
                            "type": "FILE",
                            "border": None,
                            "layout_type": "标准布局",
                            "created_by": "ycm2",
                            "platform": "pc",
                            "biz_code": "5e1c6653437043bdafc8aafabd349713",
                            "theme": "colorful_white",
                            "id": "39ec64d8-7b52-7f98-d6eb-c09d94a8ebd1",
                            "create_type": 1,
                            "default_show": 0,
                            "modified_on": "2019-03-06T19:30:55",
                            "level_code": "0035-0001-",
                            "user_group_id": "",
                            "data_report_id": None,
                            "refresh_rate": None,
                            "parent_id": "39ec64f2-0348-f4e8-d3a6-44030227670f",
                            "is_multiple_screen": 0,
                            "icon": "",
                            "new_layout_type": 1,
                            "is_show_mark_img": 0,
                            "type_access_released": 4,
                            "modified_by": "ycm2",
                            "description": "",
                            "background": "{\"color\":\"#EBEDF2\",\"show\":true,\"size\":\"stretch\",\"image\":\"\"}",
                            "status": 1,
                            "grid_padding": "{\"chart_background\":\"#FFFFFF\",\"chart_margin\":[10,10],\"chart_padding\":[15,15,15,15],\"container_padding\":[10,10]}",
                            "share_secret_key": "",
                            "type_selector": 1,
                            "rank": None,
                            "name": "哈哈-分发用",
                            "distribute_type": None,
                            "scale_mode": 0,
                        }
                    ],
                    "dashboard_filter_relation": [],
                }
            }
        dashboards2 = {'39ee091e-a677-288e-266d-e9a337745405': {'dashboard': [{'id': '39ee091e-a677-288e-266d-e9a337745405', 'theme': 'colorful_white', 'data_report_id': None, 'name': '单报告导入', 'type': 'FILE', 'parent_id': '', 'level_code': '1963-', 'icon': '', 'platform': 'pc', 'is_multiple_screen': 0, 'status': 1, 'user_group_id': '', 'default_show': 0, 'cover': '', 'description': '', 'layout_type': '自由布局', 'share_secret_key': '', 'layout': '{"mode":"free","platform":"pc","ratio":"16:9","width":1920,"height":1080,"lattice":10,"toolbar":"show","screenHeader":"show"}', 'scale_mode': 0, 'background': '{"show":true,"color":"RGBA(255,255,255,1)","image":"","size":"stretch"}', 'rank': None, 'build_in': 0, 'biz_code': '8e84a29ebb804df0957d35ed5a60da6e', 'border': None, 'type_access_released': 4, 'type_selector': 1, 'refresh_rate': None, 'created_on': '2019-05-27T09:40:03', 'created_by': 'data999', 'modified_on': '2019-05-27T09:40:22', 'modified_by': 'data999', 'grid_padding': '{"container_padding":[10,10],"chart_margin":[10,10],"chart_padding":[15,15,15,15],"chart_background":"#FFFFFF"}', 'create_type': 1, 'new_layout_type': 0, 'distribute_type': None, 'is_show_mark_img': 0}], 'dashboard_chart': [{'id': '5ee5ad56-8020-11e9-a07d-2f96b27164e2', 'dashboard_id': '39ee091e-a677-288e-266d-e9a337745405', 'name': '表格-1', 'chart_code': 'table', 'filter_config': None, 'page_size': 0, 'data_logic_type_code': 'default', 'chart_type': 'chart', 'content': None, 'source': '39edf4ba-d42f-9373-5f16-9befb0e2b6b9', 'created_on': '2019-05-27T09:40:12', 'created_by': 'data999', 'modified_on': '2019-05-27T09:40:18', 'modified_by': 'data999', 'data_modified_on': None, 'position': '{"i": "5ee5ad56-8020-11e9-a07d-2f96b27164e2", "col": 610, "row": 280, "size_x": 480, "size_y": 270, "z": 1501}', 'sort_method': None, 'penetrate': 0, 'parent_id': '', 'level_code': '', 'refresh_rate': '', 'display_item': '', 'desired_value': 0, 'percentage': None, 'style_type': None, 'default_value': '', 'layout': None, 'layout_extend': None, 'config': '', 'filter_relation': 0, 'layers': None, 'column_order': None, 'enable_subtotal': 0, 'enable_summary': 0}], 'dashboard_folders': [], 'dashboard_chart_dim': [{'dashboard_chart_id': '5ee5ad56-8020-11e9-a07d-2f96b27164e2', 'dim': '39edf4bd-0666-ccf5-e80c-839b6840c135', 'alias': 'pid', 'content': '', 'formula_mode': '', 'created_by': 'data999', 'modified_by': 'data999', 'rank': 0, 'sort': '', 'id': '63a901c0-8020-11e9-a07d-2f96b27164e2', 'dashboard_jump_config': None, 'note': None, 'is_subtotal_cate': 0}], 'dashboard_chart_num': [], 'dashboard_chart_filter': [], 'dashboard_chart_filter_relation': [], 'dashboard_chart_layers': [], 'dashboard_chart_markline': [], 'dashboard_chart_desire': [], 'dashboard_chart_params': [], 'dashboard_chart_params_jump': [], 'dashboard_chart_comparison': [], 'dashboard_chart_penetrate_relation': [], 'dashboard_chart_field_sort': [], 'dashboard_chart_selector': [], 'dashboard_chart_selector_field': [], 'dashboard_filter': [], 'dashboard_filter_relation': [], 'dashboard_dataset_field_relation': [], 'dashboard_released_snapshot_chart': [{'increment_id': 71628, 'id': '5ee5ad56-8020-11e9-a07d-2f96b27164e2', 'snapshot_id': '39ee091e-a677-288e-266d-e9a337745405', 'dashboard_id': '39ee091e-a677-288e-266d-e9a337745405', 'name': '表格-1', 'chart_code': 'table', 'chart_type': 'chart', 'content': None, 'source': '39edf4ba-d42f-9373-5f16-9befb0e2b6b9', 'level_code': '', 'parent_id': '', 'display_item': '', 'refresh_rate': '', 'penetrate': 0, 'sort_method': '', 'position': '{"i": "5ee5ad56-8020-11e9-a07d-2f96b27164e2", "col": 610, "row": 280, "size_x": 480, "size_y": 270, "z": 1501}', 'percentage': None, 'default_value': '', 'layout': None, 'layout_extend': None, 'config': '', 'filter_config': None, 'page_size': 0, 'data_modified_on': None, 'dims': '[{"dashboard_chart_id": "5ee5ad56-8020-11e9-a07d-2f96b27164e2", "dim": "39edf4bd-0666-ccf5-e80c-839b6840c135", "dashboard_jump_config": None, "formula_mode": "", "alias": "pid", "content": "", "rank": 0, "note": None, "sort": "", "is_subtotal_cate": 0, "dataset_field_id": "39edf4bd-0666-ccf5-e80c-839b6840c135", "alias_name": "pid", "field_group": "\\u7ef4\\u5ea6", "dataset_id": "39edf4ba-d42f-9373-5f16-9befb0e2b6b9", "data_type": "\\u5b57\\u7b26\\u4e32", "col_name": "PID_3847424903", "expression": None, "type": "\\u666e\\u901a", "visible": 1, "chart_params_jump": []}]', 'nums': '[]', 'comparisons': '[]', 'zaxis': '[]', 'desires': '[]', 'filters': '[]', 'filter_relation': 0, 'marklines': '[]', 'penetrates': '[]', 'chart_params': '[]', 'jump': '[]', 'data_logic_type_code': 'default', 'layers': '[]', 'column_order': None, 'source_type': 'SQL', 'source_content': '{"data_source_id": "39ed9818-2895-4b23-02b4-78728c2c6a6a", "count": 10837, "sql": "select id,pid,cpid,one_class,two_class,name,type,type_code,biz_type,address,place,distance,tel,province_code,province_name,city_code,city_name,ad_code,ad_name,entr_location,exit_location,navi_poiid,grid_code,alias,business_area,parking_type,indoor_map,floor,true_floor,tags,tag,task_id,address_longitude,address_latitude,address_country,address_province,address_city,address_district,address_street,address_level from shop_info", "create_table_sql": "create table if not exists dataset_tmp_5ce607ad11990 (`ID_3652782871` int ,`PID_3847424903` varchar(90) ,`CPID_4045999082` varchar(150) ,`ONECLASS_5150084609` varchar(150) ,`TWOCLASS_5162339865` varchar(150) ,`NAME_4046523371` varchar(360) ,`TYPE_4053208076` varchar(360) ,`TYPECODE_5162405382` varchar(180) ,`BIZTYPE_4922346928` varchar(150) ,`ADDRESS_4684516656` text ,`PLACE_4254010447` varchar(150) ,`DISTANCE_4918676885` varchar(60) ,`TEL_3848211343` varchar(300) ,`PROVINCECODE_6177099690` int ,`PROVINCENAME_6178410416` varchar(150) ,`CITYCODE_5153689085` int ,`CITYNAME_5154999811` varchar(300) ,`ADCODE_4678028553` int ,`ADNAME_4679339279` varchar(300) ,`ENTRLOCATION_6169300923` varchar(150) ,`EXITLOCATION_6170546108` varchar(150) ,`NAVIPOIID_5396893292` varchar(300) ,`GRIDCODE_5147463146` varchar(150) ,`ALIAS_4251323476` varchar(150) ,`BUSINESSAREA_6172905390` varchar(150) ,`PARKINGTYPE_5907156823` varchar(150) ,`INDOORMAP_5397876338` tinyint(1) ,`FLOOR_4255911020` varchar(60) ,`TRUEFLOOR_5407313547` varchar(60) ,`TAGS_4048227321` text ,`TAG_3847359366` int ,`TASKID_4691660073` int ,`ADDRESSLONGITUDE_7279677786` double ,`ADDRESSLATITUDE_6986076395` double ,`ADDRESSCOUNTRY_6706237603` varchar(762) ,`ADDRESSPROVINCE_6993875189` varchar(762) ,`ADDRESSCITY_5891624776` varchar(762) ,`ADDRESSDISTRICT_6986731765` varchar(762) ,`ADDRESSSTREET_6430658598` varchar(762) ,`ADDRESSLEVEL_6154751911` varchar(762) )", "tmp_table_name": "dataset_tmp_5ce607ad11990", "sql_table_names": ["shop_info"]}', 'label_id': None, 'label_tmpl_id': None, 'label_org_id': None, 'label_name': None, 'created_by': 'data999', 'modified_by': 'data999', 'component_filter': '[]', 'chart_params_jump': '[]', 'penetrate_relation': '[]', 'penetrate_filter_relation': '[]', 'chart_linkage': '[]', 'chart_filter': '[]', 'chart_default_value': '[]', 'var_relations': '[]', 'chart_vars': '[]', 'field_sorts': '[]', 'enable_subtotal': 0, 'enable_summary': 0}], 'dashboard_released_snapshot_dashboard': [{'increment_id': 13876, 'id': '39ee091e-a677-288e-266d-e9a337745405', 'snapshot_id': '39ee091e-a677-288e-266d-e9a337745405', 'data_type': 0, 'name': '单报告导入', 'type': 'FILE', 'level_code': '1963-', 'platform': 'pc', 'is_multiple_screen': 0, 'status': 1, 'user_group_id': '', 'cover': '', 'selectors': '{}', 'dashboard_filters': '[]', 'share_secret_key': '', 'layout': '{"mode": "free", "platform": "pc", "ratio": "16:9", "width": 1920, "height": 1080, "lattice": 10, "toolbar": "show", "screenHeader": "show"}', 'scale_mode': 0, 'background': '{"show": true, "color": "RGBA(255,255,255,1)", "image": "", "size": "stretch"}', 'biz_code': '8e84a29ebb804df0957d35ed5a60da6e', 'theme': 'colorful_white', 'type_access_released': 4, 'refresh_rate': '', 'created_by': 'data999', 'modified_by': 'data999', 'rank': 0, 'grid_padding': '{"container_padding":[10,10],"chart_margin":[10,10],"chart_padding":[15,15,15,15],"chart_background":"#FFFFFF"}', 'new_layout_type': 0, 'create_type': 1, 'is_show_mark_img': 0}, {'increment_id': 13877, 'id': '39ee091e-a677-288e-266d-e9a337745405', 'snapshot_id': '39ee091e-a677-288e-266d-e9a337745405', 'data_type': 1, 'name': '单报告导入', 'type': 'FILE', 'level_code': '1963-', 'platform': 'pc', 'is_multiple_screen': 0, 'status': 1, 'user_group_id': '', 'cover': '', 'selectors': '{}', 'dashboard_filters': '[]', 'share_secret_key': '', 'layout': '{"mode": "free", "platform": "pc", "ratio": "16:9", "width": 1920, "height": 1080, "lattice": 10, "toolbar": "show", "screenHeader": "show"}', 'scale_mode': 0, 'background': '{"show": true, "color": "RGBA(255,255,255,1)", "image": "", "size": "stretch"}', 'biz_code': '8e84a29ebb804df0957d35ed5a60da6e', 'theme': 'colorful_white', 'type_access_released': 4, 'refresh_rate': '', 'created_by': 'data999', 'modified_by': 'data999', 'rank': 0, 'grid_padding': '{"container_padding":[10,10],"chart_margin":[10,10],"chart_padding":[15,15,15,15],"chart_background":"#FFFFFF"}', 'new_layout_type': 0, 'create_type': 1, 'is_show_mark_img': 0}], 'screen_dashboard': [{'id': '39ee091e-f350-1c68-e62c-33ddaf8abf10', 'dashboard_id': '39ee091e-a677-288e-266d-e9a337745405', 'screen_id': '39ee091e-a677-288e-266d-e9a337745405', 'rank': 1, 'type': 1, 'created_by': 'data999', 'modified_by': 'data999'}], 'dashboard_filter_chart': [], 'dashboard_filter_chart_relation': [], 'dashboard_linkage': [], 'dashboard_linkage_relation': [], 'dashboard_filter_chart_default_values': [], 'dashboard_extra': [{'dashboard_id': '39ee091e-a677-288e-266d-e9a337745405', 'edit_on': None, 'released_on': '2019-05-27T09:40:23', 'created_by': 'data999', 'modified_by': 'data999'}], 'dashboard_jump_config': [], 'dashboard_jump_relation': [], 'dashboard_vars_jump_relation': [], 'dashboard_dataset_vars_relation': [], 'dashboard_component_filter': [], 'dashboard_component_filter_field_relation': []}, '39ee095a-4401-45d4-573d-069685727335': {'dashboard': [{'id': '39ee095a-4401-45d4-573d-069685727335', 'theme': 'colorful_white', 'data_report_id': None, 'name': '导入报告3', 'type': 'FILE', 'parent_id': '39ee0959-d51d-a800-6092-9e798e3541a9', 'level_code': '1965-0001-', 'icon': '', 'platform': 'pc', 'is_multiple_screen': 0, 'status': 1, 'user_group_id': '', 'default_show': 0, 'cover': '', 'description': '', 'layout_type': '自由布局', 'share_secret_key': '', 'layout': '{"mode":"free","platform":"pc","ratio":"16:9","width":1920,"height":1080,"lattice":10,"toolbar":"show","screenHeader":"show"}', 'scale_mode': 0, 'background': '{"show":true,"color":"RGBA(255,255,255,1)","image":"","size":"stretch"}', 'rank': None, 'build_in': 0, 'biz_code': 'cc7b9aad6e5846f8a20c9c6030afa486', 'border': None, 'type_access_released': 4, 'type_selector': 1, 'refresh_rate': None, 'created_on': '2019-05-27T10:45:10', 'created_by': 'data999', 'modified_on': '2019-05-27T10:45:37', 'modified_by': 'data999', 'grid_padding': '{"container_padding":[10,10],"chart_margin":[10,10],"chart_padding":[15,15,15,15],"chart_background":"#FFFFFF"}', 'create_type': 1, 'new_layout_type': 0, 'distribute_type': None, 'is_show_mark_img': 0}], 'dashboard_chart': [{'id': '7474d8e6-8029-11e9-9f73-af97cb348c3e', 'dashboard_id': '39ee095a-4401-45d4-573d-069685727335', 'name': '表格-1', 'chart_code': 'table', 'filter_config': None, 'page_size': 0, 'data_logic_type_code': 'default', 'chart_type': 'chart', 'content': None, 'source': '39eda8d8-efbb-1ee4-3ad7-16f50982b56d', 'created_on': '2019-05-27T10:45:15', 'created_by': 'data999', 'modified_on': '2019-05-27T10:45:30', 'modified_by': 'data999', 'data_modified_on': None, 'position': '{"i": "7474d8e6-8029-11e9-9f73-af97cb348c3e", "col": 610, "row": 300, "size_x": 480, "size_y": 270, "z": 1501}', 'sort_method': None, 'penetrate': 0, 'parent_id': '', 'level_code': '', 'refresh_rate': '', 'display_item': '', 'desired_value': 0, 'percentage': None, 'style_type': None, 'default_value': '', 'layout': None, 'layout_extend': None, 'config': '', 'filter_relation': 0, 'layers': None, 'column_order': None, 'enable_subtotal': 0, 'enable_summary': 0}], 'dashboard_folders': [{'id': '39ee0959-d51d-a800-6092-9e798e3541a9', 'theme': 'tech_blue', 'data_report_id': None, 'name': '平铺目录', 'type': 'FOLDER', 'parent_id': '', 'level_code': '1965-', 'icon': '', 'platform': 'pc', 'is_multiple_screen': 0, 'status': 0, 'user_group_id': '', 'default_show': 0, 'cover': '', 'description': '', 'layout_type': '标准布局', 'share_secret_key': '', 'layout': None, 'scale_mode': 0, 'background': None, 'rank': 0, 'build_in': 0, 'biz_code': '3da3c2a87a4a416687e36d203f7b122a', 'border': None, 'type_access_released': 4, 'type_selector': 1, 'refresh_rate': None, 'created_on': '2019-05-27T10:44:41', 'created_by': 'data999', 'modified_on': '2019-05-27T10:47:29', 'modified_by': 'data999', 'grid_padding': None, 'create_type': 0, 'new_layout_type': 0, 'distribute_type': 0, 'is_show_mark_img': 0}], 'dashboard_chart_dim': [{'dashboard_chart_id': '7474d8e6-8029-11e9-9f73-af97cb348c3e', 'dim': '39eda8d9-24f2-0f99-5b4d-43b404dcb8bd', 'alias': 'aaa', 'content': '', 'formula_mode': '', 'created_by': 'data999', 'modified_by': 'data999', 'rank': 0, 'sort': '', 'id': '7f20c7e0-8029-11e9-9f73-af97cb348c3e', 'dashboard_jump_config': None, 'note': None, 'is_subtotal_cate': 0}], 'dashboard_chart_num': [], 'dashboard_chart_filter': [], 'dashboard_chart_filter_relation': [], 'dashboard_chart_layers': [], 'dashboard_chart_markline': [], 'dashboard_chart_desire': [], 'dashboard_chart_params': [], 'dashboard_chart_params_jump': [], 'dashboard_chart_comparison': [], 'dashboard_chart_penetrate_relation': [], 'dashboard_chart_field_sort': [], 'dashboard_chart_selector': [], 'dashboard_chart_selector_field': [], 'dashboard_filter': [], 'dashboard_filter_relation': [], 'dashboard_dataset_field_relation': [], 'dashboard_released_snapshot_chart': [{'increment_id': 71630, 'id': '7474d8e6-8029-11e9-9f73-af97cb348c3e', 'snapshot_id': '39ee095a-4401-45d4-573d-069685727335', 'dashboard_id': '39ee095a-4401-45d4-573d-069685727335', 'name': '表格-1', 'chart_code': 'table', 'chart_type': 'chart', 'content': None, 'source': '39eda8d8-efbb-1ee4-3ad7-16f50982b56d', 'level_code': '', 'parent_id': '', 'display_item': '', 'refresh_rate': '', 'penetrate': 0, 'sort_method': '', 'position': '{"i": "7474d8e6-8029-11e9-9f73-af97cb348c3e", "col": 610, "row": 300, "size_x": 480, "size_y": 270, "z": 1501}', 'percentage': None, 'default_value': '', 'layout': None, 'layout_extend': None, 'config': '', 'filter_config': None, 'page_size': 0, 'data_modified_on': None, 'dims': '[{"dashboard_chart_id": "7474d8e6-8029-11e9-9f73-af97cb348c3e", "dim": "39eda8d9-24f2-0f99-5b4d-43b404dcb8bd", "dashboard_jump_config": None, "formula_mode": "", "alias": "aaa", "content": "", "rank": 0, "note": None, "sort": "", "is_subtotal_cate": 0, "dataset_field_id": "39eda8d9-24f2-0f99-5b4d-43b404dcb8bd", "alias_name": "aaa", "field_group": "\\u7ef4\\u5ea6", "dataset_id": "39eda8d8-efbb-1ee4-3ad7-16f50982b56d", "data_type": "\\u5b57\\u7b26\\u4e32", "col_name": "AAA_3979676526", "expression": None, "type": "\\u666e\\u901a", "visible": 1, "chart_params_jump": []}]', 'nums': '[]', 'comparisons': '[]', 'zaxis': '[]', 'desires': '[]', 'filters': '[]', 'filter_relation': 0, 'marklines': '[]', 'penetrates': '[]', 'chart_params': '[]', 'jump': '[]', 'data_logic_type_code': 'default', 'layers': '[]', 'column_order': None, 'source_type': 'SQL', 'source_content': '{"sql_table_names": ["1pdo"], "tmp_table_name": "dataset_tmp_5cd29aa5cb245", "sql": "select aaa,bbb,ccc,ddd,eee,fff,ggg,hhh,a1,a2,a3,a4,a5,a6,a7,a8,a9,b1,b2,b3,b4,b5,b6,b7,b8,b9,c1,c2,c3,c4,c5 from 1pdo", "data_source_id": "00000000-1111-1111-2222-000000000000", "count": 2, "create_table_sql": "create table if not exists dataset_tmp_5cd29aa5cb245 (`AAA_3979676526` text ,`BBB_3980069745` varchar(150) ,`CCC_3980462964` smallint(6) ,`DDD_3980856183` mediumint(9) ,`EEE_3981249402` int ,`FFF_3981642621` int ,`GGG_3982035840` int ,`HHH_3982429059` bigint(20) ,`A_3784772317` double ,`A_3784837854` double ,`A_3784903391` float ,`A_3784968928` decimal(11,0) ,`A_3785034465` decimal(11,0) ,`A_3785100002` char(150) ,`A_3785165539` varchar(765) ,`A_3785231076` char(50) ,`A_3785296613` varchar(255) ,`B_3784903390` text ,`B_3784968927` text ,`B_3785034464` timestamp ,`B_3785100001` datetime ,`B_3785165538` text ,`B_3785231075` text ,`B_3785296612` text ,`B_3785362149` longtext ,`B_3785427686` text ,`C_3785034463` text ,`C_3785100000` text ,`C_3785165537` longtext ,`C_3785231074` char(3) ,`C_3785296611` char(15) )"}', 'label_id': None, 'label_tmpl_id': None, 'label_org_id': None, 'label_name': None, 'created_by': 'data999', 'modified_by': 'data999', 'component_filter': '[]', 'chart_params_jump': '[]', 'penetrate_relation': '[]', 'penetrate_filter_relation': '[]', 'chart_linkage': '[]', 'chart_filter': '[]', 'chart_default_value': '[]', 'var_relations': '[]', 'chart_vars': '[]', 'field_sorts': '[]', 'enable_subtotal': 0, 'enable_summary': 0}], 'dashboard_released_snapshot_dashboard': [{'increment_id': 13880, 'id': '39ee095a-4401-45d4-573d-069685727335', 'snapshot_id': '39ee095a-4401-45d4-573d-069685727335', 'data_type': 0, 'name': '导入报告3', 'type': 'FILE', 'level_code': '1965-0001-', 'platform': 'pc', 'is_multiple_screen': 0, 'status': 1, 'user_group_id': '', 'cover': '', 'selectors': '{}', 'dashboard_filters': '[]', 'share_secret_key': '', 'layout': '{"mode": "free", "platform": "pc", "ratio": "16:9", "width": 1920, "height": 1080, "lattice": 10, "toolbar": "show", "screenHeader": "show"}', 'scale_mode': 0, 'background': '{"show": true, "color": "RGBA(255,255,255,1)", "image": "", "size": "stretch"}', 'biz_code': 'cc7b9aad6e5846f8a20c9c6030afa486', 'theme': 'colorful_white', 'type_access_released': 4, 'refresh_rate': '', 'created_by': 'data999', 'modified_by': 'data999', 'rank': 0, 'grid_padding': '{"container_padding":[10,10],"chart_margin":[10,10],"chart_padding":[15,15,15,15],"chart_background":"#FFFFFF"}', 'new_layout_type': 0, 'create_type': 1, 'is_show_mark_img': 0}, {'increment_id': 13881, 'id': '39ee095a-4401-45d4-573d-069685727335', 'snapshot_id': '39ee095a-4401-45d4-573d-069685727335', 'data_type': 1, 'name': '导入报告3', 'type': 'FILE', 'level_code': '1965-0001-', 'platform': 'pc', 'is_multiple_screen': 0, 'status': 1, 'user_group_id': '', 'cover': '', 'selectors': '{}', 'dashboard_filters': '[]', 'share_secret_key': '', 'layout': '{"mode": "free", "platform": "pc", "ratio": "16:9", "width": 1920, "height": 1080, "lattice": 10, "toolbar": "show", "screenHeader": "show"}', 'scale_mode': 0, 'background': '{"show": true, "color": "RGBA(255,255,255,1)", "image": "", "size": "stretch"}', 'biz_code': 'cc7b9aad6e5846f8a20c9c6030afa486', 'theme': 'colorful_white', 'type_access_released': 4, 'refresh_rate': '', 'created_by': 'data999', 'modified_by': 'data999', 'rank': 0, 'grid_padding': '{"container_padding":[10,10],"chart_margin":[10,10],"chart_padding":[15,15,15,15],"chart_background":"#FFFFFF"}', 'new_layout_type': 0, 'create_type': 1, 'is_show_mark_img': 0}], 'screen_dashboard': [{'id': '39ee095a-ad47-be8a-6e21-04d627795d6f', 'dashboard_id': '39ee095a-4401-45d4-573d-069685727335', 'screen_id': '39ee095a-4401-45d4-573d-069685727335', 'rank': 1, 'type': 1, 'created_by': 'data999', 'modified_by': 'data999'}], 'dashboard_filter_chart': [], 'dashboard_filter_chart_relation': [], 'dashboard_linkage': [], 'dashboard_linkage_relation': [], 'dashboard_filter_chart_default_values': [], 'dashboard_extra': [{'dashboard_id': '39ee095a-4401-45d4-573d-069685727335', 'edit_on': None, 'released_on': '2019-05-27T10:45:37', 'created_by': 'data999', 'modified_by': 'data999'}], 'dashboard_jump_config': [], 'dashboard_jump_relation': [], 'dashboard_vars_jump_relation': [], 'dashboard_dataset_vars_relation': [], 'dashboard_component_filter': [], 'dashboard_component_filter_field_relation': []}, '39ee095c-649d-6a85-6025-ba7bc6a09e5a': {'dashboard': [{'id': '39ee095c-649d-6a85-6025-ba7bc6a09e5a', 'theme': 'colorful_white', 'data_report_id': None, 'name': '导入报告4', 'type': 'FILE', 'parent_id': '39ee095c-22e0-cec2-53ea-5b3fbfbab8de', 'level_code': '1965-0002-0001-', 'icon': '', 'platform': 'pc', 'is_multiple_screen': 0, 'status': 1, 'user_group_id': '', 'default_show': 0, 'cover': '', 'description': '', 'layout_type': '自由布局', 'share_secret_key': '', 'layout': '{"mode":"free","platform":"pc","ratio":"16:9","width":1920,"height":1080,"lattice":10,"toolbar":"show","screenHeader":"show"}', 'scale_mode': 0, 'background': '{"show":true,"color":"RGBA(255,255,255,1)","image":"","size":"stretch"}', 'rank': None, 'build_in': 0, 'biz_code': 'e14e9d7b86e5494a93a04a63a760f94a', 'border': None, 'type_access_released': 4, 'type_selector': 1, 'refresh_rate': None, 'created_on': '2019-05-27T10:47:29', 'created_by': 'data999', 'modified_on': '2019-05-27T10:47:45', 'modified_by': 'data999', 'grid_padding': '{"container_padding":[10,10],"chart_margin":[10,10],"chart_padding":[15,15,15,15],"chart_background":"#FFFFFF"}', 'create_type': 1, 'new_layout_type': 0, 'distribute_type': None, 'is_show_mark_img': 0}], 'dashboard_chart': [{'id': 'c73514f1-8029-11e9-8992-f15bde93e4ce', 'dashboard_id': '39ee095c-649d-6a85-6025-ba7bc6a09e5a', 'name': '表格-1', 'chart_code': 'table', 'filter_config': None, 'page_size': 0, 'data_logic_type_code': 'default', 'chart_type': 'chart', 'content': None, 'source': '39ed4bec-e098-301d-3dd6-9ec8b51d2250', 'created_on': '2019-05-27T10:47:33', 'created_by': 'data999', 'modified_on': '2019-05-27T10:47:39', 'modified_by': 'data999', 'data_modified_on': None, 'position': '{"i": "c73514f1-8029-11e9-8992-f15bde93e4ce", "col": 720, "row": 300, "size_x": 480, "size_y": 270, "z": 1501}', 'sort_method': None, 'penetrate': 0, 'parent_id': '', 'level_code': '', 'refresh_rate': '', 'display_item': '', 'desired_value': 0, 'percentage': None, 'style_type': None, 'default_value': '', 'layout': None, 'layout_extend': None, 'config': '', 'filter_relation': 0, 'layers': None, 'column_order': None, 'enable_subtotal': 0, 'enable_summary': 0}], 'dashboard_folders': [{'id': '39ee0959-d51d-a800-6092-9e798e3541a9', 'theme': 'tech_blue', 'data_report_id': None, 'name': '平铺目录', 'type': 'FOLDER', 'parent_id': '', 'level_code': '1965-', 'icon': '', 'platform': 'pc', 'is_multiple_screen': 0, 'status': 0, 'user_group_id': '', 'default_show': 0, 'cover': '', 'description': '', 'layout_type': '标准布局', 'share_secret_key': '', 'layout': None, 'scale_mode': 0, 'background': None, 'rank': 0, 'build_in': 0, 'biz_code': '3da3c2a87a4a416687e36d203f7b122a', 'border': None, 'type_access_released': 4, 'type_selector': 1, 'refresh_rate': None, 'created_on': '2019-05-27T10:44:41', 'created_by': 'data999', 'modified_on': '2019-05-27T10:47:29', 'modified_by': 'data999', 'grid_padding': None, 'create_type': 0, 'new_layout_type': 0, 'distribute_type': 0, 'is_show_mark_img': 0}, {'id': '39ee095c-22e0-cec2-53ea-5b3fbfbab8de', 'theme': 'tech_blue', 'data_report_id': None, 'name': '平铺2', 'type': 'FOLDER', 'parent_id': '39ee0959-d51d-a800-6092-9e798e3541a9', 'level_code': '1965-0002-', 'icon': '', 'platform': 'pc', 'is_multiple_screen': 0, 'status': 0, 'user_group_id': '', 'default_show': 0, 'cover': '', 'description': '', 'layout_type': '标准布局', 'share_secret_key': '', 'layout': None, 'scale_mode': 0, 'background': None, 'rank': 0, 'build_in': 0, 'biz_code': '137a092721e9430ba3c5ad49ff507c39', 'border': None, 'type_access_released': 4, 'type_selector': 1, 'refresh_rate': None, 'created_on': '2019-05-27T10:47:12', 'created_by': 'data999', 'modified_on': '2019-05-27T10:47:29', 'modified_by': 'data999', 'grid_padding': None, 'create_type': 0, 'new_layout_type': 0, 'distribute_type': 0, 'is_show_mark_img': 0}], 'dashboard_chart_dim': [{'dashboard_chart_id': 'c73514f1-8029-11e9-8992-f15bde93e4ce', 'dim': '39ed4bec-e098-103e-98da-5b3c19fbdaa7', 'alias': '出勤人数', 'content': '', 'formula_mode': '', 'created_by': 'data999', 'modified_by': 'data999', 'rank': 0, 'sort': '', 'id': 'cccafba3-8029-11e9-8992-f15bde93e4ce', 'dashboard_jump_config': None, 'note': None, 'is_subtotal_cate': 0}], 'dashboard_chart_num': [], 'dashboard_chart_filter': [], 'dashboard_chart_filter_relation': [], 'dashboard_chart_layers': [], 'dashboard_chart_markline': [], 'dashboard_chart_desire': [], 'dashboard_chart_params': [], 'dashboard_chart_params_jump': [], 'dashboard_chart_comparison': [], 'dashboard_chart_penetrate_relation': [], 'dashboard_chart_field_sort': [], 'dashboard_chart_selector': [], 'dashboard_chart_selector_field': [], 'dashboard_filter': [], 'dashboard_filter_relation': [], 'dashboard_dataset_field_relation': [], 'dashboard_released_snapshot_chart': [{'increment_id': 71631, 'id': 'c73514f1-8029-11e9-8992-f15bde93e4ce', 'snapshot_id': '39ee095c-649d-6a85-6025-ba7bc6a09e5a', 'dashboard_id': '39ee095c-649d-6a85-6025-ba7bc6a09e5a', 'name': '表格-1', 'chart_code': 'table', 'chart_type': 'chart', 'content': None, 'source': '39ed4bec-e098-301d-3dd6-9ec8b51d2250', 'level_code': '', 'parent_id': '', 'display_item': '', 'refresh_rate': '', 'penetrate': 0, 'sort_method': '', 'position': '{"i": "c73514f1-8029-11e9-8992-f15bde93e4ce", "col": 720, "row": 300, "size_x": 480, "size_y": 270, "z": 1501}', 'percentage': None, 'default_value': '', 'layout': None, 'layout_extend': None, 'config': '', 'filter_config': None, 'page_size': 0, 'data_modified_on': None, 'dims': '[{"dashboard_chart_id": "c73514f1-8029-11e9-8992-f15bde93e4ce", "dim": "39ed4bec-e098-103e-98da-5b3c19fbdaa7", "dashboard_jump_config": None, "formula_mode": "", "alias": "\\u51fa\\u52e4\\u4eba\\u6570", "content": "", "rank": 0, "note": None, "sort": "", "is_subtotal_cate": 0, "dataset_field_id": "39ed4bec-e098-103e-98da-5b3c19fbdaa7", "alias_name": "\\u51fa\\u52e4\\u4eba\\u6570", "field_group": "\\u7ef4\\u5ea6", "dataset_id": "39ed4bec-e098-301d-3dd6-9ec8b51d2250", "data_type": "\\u5b57\\u7b26\\u4e32", "col_name": "col1", "expression": "", "type": "\\u666e\\u901a", "visible": 1, "chart_params_jump": []}]', 'nums': '[]', 'comparisons': '[]', 'zaxis': '[]', 'desires': '[]', 'filters': '[]', 'filter_relation': 0, 'marklines': '[]', 'penetrates': '[]', 'chart_params': '[]', 'jump': '[]', 'data_logic_type_code': 'default', 'layers': '[]', 'column_order': None, 'source_type': 'UNION', 'source_content': '{"sql": "select [\\u65f6\\u95f4],[\\u5ba2\\u6237\\u6570],[\\u51fa\\u52e4\\u4eba\\u6570],[\\u51fa\\u52e4\\u7387],[\\u6d3b\\u8dc3\\u4eba\\u6570],[\\u6d3b\\u8dc3\\u5ea6],[\\u6765\\u7535\\u5ba2\\u6237\\u6570],[\\u6765\\u8bbf\\u5ba2\\u6237\\u6570],[\\u603b\\u8ddf\\u8fdb\\u5ba2\\u6237\\u6570],[\\u603b\\u8ddf\\u8fdb\\u6b21\\u6570],[\\u7f6e\\u4e1a\\u987e\\u95ee],[\\u884c\\u9500\\u4eba\\u5458],[\\u9500\\u552e\\u7ecf\\u7406],[\\u804c\\u5458],[\\u5360\\u6bd4],[\\u8fd17\\u5929\\u903e\\u671f\\u5ba2\\u6237\\u6570],[\\u8fd17\\u5929\\u79bb\\u804c\\u672a\\u5904\\u7406\\u5ba2\\u6237\\u6570],[\\u4eca\\u65e5\\u4ea4\\u6613\\u53cd\\u8865\\u6bd4\\u4f8b],[\\u65b0\\u589e\\u5ba2\\u6237\\u6570],[\\u4ea4\\u6613\\u53cd\\u8865],[\\u65e5\\u671f],[\\u903e\\u671f\\u6570],[\\u79bb\\u804c\\u672a\\u5904\\u7406\\u6570] from {\\u5185\\u7f6e\\u6a21\\u677f\\u6570\\u636e\\u96c6_\\u4eca\\u65e5\\u4f5c\\u6218\\u6001\\u52bf}", "replace_sql": "select col22,col23,col1,col2,col3,col4,col5,col6,col7,col8,col9,col10,col11,col12,col13,col14,col15,col16,col17,col18,col19,col20,col21 from dataset_15cfa892a1a107bb", "count": 0, "sql_table_names": ["{\\u5185\\u7f6e\\u6a21\\u677f\\u6570\\u636e\\u96c6_\\u4eca\\u65e5\\u4f5c\\u6218\\u6001\\u52bf}"]}', 'label_id': None, 'label_tmpl_id': None, 'label_org_id': None, 'label_name': None, 'created_by': 'data999', 'modified_by': 'data999', 'component_filter': '[]', 'chart_params_jump': '[]', 'penetrate_relation': '[]', 'penetrate_filter_relation': '[]', 'chart_linkage': '[]', 'chart_filter': '[]', 'chart_default_value': '[]', 'var_relations': '[]', 'chart_vars': '[]', 'field_sorts': '[]', 'enable_subtotal': 0, 'enable_summary': 0}], 'dashboard_released_snapshot_dashboard': [{'increment_id': 13882, 'id': '39ee095c-649d-6a85-6025-ba7bc6a09e5a', 'snapshot_id': '39ee095c-649d-6a85-6025-ba7bc6a09e5a', 'data_type': 0, 'name': '导入报告4', 'type': 'FILE', 'level_code': '1965-0002-0001-', 'platform': 'pc', 'is_multiple_screen': 0, 'status': 1, 'user_group_id': '', 'cover': '', 'selectors': '{}', 'dashboard_filters': '[]', 'share_secret_key': '', 'layout': '{"mode": "free", "platform": "pc", "ratio": "16:9", "width": 1920, "height": 1080, "lattice": 10, "toolbar": "show", "screenHeader": "show"}', 'scale_mode': 0, 'background': '{"show": true, "color": "RGBA(255,255,255,1)", "image": "", "size": "stretch"}', 'biz_code': 'e14e9d7b86e5494a93a04a63a760f94a', 'theme': 'colorful_white', 'type_access_released': 4, 'refresh_rate': '', 'created_by': 'data999', 'modified_by': 'data999', 'rank': 0, 'grid_padding': '{"container_padding":[10,10],"chart_margin":[10,10],"chart_padding":[15,15,15,15],"chart_background":"#FFFFFF"}', 'new_layout_type': 0, 'create_type': 1, 'is_show_mark_img': 0}, {'increment_id': 13883, 'id': '39ee095c-649d-6a85-6025-ba7bc6a09e5a', 'snapshot_id': '39ee095c-649d-6a85-6025-ba7bc6a09e5a', 'data_type': 1, 'name': '导入报告4', 'type': 'FILE', 'level_code': '1965-0002-0001-', 'platform': 'pc', 'is_multiple_screen': 0, 'status': 1, 'user_group_id': '', 'cover': '', 'selectors': '{}', 'dashboard_filters': '[]', 'share_secret_key': '', 'layout': '{"mode": "free", "platform": "pc", "ratio": "16:9", "width": 1920, "height": 1080, "lattice": 10, "toolbar": "show", "screenHeader": "show"}', 'scale_mode': 0, 'background': '{"show": true, "color": "RGBA(255,255,255,1)", "image": "", "size": "stretch"}', 'biz_code': 'e14e9d7b86e5494a93a04a63a760f94a', 'theme': 'colorful_white', 'type_access_released': 4, 'refresh_rate': '', 'created_by': 'data999', 'modified_by': 'data999', 'rank': 0, 'grid_padding': '{"container_padding":[10,10],"chart_margin":[10,10],"chart_padding":[15,15,15,15],"chart_background":"#FFFFFF"}', 'new_layout_type': 0, 'create_type': 1, 'is_show_mark_img': 0}], 'screen_dashboard': [{'id': '39ee095c-a37e-59cb-b7dd-a375fd40f897', 'dashboard_id': '39ee095c-649d-6a85-6025-ba7bc6a09e5a', 'screen_id': '39ee095c-649d-6a85-6025-ba7bc6a09e5a', 'rank': 1, 'type': 1, 'created_by': 'data999', 'modified_by': 'data999'}], 'dashboard_filter_chart': [], 'dashboard_filter_chart_relation': [], 'dashboard_linkage': [], 'dashboard_linkage_relation': [], 'dashboard_filter_chart_default_values': [], 'dashboard_extra': [{'dashboard_id': '39ee095c-649d-6a85-6025-ba7bc6a09e5a', 'edit_on': None, 'released_on': '2019-05-27T10:47:45', 'created_by': 'data999', 'modified_by': 'data999'}], 'dashboard_jump_config': [], 'dashboard_jump_relation': [], 'dashboard_vars_jump_relation': [], 'dashboard_dataset_vars_relation': [], 'dashboard_component_filter': [], 'dashboard_component_filter_field_relation': []}}

        assert isinstance(dashboards, dict)
        assert isinstance(dashboards2, dict)
        res = dashboard_service.deliver_dashboard_data(project_code, dashboards2, distribute_type=distribute_type,
                                                       target_dashboard_folder_id='',
                                                       include_folder=False)
        assert res == True
