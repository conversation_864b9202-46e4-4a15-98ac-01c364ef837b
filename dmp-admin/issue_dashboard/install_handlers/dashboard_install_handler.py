#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ---------------- 标准模块 ----------------
import json
import logging
from datetime import datetime

# ---------------- 业务模块 ----------------
from issue_dashboard.install_handlers.base_handler import BaseInstallHandler, execute_logger
from dmplib.utils.errors import UserError

logger = logging.getLogger(__name__)

# 跳过处理的键名，一般为数据表名，配置后将不收集这些表的id
EXCLUDE_TABLES = ["dap_bi_dashboard_folders", "dap_bi_dashboard_released_snapshot_chart", "dap_bi_dashboard_released_snapshot_dashboard"]


class DashboardInstallHandler(BaseInstallHandler):
    def __init__(self, package_data):
        self.handler_source = "dashboard"
        super().__init__(package_data=package_data, handler_source=self.handler_source)
        self.collect_exclude_tables = EXCLUDE_TABLES

    def _init_model_file_name(self):
        return "dashboard_replace_key_models"

    @staticmethod
    def _get_table_name_list_for_init(dashboards: dict):
        first_operate_dashboard = list(dashboards.values())[0]
        return list(first_operate_dashboard.keys()) if first_operate_dashboard else []

    @staticmethod
    def _replace_dashboard_fields(data_list: list):
        """
        处理报告数据表的一些字段
        :param data_list:
        :return:
        """
        for data_item in data_list:
            data_item["status"] = 0  # 安装后的报告默认为未安装状态
            data_item["name"] = data_item.get("name", "") + "_" + datetime.now().strftime('%Y%m%d%H%M%S')  # 报告名称加时间戳区分

    def _replace_specific_fields(self, dashboards: dict):
        """
        处理一些特殊字段值的替换，如：报告名称，报告发布状态
        :return:
        """
        for operate_dashboard in dashboards.values():
            for table_name, data_list in operate_dashboard.items():
                if table_name in self.collect_exclude_tables:
                    operate_dashboard[table_name] = []
                    continue

                if table_name in ["dap_bi_dashboard"]:
                    self._replace_dashboard_fields(data_list)

    @execute_logger
    def execute(self):
        if not self.package_data:
            return self.package_data
        dashboards = self.package_data.get("dashboards")
        if not dashboards:
            return self.package_data

        self._init_replace_key_models(self._get_table_name_list_for_init(dashboards))
        for operate_dashboard in dashboards.values():
            self.collect_ids(operate_dashboard)

        self._replace_specific_fields(dashboards)
        self.package_data["dashboards"] = self.replace_data(dashboards)
        return self.package_data
