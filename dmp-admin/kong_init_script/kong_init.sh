#!/bin/bash

if [ "$KONG_HOST"x = ""x ]; then
    KONG_HOST='dmp-kong2'
fi

endpoint="http://$KONG_HOST:8001"

function addService()
{
    # $1 service name $2 url
    echo ""
    echo "------"
    echo "create/update service $1 - $2"
    curl -Ss -X PUT $endpoint/services/$1 \
      --data "name=$1" \
      --data "url=$2" \
      --data "retries=0"
}

function addRoute()
{
    # $1 route_name $2 methods $3 paths
    # strip_path参数指定是否要drop掉原路由的path路径
    echo ""
    echo "------"
    echo "create/update routes $1 - $2 - $3"
    curl -Ss -X PUT $endpoint/services/$1/routes/$1 \
      --data "name=$1" \
      --data "$2" \
      --data "$3" \
      --data "strip_path=true"
}

function addPlugin()
{
    # $1 route_name
    echo ""
    echo "------"
    echo "add plugin $1"
    curl -Ss -X POST $endpoint/routes/$1/plugins \
      --data "name=key-auth" \
      --data "config.hide_credentials=true"
}

function addApi()
{
  # 为了兼容老版本的api接口, 这里对route和service进行一一映射, 一个route对应一个service, route_name=service_name
  # $1 name $2 service_url $3 methods[] $4 paths[]
  echo ""
  echo "============================"
  echo "create/update api $1 - $2 - $3 - $4"
  addService $1 $2
  addRoute $1 $3 $4
  addPlugin $1
}

function get_default_consumer_key()
{
    # 获取JWT的公共秘钥
    script_dir=$(dirname "$(realpath "$0")")
    root_dir=$(dirname "$script_dir")
    CONFIG_FILE="$root_dir/app.config"
    SECTION="JWT"
    KEY="common_secret"
    value=$(grep -A20 "\[${SECTION}\]" "$CONFIG_FILE" | grep "${KEY} =" | cut -d'=' -f2 | tr -d ' ')
    echo "$value"
}

function addDefaultConsumer()
{
    # 注册kong服务的默认消费者 default_consumer
    echo ""
    echo "------"
    echo "addDefaultConsumer $1"

    curl -Ss -X POST $endpoint/consumers --data "username=default_consumer&custom_id=default_consumer"
    curl -Ss -X POST $endpoint/consumers/default_consumer/key-auth --data "key=$1"
}

#### dmp
addApi dmp_label_add http://dmp:8000/openapi/label/add methods[]=POST paths[]=/openapi/label/add
addApi dmp_label_check_logical_expression http://dmp:8000/openapi/label/check_logical_expression methods[]=GET paths[]=/openapi/label/check_logical_expression
addApi dmp_indicator_type_list http://dmp:8000/openapi/indicator/type/list methods[]=GET paths[]=/openapi/indicator/type/list
addApi dmp_label_list http://dmp:8000/openapi/label/list methods[]=GET paths[]=/openapi/label/list
addApi dmp_template_list http://dmp:8000/openapi/template/list methods[]=GET paths[]=/openapi/template/list
addApi dmp_get_organ http://dmp:8000/openapi/get_organ methods[]=GET paths[]=/openapi/get_organ
addApi dmp_send_message http://dmp:8000/openapi/send_message methods[]=POST paths[]=/openapi/send_message
addApi dmp_get_oss_config http://dmp:8000/openapi/get_oss_config methods[]=GET paths[]=/openapi/get_oss_config
addApi dmp_user_add http://dmp:8000/openapi/user/add methods[]=POST paths[]=/openapi/user/add
addApi dmp_dashboard_list http://dmp:8000/openapi/dashboard/list methods[]=GET paths[]=/openapi/dashboard/list
addApi dmp_user_upset http://dmp:8000/openapi/user/upset methods[]=POST paths[]=/openapi/user/upset
addApi dmp_user_role_upset http://dmp:8000/openapi/user/role/upset methods[]=POST paths[]=/openapi/user/role/upset
addApi dmp_rbac_funcs_list http://dmp:8000/openapi/rbac/funcs/list methods[]=GET paths[]=/openapi/rbac/funcs/list
addApi dmp_check_flow http://dmp:8000/openapi/check_flow methods[]=GET paths[]=/openapi/check_flow
addApi dmp_flow_enable http://dmp:8000/openapi/flow/enable methods[]=POST paths[]=/openapi/flow/enable
addApi dmp_flow_delete_schedule http://dmp:8000/openapi/flow/delete/schedule methods[]=POST paths[]=/openapi/flow/delete/schedule
addApi dmp_dashboard_all http://dmp:8000/openapi/dashboard/all methods[]=GET paths[]=/openapi/dashboard/all
addApi dmp_page_chart_download http://dmp:8000/openapi/dashboard/page_chart/download/list methods[]=GET paths[]=/openapi/dashboard/page_chart/download/list
addApi user_info http://dmp:8000/openapi/user/info methods[]=GET paths[]=/openapi/user/info
addApi erp_message_flow_nofity http://dmp:8000/openapi/message/flow/notify methods[]=POST paths[]=/openapi/message/flow/notify
addApi data_source_mysql_update http://dmp:8000/openapi/data_source/update methods[]=POST paths[]=/openapi/data_source/update
addApi user_sync_setting http://dmp:8000/openapi/user_sync/setting methods[]=POST paths[]=/openapi/user_sync/setting
addApi data_source_replace_into http://dmp:8000/openapi/data_source/replace_into methods[]=POST paths[]=/openapi/data_source/replace_into
addApi register_rundeck_system_event http://dmp:8000/openapi/rundeck_job/upset methods[]=POST paths[]=/openapi/rundeck_job/upset
addApi register_dataset_subject_inspect_result http://dmp:8000/openapi/dataset_subject/inspect_result methods[]=POST paths[]=/openapi/dataset_subject/inspect_result
addApi self_service_check http://dmp:8000/openapi/self_service/check methods[]=POST paths[]=/openapi/self_service/check
addApi self_service_refresh_post http://dmp:8000/openapi/self_service/refresh methods[]=POST paths[]=/openapi/self_service/refresh
addApi self_service_refresh_get http://dmp:8000/openapi/self_service/refresh methods[]=GET paths[]=/openapi/self_service/refresh
addApi refresh_all http://dmp:8000/openapi/self_service/refresh_all methods[]=GET paths[]=/openapi/self_service/refresh_all
addApi screen_relative_dashboards http://dmp:8000/openapi/screen/dashboards methods[]=GET paths[]=/openapi/screen/dashboards
addApi screen_relative_dashboards_auth http://dmp:8000/openapi/screen/upload_auth methods[]=POST paths[]=/openapi/screen/upload_auth
addApi dashboard_released_chart http://dmp:8000/openapi/dashboard/released_chart methods[]=GET paths[]=/openapi/dashboard/released_chart
addApi portal_list http://dmp:8000/openapi/portal/list methods[]=GET paths[]=/openapi/portal/list
addApi portal_dashboards http://dmp:8000/openapi/portal/dashboards methods[]=GET paths[]=/openapi/portal/dashboards
addApi portal.upload_auth http://dmp:8000/openapi/portal/upload_auth methods[]=POST paths[]=/openapi/portal/upload_auth
addApi dmp_embedded_dashboard_list http://dmp:8000/openapi/embedded/dashboard/list methods[]=GET paths[]=/openapi/embedded/dashboard/list
addApi dmp_publish_report http://dmp:8000/openapi/publish_report methods[]=POST paths[]=/openapi/publish_report
addApi dmp_get_dashboard http://dmp:8000/openapi/get_dashboard methods[]=GET paths[]=/openapi/get_dashboard
addApi dmp_data_source_add http://dmp:8000/openapi/data_source/add methods[]=POST paths[]=/openapi/data_source/add


#### dmp-admin
addApi dmp_admin_project_add http://dmp-admin:8000/openapi/projects methods[]=POST paths[]=/openapi/projects
addApi dmp_admin_rds_add http://dmp-admin:8000/openapi/rds methods[]=POST paths[]=/openapi/rds
addApi dmp_admin_rds_list http://dmp-admin:8000/openapi/rds-list methods[]=POST paths[]=/openapi/rds-list
addApi dmp_admin_notify_create_tenant http://dmp-admin:8000/openapi/notify/create-tenant methods[]=POST paths[]=/openapi/notify/create-tenant
addApi tenant_opening_status http://dmp-admin:8000/openapi/tenant/task/status methods[]=GET paths[]=/openapi/tenant/task/status
addApi tenant_opening http://dmp-admin:8000/openapi/tenant/open methods[]=POST paths[]=/openapi/tenant/open
addApi tenant_license_update http://dmp-admin:8000/openapi/tenant/license/update methods[]=POST paths[]=/openapi/tenant/license/update
addApi tenant_upgrade http://dmp-admin:8000/openapi/tenant/upgrade methods[]=POST paths[]=/openapi/tenant/upgrade
addApi tenant_info http://dmp-admin:8000/openapi/tenant/info methods[]=GET paths[]=/openapi/tenant/info
addApi tenant_area http://dmp-admin:8000/openapi/tenant/area methods[]=GET paths[]=/openapi/tenant/area
addApi dmp_admin_deliver_callback http://dmp-admin:8000/openapi/deliver_callback methods[]=POST paths[]=/openapi/deliver_callback
addApi dmp_admin_delete_tenant http://dmp-admin:8000/openapi/delete_tenant methods[]=GET paths[]=/openapi/delete_tenant


#### bigdata
addApi bigdata_send_message_new http://bigdata-api:8080/openapi/send_message methods[]=POST paths[]=/bigdata/openapi/send_message
addApi bigdata_check_flow_new http://bigdata-api:8080/openapi/check_flow methods[]=GET paths[]=/bigdata/openapi/check_flow

##### test
#addApi test_case http://bigdata-api:8080/openapi/test/case methods[]=GET paths[]=/bigdata/openapi/test/case



# 注册默认消费者 default_consumer 公共秘钥
if [[ "$(get_default_consumer_key)" =~ ^[[:space:]]*$ ]]; then
  # 公共秘钥为空白字符
  echo "get_default_consumer_key is space string"
else
  addDefaultConsumer $(get_default_consumer_key)
fi
