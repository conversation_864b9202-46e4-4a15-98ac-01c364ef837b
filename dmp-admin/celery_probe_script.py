import sys, os

os.environ['DMP_CFG_FILE_PATH'] = os.path.join(os.path.dirname(__file__), 'app.config')

from components.celery import celery as celery_app
from celery.app.control import Inspect

from dmplib.redis import get_redis_conn


def redis_is_ready():
    try:
        get_redis_conn().set('probe', 1, 5)
        print("OK")
        sys.exit(0)
    except Exception as e:
        print(e)
        sys.exit(5)


def ping(worker_name):
    i = Inspect(
        app=celery_app
    )
    r = i.ping([worker_name])
    if r is None or not r:
        sys.exit(4)
    elif r and 'ok' not in r.get(worker_name):
        sys.exit(4)
    else:
        print("OK")
        sys.exit(0)


def active_queues(worker_name):
    i = Inspect(
        app=celery_app
    )
    queue_name = os.environ.get('CELERY_DEFAULT_QUEUE') or 'celery'
    res = i.active_queues()
    queues = res.get(worker_name)
    if not queues:
        sys.exit(5)
    else:
        for queue in queues:
            if queue_name == queue.get('name'):
                print("OK")
                sys.exit(0)
        sys.exit(5)


if __name__ == "__main__":
    args = sys.argv
    worker_name = args[1]
    action = args[2]

    if action == 'startup':
        redis_is_ready()
    elif action == 'ping':
        ping(worker_name)
    elif action == 'active':
        active_queues(worker_name)
    else:
        sys.exit(0)

