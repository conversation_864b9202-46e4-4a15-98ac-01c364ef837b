# -*- coding: UTF-8 -*-
import json
import os
import traceback
from datetime import datetime
from json import JSONDecodeError
from urllib.parse import urlparse

import asyncio
from pyppeteer import launch
from pyppeteer.network_manager import Request, Response

from components.oss import OSSFileProxy
from components.strings import seq_id
from flow.flow_context import FlowContext
from node.node import Node
from node.node_execution import NodeExecution, logger
from node.node_result import NodeResult
from components import config

import time

import jwt
import base64
from urllib.parse import urlparse


class CheckDashboard(NodeExecution):

    def __init__(self, context: FlowContext):
        logger.info("start CheckDashboard")

        self.context = context
        self.url = None
        self.tab_list = []
        self.start_time = self.now_date()
        self.finish_time = self.now_date()
        self.flow_id = None
        self.task_id = None
        self.dashboard_id = None
        self.dashboard_type = None
        self.check_type = None
        self.chart_data_list = []
        self.get_data_count = 0
        self.get_data_empty_count = 0

    def execution(self):
        try:

            sql = ' select *  from dap_bi_shuxin15_upgrade_dashboard_check_task  where flow_id=%(flow_id)s  '
            logger.info("start CheckDashboard execution")
            with self.context.get_project_db() as db:
                rv = db.query_one(sql, {'flow_id': self.context.flow_id})

            # 没有报告，可能已经被删除
            if not rv:
                return
            if rv.get('status') > 0:
                return NodeResult(True, '0012 已经执行')
            self.dashboard_id = rv.get('dashboard_id')
            self.url = rv.get('open_url')
            self.flow_id = rv.get('flow_id')
            self.task_id = rv.get('task_id')
            self.dashboard_id = rv.get('dashboard_id')
            self.check_type = rv.get('check_type')
            self.dashboard_type = rv.get('dashboard_type')
            payload = {'redirect': self.url,
                       'tenant_code': self.context.project_code,
                       'account': rv.get('open_usercode', self.context.project_code)}
            self.url = generate_sso_login_url(self.url, payload)
            asyncio.get_event_loop().run_until_complete(self.run())
        except (asyncio.InvalidStateError, RuntimeError, KeyboardInterrupt) as e:
            logger.info(f'0013 CheckDashboard, 错误内容: {str(e)}')
            raise Exception(f'0013 计算hd高度失败失败, 错误内容: {str(e)}') from e
        return NodeResult(True, 'CheckDashboard成功')

    async def run(self):
        path = config.get('Chromium.path', '/usr/lib/chromium/chromium')
        browser = None
        try:
            # browser = await launch(headless=True, userDataDir=r'D:\temporary', ignoreHTTPSErrors=True, dumpio=True,
            #                        args=['--disable-infobars', '--no-sandbox'])
            browser = await launch(headless=True, ignoreHTTPSErrors=True, dumpio=True, executablePath=path,
                                   args=['--disable-infobars', '--no-sandbox'])
            page = await browser.newPage()
            page.setDefaultNavigationTimeout(10 * 30 * 1000)
            # await page.setRequestInterception(True)  # 启用拦截器
            # page.on("request", request)
            # page.on("response", self.get_content)

            page.on('request', lambda req: asyncio.ensure_future(self.intercept_request(req)))
            # 设置response拦截器
            page.on('response', lambda rep: asyncio.ensure_future(self.intercept_response(rep)))
            await page.goto(self.url, {'timeout': 10 * 1000, 'waitUntil': ['networkidle0']})
            await asyncio.sleep(3)
            i = 0
            if self.dashboard_type == 'activereport':
                print(f'{datetime.now()}:点击按钮')
                while i < 20:
                    btn_primary = await page.querySelector('#root .preview-container-box .ant-btn-primary')
                    if btn_primary:
                        await page.click('#root .preview-container-box .ant-btn-primary')
                        i = 0
                        break
                    print(f'{datetime.now()}:未获取到按钮')
                    i += 1
                while not self.chart_data_list and i < 20:
                    await asyncio.sleep(1)
                    i += 1
            else:
                while not self.get_data_count or self.get_data_count - self.get_data_empty_count > len(
                        self.chart_data_list) or i < 5 or i > 30:
                    await asyncio.sleep(1)
                    print(f'等待{i}：{self.get_data_count}-{self.get_data_empty_count}:{len(self.chart_data_list)}')
                    i += 1
            print(f'{datetime.now()}:关闭页面')
            page.close()
            self.finish_time = self.now_date()
            self.update_task_data({"status": 1, "start_time": self.start_time,
                                   "finish_time": self.finish_time})
            self.add_dashboard_chart_check(self.chart_data_list)

        except Exception as ex:
            self.update_task_data({"status": 2, "error_msg": self.url + str(ex), "start_time": self.start_time,
                                   "finish_time": self.finish_time})
            logger.info(traceback.format_exc())
            print('catch error')
            print(traceback.format_exc())
            raise Exception('checkdashboard，错误内容：' + str(ex))
        finally:
            if browser:
                try:
                    await browser.close()
                except Exception as ex:
                    print(ex)
            self.kill_crawler()
            time.sleep(1)

    # 请求拦截器函数，设置拦截条件并可作修改
    async def intercept_request(self, interceptedRequest):
        if self.dashboard_type != 'activereport' and (
                'dashboard_chart/chart/data' in interceptedRequest.url or 'dashboard_chart/chart/get_total' in interceptedRequest.url):
            self.get_data_count += 1
            #  print(f'请求计算：{len(self.get_data_count )}:{request.url}')
        await interceptedRequest.continue_()
    # 响应拦截器函数，设置拦截条件并可作修改

    async def intercept_response(self, interceptedResponse):
        is_success = 1
        error_msg = ''
        print(f'{datetime.now()}:{interceptedResponse.url}')
        if self.dashboard_type == 'activereport':
            if '/render' in interceptedResponse.url or ('preview_info' in interceptedResponse.url and interceptedResponse.status != 200):
                content = await interceptedResponse.text()
                content_obj = json.loads(content)
                if not content_obj.get('status', False) or content_obj.get('status',
                                                                           False) == 'Failed' or content_obj.get('code',
                                                                                                                 False) == '400':
                    is_success = 0
                    error_msg = content
                self.chart_data_list.append(
                    {"chart_check_id": seq_id(), "flow_id": self.flow_id, "task_id": self.task_id,
                     "dashboard_id": self.dashboard_id, "chart_id": self.dashboard_id, "is_success": is_success,
                     "error_msg": error_msg, "check_type": self.check_type})
            return

        if 'dashboard_chart/chart/data' not in interceptedResponse.url and 'dashboard_chart/chart/get_total' not in interceptedResponse.url:
            return
        content = await interceptedResponse.text()
        print(interceptedResponse.url)
        print(content)
        content_obj = json.loads(content)
        if not content_obj.get('data') or not isinstance(content_obj.get('data'), dict):
            self.get_data_empty_count += 1
            return
        for key, data in content_obj.get('data').items():
            is_success = 1
            error_msg = ''
            if data.get('execute_status') != 200:
                is_success = 0
                error_msg = data.get('msg')
            self.chart_data_list.append({"chart_check_id": seq_id(), "flow_id": self.flow_id, "task_id": self.task_id,
                                         "dashboard_id": self.dashboard_id, "chart_id": key, "is_success": is_success,
                                         "error_msg": error_msg, "check_type": self.check_type})

    async def get_content(self, response: Response):
        """
            # 注意这里不需要设置 page.setRequestInterception(True)
            page.on("response", get_content)
        :param response:
        :return:
        """
        is_success = 1
        error_msg = ''
        print(f'{datetime.now()}:{response.url}')
        if self.dashboard_type == 'activereport':
            if '/render' in response.url or ('preview_info' in response.url and response.status != 200):
                content = await response.text()
                content_obj = json.loads(content)
                if not content_obj.get('status', False) or content_obj.get('status',
                                                                           False) == 'Failed' or content_obj.get('code',
                                                                                                                 False) == '400':
                    is_success = 0
                    error_msg = content
                self.chart_data_list.append(
                    {"chart_check_id": seq_id(), "flow_id": self.flow_id, "task_id": self.task_id,
                     "dashboard_id": self.dashboard_id, "chart_id": self.dashboard_id, "is_success": is_success,
                     "error_msg": error_msg, "check_type": self.check_type})
            return

        if 'dashboard_chart/chart/data' not in response.url and 'dashboard_chart/chart/get_total' not in response.url:
            return
        content = await response.text()
        print(response.url)
        print(content)
        content_obj = json.loads(content)
        if not content_obj.get('data') or not isinstance(content_obj.get('data'), dict):
            self.get_data_empty_count += 1
            return
        for key, data in content_obj.get('data').items():
            is_success = 1
            error_msg = ''
            if data.get('execute_status') != 200:
                is_success = 0
                error_msg = data.get('msg')
            self.chart_data_list.append({"chart_check_id": seq_id(), "flow_id": self.flow_id, "task_id": self.task_id,
                                         "dashboard_id": self.dashboard_id, "chart_id": key, "is_success": is_success,
                                         "error_msg": error_msg, "check_type": self.check_type})

    def update_task_data(self, data):
        with self.context.get_project_db() as db:
            db.update('dap_bi_shuxin15_upgrade_dashboard_check_task', data,
                      {'flow_id': self.context.flow_id})

    def add_dashboard_chart_check(self, data):
        """
        报告截图写入租户库
        """
        with self.context.get_project_db() as db:
            fields = ['chart_check_id', 'flow_id', 'task_id', 'dashboard_id', 'chart_id', 'check_type', 'is_success',
                      'error_msg']
            db.insert_multi_data('dap_bi_shuxin15_upgrade_dashboard_chart_check_task', data, fields)

    @staticmethod
    def now_date():
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def kill_crawler(self):
        cmd = 'ps -ef | grep chromium'
        lines = os.popen(cmd)
        for line in lines:
            if line.find('grep mx_skyfile_serv') != -1:
                continue
            vars_line = line.split()
            pid = vars_line[1]  # get pid
            proc = ''.join(vars_line[7:])  # get proc description 1
            proc = proc if len(proc) > 20 else proc[:20]
            out = os.system('kill -9 ' + pid)
            print(f'kill <{pid}, {out}> - ' + proc)


def generate_sso_login_url(url, payload):
    """
    生成DMP标准单点登录连接
    :param payload:
    :return:
    """
    jwt_secret = '0UZR4h#@'
    jwt_alg = 'HS256'
    parsed_uri = urlparse(url)
    domain = "{uri.scheme}://{uri.netloc}/".format(uri=parsed_uri)
    login_template = domain + 'api/user/sso/login?_from=oa&access_token=%s'
    payload['exp'] = int(time.time()) + 3600
    # 开发环境 版本不对导致需要多一次encde
    access_token = base64.b64encode(jwt.encode(payload, jwt_secret, jwt_alg)).decode('utf-8')
    return login_template % access_token


project_code = 'ompgczgcszhb'


def get_exec_list():
    sql = ' select *  from dap_bi_shuxin15_upgrade_dashboard_check_task where  status=0  '
    context = FlowContext(project_code, '')

    with context.get_project_db() as db:
        return db.query(sql)


if __name__ == '__main__':
    flow_id_list = get_exec_list()
    for task in flow_id_list:
        flow_id = task.get('flow_id')
        flow_instance_id = ''
        context = FlowContext(project_code, flow_id)
        node = CheckDashboard(context)
        node.execution()
