#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""

    <NAME_EMAIL> on 2017/8/21.
"""
import os
import io
from abc import ABCMeta, abstractmethod

from oss2.api import Bucket
from oss2.auth import Auth
from oss2.exceptions import OssError
from minio import Minio
from minio.error import InvalidResponseError, MinioException
from urllib.parse import urlparse, unquote
from components import config
from components.errors import UserError
from components.strings import seq_id


class BaseOSS(metaclass=ABCMeta):
    """
    存储对象服务基类
    """

    def url_to_obj_key(self, oss_file_url, bucket=None):
        """
        从OSS URL地址获取object key
        :param str oss_file_url:
        :return:
        """
        if bucket:
            start_index = oss_file_url.find(bucket + '/')
            # 如果找到了，返回从起始值开始到结束的部分
            if start_index != -1:
                return (oss_file_url[start_index:]).lstrip('/')
        return urlparse(oss_file_url).path.lstrip('/')


    @staticmethod
    def validate_file_extension(file_name, allow_extensions):
        if not file_name or not allow_extensions:
            return
        extension = os.path.splitext(os.path.split(file_name)[-1])[-1]
        if extension not in allow_extensions:
            raise UserError(message='只允许的文件类型为:' + ','.join(allow_extensions))

    @abstractmethod
    def upload(self, file, root=None, file_name=None, max_size=None, allow_extensions=None, **kwargs):
        raise NotImplementedError('Not implemented!')

    @abstractmethod
    def download_file_list(self, root, local_dir, max_keys=None):
        raise NotImplementedError('Not implemented!')

    @abstractmethod
    def object_list(self, root, max_keys=None):
        raise NotImplementedError('Not implemented!')

    @abstractmethod
    def delete_file_list(self, root, max_keys=None):
        raise NotImplementedError('Not implemented!')

    @abstractmethod
    def get_object(self, key, is_url=None):
        raise NotImplementedError('Not implemented!')

    @abstractmethod
    def delete(self, key, is_url=None):
        raise NotImplementedError('Not implemented!')


class OSSFileProxy:
    def __init__(self, service=None):
        self.service = service or config.get('OSS_Config.service')
        self._auth = None
        self._bucket = None
        self.proxy = None
        if self.service == 'OSS':
            self.proxy = OSSFile()
        elif self.service.upper() in ['MINIO', 'OBS']:
            self.proxy = MinioService()
        else:
            raise UserError("配置不存在")

    @property
    def auth(self):
        return self.proxy.auth

    @property
    def bucket(self):
        return self.proxy.bucket

    def upload(self, file, root=None, file_name=None, max_size=None, allow_extensions=None, **kwargs):
        """
        上传文件
        :param key:
        :param object|io.BufferedReader file:
        :param str root:
        :param str file_name:
        :param int max_size:
        :param list allow_extensions:
        :param str private:
        :return str: file url
        """
        return self.proxy.upload(
            file,
            root=root,
            file_name=file_name,
            max_size=max_size,
            allow_extensions=allow_extensions,
            **kwargs
        )

    @staticmethod
    def validate_file_extension(file_name, allow_extensions):
        if not file_name or not allow_extensions:
            return
        extension = os.path.splitext(os.path.split(file_name)[-1])[-1]
        if extension not in allow_extensions:
            raise UserError(message='只允许的文件类型为:' + ','.join(allow_extensions))

    def delete(self, key, is_url=None):
        """
        删除文件
        :param str key: object_key or oss_file_url
        :param bool is_url:
        :return:
        """
        if is_url:
            key = unquote(key)
        return self.proxy.delete(key, is_url=is_url)

    def object_list(self, root=None, max_keys=None):
        return self.proxy.object_list(root=root, max_keys=max_keys)

    @staticmethod
    def url_to_obj_key(oss_file_url):
        """
        从OSS URL地址获取object key
        :param str oss_file_url:
        :return:
        """
        return urlparse(oss_file_url).path.lstrip('/')

    def download_file_list(self, root, local_dir, max_keys=None):
        """
        批量下载文件到本地
        :param root:
        :param local_dir:
        :param max_keys:
        :return:
        """
        return self.proxy.download_file_list(root, local_dir, max_keys=max_keys)

    def delete_file_list(self, root, max_keys=None):
        """
        删除文件列表
        :param root:
        :param max_keys:
        :return:
        """
        return self.proxy.delete_file_list(root, max_keys=max_keys)

    def get_object(self, key, is_url=None):
        """get oss object

        Args:
            key (string): url or key
            is_url (bool, optional): Defaults to None. if url

        Returns:
            oss2.models.GetObjectResult:
        """
        if is_url:
            key = unquote(key)
        return self.proxy.get_object(key, is_url=is_url)


class OSSFile(BaseOSS):
    def __init__(self, access_key_id=None, access_key_secret=None, bucket=None, endpoint=None):
        super(OSSFile, self).__init__()
        self.access_key_id = access_key_id or config.get('OSS.access_key_id')
        self.access_key_secret = access_key_secret or config.get('OSS.access_key_secret')
        self.bucket_name = bucket or config.get('OSS.bucket')
        self.endpoint = endpoint or config.get('OSS.endpoint')
        self._auth = None
        self._bucket = None

    @property
    def auth(self):
        if self._auth:
            return self._auth
        self._auth = Auth(self.access_key_id, self.access_key_secret)
        return self._auth

    @property
    def bucket(self):
        if self._bucket:
            return self._bucket
        self._bucket = Bucket(self.auth, self.endpoint, self.bucket_name)
        return self._bucket

    def upload(self, file, root=None, file_name=None, max_size=None, allow_extensions=None, **kwargs):
        """
        上传文件
        :param key:
        :param object|io.BufferedReader file:
        :param str root:
        :param str file_name:
        :param int max_size:
        :param list allow_extensions:
        :param str private:
        :return str: file url
        """
        key = kwargs.get('key')
        _file_name = ''
        if not file_name:
            file_name = seq_id() + os.path.splitext(os.path.split(_file_name or file.name)[-1])[-1]
        self.validate_file_extension(file_name, allow_extensions)
        if not key:
            key = ('%s/%s' % (root or '', file_name)).strip('/')
        try:
            data = file.read()
            if max_size and len(data) > max_size:
                raise UserError(message="文件大小超过{}M限制".format(max_size / (1024 * 1024)))
            headers = {}
            if kwargs.get('private'):
                headers["x-oss-object-acl"] = "private"
            put_obj_result = self.bucket.put_object(key, data, headers=headers)
        except OssError as e:
            raise UserError(message='上传文件失败：' + e.message)
        if put_obj_result.status == 200:
            return unquote(put_obj_result.resp.response.url)
        raise UserError(message='文件上传失败')

    @staticmethod
    def validate_file_extension(file_name, allow_extensions):
        if not file_name or not allow_extensions:
            return
        extension = os.path.splitext(os.path.split(file_name)[-1])[-1]
        if extension not in allow_extensions:
            raise UserError(message='只允许的文件类型为:' + ','.join(allow_extensions))

    def delete(self, key, is_url=None):
        """
        删除文件
        :param str key: object_key or oss_file_url
        :param bool is_url:
        :return:
        """
        if not key:
            return False
        if is_url:
            key = self.url_to_obj_key(key)
        try:
            if not self.bucket.object_exists(key):
                return False
            self.bucket.delete_object(key)
            return True
        except OssError as e:
            raise UserError(message='删除文件失败：' + e.message)

    def object_list(self, root, max_keys=None):
        return self.bucket.list_objects(root, max_keys=max_keys or 100).object_list

    def url_to_obj_key(self, oss_file_url, bucket=None):
        """
        从OSS URL地址获取object key
        :param str oss_file_url:
        :return:
        """
        if bucket:
            start_index = oss_file_url.find(bucket + '/')
            # 如果找到了，返回从起始值开始到结束的部分
            if start_index != -1:
                return (oss_file_url[start_index:]).lstrip('/')
        return urlparse(oss_file_url).path.lstrip('/')


    def download_file_list(self, root, local_dir, max_keys=None):
        """
        批量下载文件到本地
        :param root:
        :param local_dir:
        :param max_keys:
        :return:
        """
        for file_info in self.object_list(root, max_keys):
            file_name = os.path.join(local_dir, os.path.split(file_info.key)[-1])
            if not self.bucket.get_object_meta(file_info.key).content_length:
                continue
            self.bucket.get_object_to_file(file_info.key, file_name)

    def delete_file_list(self, root, max_keys=None):
        """
        删除文件列表
        :param root:
        :param max_keys:
        :return:
        """
        key_list = [o.key for o in self.object_list(root, max_keys)]
        if not key_list:
            return False
        self.bucket.batch_delete_objects(key_list)
        return True

    def get_object(self, key, is_url=None):
        """get oss object

        Args:
            key (string): url or key
            is_url (bool, optional): Defaults to None. if url

        Returns:
            oss2.models.GetObjectResult:
        """

        if not key:
            return None
        if is_url:
            key = self.url_to_obj_key(key)
        if not self.bucket.object_exists(key):
            return None
        return self.bucket.get_object(key)


class MinioService(BaseOSS):
    def __init__(self, access_key_id=None, access_key_secret=None, bucket=None, endpoint=None):
        super(MinioService, self).__init__()
        self.access_key_id = access_key_id or config.get('Minio.access_key_id')
        self.access_key_secret = access_key_secret or config.get('Minio.access_key_secret')
        self.bucket_name = bucket or config.get('Minio.bucket')
        self.endpoint = endpoint or config.get('Minio.endpoint')
        self.secure = bool(self.endpoint.startswith('https'))
        self._auth = None
        self._bucket = None
        if not self.has_path(self.endpoint):
            self.minio = Minio(
                endpoint=self.endpoint.split('//')[1],
                access_key=self.access_key_id,
                secret_key=self.access_key_secret,
                secure=self.secure,
            )
            # 如果配置了内网地址，上传优先使用内网地址
        self.intranet_host = config.get('Minio.inside_endpoint', None)
        self.intranet_minio = None
        if self.intranet_host:
            intranet_secure = bool(self.intranet_host.startswith('https'))
            self.intranet_minio = Minio(
                endpoint=self.intranet_host.split('//')[1],
                access_key=self.access_key_id,
                secret_key=self.access_key_secret,
                secure=intranet_secure,
            )

    def has_path(self, url):
        # 解析 URL
        parsed_url = urlparse(url)
        # 检查路径部分是否存在
        return bool(parsed_url.path)

    @property
    def auth(self):
        return self._auth

    @property
    def bucket(self):
        if not self._get_intranet_client_first().bucket_exists(self.bucket_name):
            self._get_intranet_client_first().make_bucket(self.bucket_name)
        return self.bucket_name

    def _get_intranet_client_first(self):
        return self.intranet_minio if self.intranet_minio else self.minio

    def upload(self, file, root=None, file_name=None, max_size=None, allow_extensions=None, **kwargs):
        """
        上传文件
        :param key:
        :param object|io.BufferedReader file:
        :param str root:
        :param str file_name:
        :param int max_size:
        :param list allow_extensions:
        :param str private:
        :return str: file url
        """
        key = kwargs.get('key')
        _file_name = ''
        if not file_name:
            file_name = seq_id() + os.path.splitext(os.path.split(_file_name or file.name)[-1])[-1]
        self.validate_file_extension(file_name, allow_extensions)
        if not key:
            key = ('%s/%s' % (root or '', file_name)).strip('/')
        # 添加前缀目录
        key_path = config.get("Minio.root_path", "dp") or 'dp'
        key_path = key_path.strip('/')
        key = f'{key_path}/{key}'.strip('/')
        try:
            data = file.read()  # get bytes type data
            raw_file = io.BytesIO(data)  # minio accept BytesIO object
            if max_size and len(data) > max_size:
                raise UserError(message="文件大小超过{}M限制".format(max_size / (1024 * 1024)))
            headers = {}
            if kwargs.get('private'):
                headers["x-oss-object-acl"] = "private"
            self._get_intranet_client_first().put_object(self.bucket_name, key, raw_file, len(data))
            minio_url = "{endpint}/{bucket}/{key}".format(endpint=self.endpoint, bucket=self.bucket_name, key=key)
            unquote_url = unquote(minio_url)
            return unquote_url
        except MinioException as e:
            raise UserError(code=e.code, message='上传文件失败：' + str(e))
        raise UserError(message='文件上传失败')

    @staticmethod
    def validate_file_extension(file_name, allow_extensions):
        if not file_name or not allow_extensions:
            return
        extension = os.path.splitext(os.path.split(file_name)[-1])[-1]
        if extension not in allow_extensions:
            raise UserError(message='只允许的文件类型为:' + ','.join(allow_extensions))

    def parse_key(self, key):
        # 原oss逻辑root可能带有bucket_name, Minio自带bucket_name参数, 需要去掉
        key = key[1:] if key.startswith('/') else key
        if key and key.startswith(self.bucket_name):
            key = key.split('/', 1)[1] if '/' in key else key
        return key

    def object_list(self, root=None, max_keys=None):
        # 原oss逻辑root可能带有bucket_name, Minio自带bucket_name参数, 需要去掉
        root = self.parse_key(root)
        objects = self._get_intranet_client_first().list_objects_v2(self.bucket_name, prefix=root, recursive=True)
        return objects

    def download_file_list(self, root=None, local_dir=None, max_keys=None):
        """
        批量下载文件到本地
        :param root:
        :param local_dir:
        :param max_keys:
        :return:
        """
        object_infos = []
        for file_info in self.object_list(root=root):
            file_name = os.path.join(local_dir, os.path.split(file_info.object_name)[-1])
            # 保持和原oss逻辑一致, 如果文件大小为空则不下载
            if not file_info.size:
                continue
            object_info = self._get_intranet_client_first().fget_object(self.bucket_name, file_info.object_name, file_name)
            object_infos.append(object_info)
        return object_infos

    def delete_file_list(self, root, max_keys=None):
        """
        删除文件列表
        :param root:
        :param max_keys:
        :return:
        """
        key_list = [o.object_name for o in self.object_list(root, max_keys)]
        if not key_list:
            return False
        return self._get_intranet_client_first().remove_objects(self.bucket_name, key_list)

    def get_object(self, key, is_url=None):
        if not key:
            return None
        if is_url:
            key = self.url_to_obj_key(key, self.bucket_name)
            key = self.parse_key(key)
        try:
            return self._get_intranet_client_first().get_object(self.bucket_name, key)
        except MinioException as e:
            raise UserError(code=500, message=str(e))

    def delete(self, key, is_url=None):
        """
        删除文件
        :param str key: object_key or oss_file_url
        :param bool is_url:
        :return:
        """
        if not key:
            return False
        if is_url:
            key = self.url_to_obj_key(key, self.bucket_name)
            key = self.parse_key(key)
        try:
            if not self._get_intranet_client_first().get_object(self.bucket_name, object_name=key):
                return False
            self._get_intranet_client_first().remove_object(self.bucket_name, key)
            return True
        except MinioException as e:
            raise UserError(message='删除文件失败：' + e.message)
