#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on 2017年10月30日

@author: chenc04
"""
import json
from datetime import datetime, date
from decimal import Decimal
from functools import lru_cache

import redis as pyredis
import logging
from components import config
from components.constants import REDIS_SENTINEL, REDIS_SINGLE, REDIS_STANDALONE, REDIS_CLUSTER
from redis.connection import ConnectionError, TimeoutError
from redis import sentinel
from redis.cluster import RedisCluster


def operator_status(func):
    def gen_status(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
        except (ConnectionError, TimeoutError) as conn_error:
            logging.error("Redis无法连接，错误内容：" + str(conn_error))
            return None
        except Exception as e:
            logging.error("Redis操作错误，错误内容：" + str(e))
            return None
        return result

    return gen_status


g_redis_connection_pool = None
g_sentinel_obj = None
g_redis_cluster_pool = None


@lru_cache()
def redis_mode():
    try:
        if config.get('Redis.mode') == 'sentinel':
            return REDIS_SENTINEL
        if config.get('Redis.mode') == 'cluster':
            return REDIS_CLUSTER
        else:
            return REDIS_STANDALONE
    except Exception as e:
        logging.error(f"sentinel connect error: {e}")
        return REDIS_STANDALONE


class RedisSentinelConnection(object):

    def __init__(self, connect_timeout=None):
        self.connect_timeout = (connect_timeout or 3)*1000

    @staticmethod
    def __parse_address(address):
        if ':' not in address:
            return address
        # 按逗号分割字符串，得到多个 "ip:port" 格式的字符串
        ip_port_list = address.split(',')

        # 解析每个 "ip:port" 字符串，将其拆分为 IP 和端口
        result = []
        for item in ip_port_list:
            ip, port = item.split(':')
            result.append((ip, int(port)))  # 将端口转换为整数

        return result

    def connect(self, db=None):
        addresses = self.__parse_address(config.get("Redis.addresses"))
        db = int(config.get('Redis.db', 0)) if db is None else 0
        svc_name = config.get("Redis.svc_name") or 'mymaster'

        global g_sentinel_obj

        if g_sentinel_obj is None:
            g_sentinel_obj = sentinel.Sentinel(
                addresses,
                db=db,  # 数据库，默认为0
                socket_timeout=self.connect_timeout,  # 超时时间,单位为毫秒
                # username=config.get('Redis.username') or '',
                password=config.get('Redis.password')
            )
        master_conn = g_sentinel_obj.master_for(svc_name, socket_timeout=self.connect_timeout)
        slave_conn = g_sentinel_obj.slave_for(svc_name, socket_timeout=self.connect_timeout)

        return master_conn, slave_conn


class RedisStandalonePoolConnection(object):

    def __init__(self, connect_timeout=None):
        self.connect_timeout = connect_timeout

    def connect(self, db=None):
        db = int(config.get('Redis.db', 0)) if db is None else 0
        global g_redis_connection_pool
        if g_redis_connection_pool is None:
            g_redis_connection_pool = pyredis.ConnectionPool.from_url(
                url='redis://{username}:{password}@{host}:{port}/{db}'.format(
                    host=config.get("Redis.host"),
                    port=config.get('Redis.port'),
                    username=config.get('Redis.username') or '',
                    db=db,
                    password=config.get('Redis.password')
                ),
                socket_timeout=self.connect_timeout,
                max_connections=int(config.get('Redis.max_connections', 100) or 100),
            )
        return pyredis.StrictRedis(connection_pool=g_redis_connection_pool)


class RedisSingleConnection(object):
    def __init__(self, connect_timeout=None):
        self.connect_timeout = connect_timeout

    def connect(self, db=None):
        return pyredis.StrictRedis.from_url(
            url='redis://{username}:{password}@{host}:{port}/{db}'.format(
                host=config.get('Redis.host'),
                port=config.get('Redis.port'),
                username=config.get('Redis.username') or '',
                db=db,
                password=config.get('Redis.password') or ''
            ),
            socket_timeout=self.connect_timeout
        )


class RedisClusterConnection(object):
    def __init__(self, connect_timeout=None):
        self.connect_timeout = connect_timeout

    def connect(self, db=None):
        host = config.get("Redis.host")
        port = int(config.get('Redis.port'))
        # db = int(config.get('Redis.db', 0)) if db is None else 0

        return RedisCluster(
            host=host,
            port=port,
            username=config.get('Redis.username') or '',
            socket_timeout=self.connect_timeout,
            max_connections_per_node=True,
            max_connections=int(config.get('Redis.max_connections', 100) or 100),
            skip_full_coverage_check=True
        )


class RedisConnectionFactory(object):

    def __init__(self, connect_timeout=None):
        self.connect_timeout = connect_timeout or 10

    def create_connection(self, mode=None, db=None):
        if not mode:
            mode = redis_mode()
        if mode == REDIS_SENTINEL:
            connection = RedisSentinelConnection
        elif mode == REDIS_SINGLE:
            connection = RedisSingleConnection
        elif mode == REDIS_CLUSTER:
            connection = RedisClusterConnection
        else:
            connection = RedisStandalonePoolConnection
        return connection(self.connect_timeout).connect(db=db)


class RedisCache(object):
    def __init__(self, key_prefix=None, connect_timeout=None, mode=None):
        self.key_prefix = f'{config.get("Redis.prefix", "dp")}:'
        if key_prefix:
            self.key_prefix = f'{self.key_prefix}{key_prefix}:'
        self.connect_timeout = connect_timeout or 3

        factory = RedisConnectionFactory(connect_timeout)
        conns = factory.create_connection(mode=mode)

        if isinstance(conns, tuple):
            self._master_conn, self._slave_conn = conns[0], conns[-1]
        else:
            self._master_conn = self._slave_conn = conns

        self._connection = self._master_conn

    @operator_status
    def set_data(self, key, value):
        return self._master_conn.set(key, json.dumps(value))

    @operator_status
    def get_data(self, key):
        payload = self._slave_conn.get(key)
        if payload:
            return json.loads(payload.decode("UTF-8"))
        else:
            return None

    @operator_status
    def del_data(self, key):
        return self._master_conn.delete(key)

    @operator_status
    def keys(self):
        return self._slave_conn.keys()


    def _raw_key_exist(self, raw_key):
        return self._slave_conn.exists(raw_key)

    def _wrapper_key(self, key):
        if not key:
            raise ValueError('key')

        return '%s%s' % (self.key_prefix, key) if self.key_prefix else key

    @staticmethod
    def _dumps(value):
        return json.dumps(value, cls=ObjectEncoder)

    def _tran_value(self, value):
        if isinstance(value, (dict, list)):
            value = self._dumps(value)
        elif value is True:
            value = str(value)
        elif not isinstance(value, bytes) and not isinstance(value, str) and not isinstance(value, (int, float)):
            value = str(value)
        return value

    def set(self, key, value, time):
        if value is None or value is False:
            return
        value = self._tran_value(value)
        key = self._wrapper_key(key)
        return self._master_conn.setex(key, time=time, value=value)

    def get(self, key):
        key = self._wrapper_key(key)
        return self._slave_conn.get(key)


redis_cache = RedisCache()


class ObjectEncoder(json.JSONEncoder):
    def default(self, o):  # pylint: disable=method-hidden
        if isinstance(o, Decimal):
            return float(o)
        elif isinstance(o, datetime):
            return o.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(o, date):
            return o.strftime('%Y-%m-%d')
        else:
            return json.JSONEncoder.default(self, o)
