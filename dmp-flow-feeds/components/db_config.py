from .repository import SimpleMysql


def __get_db(current_config):
    return SimpleMysql(
        host=current_config.get('DB.host'),
        port=int(current_config.get('DB.port')),
        database=current_config.get('DB.database'),
        user=current_config.get('DB.user'),
        password=current_config.get('DB.password'),
        db_type=current_config.get('DB.db_type')
    )


def get_all_data(current_config):
    with __get_db(current_config) as db:
        config_list = db.query("select * from dap_p_global_config where app_name=%(app_name)s", {'app_name': 'dmp'})
    result = {}
    for config in config_list:
        key = config.get('key')
        value = config.get('value')
        default_value = config.get('default_value')
        group_name = config.get('group_name')

        config_key = f'{group_name}.{key}'
        if value is None:
            value = default_value
        result[config_key] = value
    return result
