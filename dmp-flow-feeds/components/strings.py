# -*- coding: utf-8 -*-
"""
    for description
"""
import datetime
import uuid
import time
import random
import csv
import io

import math


def _get_random_chars(char_length):
    chars = 'abcdef0123456789'
    i = 0
    res = ''
    while i < char_length:
        idx = math.floor(1 + random.random() * 16)
        res += chars[idx - 1:idx]
        i += 1
    return res


def seq_id():
    """
    获取有序GUID与 db中fn_newSeqId 算法保持一致
    :return:str
    """
    now = datetime.datetime.utcnow().timestamp()
    ticks = hex(round(now * 1000000))[2:]
    old_ticks = hex(round(now * 1000 + 62135625600000))[2:]
    return '%s-%s-%s%s-%s-%s' % (
        old_ticks[:8], old_ticks[8:12], ticks[10:13], _get_random_chars(1), _get_random_chars(4), _get_random_chars(12))


def uid(node=None, close_seq=None):
    """ GUID for db """

    timestamp = int(round(time.time() * 1000)) + 62135596800000
    max_uint = 4294967295

    high = int(timestamp / max_uint)
    low = int((timestamp % max_uint) - high)

    sl_high = str(hex(high))  # 十六进制字符串, 例如：0x39db
    sl_low = str(hex(low))

    guid = sl_high[2:] + sl_low[2:6] + '-' + sl_low[6:10]

    # 填充其余
    chars = 'abcdef0123456789'
    str_list = []
    i = 1
    while i <= 3:
        j = 1
        tmp = ''
        max = 12 if i == 3 else 4
        while j <= max:
            ind = random.randint(0, 15)
            tmp = tmp + chars[ind:(ind + 1)]
            j = j + 1

        str_list.append(tmp)
        i = i + 1

    guid = guid + '-' + '-'.join(str_list)
    return guid


def uid4():
    return str(uuid.uuid4())


def str_getcsv(string, delimiter=',', enclosure='"', escape='\\'):
    with io.StringIO(string) as f:
        reader = csv.reader(f, delimiter=delimiter, quotechar=enclosure, escapechar=escape)
        return next(reader)


def conver_to_utf8(string):
    return string.encode('utf-8')


def is_number(string):
    """
    判断字符串是否是数字
    :param s: 字符串，或还有数字字符串,'abc' 或者 '32423423'
    :return: True or False
    """

    try:
        float(string)  # 将字符串转换成数字成功则返回True
        return True
    except ValueError:
        return False  # 如果出现异常则返回False
