import random

from components.nacos_utils.middleware_parser.base_parser import MiddlewareBaseParser
from components.nacos_utils.enums import ResourceUsedEnum, ResourceInstanceEnum
from components.model import BaseModel


class MqUsedConfigModel(BaseModel):
    __slots__ = ('instanceId', 'code', 'vhost')

    def __init__(self, **kwargs):
        self.instanceId = kwargs.get('instanceId')
        self.code = kwargs.get('code')
        self.vhost = kwargs.get('vhost')
        super().__init__(**kwargs)


class MqInstanceModel(BaseModel):
    __slots__ = ('id', 'name', 'host', 'username', 'password', 'type', 'port')

    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.name = kwargs.get('name')
        self.host = kwargs.get('host')
        self.username = kwargs.get('username')
        self.password = kwargs.get('password')
        self.type = kwargs.get('type')
        self.port = kwargs.get('port')
        self.addresses = kwargs.get('addresses')

        super().__init__(**kwargs)


class MqParser(MiddlewareBaseParser):

    def __init__(self, config_data):
        super().__init__(config_data)

    @staticmethod
    def __choice_address(address):
        # 按逗号分割字符串，得到多个 "ip:port" 格式的字符串
        ip_port_list = address.split(',')

        # 解析每个 "ip:port" 字符串，将其拆分为 IP 和端口
        result = []
        for item in ip_port_list:
            ip, port = item.split(':')
            result.append((ip, int(port)))  # 将端口转换为整数

        return random.choice(result)

    def parse(self):
        # 1. 获取资源配置
        used_config = self.get_used_config(ResourceUsedEnum.MQ.value)
        if not used_config:
            return {}
        used_config_model = MqUsedConfigModel(**used_config)
        # 2. 根据资源的实例ID获取实例配置
        instance_config = self.get_instance_config(ResourceInstanceEnum.MQ.value, used_config_model.instanceId)
        if not instance_config:
            return {}
        instance_config_model = MqInstanceModel(**instance_config)

        del self.config_data[ResourceUsedEnum.MQ.value]
        # 3. 将nacos配置映射到app.config配置
        host, port = self.__choice_address(instance_config_model.addresses)
        return {
            'RabbitMQ.host': host,
            'RabbitMQ.password': instance_config_model.password,
            'RabbitMQ.type': instance_config_model.type,
            'RabbitMQ.user': instance_config_model.username,
            'RabbitMQ.vhost': used_config_model.vhost,
            'RabbitMQ.port': port
        }
