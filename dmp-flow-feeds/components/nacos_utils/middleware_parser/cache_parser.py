from components.nacos_utils.middleware_parser.base_parser import MiddlewareBaseParser
from components.nacos_utils.enums import ResourceUsedEnum, ResourceInstanceEnum, RedisMode
from components.model import BaseModel


class CacheUsedConfigModel(BaseModel):
    __slots__ = ('instanceId', 'code', 'prefix', 'dbIndex')

    def __init__(self, **kwargs):
        self.instanceId = kwargs.get('instanceId')
        self.code = kwargs.get('code')
        self.prefix = kwargs.get('prefix')
        self.dbIndex = kwargs.get('dbIndex')
        super().__init__(**kwargs)


class CacheInstanceModel(BaseModel):
    __slots__ = ('id', 'name', 'host', 'username', 'password', 'type', 'mode', 'masterName')

    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.name = kwargs.get('name')
        self.host = kwargs.get('host')
        self.username = kwargs.get('username')
        self.password = kwargs.get('password')
        self.type = kwargs.get('type')
        self.mode = kwargs.get('mode')
        self.addresses = kwargs.get('addresses')
        self.masterName = kwargs.get('masterName')
        super().__init__(**kwargs)


class CacheParser(MiddlewareBaseParser):

    def __init__(self, config_data):
        super().__init__(config_data)

    def parse(self):
        # 1. 获取资源配置
        used_config = self.get_used_config(ResourceUsedEnum.CACHE.value)
        if not used_config:
            return {}
        used_config_model = CacheUsedConfigModel(**used_config)
        # 2. 根据资源的实例ID获取实例配置
        instance_config = self.get_instance_config(ResourceInstanceEnum.CACHE.value, used_config_model.instanceId)
        if not instance_config:
            return {}
        instance_config_model = CacheInstanceModel(**instance_config)
        # 3. 将nacos配置映射到app.config配置
        if instance_config_model.mode == RedisMode.STANDALONE.value:
            host, port = instance_config_model.addresses.split(':')
        else:
            host = instance_config_model.addresses
            port = None
        del self.config_data[ResourceUsedEnum.CACHE.value]
        return {
            'Redis.host': host,
            'Redis.addresses': instance_config_model.addresses,
            'Redis.password': instance_config_model.password,
            'Redis.db': used_config_model.dbIndex,
            'Redis.celery_db': used_config_model.dbIndex,
            'Redis.admin_celery_db': used_config_model.dbIndex,
            'Redis.username': instance_config_model.username,
            'Redis.prefix': used_config_model.prefix,
            'Redis.mode': instance_config_model.mode,
            'Redis.port': port,
            'Redis.svc_name': instance_config_model.masterName,
        }
