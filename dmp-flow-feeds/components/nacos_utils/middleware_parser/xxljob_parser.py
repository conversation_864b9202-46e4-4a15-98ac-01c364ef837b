from components.nacos_utils.middleware_parser.base_parser import MiddlewareBaseParser
from components.nacos_utils.enums import ResourceUsedEnum, ResourceInstanceEnum
from components.model import BaseModel


class XXLJobUsedConfigModel(BaseModel):
    __slots__ = ('instanceId', 'code', 'executor')

    def __init__(self, **kwargs):
        self.instanceId = kwargs.get('instanceId')
        self.code = kwargs.get('code')
        self.executor = kwargs.get('executor')
        super().__init__(**kwargs)


class XXLJobInstanceModel(BaseModel):
    __slots__ = ('id', 'name', 'host', 'port', 'username', 'password', 'accessToken')

    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.name = kwargs.get('name')
        self.host = kwargs.get('host')
        self.port = kwargs.get('port')
        self.username = kwargs.get('username')
        self.password = kwargs.get('password')
        self.accessToken = kwargs.get('accessToken')
        super().__init__(**kwargs)


class XXLJobParser(MiddlewareBaseParser):

    def __init__(self, config_data):
        super().__init__(config_data)

    def parse(self):
        # 1. 获取资源配置
        used_config = self.get_used_config(ResourceUsedEnum.SCHEDULER.value)
        if not used_config:
            return {}
        used_config_model = XXLJobUsedConfigModel(**used_config)
        # 2. 根据资源的实例ID获取实例配置
        instance_config = self.get_instance_config(ResourceInstanceEnum.SCHEDULER.value, used_config_model.instanceId)
        if not instance_config:
            return {}
        instance_config_model = XXLJobInstanceModel(**instance_config)

        del self.config_data[ResourceUsedEnum.SCHEDULER.value]

        # 3. 将nacos配置映射到app.config配置
        return {
            'XXL-JOB.default_group_appname': used_config_model.executor,
            'XXL-JOB.access_token': instance_config_model.accessToken,
            'XXL-JOB.admin_host': instance_config_model.host,
            'XXL-JOB.admin_username': instance_config_model.username,
            'XXL-JOB.admin_password': instance_config_model.password,
            'XXL-JOB.default_group_title': instance_config_model.name,
            'App.scheduler': 'xxl-job'
        }
