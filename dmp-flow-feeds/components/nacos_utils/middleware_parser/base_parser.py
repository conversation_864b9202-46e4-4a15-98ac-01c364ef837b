import json

from components.nacos_utils.enums import DbBusinessEnum, ResourceCode


class MiddlewareBaseParser(object):

    def __init__(self, config_data):
        self.config_data = config_data

    @staticmethod
    def loads(data):
        if isinstance(data, str):
            data = json.loads(data)
        return data

    def get_used_config(self, resource_used_enum, resource_code=ResourceCode.DP.value, tenant_code=None, db_business_enum=None):
        resource_used = self.config_data.get(resource_used_enum)
        if not resource_used:
            return {}
        resource_used = self.loads(resource_used)
        for item in resource_used.get('items'):
            if tenant_code:
                if (
                        item.get('tenantCode') == tenant_code
                        and item.get('code') == resource_code
                        and item.get('category') == db_business_enum
                ):
                    return item
            elif db_business_enum and item.get('code') == resource_code and item.get('category') == db_business_enum:
                return item
            elif item.get('code') == resource_code and not db_business_enum:
                return item
        return {}

    def get_instance_config(self, resource_instance_enum, instance_id):
        resource_instance = self.config_data.get(resource_instance_enum)
        if not resource_instance:
            return {}
        resource_instance = self.loads(resource_instance)
        for item in resource_instance.get('items'):
            if item.get('id') == instance_id:
                return item
        return {}

    def parse(self):
        return {}
