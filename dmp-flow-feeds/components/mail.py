#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2017/5/13.
"""
import re
import smtplib
from base64 import b64encode
from email import encoders
from email.mime.base import MIMEBase
from email.mime.multipart import MIMEMult<PERSON>art
from urllib import request
from email.header import Header
from email.mime.image import MIMEImage
from email.mime.text import MIMEText
from email.utils import formataddr, parseaddr

import htmlmin

from components import config, auth_util, repository
from components.errors import UserError
from components.ingrate_platform import IngratePlatformApi


class Mail(object):
    __slots__ = ['subject', 'body', 'sender', 'receiver']

    def __init__(self, **kwargs):
        self.subject = kwargs.get('subject')
        self.body = kwargs.get('body')
        self.sender = kwargs.get('from', MailContact(**{'name': kwargs.get('addresser_name')}))
        self.receiver = kwargs.get('receiver', [])


class MailContact(object):
    __slots__ = ['name', 'mail']

    def __init__(self, **kwargs):
        self.name = kwargs.get('name', config.get('Email.name'))
        self.mail = kwargs.get('mail', config.get('Email.account'))

    def __str__(self):
        return '%s<%s>' % (self.name, self.mail)


def _format_email_address(s):
    name, address = parseaddr(s)
    return formataddr((Header(name, 'utf-8').encode(), address))

def _get_attachment_base64(at):
    if not at:
        return {}
    encoders.encode_base64(at)
    payload = at.get_payload(decode=True)
    return {'fileName': at.get_filename(), 'fileBase64': b64encode(payload).decode("utf-8")}

def _find_failed_email(send_list):
    failures = {}
    for send_res in send_list:
        # 2：成功
        if send_res.get('sendStatus') == 2:
            continue
        failures[send_res.get('receiverMail')] = send_res.get('sendMessage')
    return failures


def _get_tenant_email_config(code):
    if not code:
        return None
    sql = 'select * from dap_bi_environment_common_setting where item=%(item)s and category=%(category)s'
    config_sender = None
    with repository.get_master_db() as db:
        db.connect()
        config_sender = db.query_one(sql, {'item': code, 'category': 'email_sender'})

    #如果没有配置使用默认租户myykj
    #如果配置了但是没有value表示走数见邮件
    #如果配置了租户则使用对应租户走公共服务发送邮件
    if not config_sender:
        return config.get('Email.public_email_service_tenant_code')
    sender = config_sender.get('value')
    if sender:
        return sender
    return None


def send(mail, code, subtype='plain', msg_image=None, msg_attachment=None, cover_flag=False):
    if auth_util.is_env_enable_skyline_auth():
        mip_send(mail, code, subtype, msg_image, msg_attachment, cover_flag)
    else:
        smtp_send(mail, subtype, msg_image, msg_attachment, cover_flag)

def mip_send(mail, code, subtype='plain', msg_image=None, msg_attachment=None, cover_flag=False):
    msg = MIMEMultipart('mixed')
    img_base64 = ''
    if msg_image:
        msg.attach(msg_image)
        img_base64 = 'data:image/png;base64,' + _get_attachment_base64(msg_image).get('fileBase64')
    if not cover_flag:
        mail.body = add_img(mail.body, msg)
    files = []
    if msg_attachment:
        if isinstance(msg_attachment, list):
            for at in msg_attachment:
                files.append(_get_attachment_base64(at))
        else:
            files = [_get_attachment_base64(msg_attachment)]
    if img_base64:
        mail.body = mail.body.replace('cid:img1', img_base64)
    mail.body = htmlmin.minify(mail.body, remove_empty_space=True, remove_comments=True)
    params = {
        'subject': mail.subject,
        'content': mail.body,
        'mode': 1,
        'receiverList': [{'receiverMail': rec.mail, 'receiverName': rec.name} for rec in mail.receiver],
        'fileList': files
    }
    res = IngratePlatformApi().send_email(params, code)
    send_list = res.get('sendResult', [])
    failures = _find_failed_email(send_list)
    return failures

def smtp_send(mail, subtype='plain', msg_image=None, msg_attachment=None, cover_flag=False):
    """
    发送邮件
    :param components.mail.Mail mail:
    :param str subtype:
    :return:
    """
    if not mail.subject:
        raise UserError(message='缺少邮件主题')
    if not mail.body:
        raise UserError(message='缺少邮件内容')
    if not mail.receiver or not isinstance(mail.receiver, list):
        raise UserError(message='缺少收件人')
    if not mail.sender:
        raise UserError(message='缺少发件人')
    try:
        msg = MIMEMultipart('mixed')
        if msg_image:
            msg.attach(msg_image)
        if msg_attachment:
            msg.attach(msg_attachment)
        if not cover_flag:
            mail.body = add_img(mail.body, msg)
        msg_text = MIMEText(mail.body, subtype, 'utf-8')
        msg.attach(msg_text)
        msg['From'] = _format_email_address(str(mail.sender))
        msg['To'] = ','.join([_format_email_address(str(t)) for t in mail.receiver if isinstance(t, MailContact)])
        msg['Subject'] = Header(mail.subject, 'utf-8').encode()
        server = config.get('Email.smtp_server')
        smtp_timeout = int(config.get('Email.smtp_timeout') or 30)
        port = int(config.get('Email.smtp_port'))
        use_ssl = int(config.get('Email.smtp_enable_ssl')) == 1
        server = smtplib.SMTP_SSL(server, port, timeout=smtp_timeout) if use_ssl else \
            smtplib.SMTP(server, port, timeout=smtp_timeout)
        server.login(mail.sender.mail, config.get('Email.password'))
        result = server.sendmail(mail.sender.mail, [t.mail for t in mail.receiver if isinstance(t, MailContact)],
                        msg.as_string())
        server.quit()
        return result
    except Exception as e:
        raise UserError(message='邮件发送失败：' + str(e))


def add_img(body, msg):
    """
    添加图片
    :param body:
    :param msg:
    :return:
    """
    img_url_data = re.findall('(http.*?\.jpg)|(http.*?\.png)', body, flags=re.I)
    num = 1
    for img_url in img_url_data:
        for i in range(len(img_url)):
            if img_url[i]:
                msg_image = MIMEImage(request.urlopen(img_url[i]).read())
                msg_image.add_header('Content-ID', 'image' + str(num))
                msg.attach(msg_image)
                body = body.replace(img_url[i], 'cid:image' + str(num))
                num += 1
    return body


def replace_content(content, replace_dict):
    """
    替换邮件内容
    :param content:
    :param replace_dict:
    :return:
    """
    for k, v in replace_dict.items():
        content = content.replace(k, v)
    return content
