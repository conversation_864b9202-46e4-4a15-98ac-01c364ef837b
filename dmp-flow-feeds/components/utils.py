import base64
import datetime
import hmac
from datetime import timedelta

import jwt
from alibabacloud_openapi_util.sm3 import Sm3
from gmssl import sm4
from jwt import register_algorithm
from jwt.algorithms import HMACAlgorithm
from loguru import logger

from components import auth_util


def sm4_encode(key: str, value: bytes):
    """
    原文文本编码:UTF-8
    模式: CBC
    IV: 取密钥的前16位
    填充模式: PKCS#7
    块大小(密钥长度): 128位(16字节)
    :param key:
    :param value:
    :return:
    """
    iv = key[:16].encode()  # bytes类型
    crypt_sm4 = sm4.CryptSM4()
    crypt_sm4.set_key(key, sm4.SM4_ENCRYPT)
    return crypt_sm4.crypt_cbc(iv, value)  # bytes类型


def sm4_decode(key: str, value: bytes):
    """
    原文文本编码:UTF-8
    模式: CBC
    IV: 取密钥的前16位
    填充模式: PKCS#7
    块大小(密钥长度): 128位(16字节)
    :param key:
    :param value:
    :return:
    """
    iv = key[:16].encode()  # bytes类型
    crypt_sm4 = sm4.CryptSM4()
    crypt_sm4.set_key(key, sm4.SM4_DECRYPT)
    return crypt_sm4.crypt_cbc(iv, value)  # bytes类型


def sm3_encode(key, src_data):
    try:
        key = fill_padding(key.decode())
        key = base64.urlsafe_b64decode(key)
        mac = hmac.new(key, digestmod=Sm3)
        mac.update(src_data)
        return mac.digest()
    except Exception as e:
        logger.info("Failed to encrypt with key: %s", e)
        raise RuntimeError("Exception occurred during encryption with key")


def fill_padding(s):
    miss = 4 - len(s) % 4
    if miss:
        s += '=' * miss
    return s


class SM4Algorithm(HMACAlgorithm):

    def __init__(self, hash_alg=None):
        super().__init__(hash_alg)

    def sign(self, msg, key):
        return sm4_encode(key, msg)

    def verify(self, msg, key, sig):
        return sig == self.sign(msg, key)


class SM3Algorithm(HMACAlgorithm):

    def __init__(self, hash_alg=None):
        super().__init__(hash_alg)

    def sign(self, msg, key):
        return sm3_encode(key, msg)

    def verify(self, msg, key, sig):
        print(f'sig={base64.b64encode(sig)}')
        return sig == self.sign(msg, key)


def jwt_patch():
    try:
        register_algorithm('SM4', SM4Algorithm())
        register_algorithm('SM3', SM3Algorithm())
    except Exception as e:
        logger.error(f'注册jwt算法:{str(e)}')


if __name__ == '__main__':
    import os

    os.environ['prometheus_multiproc_dir'] = '/tmp'
    jwt_patch()
    s = 'eyJrdHkiOiJvY3QiLCJ0eXAiOiJKV1QiLCJhbGciOiJTTTMiLCJraWQiOiIxNjk5NDI0ODg5In0.eyJzdWIiOiJ3b3JrYmVuY2giLCJuYmYiOjE3MDU1NTkzOTUsImV4cCI6MjA2NTU2Mjk5NSwianRpIjoiM2ExMDJkYTYtZDNiYi1lNWRhLTZhYjUtZjU1MWY1MzJhMmZiIn0.7HtOl7NljKoghdIlLzY78bMbdDG3HC2jctrDF7tC0ds'
    k = 'ByM1SysPpbyDfgZld3umj1qzKObwVMkoqQ-EstJQLr_T-1qS0gZH75aKtMN3Yj0iPS4hcgUuTwjAzZr1Z9CAow'
    # s = jwt.decode(s, k, algorithms='SM3')
    # print(s)

    payload = {
        'sub': 'dmp',
        'nbf': int((datetime.datetime.now() - timedelta(hours=1)).timestamp()),
        'exp': int((datetime.datetime.now() + timedelta(hours=1)).timestamp()),
        'jti': 'aaaaaa',
    }
    headers = {'kid': '1699424889', 'k': k}
    token = jwt.encode(payload, headers=headers, key=k, algorithm='SM3')
    print(token)
    decode = jwt.decode(token, k, algorithms='SM3')
    print(decode)

    t = auth_util.gen_auth_token({'a':'12312321321321321'})
    print(t)
    d = auth_util.verify_token(t)
    print(d)
