#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    <NAME_EMAIL> on 2018/4/17.
"""
import requests
import logging
import functools
from requests.exceptions import RequestException
from components.errors import UserError
from components.constants import OPENAPI_HOST

MESSAGE_URL = {
    '数据源': {'required_keys': ['id', 'source_type'], 'base_url': '/datawork/data-integration/edit/'},
    '数据集': {'required_keys': ['id', 'name'], 'base_url': '/flow/ops-instance/'},
    '订阅': {'required_keys': ['id'], 'base_url': '/feeds/detail/'}
}


def singleton(cls):
    instances = {}

    def _singleton(*args, **kargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kargs)
        return instances[cls]
    return _singleton


@singleton
class Message(object):

    """
    调用方式

    1）系统消息
    Message().system_message(project_code, message_dict)
    message_dict示例：{
        'source': '数据源', # 消息来源模块（必填）目前有数据源、数据集、单图、报告、多屏、应用门户、订阅
        'title': '标题', # 消息标题内容（必填）
        'url'： '/flow/ops-instance/xxxx/xxxx'  # 消息处理url(可选)
        'level': '普通'  # 消息等级(可选) 目前有 非常紧急、紧急、普通、提示
    }


    2) 运营消息
    Message().operate_message(project_code, message_dict)
    message_dict示例：{
        'source': '组件中心', # 消息来源模块（必填）目前有组件中心、模板中心、版本更新、通知
        'title': '标题', # 消息标题内容（必填）
        'url'： '/flow/ops-instance/xxxx/xxxx'  # 消息处理url(可选)
        'level': '普通'  # 消息等级(可选) 目前有 非常紧急、紧急、普通、提示
    }
    ...

    """

    def __init__(self, host=None):
        """
        :param str host:接口地址
        """
        self.url = '%s/message/add' % (host if host else OPENAPI_HOST)

    @staticmethod
    def check(message_dict):
        """
        检查参数
        :param message_dict:
        :return:
        """
        required_keys = ['source', 'type', 'title']
        option_keys = ['url', 'source_id', 'level']
        # 检查必填项
        _message = {}
        for key in required_keys:
            if key not in message_dict.keys():
                raise UserError(message='添加消息{}为必填项'.format(key))
            _message[key] = message_dict.pop(key)
        for key in option_keys:
            try:
                _message[key] = message_dict.pop(key)
            except KeyError:
                pass
        return _message

    def system_data_set_message(self, project_code, message_dict):
        """
        系统消息中数据集的消息调用接口，主要的意义减少参数传递，方便调用
        :param project_code:
        :param message_dict:
        :return:
        """
        if not message_dict.get('id') or not message_dict.get('name'):
            raise UserError(message='添加系统消息id,name为必填项')
        message_dict['type'] = '系统消息'
        message_dict['source'] = '数据集'
        message_dict['url'] = '/flow/ops-instance/{}/{}'.format(message_dict.pop('id'), message_dict.pop('name'))
        return self.api_request(project_code, message_dict)

    def system_data_source_message(self, project_code, message_dict):
        """
        系统消息中数据源的消息调用接口，主要的意义减少参数传递，方便调用
        :param project_code:
        :param message_dict:
        :return:
        """
        if not message_dict.get('id') or not message_dict.get('source_type'):
            raise UserError(message='添加系统消息中数据源消息id,source_type为必填项')
        message_dict['type'] = '系统消息'
        message_dict['source'] = '数据源'
        message_dict['url'] = '/datawork/data-integration/edit/{}/{}'.format(message_dict.pop('source_type').lower(),
                                                                        message_dict.pop('id'))
        return self.api_request(project_code, message_dict)

    def system_feed_message(self, project_code, message_dict):
        """
        系统消息中邮件订阅的消息调用接口，主要的意义减少参数传递，方便调用
        :param project_code:
        :param message_dict:
        :return:
        """
        message_dict['type'] = '系统消息'
        message_dict['source'] = '订阅'
        return self.api_request(project_code, message_dict)

    def add(self, project_code, message_dict):
        """
        添加消息
        :param project_code:
        :param message_dict:
        :return:
        """
        return self.api_request(project_code, message_dict)

    def system_message(self, project_code, message_dict):
        """
        系统消息中的消息调用接口，主要的意义减少参数传递，方便调用
        :param project_code:
        :param message_dict: {source, title, url}
        :return:
        """
        message_dict['type'] = '系统消息'
        return self.api_request(project_code, message_dict)

    def operate_message(self, project_code, message_dict):
        """
        运营消息中的消息调用接口，主要的意义减少参数传递，方便调用
        :param project_code:
        :param message_dict: {source, title, url}
        :return:
        """
        message_dict['type'] = '运营消息'
        return self.api_request(project_code, message_dict)

    def api_request(self, project_code, message_dict):
        """
        添加消息(请求API)
        :param project_code:
        :param message_dict: {source, type, title, url, source_id}
        :return:
        """
        _message = self.check(message_dict)

        headers = {'X-TENANT': project_code}
        try:
            response = requests.post(self.url, headers=headers, data=_message)
            if response.status_code != 200:
                logging.error({'url': self.url, 'error': response.reason})
                return None
            response.encoding = 'utf-8'
            return response.json().get('result')
        except RequestException as e:
            raise UserError(message='连接失败:' + str(e))

    @staticmethod
    def generate_url(source, type, message_dict):
        """
        生成 url
        :param source:
        :param type:
        :param message_dict:
        :return:
        """
        required_keys = MESSAGE_URL.get(source).get('required_keys')
        base_url = MESSAGE_URL.get(source).get('base_url')
        for key in required_keys:
            if not message_dict.get(key):
                raise Exception('{}中{}消息必须有{}参数'.format(type, source, key))
        if source == '数据源':
            path = '{}/{}'.format(message_dict.get('source_type').lower(), message_dict.get('id'))
        elif source == '数据集':
            path = '{}/{}'.format(message_dict.get('id'), message_dict.get('name'))
        else:
            path = message_dict.get('id')
        return '{}{}'.format(base_url, path)


# 主要是觉得类的链式调用太长，不利于阅读，重新封装
def send_message(project_code, message_dict, source=None, type=None):
    """
    发送消息
    :param project_code:
    :param message_dict:
    :param source:
    :param type:
    :return:
    """
    message = Message()
    if not message_dict.get('url'):
        message_dict['url'] = message.generate_url(source, type, message_dict)
    message_dict['type'] = type
    message_dict['source'] = source
    message.api_request(project_code, message_dict)


# 数据集消息
data_set_message = functools.partial(send_message, type='系统消息', source='数据集')

# 数据源消息
data_source_message = functools.partial(send_message, type='系统消息', source='数据源')

# 邮件订阅消息
feed_message = functools.partial(send_message, type='系统消息', source='订阅')

