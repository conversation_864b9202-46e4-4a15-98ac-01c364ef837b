from enum import Enum, unique

import logging

from components import auth_util, config
from components.enums import SkylineApps
from components.nacos_client import NacosClient

INSIDE_URL_SUFFIX = '.insideUrl'
URL_SUFFIX = '.url'


class AppHosts(object):

    @staticmethod
    def get(app:SkylineApps, inside=True):
        logging.info(f'app:{app}')
        if not app:
            return None
        enabled = auth_util.is_env_enable_skyline_auth()
        #如果未开启统一认证使用容器云配置
        if not enabled:
            mks_key = app.mks_key()
            if not mks_key:
                return ''
            return config.get(app.mks_key(), '')

        if inside:
            suffix = INSIDE_URL_SUFFIX
        else:
            suffix = URL_SUFFIX
        key = app.value + suffix
        val = NacosClient.get(key, '')
        return val


def get_cookie_path():
    url = AppHosts.get(SkylineApps.DP, False)
    url = url.rstrip('/')
    return '/dp' if url.endswith('/dp') else '/'

