# -*- coding: utf-8 -*-
"""
    for description
"""
import os
import logging
import datetime


def init_logging(file_name="app"):
    _level = 'INFO'
    _handlers = 'console'
    _fmt = '%(asctime)s %(filename)s[line:%(lineno)d]  %(exc_text)s %(message)s'
    _formatter = logging.Formatter(_fmt)
    _logger = logging.root
    _logger.setLevel(_level)

    _handlers = _handlers.split(',')

    if 'file' in _handlers:
        log_date = datetime.datetime.now().strftime("%Y-%m-%d")

        log_folder = os.path.join(os.path.join(os.path.realpath(
            os.path.join(os.path.dirname(os.path.realpath(__file__)), '../')), "runtime"), "log")

        log_path = os.path.join(log_folder, file_name + "-" + log_date + ".log")

        if not os.path.exists(log_folder):
            os.makedirs(log_folder)

        open(log_path, 'a+')

        _file_handler = logging.FileHandler(log_path, encoding="UTF-8")
        _file_handler.setLevel(_level)
        _file_handler.setFormatter(_formatter)
        _logger.addHandler(_file_handler)

    if 'console' in _handlers:
        _console_handler = logging.StreamHandler()
        _console_handler.setFormatter(_formatter)
        _console_handler.setLevel(_level)
        _logger.addHandler(_console_handler)

    logging.getLogger("pika").setLevel(logging.WARNING)