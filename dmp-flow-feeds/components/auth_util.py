import datetime
import hashlib
import json
import logging
from datetime import timedelta
import os

import jwt

from components.errors import UserError
from components.nacos_client import NacosClient
from components.strings import seq_id

AUTHORIZATION_KEY = 'my-api-authorization'
TENANT_KEY = 'x-my-tenantCode'
SYSTEM_SETTING_CATEGORY = 'skyline_auth'
SYSTEM_SETTING_ITEM = 'exclude_tenants'
CACHE_KEY = 'skyline_auth:exclude_tenants'

def get_jwt_secret():
    # 使用统一认证秘钥的md5
    k = NacosClient.get('appSecret')
    md5_hash = hashlib.md5()
    md5_hash.update(k.encode('utf-8'))
    k = md5_hash.hexdigest()
    return k

def get_auth_header(request):
    token = request.headers.get(AUTHORIZATION_KEY.upper())
    if not token:
        token = request.headers.get(AUTHORIZATION_KEY, '')
    return token

def is_enable_skyline_auth(code=None):
    # 当前环境是否开启了天际统一认证
    enabled = is_env_enable_skyline_auth()
    if enabled:
        if code:
            val = get_exclude_tenants()
            if val and code in val.split(','):
                return False
    return enabled

def get_exclude_tenants():
    # 获取需要排除的租户
    from components.redis import RedisCache
    from components.repository import get_db, get_master_db

    conn = RedisCache()
    val = conn.get(CACHE_KEY)
    if not val:
        with get_master_db() as db:
            val = db.query_scalar(
                'select `value` from `dap_bi_environment_common_setting` where `category` = %(category)s and `item` = %(item)s',
                {'category': 'skyline_auth', 'item': 'exclude_tenants'}
            )
        if val:
            conn.set(CACHE_KEY, val, 300)
    else:
        val = val.decode()
    return val

def is_env_enable_skyline_auth():
    from components import config
    # 当前环境是否开启了天际统一认证
    enabled = os.environ.get('SKYLINE_AUTH_ENABLE') or config.get('Skyline.skyline_auth_enable', False)
    enabled = enabled in [True, 'true']
    return enabled

def verify_token(token):
    #验证统一认证jwt
    header = jwt.get_unverified_header(token)
    kid = header.get('kid')
    if not kid:
        raise UserError(message='header中不包含kid')
    jwks = get_jwks()
    jwk = None
    for item in jwks:
        if kid == item['kid']:
            jwk = item
            break
    if not jwk:
        raise UserError(message=f'kid[{kid}]没有对应的jwk')
    try:
        payload = jwt.decode(jwt=token, key=jwk['k'], algorithms=jwk.get('alg', 'SM3'))
    except Exception as e:
        logging.error(f'应用认证解析jwt异常:{str(e)}')
        raise UserError(message='jwt解码失败')
    return payload


def get_jwks(code=None):
    #获取统一配置的jwk
    jwks = None
    if code:
        jwks = NacosClient.getByTenant('appSecret', code, '')
    if not jwks:
        jwks = NacosClient.get('appSecret')
    if not jwks:
        return None
    jwks = json.loads(jwks)
    jwks = sorted(jwks, key=lambda x: int(x.get("kid")), reverse=True)
    return jwks


def get_recently_jwk():
    # return {"kid":"1704953026","k":"3a100b02e5a9cd3pe8gx784tvvihjvot"}
    jwks = get_jwks()
    if not jwks:
        raise UserError(message='当前环境未配置jwk')
    return jwks[0]


def gen_auth_token(code = None, params: dict = {}):
    #生成统一认证jwt
    if not is_env_enable_skyline_auth():
        return None
    jwks = get_jwks(code)
    if not jwks:
        return None
    jwk = jwks[0]
    headers = {
        'kid': jwk.get('kid', ''),
    }
    params = params if params else {}
    payload = {
        'sub': 'dmp',
        'nbf': int((datetime.datetime.now() - timedelta(hours=1)).timestamp()),
        'exp': int((datetime.datetime.now() + timedelta(hours=1)).timestamp()),
        'jti': seq_id(),
        **params
    }
    token = jwt.encode(payload=payload, headers=headers, key=jwk['k'], algorithm=jwk.get('alg', 'SM3'))
    return token

