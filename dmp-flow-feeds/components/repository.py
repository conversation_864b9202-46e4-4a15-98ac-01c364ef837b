#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# pylint: skip-file
"""
    数据库基本操作
    <NAME_EMAIL> on 2017/3/14.
"""
import json
import logging
import re
from copy import deepcopy
import functools
import datetime
import time
from sqlalchemy import create_engine, text, NullPool
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import OperationalError, ProgrammingError, DatabaseError
from sqlalchemy.engine.row import RowMapping
from urllib.parse import quote
import builtins

import pymysql
from pymysql.constants import CR
from sshtunnel import BaseSSHTunnelForwarderError, SSHTunnelForwarder
from warnings import resetwarnings
from components import global_config
from components.errors import ServerError, InvalidArgumentError
from components.nacos_client import tenant_clean_db_get, tenant_db_get


def retry(tries=-1, delay=5):
    """
    tries: -1表示无限循环, 默认是无限循环
    """
    def decorate(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            _tries = tries
            while _tries:
                try:
                    return func(self, *args, **kwargs)
                except pymysql.err.OperationalError as e:
                    # 只有mysql服务异常的场景才重试
                    if e.args and e.args[0] in [CR.CR_CONNECTION_ERROR, CR.CR_CONN_HOST_ERROR, CR.CR_IPSOCK_ERROR,
                                                CR.CR_UNKNOWN_HOST, CR.CR_SERVER_GONE_ERROR]:
                        _tries -= 1
                        if not _tries:
                            raise e
                        logging.error(
                            f"MySQL connection host {self.host} failed and retry every {delay} seconds ...... err: {e}"
                        )
                        time.sleep(delay)
                        if func.__name__ != 'connect':
                            self.connect()
                    else:
                        raise e
        return wrapper
    return decorate


class SimpleMysql:
    conn = None
    cur = None

    def __init__(self, **kwargs):
        """ construct """
        self.host = kwargs.get("host", "localhost")
        self.port = kwargs.get("port", 3306)
        self.database = kwargs.get("database")
        self.user = kwargs.get('user')
        self.password = kwargs.get('password')

        self.keep_alive = kwargs.get("keep_alive", False)
        self.charset = kwargs.get("charset", "utf8")
        self.autocommit = kwargs.get("autocommit", False)
        self.connect_timeout = kwargs.get('connect_timeout', 5)

        # ssh
        self.ssh = None
        self.use_ssh = kwargs.get('use_ssh', False)
        self.ssh_host = kwargs.get('ssh_host')
        self.ssh_port = kwargs.get('ssh_port', 22)
        self.ssh_user = kwargs.get('ssh_user')
        self.ssh_password = kwargs.get('ssh_password')

        # 重试
        self.retry = 3
        self.retry_max = 3

        self._check_args()
        # db_type
        self.db_type = kwargs.get('db_type') or global_config.DB_TYPE or ''

        self.engine = None

        self.connect()

    def _check_args(self):
        """ check args of structure"""
        if self.database is None or self.user is None or self.password is None:
            raise InvalidArgumentError(500, 'db参数配置不完整')
        if self.use_ssh and (not self.ssh_host or not self.ssh_port or not self.ssh_user or not self.ssh_password):
            raise InvalidArgumentError(500, 'ssh参数配置不完整')

    @retry(tries=-1, delay=5)
    def connect(self):
        """Connect to the mysql server"""

        try:
            if self.use_ssh:
                try:
                    self.ssh = SSHTunnelForwarder(
                        (self.ssh_host, int(self.ssh_port)),
                        ssh_username=self.ssh_user,
                        ssh_password=self.ssh_password,
                        remote_bind_address=(self.host, int(self.port)),
                    )
                    self.ssh.start()
                except BaseSSHTunnelForwarderError:
                    logging.exception('ssh connection failed')
                    self.ssh = None

            # if self.host == "rm-bp10jpberdfgm88e5.mysql.rds.aliyuncs.com":
            #     self.host = "rm-bp10jpberdfgm88e5vo.mysql.rds.aliyuncs.com"
            #     # self.host = "*********"
            #     self.port = 3306
            # elif self.host == "rm-bp1f9pc4yqu7v84x2.mysql.rds.aliyuncs.com":
            #     self.host = 'rm-bp1f9pc4yqu7v84x2mo.mysql.rds.aliyuncs.com'
            #     self.port = 3306
            # elif self.db_type == 'DM' and self.host == '************':
            #     self.host = '************'
            #     self.port = 5236
            #     self.user = 'mycloud'
            #     self.passwd = 'hVbMcpBtk93x'

            if self.db_type == 'DM':
                url = 'dm+dmPython://{user}:{passwd}@{host}:{port}/'.format(
                    user=quote(self.user),
                    passwd=quote(self.password),
                    host=self.host,
                    port=self.port,
                )
                connect_args = {
                    'local_code': 1,
                    'connection_timeout': 15,
                    'schema': self.database
                }
            else:
                url = 'mysql+pymysql://{user}:{passwd}@{host}:{port}/{db}?ssl_disabled=True'.format(
                    user=quote(self.user),
                    passwd=quote(self.password),
                    host=self.host,
                    port=self.port,
                    db=self.database,
                )
                connect_args = {}
            self.engine = create_engine(
                url=url,
                echo=int(global_config.DB_ECHO) if global_config.DB_ECHO else 0,
                # isolation_level='REPEATABLE_READ' if not self.autocommit else 'AUTOCOMMIT',
                connect_args=connect_args,
                poolclass=NullPool
            )
            self.conn = sessionmaker(bind=self.engine)()
        except BaseException as e:
            if self.retry > 0:
                self.retry -= 1
                logging.exception(
                    "MySQL connection failed and retry %s, host: %s", (self.retry_max - self.retry), self.host
                )
                self.connect()
            else:
                raise e

    def _convert_to_json(self, cur_result, one=None):

        if not cur_result:
            return None if one else []
        cur_result = [cur_result] if one else cur_result

        cur = self.cur
        r = [dict((cur.description[i][0], value) for i, value in enumerate(row)) for row in cur_result]

        return (r[0] if r else None) if one else r

    @staticmethod
    def format_params(sql, params):
        copy_params = deepcopy(params)
        if isinstance(params, dict):
            for k, v in params.items():
                if isinstance(v, (list, tuple)):
                    sql = sql.replace(f"%%({k})s", f"(:{k})").replace(f"%({k})s", f"(:{k})")
                    kk_list = []
                    for index, i in enumerate(v):
                        kk = f"{k}_{index}"
                        copy_params[kk] = i
                        kk_list.append(kk)
                    del copy_params[k]
                    sql = sql.replace(f":{k}", ", ".join([f':{j}' for j in kk_list]))
                else:
                    if isinstance(v, (datetime.datetime, datetime.date)):
                        copy_params[k] = v.strftime('%Y-%m-%d %H:%M:%S')
                    sql = sql.replace(f"%({k})s", f":{k}")
        return sql, copy_params

    @staticmethod
    def adapter_dm(sql):
        sql = sql.replace('`', '"').replace('GROUP_CONCAT(', 'wm_concat(').replace('group_concat(', 'wm_concat(')
        return sql

    def row2dict(self, row):
        if isinstance(row, RowMapping):
            row = dict(row)
        return row

    def query_scalar(self, sql, params=None):
        cur = self._execute(sql, params)
        result = cur.fetchone()
        result = self.row2dict(result) if result else result
        if result:
            return list(result.values())[0]
        return None

    def query_columns(self, sql, params=None):
        cur = self._execute(sql, params)
        result = [self.row2dict(row) for row in cur.fetchall()]
        if result:
            return [list(t.values())[0] for t in result]
        return None

    def query_one(self, sql, params=None, append_limit=None):
        if append_limit:
            pattern = r';|limit.*'
            sql = re.subn(pattern, ' ', sql, flags=re.I)[0].strip() + ' LIMIT 1'
        cur = self._execute(sql, params)
        result = cur.fetchone()

        return self.row2dict(result) if result else result

    def query(self, sql, params=None, offset=None, limit=None):
        if offset is not None or limit is not None:
            sql += " LIMIT {}, {}".format(0 if offset is None else offset, limit)

        cur = self._execute(sql, params=params)
        result = [self.row2dict(row) for row in cur.fetchall()]
        return result

    def insert(self, table, data, commit=True):
        """
        新增一条记录
        :param table: 表名
        :param data: dict()
        :param commit: bool 是否立即提交, 如果有多个处理需要事物提交，则最后调用commit()方法提交
        :return: 受影响的行数
        """
        if data and isinstance(data, dict):
            if not data.get('created_by'):
                data['created_by'] = 'dmp-flow'
            if not data.get('modified_by'):
                data['modified_by'] = 'dmp-flow'
        query = self._serialize_insert(data)

        sql = "INSERT INTO `%s` (%s) VALUES(%s)" % (table.strip('`'), query[0], query[1])

        affect_row = self._execute(sql, data, dict_cursor=False).rowcount
        if commit:
            self.commit()
        return affect_row

    def insert_multi_data(self, table, list_data, fields, commit=True): # NOSONAR
        """
        添加多行数据
        :param str table:
        :param list list_data:
        :param list fields:
        :param bool commit:
        :return:
        """
        params = {}
        values = []
        tmp = 0
        cur_account = 'dmp-flow'
        for data in list_data:
            if not isinstance(data, dict):
                continue
            tmp_val = []
            for c in fields:
                if not isinstance(c, str):
                    continue
                p_name = c + '_' + str(tmp)
                tmp_val.append('%(' + p_name + ')s')
                params[p_name] = data.get(c)
            if 'created_by' not in fields:
                tmp_val.append('%(created_by)s')
            if 'modified_by' not in fields:
                tmp_val.append('%(modified_by)s')
            values.append('(' + ','.join(tmp_val) + ')')
            tmp += 1
        if not values:
            return 0
        if 'created_by' not in fields:
            fields.append('created_by')
            params['created_by'] = cur_account
        if 'modified_by' not in fields:
            fields.append('modified_by')
            params['modified_by'] = cur_account
        sql = 'INSERT INTO {table}({cols}) VALUES {values};'.format(
            table=table, cols=','.join(['`' + c + '`' for c in fields]), values=','.join(values)
        )
        affect_row = self._execute(sql, params, dict_cursor=False).rowcount
        if commit:
            self.commit()
        return affect_row

    def insert_multi_data_extend(self, table, list_data, fields, commit=True):
        """
        添加多行数据
        :param str table:
        :param list list_data:
        :param list fields:
        :param bool commit:
        :return:
        """
        params = {}
        values = []
        tmp = 0
        for data in list_data:
            if not isinstance(data, dict):
                continue
            tmp_val = []
            for c in fields:
                if not isinstance(c, str):
                    continue
                p_name = c + '_' + str(tmp)
                tmp_val.append('%(' + p_name + ')s')
                params[p_name] = data.get(c)
            values.append('(' + ','.join(tmp_val) + ')')
            tmp += 1
        if not values:
            return 0
        sql = 'INSERT INTO {table}({cols}) VALUES {values};'.format(
            table=table, cols=','.join(['`' + c + '`' for c in fields]), values=','.join(values)
        )
        affect_row = self._execute(sql, params, dict_cursor=False).rowcount
        if commit:
            self.commit()
        return affect_row

    # 批量插入executemany
    def insert_by_many(self, table_name, table_data, table_field):
        if not table_data:
            return True, '0'
        values = ['%s' for _ in range(len(table_data[0]))]
        try:
            sql = 'INSERT INTO {table_name} ({table_field}) values({values})'.format(
                table_name=table_name, table_field=','.join(table_field), values=','.join(values)
            )
            # 批量插入
            rowcount = self.cur.executemany(sql, tuple(table_data))
            self.commit()
            return True, rowcount
        except Exception as e:
            self.rollback()
            return False, str(e)

    def update(self, table, data, condition=None, commit=True):
        """
        更新记录
        :param table: 表名
        :param data: dict()
        :param condition: dict()
        :param commit: bool 是否立即提交, 如果有多个处理需要事物提交，则最后调用commit()方法提交
        :return:
        """
        if data and isinstance(data, dict) and not data.get('modified_by'):
            data['modified_by'] = 'dmp-flow'
        query = self._serialize_update(data)
        sql = "UPDATE %s SET %s" % (table, query)
        params = deepcopy(data)
        if condition:
            sql += " WHERE %s" % ' AND '.join([f'`{k}`= :{k}' for k, _ in condition.items()])
            params.update(condition)
        affect_row = self._execute(sql, params, dict_cursor=False).rowcount
        if commit:
            self.commit()
        return affect_row

    def delete_by_id(self, table, row_id, commit=True):
        """Delete rows based on a where condition"""
        sql = "DELETE FROM %s WHERE id='%s'" % (table, row_id)
        affect_row = self._execute(sql, dict_cursor=False).rowcount
        if commit:
            self.commit()
        return affect_row

    def delete(self, table, condition=None, commit=True):
        """Delete rows based on a where condition"""
        sql = "DELETE FROM %s" % table
        params = condition
        if condition and len(condition) > 0:
            sql += " WHERE %s" % ' AND '.join([f'`{k}`= %({k})s' for k, _ in condition.items()])
            logging.debug("execute sql: {}, with: \n ({})".format(sql, json.dumps(params)))
        affect_row = self._execute(sql, params, dict_cursor=False).rowcount
        if commit:
            self.commit()
        return affect_row

    def exec(self, sql, params=None, commit=True):
        affect_row = self._execute(sql, params, dict_cursor=False).rowcount
        if commit:
            self.commit()
        return affect_row

    @retry(tries=-1, delay=5)
    def execute(self, sql, params=None):
        """
        check if connection is alive. if not, reconnect
        :param sql:
        :param params:
        :rtype Cursor:
        """
        resetwarnings()
        logging.debug("execute sql: {}, with: \n ({})".format(sql, json.dumps(params)))
        self.cur.execute(sql, params)
        return self.commit()

    @retry(tries=-1, delay=5)
    def _execute(self, sql, params=None, dict_cursor=True):
        """
        check if connection is alive. if not, reconnect
        :param sql:
        :param params:
        :rtype Cursor:
        """
        try:
            resetwarnings()
            logging.debug(params)
            logging.debug("execute sql: {}, with: \n ({})".format(sql, json.dumps(params)))
            if self.db_type == 'DM':
                sql = self.adapter_dm(sql)
            sql, new_params = self.format_params(sql, params)
            cur = self.conn.execute(text(sql), new_params)
        except (OperationalError, ProgrammingError, DatabaseError) as e:
            logging.exception(e)
            raise e
        return cur.mappings() if dict_cursor else cur

    def commit(self):
        """Commit a transaction (transactional engines like InnoDB require this)"""
        return self.conn.commit()

    def rollback(self):
        return self.conn.rollback()

    def is_open(self):
        """Check if the connection is open"""
        return self.conn.is_active

    def end(self):
        """Kill the connection"""
        if self.cur:
            self.cur.close()
        if self.conn:
            self.conn.close()
        if self.ssh:
            self.ssh.close()
        if self.engine:
            self.engine.dispose()

    @staticmethod
    def _serialize_insert(data):
        """Format insert dict values into strings"""
        keys = f"`{'`,`'.join(data.keys())}`"
        # vals = ('%s,' * len(data))[0:-1]
        vals = ', '.join([f':{k}' for k in data.keys()])

        return [keys, vals]

    @staticmethod
    def _serialize_update(data):
        """Format update dict values into string"""
        return ','.join([f'`{f}`=:{f}' for f in data.keys()])

    def __enter__(self):
        return self

    def __exit__(self, error_type, value, traceback):
        if error_type:
            print(error_type, value, traceback)
        self.end()


def get_master_db():
    from . import config
    """

    :return SimpleMysql:
    """
    return SimpleMysql(
        host=config.get('DB.host'),
        port=int(config.get('DB.port')),
        database=config.get('DB.database'),
        user=config.get('DB.user'),
        password=config.get('DB.password'),
        db_type=config.get('DB.db_type')
    )


def get_db(code=None, db_name_suffix=''):
    """
    获取项目的db对象
    :param str code:
    :param str db_name_suffix:
    :return SimpleMysql: SimpleMysql
    """
    db_config = get_db_config(code, db_name_suffix)
    if not db_config:
        return ServerError('%s的rds配置不存在' % code)
    return SimpleMysql(
        host=db_config.get('host'),
        port=db_config['port'],
        database=db_config['database'],
        user=db_config['user'],
        password=db_config['password'],
        db_type=db_config.get('db_type')
    )


def get_data_db():
    """
    获取项目Data库
    :return:
    """
    return get_db(db_name_suffix='data')


def _db_cache_key(code, suffix=''):
    if suffix:
        suffix = ':' + suffix
    return f'Project:DB:Config:{code}{suffix}'


def get_db_config(code=None, db_name_suffix=None):
    """
    获取DB配置
    :param code:
    :param db_name_suffix:
    :return:
    """

    if code is None or code == '':
        code = getattr(builtins, 'code')

    data_db_suffix = 'data'

    if db_name_suffix == data_db_suffix:
        config = tenant_clean_db_get(code)
    else:
        config = tenant_db_get(code)

    return config


def get_odps_config(code=None):
    """
    获取ODPS配置
    :param str code: 项目code
    :return:
    """
    if code is None or code == '':
        code = getattr(builtins, 'code')
    with get_master_db() as db:
        sql = (
            'SELECT odps_proj AS project_name,odps_access_id AS access_id,odps_access_secret AS access_key '
            'FROM dap_bi_tenant_setting WHERE code=%(code)s '
        )
        return db.query_one(sql, {'code': code})


def get_data_db_config():
    """
    获取项目data库配置
    :return:
    """
    return get_db_config(db_name_suffix='data')


def get_data(table_name, conditions, fields, multi_row=None, from_config_db=False, order_by=None):
    """
    获取表数据
    :param str table_name: 表名
    :param dict conditions: 条件
    :param list fields: 列名
    :param bool multi_row: 是否返回多行，默认返回单行
    :param bool from_config_db: 是否查询配置库
    :param any order_by: 排序
    :return:
    """
    if not conditions:
        return None
    sql = 'SELECT {col} FROM `{table_name}` ' ''.format(
        col='`' + '`,`'.join(fields) + '`' if fields else '*', table_name=table_name.strip("`")
    )
    if conditions and isinstance(conditions, dict):
        sql += _parse_where(conditions)
    if order_by:
        sql += _parse_order_by(order_by)
    _get_db = get_master_db if from_config_db else get_db
    with _get_db() as db:
        if multi_row:
            return db.query(sql, conditions)
        else:
            sql += ' LIMIT 1 '
            return db.query_one(sql, conditions)


def get_data_order_by(table_name, conditions, fields=None, multi_row=None, order_by=None, pagination=None):
    """
    获取表数据
    :param Pagination pagination:
    :param str table_name: 表名
    :param dict conditions: 条件
    :param list fields: 列名
    :param bool multi_row: 是否返回多行，默认返回单行
    :param list order_by: 排序字段
    :return:
    """
    sql = 'SELECT {col} FROM `{table_name}` ' ''.format(
        col='`' + '`,`'.join(fields) + '`' if fields else '*', table_name=table_name.strip("`")
    )
    if conditions and isinstance(conditions, dict):
        sql += 'WHERE ' + ' AND '.join(['`{col}` = %({col})s'.format(col=col) for col in conditions.keys()])
    if order_by and isinstance(order_by, list):
        sql += ' ORDER BY ' + ','.join([ob[0] + ' ' + ob[1] for ob in order_by if isinstance(ob, tuple)])
    with get_db() as db:
        if multi_row:
            if pagination is not None:
                sql += 'LIMIT %d, %d' % ((pagination.page - 1) * pagination.pagesize, pagination.pagesize)
            return db.query(sql, conditions)
        else:
            sql += ' LIMIT 1 '
            return db.query_one(sql, conditions)


def get_data_scalar(table_name, conditions, col_name):
    """
    获取第一行第一列数据
    :param str table_name:
    :param dict conditions:
    :param str col_name:
    :return:
    """
    if not conditions or not col_name:
        return None
    sql = 'SELECT `{col}` FROM `{table_name}` '.format(col=col_name, table_name=table_name.strip("`"))
    sql += 'WHERE ' + ' AND '.join(['`{col}` = %({col})s'.format(col=col) for col in conditions.keys()])
    sql += ' LIMIT 1 '
    with get_db() as db:
        return db.query_scalar(sql, conditions)


def get_columns(table_name, conditions, col_name):
    """
    获取表中一列数据
    :param str table_name:
    :param dict conditions:
    :param str col_name:
    :return list:
    """
    if not conditions or not col_name:
        return None
    sql = 'SELECT `{col}` FROM `{table_name}` '.format(col=col_name, table_name=table_name.strip("`"))
    sql += 'WHERE ' + ' AND '.join(['`{col}` = %({col})s'.format(col=col) for col in conditions.keys()])
    with get_db() as db:
        return db.query_columns(sql, conditions)


def add_data(table_name, data):
    """
    添加数据
    :param str table_name:
    :param dict data:
    :return bool:
    """
    if not data:
        return False
    with get_db() as db:
        return db.insert(table_name, data) == 1


def add_model(table_name, model, fields=None):
    """
    添加数据
    :param str table_name:
    :param base.models.BaseModel model:
    :param list fields:
    :return bool:
    """
    data = model.get_dict(fields)
    return add_data(table_name, data)


def add_list_data(table_name, list_data, fields, commit=True):
    """
    添加多行数据
    :param table_name:
    :param list_data:
    :param fields:
    :param commit:
    :return:
    """
    if not list_data or not isinstance(list_data, list):
        return False
    with get_db() as db:
        return db.insert_multi_data(table_name, list_data, fields, commit) > 0


def update_data(table_name, data, condition):
    """
    更新数据
    :param str table_name:
    :param dict data:
    :param dict condition:
    :return int:
    """
    if not data:
        return 0
    with get_db() as db:
        return db.update(table_name, data, condition)


def update_model(table_name, model, condition, fields=None):
    """
    更新数据
    :param str table_name:
    :param base.models.BaseModel model:
    :param dict condition:
    :param list fields:
    :return int:
    """
    data = model.get_dict(fields)
    return update_data(table_name, data, condition)


def delete_data(table_name, condition):
    """
    删除数据
    :param str table_name:
    :param dict condition:
    :return int:
    """
    if not condition:
        return 0
    sql = "DELETE FROM `%s`" % table_name
    sql += _parse_where(condition)
    return get_db().exec(sql, condition)


def _parse_where(condition: dict) -> str:
    if not condition:
        return ''
    and_items = []
    copy_condition = deepcopy(condition)
    for k, v in copy_condition.items():
        field = k
        operator = '='
        if isinstance(v, list):
            operator = ' IN '
            kk_list = []
            for i, vv in enumerate(v):
                kk = f'{k}_{i}'
                condition[kk] = vv
                kk_list.append(kk)
            and_items.append('{}{}{}'.format(_parse_field(field), operator, f"({', '.join([f':{i}' for i in kk_list])})"))
            del condition[k]
            break
        elif v is None:
            operator = ' IS '
        else:
            ma = re.match('(.*?)(<>|!=|<=|>=|=|>|<| like| regexp)$', k, re.IGNORECASE)
            if ma:
                field, operator = ma.groups()
                ori_operator = operator
                operator_lower = operator.strip().lower()
                operator_map = {'like': ' LIKE ', 'regexp': ' REGEXP '}
                operator = operator_map.get(operator_lower, operator)
                del condition[k]
                c_k = k.replace(ori_operator, '').strip()
                condition[c_k] = v
                k = f":{c_k}"
            else:
                k = f":{k}"
        ma = re.match('(.*?) (is not|not)$', field.strip(), re.IGNORECASE)
        if ma:
            field, _ = ma.groups()
            if operator == ' IS ':
                operator += 'NOT '
            else:
                operator = ' NOT' + operator
        and_items.append('{}{}{}'.format(_parse_field(field), operator, k))
    return ' WHERE ' + ' AND '.join(and_items)


def data_is_exists(table_name, condition=None, exclude_condition=None):
    """
    数据是否存在
    :param str table_name:
    :param dict condition:
    :param dict exclude_condition: 不包括的条件
    :return:
    """
    sql = 'SELECT 1 FROM `%s` ' % (table_name,)
    where = []
    params = {}
    if condition:
        for k, v in condition.items():
            where.append('`{c}` = %({c})s'.format(c=k))
            params[k] = v
    if exclude_condition:
        for k, v in exclude_condition.items():
            where.append('`{c}`<> %({c})s '.format(c=k))
            params[k] = v
    sql += (' WHERE ' + ' AND '.join(where)) if where else ''
    sql += ' LIMIT 1 '
    with get_db() as db:
        return db.query_scalar(sql, params)


def get_data_max_rank(table_name, rank_col_name=None, condition=None):
    """
    获取最大的数据
    :param str table_name:
    :param str rank_col_name: 默认为 rank
    :param dict condition:
    :return int:
    """
    if not rank_col_name:
        rank_col_name = 'rank'
    sql = 'SELECT IFNULL(MAX(`%s`),0)+1 AS `rank` FROM `%s`' % (rank_col_name, table_name)
    params = {}
    if condition:
        sql += ' WHERE 1=1 '
        for k in condition.keys():
            sql += ' AND `%s` = %%(%s)s' % (k, k)
            params[k] = condition[k]
    with get_db() as db:
        return db.query_scalar(sql, params)


def data_db_table_is_exists(table_name):
    """
    数据表是否存在
    :param table_name:
    :return:
    """
    sql = (
        'SELECT COUNT(1) AS is_exists FROM information_schema.TABLES '
        'WHERE table_schema= DATABASE() AND `table_name`=%(table_name)s '
    )
    with get_data_db() as db:
        return db.query_scalar(sql, {'table_name': table_name})


def delete_data_db_table(table_name):
    """
    删除数据表
    :param table_name:
    :return:
    """
    sql = 'DROP TABLE IF EXISTS `%s`' % table_name
    with get_data_db() as db:
        return db.exec(sql)


def _parse_field(field: str) -> str:
    """解析 select 的一个字段"""
    if not field or field == '*':
        return '*'
    field = field.strip()
    org_str = (' ', '(', '`')
    for v in org_str:
        if v in field:
            return field
    arr = ['`%s`' % v for v in field.split('.')]
    return '.'.join(arr)


def _parse_order_by(order_by: '') -> str:
    ret_arr = []
    if order_by:
        if isinstance(order_by, str):
            items = order_by.split(',')
            for item in items:
                *fields, asc_or_desc = item.strip().split(' ')
                ret_arr.append(_parse_field(' '.join(fields)) + ' ' + asc_or_desc.upper())
        if isinstance(order_by, tuple):
            order_by = [order_by]
        if isinstance(order_by, list):
            ret_arr = [_parse_field(ob[0]) + ' ' + ob[1] for ob in order_by if isinstance(ob, tuple)]
    return ' ORDER BY ' + ','.join(ret_arr) if ret_arr else ''


def get_data_by_sql(sql, params, pagination=None):
    """
    根据sql语句获取数据
    :param str sql:
    :param dict params:
    :param Pagination pagination:
    :return:
    """
    if not sql:
        raise ValueError('sql not allow empty')

    db = get_db()
    if pagination is not None:
        sql += 'LIMIT %d, %d' % ((pagination.page - 1) * pagination.pagesize, pagination.pagesize)
    return db.query(sql, params)
