#!/usr/bin/env python
# -*- coding:utf-8 -*-
# @FileName  :ingrate_platform.py.py
# @Time      :2022/5/17 10:01
# <AUTHOR>
from typing import List, Dict
from datetime import datetime
import requests
from loguru import logger
import json
import curlify

from components import config, auth_util
from components.app_hosts import AppHosts
from components.enums import IngrateAction, SkylineApps
from components.errors import UserError
from components.redis import RedisCache
from components.repository import get_db

ACCESS_TOKEN_KEY = "access_token:ingrate"


def get_tenant_ingrate_platform_config():
    with get_db() as db:
        data = db.query_scalar(
            'select `value` from dap_bi_system_setting where `category`=%(category)s and `item`=%(item)s',
                               {"category": 'ingrate_platform', "item": 'basic_data_platform'}
        )
    if data:
        try:
            json_data = json.loads(data)
        except Exception as e:
            logger.error(f'解析租户集成平台信息错误：data: {data}, error: {str(e)}')
            json_data = {}
    else:
        json_data = {}
    return json_data


class IngratePlatformApi(object):

    URI_MAPPING = {
        'USER_RIGHTS': {
            'IPAAS': '/api/basicdata/DMPCheckUserReportAuthorize',
            'APAAS': '/pub/Mysoft.PubPlatform.Organization.PublicServices.IStandardRolePublicService/DMPCheckUserReportAuthorize'
        },
        'APP_PUBLISH': {
            'IPAAS': '/api/basicdata/DMPSaveGroupAndReports',
            'APAAS': '/pub/Mysoft.PubPlatform.Organization.PublicServices.IStandardRolePublicService/DMPSaveGroupAndReports'
        },
        'APP_STATUS_SYNC': {
            'IPAAS': '/api/basicdata/DMPOperateApplication',
            'APAAS': '/pub/Mysoft.PubPlatform.Organization.PublicServices.IStandardRolePublicService/DMPOperateApplication'
        },
        'GET_AUTH_APP': {
            'IPAAS': '/api/basicdata/DMPQueryUseApplications',
            'APAAS': '/pub/Mysoft.PubPlatform.Organization.PublicServices.IStandardRolePublicService/DMPQueryUseApplications'
        },
        'GET_ORG_LIST': {
            'IPAAS': '/api/basicdata/direct/QueryOrganizationList',
            'APAAS': '/pub/Mysoft.PubPlatform.Organization.PublicServices.IBasicDataPublicService/QueryOrganizationList'
        },
        'GET_USER_LIST': {
            'IPAAS': '/api/basicdata/direct/QueryUserList',
            'APAAS': '/pub/Mysoft.PubPlatform.Organization.PublicServices.IBasicDataPublicService/QueryUserList'
        },
        'GET_I18N_CONFIG': {
            'IPAAS': '/api/basicdata/QueryUserInternaltionalConfig',
            'APAAS': '/pub/03011801/CountryRegion/queryUserInternaltionalConfig'
        }
    }

    def __init__(self, code='', timeout=30, action=IngrateAction.Call.value):
        self.code = code
        self.timeout = timeout
        self.action = action
        self.__load_config()

    def __load_config(self):
        self.directly_req = True
        self.host = AppHosts.get(SkylineApps.APAAS).rstrip('/')
        self.client_id = config.get("IngratePlatform.client_id") or ""
        self.client_secret = config.get("IngratePlatform.client_secret") or ""
        self.report_authorize_url = config.get('IngratePlatform.report_authorize_url') or ""
        self.cache = RedisCache(key_prefix=f"IngratePlatform:{self.action}")

    def get_uri(self, method):
        conf = IngratePlatformApi.URI_MAPPING.get(method)
        if not conf:
            return ''
        if self.directly_req:
            return conf.get('APAAS')
        else:
            return conf.get('IPAAS')
    def portal_check_ingrate_platform_is_exist(self):
        # 基础数据平台授权的门户场景目前只检查租户配置是否存在
        try:
            host = self.host
            client_id = self.client_id
            client_secret = self.client_secret
            if all([host, client_secret, client_id]):
                return True
            else:
                return False
        except:
            return False

    def get_saas_access_token(self, host=None, client_id=None, client_secret=None):
        cache_key = ACCESS_TOKEN_KEY+':saas'
        access_token = self.cache.get(cache_key)
        if not access_token:
            host = host or self.host
            client_id = client_id or self.client_id
            client_secret = client_secret or self.client_secret
            result = self._do_request(
                method="post",
                url=f"{host}/MIPApiAuth/Token",
                params={
                    "client_id": client_id,
                    "client_secret": client_secret
                },
                is_header=False
            )
            access_token = result.get("access_token")
            if not access_token:
                raise UserError(message="saas基础数据平台获取access_token错误：{}".format(result))
            expires_in = int(result.get("expires_in", 3600))
            self.cache.set(cache_key, access_token, int(expires_in / 2))
        else:
            access_token = access_token.decode()
        return access_token

    def get_access_token(self):
        access_token = self.cache.get(ACCESS_TOKEN_KEY)
        if not access_token:
            result = self._do_request(
                method="post",
                url=f"{self.host}/MIPApiAuth/Token",
                params={
                    "client_id": self.client_id,
                    "client_secret": self.client_secret
                },
                is_header=False
            )
            access_token = result.get("access_token")
            if not access_token:
                raise UserError(message="基础数据平台获取access_token错误：{}".format(result))
            expires_in = int(result.get("expires_in", 3600))
            self.cache.set(ACCESS_TOKEN_KEY, access_token, int(expires_in / 2))
        else:
            access_token = access_token.decode()
        return access_token

    @property
    def headers(self):
        #统一应用认证
        headers = {
            'Content-Type': 'application/json',
            "_my-skyline-yunerp-tenantcode_": self.code,
            "tenantCode": self.code,
        }
        if auth_util.is_enable_skyline_auth(self.code):
            token = auth_util.gen_auth_token(self.code)
            if token:
                headers[auth_util.AUTHORIZATION_KEY] = token
        else:
            headers["Authorization"] = "Bearer " + self.get_access_token()
        return headers

    @property
    def sample_headers(self):
        headers = {
            'Content-Type': 'application/json',
            "tenantCode": self.code,
        }
        if auth_util.is_env_enable_skyline_auth():
            token = auth_util.gen_auth_token(self.code)
            if token:
                headers[auth_util.AUTHORIZATION_KEY] = token
        return headers


    def _do_request(self, method, url, params, is_header=True, extra_header={}):
        headers = None
        start_time = datetime.now()
        result = ''
        curl_info = ''
        is_success = 0
        try:
            headers = self.headers if is_header else self.sample_headers
            headers.update(extra_header)
            logger.error(f"基础平台请求 url: {url}")
            if method.upper() == "GET":
                res = requests.get(url, params=params, headers=headers, timeout=self.timeout)
            else:
                res = requests.post(url, json=params, headers=headers, timeout=self.timeout)
            result = res.text
            curl_info = curlify.to_curl(res.request, compressed=True)
            logger.error(f"基础平台请求 curl: {curl_info}, res: {result}")
            return res.json()
        except Exception as e:
            message = "请求基础数据平台错误: {}".format(str(e))
            logger.error(message)
            raise UserError(message=message) from e


    def send_email(self, params, code):
        # 优先取内网
        self.host = AppHosts.get(SkylineApps.APAAS)
        if not self.host:
            self.host = AppHosts.get(SkylineApps.APAAS, False)
        platform_code = config.get("ReportCenter.open_print_aid","mysoft_cy_saas")
        header = {
            "platformCode": platform_code,
            "thirdTenantCode": code,
            auth_util.AUTHORIZATION_KEY: auth_util.gen_auth_token(),
            "tenantCode": code,
        }
        result = self._do_request(
            method="post",
            url=f"{self.host}/pub/40040302/email/send",
            params=params,
            extra_header=header
        )
        logger.info(f'邮件发送结果:{result}')
        if result.get('code') not in ['0', 0]:
            logger.error(f'请求基础数据平台发送邮件失败:{result.get("msg")}')
            raise UserError(message=f'请求基础数据平台发送邮件失败:{result.get("msg")}')
        rs = result.get("data", {})
        return rs
