#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import requests
import logging

import os
from components import config
from components.errors import UserError


ENV_CODE_MAP = {
    "cmsk": "招商",
    "xhl": "新华联",
    "zzkqdc": "康桥",
    "ldxg": "绿地香港",
    "ldjx": "绿地江西",
    "jfdc": "俊发",
    "rongsheng": "荣盛",
    "agile": "雅居乐",
    "sgdc": "山钢",
    "bcdmp": "北辰",
    "test": "云创测试环境"
}


class BaseWechatRobot:
    """
    https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=907f6315-3978-4d2a-8d95-199242697514
    """
    def __init__(self, robot_url=None):
        self.robot_url = robot_url or config.get("Subscribe.warning_url")
        if not self.robot_url:
            raise UserError(message="企业微信机器人URL未指定或未配置")

    def request(self, content, timeout=None, retry=3):
        data = {"msgtype": "text", "text": {"content": content}}
        response = requests.post(self.robot_url, json=data, timeout=timeout or 30)
        if response.status_code != 200:
            # 记录日志
            logging.error("调用企业微信机器人接口失败,还有%s次机会重试", (retry - 1))
            if retry > 0:
                return self.request(content, timeout=timeout, retry=retry - 1)
            # 3次重试还是失败，报错
            raise UserError(message="调用企业微信机器人接口失败，请检查配置或联系管理员!")
        return response.json()


class SubscribeRobot(BaseWechatRobot):
    def __init__(self,
                 robot_url="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=907f6315-3978-4d2a-8d95-199242697514",
                 client=None,
                 tenant=None,
                 subscribe_name=None,
                 detail_url=None,
                 sub_type=None):
        super(SubscribeRobot, self).__init__(robot_url=robot_url)
        self.client = client if client else get_client()
        self.tenant = tenant
        self.subscribe_name = subscribe_name
        self.detail_url = detail_url
        self.sub_type = sub_type if sub_type else ''

    def send_message(self, timeout=None, retry=3):
        content = self.gen_subscribe_alarm_content()
        return self.request(content=content, timeout=timeout, retry=retry)

    def gen_subscribe_alarm_content(self):
        return """{sub_type}订阅异常
环境：{client}
租户：{tenant}
【{subscribe_name}】订阅存在异常，详情：{detail_url}""".format(sub_type=self.sub_type,
                                                   client=self.client,
                                                   tenant=self.tenant,
                                                   subscribe_name=self.subscribe_name,
                                                   detail_url=self.detail_url)


def get_client():
    client_code = os.environ['CONFIG_AGENT_CLIENT_CODE'] = 'test'
    return ENV_CODE_MAP[client_code] if client_code in ENV_CODE_MAP else '未知环境'


