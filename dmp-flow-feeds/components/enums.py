from enum import Enum, unique


@unique
class IngrateAction(Enum):
    """
    集成平台接口调用类型
    """
    Register = "register"   # 接口注册到集成平台
    Call = "call"       # 通过集成平台调用注册到集成平台的接口


@unique
class SkylineApps(Enum):

    DP = 'dp'
    DMP = 'dmp'
    #集成平台
    IPAAS = 'ipaas'
    #超级工作台门户站点
    WORKBENCH_PORTAL = 'workbench.protal'
    #超级工作台消息应用
    WORKBENCH_MESSAGE = 'workbench.message'
    #通行证服务
    WORKBENCH_AUTH = 'workbench.auth'
    #运营平台开户
    OMP_TENANT = 'tenantProxy'
    #数芯
    DAP = 'dap'
    #套打
    PUBSERVICE_PRINT = 'pubservice.print'
    #公共服务(电签)
    PUBSERVICE_ESIGN = 'pubservice.esign'
    #建模平台
    APAAS = 'apaas'
    #数见报表(erp报表)
    SJBB = 'sjbb'
    #文档服务
    DOCUMENT = 'pubservice.document'

    def mks_key(self):
        if self == SkylineApps.IPAAS:
            return 'IngratePlatform.host'
        elif self == SkylineApps.WORKBENCH_PORTAL:
            return 'Superportal.host'
        elif self == SkylineApps.DAP:
            return 'ThirdDatasource.bigdata_api_host'
        elif self == SkylineApps.DMP:
            return 'Domain.dmp'
        elif self == SkylineApps.PUBSERVICE_PRINT:
            return 'ReportCenter.open_print_url'
        else:
            return None

    @staticmethod
    def custom_key(key):
        class CustomKey(str):
            @staticmethod
            def mks_key():
                return '不支持自定义站点的服务内配置获取'
            @property
            def value(self):
                return self.__str__()

        return CustomKey(key)