# --coding:utf-8--

SQL_MAPPING = {
    "table_is_exist": {
        'MySQL': 'SELECT COUNT(1) AS `is_exists` FROM `information_schema`.`TABLES` WHERE `table_schema`= DATABASE() AND `table_name`=:table_name ',
        'DM': "SELECT count(1) as IS_EXISTS FROM SYSOBJECTS where NAME=:table_name"
    },
    "table_columns": {
        'MySQL': """
        SELECT COLUMN_NAME AS `name`,COLUMN_TYPE AS `type` ,COLUMN_COMMENT AS `comment`  
        FROM information_schema.COLUMNS  WHERE TABLE_SCHEMA =DATABASE() AND TABLE_NAME=:table_name  ORDER BY ORDINAL_POSITION
        """,
        'DM': """
        select 
         utc.COLUMN_NAME AS "name",utc.DATA_TYPE AS "type" ,ucc.COMMENTS AS "comment"
        from user_tab_columns as utc 
        inner join user_col_comments as ucc 
        on utc.COLUMN_NAME=ucc.COLUMN_NAME and utc.TABLE_NAME=ucc.TABLE_NAME
        where utc.Table_Name=:table_name
        """
    },
    "create_log_table": {
        'MySQL': """
        CREATE TABLE IF NOT EXISTS `{table_name}` ( 
        id INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
        `project_code` varchar(255) DEFAULT '',
        `flow_instance_id` char(36) DEFAULT '',
        `node_id` char(36) DEFAULT '',
        `level_name` varchar(10) DEFAULT 'INFO',
        `level_no` int DEFAULT 0,
        `line_no` int DEFAULT 0,
        `created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `file_name` varchar(255) DEFAULT '',
        `module` varchar(255) DEFAULT '',
        `func_name` varchar(255) DEFAULT '',
        `path_name` varchar(511) DEFAULT '',
        `process` int DEFAULT 0,
        `process_name` varchar(255) DEFAULT '',
        `thread` bigint DEFAULT 0,
        `thread_name` varchar(255) DEFAULT '',
        `exc_text` varchar(8191) DEFAULT '',
        `message` longtext,
        `created_on` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        KEY `idx_flow` (`project_code`,`flow_instance_id`),
        KEY `idx_node` (`project_code`,`flow_instance_id`,`node_id`))
        """,
        'DM': """
        CREATE TABLE IF NOT EXISTS "{table_name}" ( 
        id INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
        "project_code" varchar(255 char) DEFAULT '',
        "flow_instance_id" char(36) DEFAULT '',
        "node_id" char(36) DEFAULT '',
        "level_name" varchar(10 char) DEFAULT 'INFO',
        "level_no" int DEFAULT 0,
        "line_no" int DEFAULT 0,
        "created" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "file_name" varchar(255 char) DEFAULT '',
        "module" varchar(255 char) DEFAULT '',
        "func_name" varchar(255 char) DEFAULT '',
        "path_name" varchar(511 char) DEFAULT '',
        "process" int DEFAULT 0,
        "process_name" varchar(255 char) DEFAULT '',
        "thread" bigint DEFAULT 0,
        "thread_name" varchar(255 char) DEFAULT '',
        "exc_text" text,
        "message" CLOB,
        "created_on" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP)
        """
    }
}


def adapter_sql(key, db_type='MySQL'):
    """
    获取指定类型sql
    """
    sql_map = SQL_MAPPING.get(key, {})
    if db_type not in sql_map:
        db_type = 'MySQL'
    return sql_map[db_type]
