# -*- coding: UTF-8 -*-
"""
Created on 2016年8月31日

@author: chenc04
"""


import logging

from components.loggers import init_logging
init_logging()
logger = logging.getLogger(__name__)
from flow.flow_launcher import FlowLauncher
from flow.flow_control import FlowControl

RET_STATE = {
    "KILL": 143,
    "FAIL": -1,
    "OK": 0,
    "RUN": 1,
    "RETRY": 2
}

if __name__ == '__main__':
    data = {
        "project_code": "dev",
        "flow_id": "39e52bab-d585-0365-b18a-b7c7b53dd668",
        "flow_instance_id": "39e52bab-d739-7e41-7d42-72e315e16b35",
        "test_run": "1",
    }
    # FlowLauncher(**data).run_flow()
    FlowControl(**data).run()
