# -*- coding: UTF-8 -*-
import asyncio
import datetime
import decimal
import re
from json import JSONDecodeError

from pyppeteer import launch

from components.errors import UserError
from flow.flow_context import FlowContext
from node.node import Node
from http.client import HTTPException
from pyppeteer.errors import BrowserError
import pyppeteer.launcher
from urllib.error import URLError
from urllib.request import urlopen
from components import app_hosts

import json
import os
import time
import traceback
from email.mime.base import MIMEBase
from email.mime.image import MIMEImage
from urllib.parse import urlparse, urlencode, urlunparse

from components import config
from components import mail
from components import repository
from components.oss import OSSFileProxy
from components.send_message_openapi import feed_message
from node.node_execution import NodeExecution
from node.node_result import NodeResult
from components.strings import seq_id
from components.async_openapi import async_openapi_request


# https://github.com/pyppeteer/pyppeteer/issues/135
def get_ws_endpoint(url) -> str:
    url = url + '/json/version'
    timeout = time.time() + 30
    while True:
        if time.time() > timeout:
            raise BrowserError('<PERSON>rowser closed unexpectedly:\n')
        try:
            with urlopen(url) as f:
                data = json.loads(f.read().decode())
            break
        except (URLError, HTTPException):
            pass
        time.sleep(0.1)

    return data['webSocketDebuggerUrl']


pyppeteer.launcher.get_ws_endpoint = get_ws_endpoint


class EmailFeedsNode(NodeExecution):
    def __init__(self, context: FlowContext, cur_node: Node):
        super().__init__(context, cur_node)
        # 订阅信息
        self.id = None
        self.token = None

        # 权限信息
        self.type_access_released = None
        self.share_secret_key = None

        # 邮件信息
        self.report_from = None
        self.subject = None
        self.message = None
        self.addresser = None
        self.recipients = None

        # 报告属性
        self.dashboard_id = None
        self.dashboard_name = None
        self.layout = None
        self.layout_type = None
        self.platform = None
        self.width = None
        self.height = None
        self.release_url = None

        # 邮件附件和图片设置
        self.img_flag = False
        self.attachment_flag = False
        self.cover_flag = False
        # 邮件正文是否包括数据集
        self.is_dataset = False

        # 内部属性
        self._mime_image = None
        self._mime_attachment = None
        self._dashboard_cover = None

        self.html_image1_id = 'img1'
        # 简讯正文获取api接口
        self.email_content_api = '/msg/content'

    def init(self):
        sql = """
        SELECT 
              e.id,
              e.dashboard_id,
              e.subject_email,
              e.recipients,
              e.addresser,
              e.message,
              e.report_from, 
              e.release_url, 
              e.user_token,
              d.type_access_released, 
              d.share_secret_key, 
              d.name as dashboard_name,
              d.layout,
              d.layout_type,
              d.platform
        FROM dap_bi_dashboard_email_subscribe e LEFT JOIN dap_bi_dashboard d ON e.`dashboard_id` = d.`id`
        WHERE e.id=%(feeds_id)s
        """
        with self.context.get_project_db() as db:
            rv = db.query_one(sql, {'feeds_id': self.context.flow_id})

        self.id = rv.get('id')

        self.token = rv.get('user_token')
        self.type_access_released = rv.get('type_access_released')
        self.share_secret_key = rv.get('share_secret_key')

        # 邮件信息
        self.report_from = rv.get('report_from')
        self.subject = rv.get('subject_email')
        self.message = rv.get('message')
        self.addresser = rv.get('addresser')
        self.recipients = json.loads(rv.get('recipients'))
        # 检测邮件是否包含动态内容
        self.check_msg_dataset()

        # 报告信息
        self.dashboard_id = rv.get('dashboard_id')
        self.dashboard_name = rv.get('dashboard_name')
        self.layout = rv.get('layout')
        self.layout_type = rv.get('layout_type')
        self.platform = rv.get('platform')
        self.release_url = rv.get('release_url')

        # 没有报告名称，说明已经被删除
        if not self.dashboard_id:
            feed_message(self.context.project_code, {'title': f'{self.subject}邮件订阅失败', 'id': self.context.flow_id})
            raise Exception('报告不存在')

        self.update_email_detail()
        self.set_flags()

    def execution(self):
        self.init()
        try:
            asyncio.get_event_loop().run_until_complete(self.run())
        except (asyncio.InvalidStateError, RuntimeError, KeyboardInterrupt) as e:
            feed_message(self.context.project_code, {'title': f'{self.subject}邮件订阅失败', 'id': self.context.flow_id})
            raise Exception(f'邮件订阅失败, 错误内容: {str(e)}') from e
        return NodeResult(True, '邮件订阅成功')

    def get_custom_redirect_url(self, dashboard_id):
        """
        获取第三方自定义跳转url
        :param dashboard_id:
        :return:
        """

        base_redirect_url = config.get("ThirdParty.base_redirect_url", "")

        # 获取biz_code
        result = repository.get_data("dap_bi_dashboard", {"id": dashboard_id}, ["biz_code"])
        biz_code = result.get("biz_code")

        # 拼接后的url请求参数
        parsed_url = urlparse(base_redirect_url)
        extend_url_params = urlencode({"biz_code": biz_code, "code": self.context.project_code})
        new_query_params = parsed_url.query + "&" + extend_url_params if parsed_url.query else extend_url_params

        # 重新组装url
        new_redirect_url = urlunparse(
            [
                parsed_url.scheme,
                parsed_url.netloc,
                parsed_url.path,
                parsed_url.params,
                new_query_params,
                parsed_url.fragment,
            ]
        )
        return new_redirect_url

    async def run(self):
        try:
            receivers = self.get_receivers()
            receivers_models = self.init_send_log_models(receivers)
            self.update_email_send_detail_log(receivers_models, **{
                'error_reason': '', 'status': 0
            })

            # 异步并发批量请求，获取邮件动态内容
            batch_message_content = {}
            try:
                if self.is_dataset:
                    st = time.time()
                    batch_message_content = await self.batch_get_message_data(receivers_models)
                    msg = "邮件用户数：%s 取数总耗时：%s" % (str(len(receivers_models)), str(round(time.time() - st, 5)))
                    self.info(msg)
            except Exception as e:
                self.update_email_send_detail_log(receivers_models, **{
                    'error_reason': '发送邮件前获取邮件动态内容错误: %s' % self.extract_error(e), 'status': 2,
                    'actual_send_time': self.now()
                })
                raise e from e

            # 封面模式不需要截图
            try:
                if not self.cover_flag:
                    await self.screenshot_with_retry()
                    self.upload_img()
            except Exception as se:
                self.update_email_send_detail_log(receivers_models, **{
                    'error_reason': '发送邮件前截图发生错误: %s' % self.extract_error(se), 'status': 2, 'actual_send_time': self.now()
                })
                raise se from se

            # 发送邮件
            try:
                self.send_mail(receivers_models, batch_message_content)
            except UserError as ue:
                # 用户抛出异常，不更新日志详情
                self.error(ue.message)
                raise Exception('邮件发送，部分用户出现异常')
            except Exception as ee:
                self.update_email_send_detail_log(receivers_models, **{
                    'error_reason': '发送邮件时发生错误:%s' % self.extract_error(ee), 'status': 2, 'actual_send_time': self.now()
                })
                raise ee from ee

        finally:
            if os.path.exists(self.img_path):
                os.remove(self.img_path)

    async def screenshot_with_retry(self, retries=1):
        while True:
            try:
                self.info(f"生成报告截图<{self.dashboard_name}>, url: {self.dashboard_url}")
                await self.screenshot()
                self.info(f"生成报告截图成功: {self.img_path}")
                return
            except Exception as ex:
                self.info(traceback.format_exc())
                if retries == 0:
                    raise Exception('生成截图失败，错误内容：' + str(ex))
                self.info(f'生成截图失败，错误内容：{str(ex)}')
                self.info(f'重试次数: {retries}')
                retries -= 1

    def upload_img(self):
        self.info("上传图片")
        try:
            oss_file_url = OSSFileProxy().upload(open(self.img_path, 'rb'), root=config.get('OSS.root'),
                                                 file_name=f"email_img/{self.img_name}")
            self.info(f"上传报告图片到oss：{oss_file_url}")
            with self.context.get_project_db() as db:
                db.update('dap_bi_dashboard_email_subscribe_detail', {"relevancy_url": oss_file_url},
                          {'id': self.context.flow_instance_id})
        except BaseException as ex:
            raise Exception('上传oss图片，错误内容：' + str(ex))

    def kill_crawler(self):
        cmd = 'ps -ef | grep chromium'
        lines = os.popen(cmd)
        for line in lines:
            if line.find('grep mx_skyfile_serv') != -1:
                continue
            vars = line.split()
            pid = vars[1]  # get pid
            proc = ''.join(vars[7:])  # get proc description 1
            proc = proc if len(proc) > 20 else proc[:20]
            out = os.system('kill -9 ' + pid)
            self.info(f'kill <{pid}, {out}> - ' + proc)

    def now(self):
        return datetime.datetime.now()

    def init_send_log_models(self, receivers: [dict]):
        return {
            concat.get('email', ''): MobileSubscribeSendDetailLogModel(
                **{
                    'id': seq_id(),
                    'email': concat.get('email', ''),
                    'user_id': concat.get('id', ''),
                    'user_name': concat.get('name', ''),
                    'account': concat.get('account', ''),
                    'email_subscribe_id': self.context.flow_id,
                    'log_id': self.context.flow_instance_id,
                    'plan_send_time': self.context.flow_start_time,
                }
            )
            for concat in receivers
        }

    def update_email_send_detail_log(self, receivers_models, **kwargs):
        """
        更新或插入收件人的发送记录
        :receivers 整个接收人的map  {'<EMAIL>': model, '<EMAIL>': model}
        :kwargs    更新的mode的kv
        """
        # 更新model的信息
        for _, model in receivers_models.items():
            for key, val in kwargs.items():
                setattr(model, key, val)

        # 将model的信息映射到SQL的改动
        sql = """INSERT INTO `dap_bi_mobile_subscribe_send_detail_log` (%s) VALUES %s"""
        fields = MobileSubscribeSendDetailLogModel.__slots__
        fields_statement = ', '.join([f'`{f}`' for f in fields])
        values = ', '.join([
            f'({", ".join([self.str_value(getattr(model, f, "")) for f in fields])})'
            for _, model in receivers_models.items()
        ])
        sql = sql % (fields_statement, values)

        with self.context.get_project_db() as db:
            db.exec('DELETE FROM dap_bi_mobile_subscribe_send_detail_log WHERE `id` in %(ids)s', {'ids': [model.id for _, model in receivers_models.items()]})
            db.exec(sql)

    def str_value(self, s):
        if isinstance(s, (int, float, decimal.Decimal)):
            return str(s)
        elif isinstance(s, datetime.datetime):
            s = s.strftime('%Y-%m-%d %H:%M:%S')
            return f"'{s}'"
        elif isinstance(s, str):
            s = s.replace("'", "\"")
            return f"'{s}'"
        elif s is None:
            return 'null'
        return f"'{str(s)}'"

    def update_email_detail(self, oss_file_url=None):
        """更新邮件明细表"""
        with self.context.get_project_db() as db:
            detail = db.query_one("select id from dap_bi_dashboard_email_subscribe_detail where id=%(id)s ",
                                  {'id': self.context.flow_instance_id})
            detail_data = {
                "email_subscribe_id": self.id,
                "relevancy_url": oss_file_url,
                "subject_email": self.subject,
                "dashboard_id": self.dashboard_id,
                "dashboard_name": self.dashboard_name,
            }
            if detail:
                db.update('dap_bi_dashboard_email_subscribe_detail', detail_data, {'id': self.context.flow_instance_id})
            else:
                detail_data["id"] = self.context.flow_instance_id,
                db.insert('dap_bi_dashboard_email_subscribe_detail', detail_data)

    def send_mail(self, receivers_models, batch_message_content):
        """发送邮件"""
        self.info("发送邮件")
        self.info(f"cover_flag: {self.cover_flag}")
        # 是否批量发送，True：批量，False：非批量
        is_batch_send = True
        if self.is_dataset and batch_message_content:
            is_batch_send = False

        content_dict = self.replace_mail_template(is_batch_send, receivers_models, batch_message_content)
        # self.info(f"发送content_dict: {json.dumps(content_dict, ensure_ascii=False)}")

        # cover_flag 模式下,不走之前的附件，图片模式
        self.send_mail_by_batch(receivers=receivers_models, content_dict=content_dict, is_batch_send=is_batch_send)

    def replace_mail_template(self, is_batch_send, receivers_models, batch_message_content):
        """
        替换邮件模板中的占位符，获取邮件的完整发送内容
        :param is_batch_send:
        :param receivers_models:
        :param batch_message_content:
        :return:
        """
        template = self.get_email_template()
        data = {}
        # 批量发送
        if is_batch_send:
            content = self.get_email_content(template, self.message)
            data['common'] = content
        else:
            for _, r in list(receivers_models.items()):
                message_data = batch_message_content.get(r.account)
                self.info(f"{r.account} 用户正文： %s" % json.dumps(message_data, ensure_ascii=False))
                if not message_data.get("status"):
                    self.error(f"{r.account} 用户的动态内容取数异常，err：{message_data.get('msg')}")
                message = message_data.get("data") if message_data.get("status") else self.message
                content = self.get_email_content(template, message)
                data[r.account] = content
        return data

    def send_mail_by_batch(self, receivers, content_dict, is_batch_send):  # NOSONAR
        """
        :receivers 整个接收人的map  {'<EMAIL>': model, '<EMAIL>': model}
        :content   邮件内容
        :is_batch_send   发送模式 True：批量，False：单个
        """
        # 分批次发送邮件, 字典分块
        # {'<EMAIL>': model, '<EMAIL>': model, '<EMAIL>': model, '<EMAIL>': model} ->
        # [{'<EMAIL>': model, '<EMAIL>': model}, {'<EMAIL>': model, '<EMAIL>': model}]
        n = int(config.get('Email.send_person_per_time') or 10)  # 每次发送的人数
        # 单个发送模式，必须一个个发送
        if not is_batch_send:
            n = 1
        items = list(receivers.items())
        batch_receivers = [dict(items[x:x+n]) for x in range(0, len(receivers), n)] # type: [dict]
        errors = []

        for idx, batch_receiver in enumerate(batch_receivers):
            batch_result = {}
            _batch_receiver = [mail.MailContact(name=r.user_name, mail=r.email) for r in batch_receiver.values()]

            try:
                # 邮件内容获取
                if is_batch_send:
                    user_account = 'common'
                else:
                    tmp_receiver = list(batch_receiver.values())
                    user_account = tmp_receiver[0].account if tmp_receiver[0] else ''
                    if not user_account:
                        raise UserError("邮件内容获取错误-未匹配到用户账号")
                content = content_dict.get(user_account)
                if not content:
                    raise UserError(f"邮件内容获取错误-用户邮件内容为空")
                batch_result = mail.send(
                    mail.Mail(subject=self.subject, body=content, addresser_name=self.addresser,
                              receiver=_batch_receiver),
                    code=self.context.project_code,
                    subtype='html',
                    msg_image=self.mime_image if self.img_flag and not self.cover_flag else None,
                    msg_attachment=self.mime_attachment if self.attachment_flag and not self.cover_flag else None,
                    cover_flag=self.cover_flag
                ) or {}

                # 更新状态
                for email, model in batch_receiver.items():
                    if email in batch_result:
                        # 发送结果里面有这个邮箱说明这个邮箱发送失败了
                        model.status = 2
                        model.error_reason = '发送失败：%s' % str(batch_result[email])
                        model.actual_send_time = self.now()
                    else:
                        model.status = 1
                        model.error_reason = ''
                        model.actual_send_time = self.now()

            except Exception as e:
                errors.append(e)
                # 整个批次都失败了
                self.error(f'第{idx+1}批次(收件人: {[str(b) for b in _batch_receiver]})，'
                           f'发送结果：{batch_result}，发送失败：{traceback.format_exc()}')
                # 更新状态
                for email, model in batch_receiver.items():
                    model.status = 2
                    model.error_reason = '批次发送失败：%s' % self.extract_error(e)
                    model.actual_send_time = self.now()

            # 更新这个批次的数据
            self.update_email_send_detail_log(batch_receiver)

        if errors:
            raise UserError(message='发送邮件过程中出现异常，%s' % '; '.join([self.extract_error(e) for e in errors]))

    @staticmethod
    def extract_error(e: BaseException):
        return e.message if isinstance(e, UserError) else str(e)

    def get_receivers(self):
        user_ids = []
        for user in self.recipients:
            user_ids.append(user.get('id'))
        condition_string = ','.join([f"'{user_id}'" for user_id in user_ids])
        sql = f"select `name`, `email`, `id`, `account` from `dap_p_user` where `id` in ({condition_string})"
        with self.context.get_project_db() as db:
            user_data = db.query(sql)
            return user_data

    def get_email_content(self, template, message):
        """
        获取邮件的内容
        :param template: 邮件模板
        :param message: 邮件的正文内容
        :return:
        """
        replace_dict = {
            '{内容}': message,
            '{报告URL}': self.dashboard_url,
            '{报告截图}': f'cid:{self.html_image1_id}' if self.img_flag else ''
        }
        if self.cover_flag:
            replace_dict['{报告封面}'] = self.dashboard_cover
        return mail.replace_content(template.get('content'), replace_dict)

    def get_email_template(self):
        """
        获取邮件模板
        :return:
        """
        sql = 'SELECT `id`,`name`,`type`,`subject`,`content`,`send_mode` FROM dap_bi_email_template WHERE `type`=%(type)s '
        with repository.get_master_db() as db:
            db.connect()
            email_type = 6 if self.cover_flag else 3
            template = db.query_one(sql, {'type': email_type})
        if not template:
            raise Exception('不存在邮件模板')
        return template

    @property
    def dashboard_cover(self):
        if self._dashboard_cover:
            return self._dashboard_cover
        cover = repository.get_data("dap_bi_dashboard", conditions={"id": self.dashboard_id}, fields=["cover"])
        # 默认封面
        self._dashboard_cover = "https://dmp-prod.oss-cn-hangzhou.aliyuncs.com/dmp-prod/39eeecac-0c1e-5466-37e0-eb8a6130a072.png"
        if cover and cover.get("cover"):
            self._dashboard_cover = cover.get("cover")
        return self._dashboard_cover

    @property
    def mime_attachment(self):
        """获取邮件图片附件MIMEApplication对象"""
        if self._mime_attachment:
            return self._mime_attachment
        fp = open(self.img_path, 'rb')
        part = MIMEBase("application", "msword")
        part.set_payload(fp.read(), 'utf-8')
        fp.close()
        part.add_header('Content-Disposition', 'attachment', filename=f'{self.subject}.jpg')
        self._mime_attachment = part
        return self._mime_attachment

    @property
    def mime_image(self):
        """获取邮件图片MIMEImage对象"""
        if self._mime_image:
            return self._mime_image
        fp = open(self.img_path, 'rb')
        msg_image = MIMEImage(fp.read())
        fp.close()
        msg_image.add_header('Content-ID', self.html_image1_id)
        self._mime_image = msg_image
        return self._mime_image

    def set_flags(self):
        """设置邮件附件和图片的标志"""
        if not self.report_from:
            return
        report_from_data = json.loads(self.report_from)
        if 1 in report_from_data:
            self.attachment_flag = True
        if 2 in report_from_data:
            self.img_flag = True
        if 3 in report_from_data:
            self.cover_flag = True

    @property
    def img_name(self):
        return f'{self.context.flow_instance_id}.png'

    @property
    def img_path(self):
        return os.path.join(os.path.realpath(os.path.join(os.path.dirname(os.path.realpath(__file__)), '../')),
                                self.img_name)

    @property
    def dashboard_url(self):
        if self.type_access_released and self.type_access_released in ['3', 3] and config.get("ThirdParty.base_redirect_url", ""):
            url = self.get_custom_redirect_url(self.dashboard_id) or self.release_url
        else:
            url = self.release_url
        return f'{url}&email=1'

    def gen_token_cookie(self):
        return {
            'domain': f'{urlparse(self.dashboard_url).netloc}',
            'name': config.get('App.custom_cookie_token_name', 'dap_token'),
            'value': self.token,
            'expires': int(time.time()) + 7200 + 3600 * 24 * 10,
            'path': app_hosts.get_cookie_path(),
            'httpOnly': False,
            'secure': False
        }

    async def screenshot(self):
        """截图"""
        path = config.get('Chromium.path', '/usr/lib/chromium/chromium')
        browser = None
        try:
            browser = await launch(headless=True, ignoreHTTPSErrors=True, dumpio=True, executablePath=path, args=['--disable-infobars', '--no-sandbox'])
            page = await browser.newPage()
            # 设置cookie
            await page.setCookie(self.gen_token_cookie())
            await page.goto(self.dashboard_url, {'timeout': 60 * 1000, 'waitUntil': ['networkidle2']})
            # 设置视窗宽高
            await self.parse_layout(page)
            await page.setViewport({'width': self.width, 'height': self.height})
            # 等待浏览器reload
            time.sleep(5)
            # 截图
            await page.screenshot({'path': self.img_path, 'fullPage': True})
        finally:
            if browser:
                await browser.close()
            time.sleep(1)
            self.kill_crawler()

    async def parse_layout(self, page):
        # 预设一个默认值
        self.width = 1920
        self.height = 906

        if self.layout_type == '自由布局':
            # 自由布局取元数据
            try:
                self.layout = json.loads(self.layout)
                width = self.layout.get('width') or 1920
                height = self.layout.get('height') or 1080
                self.width = int(width)
                self.height = int(height)
            except JSONDecodeError:
                return
        elif self.layout_type == '标准布局':
            # 标准布局高度自己算
            if self.platform == 'mobile':
                self.width = 375
            elif self.platform == 'pc':
                self.width = 1920
            container = await page.querySelector('#dashboard-for-view-container')
            offset_height = await page.evaluate('container => container && container.offsetHeight', container)
            if offset_height:
                self.height = offset_height

    def check_msg_dataset(self):
        """
        检测邮件内容是否包括数据集字段
        :return:
        """
        res = re.findall(r'data-datasetid=\"(.*?)\"', self.message)
        if res:
            self.is_dataset = True

    async def batch_get_message_data(self, receivers_models):
        """
        批量获取每个用户的邮件动态内容
        :param receivers_models:
        :return:
        """
        # 每次批量获取简讯内容的用户数
        n = int(config.get('Email.get_message_user_per_time') or 5)
        items = list(receivers_models.items())
        batch_receivers = [dict(items[x:x + n]) for x in range(0, len(receivers_models), n)]  # type: [dict]
        batch_result = {}
        # 批次获取用户的邮件简讯正文
        for idx, batch_receiver in enumerate(batch_receivers):
            tasks = []
            for r in batch_receiver.values():
                tasks.append(self.get_message_data(idx, r.account, batch_result))
            # 一次完成多个请求任务
            await asyncio.wait(tasks)
        return batch_result

    async def get_message_data(self, idx, account, result):
        """
        异步请求调用邮件正文内容
        :param idx: 批次号
        :param account: 用户账号
        :param result: 结果存储字典
        :return: {'alex': {'status': True, 'msg': '', 'data': '邮件正文内容'},
                    'lul': {'status': True, 'msg': '', 'data': '邮件正文内容'}}
        """
        data = {'status': False, 'msg': '', 'data': {}}
        try:
            res = await self.request_msg_api(account)
            data['status'] = True
            # 获取简讯的正文
            data['data'] = res.get('description')
        except Exception as e:
            print(traceback.format_exc())
            # 该用户取数失败
            self.error(f'第{idx+1}批次 发送人: {account}，'
                       f'取数结果：{json.dumps(res)}，取数失败：{traceback.format_exc()}')
            data['msg'] = '发送人:%s 获取内容失败：%s' % (account, self.extract_error(e))
        result[account] = data
        return data

    async def request_msg_api(self, account):
        """
        请求dmp OpenAPI接口获取简讯动态内容
        :param account:
        :return:
        {'title': '4.6.5-组件跳转测试-起始页',
        'description': '2022年3月3日<br>亲爱的客户，您好：<br> 2021年8月27日11:11:54在不使用js改变DOM结构的前提下，要为span',
        'url': '/dataview/share/3a01a296-c1ca-b9fd-6178-7efac4406b65?code=uitest'}
        """
        return await async_openapi_request(project_code=self.context.project_code,
                                           open_api_route=self.email_content_api,
                                           params={"feed_id": self.id, "account": account})


class MobileSubscribeSendDetailLogModel(object):
        __slots__ = [
            'user_name', 'status', 'actual_send_time',
            'id', 'log_id', 'user_id', 'email', 'account',
            'plan_send_time', 'app_code', 'error_reason', 'retry', 'email_subscribe_id'
        ]

        def __init__(self, **kwargs):
            self.id = kwargs.get('id', '')
            self.user_name = kwargs.get('user_name', '')
            self.actual_send_time = kwargs.get('actual_send_time', None)
            self.plan_send_time = kwargs.get('plan_send_time', '')
            self.user_id = kwargs.get('user_id', '')
            self.email = kwargs.get('email', '')
            self.account = kwargs.get('account', '')
            self.error_reason = kwargs.get('error_reason', '')
            self.email_subscribe_id = kwargs.get('email_subscribe_id', '')
            self.log_id = kwargs.get('log_id', '')
            self.status = kwargs.get('status', 0)
            self.retry = kwargs.get('retry', 0)
            self.app_code = 1000

        def __str__(self):
            return '%s<%s:%s:%s>' % (self.user_name, self.email, self.status, self.actual_send_time)
