# -*- coding: UTF-8 -*-
import json
import os
import traceback
from json import JSONDecodeError
from urllib.parse import urlparse
from datetime import datetime
import logging

import asyncio
from pyppeteer import launch

from components.oss import OSSFileProxy
from flow.flow_context import FlowContext
from node.node import Node
from node.node_execution import NodeExecution
from node.node_result import <PERSON>deR<PERSON>ult
from components import config
from components import app_hosts

from http.client import HTTPException
from pyppeteer.errors import BrowserError
import pyppeteer.launcher
import time
import jwt
from urllib.error import URLError
from urllib.request import urlopen
import urllib.parse

from components.loggers import init_logging
init_logging()
logger = logging.getLogger(__name__)

# https://github.com/pyppeteer/pyppeteer/issues/135
def get_ws_endpoint(url) -> str:
    url = url + '/json/version'
    timeout = time.time() + 30
    while True:
        if time.time() > timeout:
            raise BrowserError('<PERSON>rowser closed unexpectedly:\n')
        try:
            with urlopen(url) as f:
                data = json.loads(f.read().decode())
            break
        except (URLError, HTTPException):
            pass
        time.sleep(0.1)

    return data['webSocketDebuggerUrl']


pyppeteer.launcher.get_ws_endpoint = get_ws_endpoint


class PdfExportNode(NodeExecution):
    def __init__(self, context: FlowContext, cur_node: Node):
        super().__init__(context, cur_node)
        self.ctx = None
        self.url = None
        self.token = None
        self.download_id = None
        self.dashboard_id = None
        self.dashboard_name = None
        self.width = None
        self.height = None
        self.layout = None
        self.platform = None
        self.layout_type = None
        self.is_release = True
        self.external_conditions = None
        self.conditions_from_url = False
        self.export_type = 0

    def execution(self):
        logger.info("进入pdf导出流程")
        sql = '''
        select t1.download_id, 
               t1.dashboard_id, 
               t1.release_url, 
               t1.user_token, 
               t1.external_conditions,
               t1.export_type,
               t2.name as dashboard_name, 
               t2.layout, 
               t2.platform, 
               t2.layout_type
        from dap_bi_dashboard_pdf_export_task t1 
        inner join dap_bi_dashboard t2
        on t1.dashboard_id=t2.id
        where flow_id=%(flow_id)s
        '''

        with self.context.get_project_db() as db:
            rv = db.query_one(sql, {'flow_id': self.context.flow_id})

        # 没有报告，可能已经被删除
        if not rv.get('dashboard_id'):
            raise Exception('报告不存在')

        # 导出数据类型，0：PDF，1：png图片，默认为0
        self.export_type = export_type = rv.get("export_type")
        if export_type == 1:
            self.url = rv.get('release_url')
        else:
            self.url = self.generate_url(rv.get('release_url'), rv.get('external_conditions'))
        if '/preview/' in self.url:
            self.is_release = False

        self.token = rv.get('user_token')
        self.download_id = rv.get('download_id')
        self.dashboard_id = rv.get('dashboard_id')
        self.external_conditions = rv.get('external_conditions')

        self.layout = rv.get('layout')
        self.platform = rv.get('platform')
        self.layout_type = rv.get('layout_type')
        self.dashboard_name = rv.get('dashboard_name')

        if self.is_release:
            sql = 'select name as dashboard_name from dap_bi_dashboard_released_snapshot_dashboard where id=%(dashboard_id)s and data_type=1'
            with self.context.get_project_db() as db:
                rv = db.query_one(sql, {'dashboard_id': self.dashboard_id})

            self.dashboard_name = rv.get('dashboard_name')

        return self.export()

    def export(self):
        if self.export_type == 1:
            rs = self.export_screenshot()
        else:
            rs = self.export_pdf()
        return rs

    def export_pdf(self):
        try:
            logger.info("开始导出")
            asyncio.get_event_loop().run_until_complete(self.run_with_retry())
        except (asyncio.InvalidStateError, RuntimeError, KeyboardInterrupt) as e:
            raise Exception(f'pdf导出失败, 错误内容: {str(e)}') from e
        return NodeResult(True, 'pdf导出成功')

    def export_screenshot(self):
        """
        报告图片导出
        """
        try:
            asyncio.get_event_loop().run_until_complete(self.run_export_screenshot())
        except (asyncio.InvalidStateError, RuntimeError, KeyboardInterrupt) as e:
            raise Exception(f'screenshot导出失败, 错误内容: {str(e)}') from e
        return NodeResult(True, 'screenshot导出成功')

    def generate_url(self, url, external_conditions):
        if self.conditions_from_url and external_conditions:
            # 如果前端传了external_conditions, 将external_conditions附加到url上
            rv = urllib.parse.urlparse(url)
            qs = urllib.parse.parse_qs(rv.query)
            qs['external_conditions'] = [external_conditions]
            rv = rv._replace(query=urllib.parse.urlencode(qs, doseq=True))
            return rv.geturl()
        return url

    async def parse_layout(self, page):
        # 预设一个默认值
        self.width = 1920
        self.height = 906

        if self.layout_type == '自由布局':
            # 自由布局取元数据
            try:
                layout = json.loads(self.layout)
                self.width = int(layout.get('width'))
                self.height = int(layout.get('height'))
            except JSONDecodeError:
                return
        elif self.layout_type == '标准布局':
            # 标准布局高度自己算
            if self.platform == 'mobile':
                self.width = 375
            elif self.platform == 'pc':
                self.width = 1920
            container = await page.querySelector('#dashboard-for-view-container')
            offset_height = await page.evaluate('container => container && container.offsetHeight', container)
            if offset_height:
                self.height = offset_height

    async def run_with_retry(self, retries=1):
        while True:
            try:
                print(f"导出报告<{self.dashboard_name}>")
                print(f"是否发布: {self.is_release}, url: {self.url}")
                await self.run()
                print("pdf导出成功")
                return
            except Exception as ex:
                print(traceback.format_exc())
                if retries == 0:
                    self.update_task_data({"status": 3})
                    raise Exception('导出pdf失败，错误内容：' + str(ex))
                print(f'导出pdf失败，错误内容：{str(ex)}')
                print(f'重试次数: {retries}')
                retries -= 1

    async def run(self):
        path = config.get('Chromium.path', '/usr/lib/chromium/chromium')
        timeout_config = config.get('Chromium.pdf_export_timeout', '120')
        try:
            timeout = int(timeout_config)
        except ValueError:
            timeout = 120
        browser = None
        try:
            browser = await launch(headless=True, ignoreHTTPSErrors=True, dumpio=True, executablePath=path, args=['--disable-infobars', '--no-sandbox'])
            page = await browser.newPage()
            # 设置cookie
            await page.setCookie(self.gen_token_cookie())
            await page.setCookie(self.gen_code_cookie())
            await page.setCookie(self.gen_extra_params_cookie())
            # 设置window.DMP_EXPORT_PDF_MODE
            await page.evaluateOnNewDocument(
                f'''() => {{ Object.defineProperty(window, 'DMP_EXPORT_PDF_MODE', {{ value: true }}); }} ''')
            if (not self.conditions_from_url) and self.external_conditions:
                # 设置localStorage
                await page.evaluateOnNewDocument(
                    f'''() => localStorage.setItem("external_conditions-{self.dashboard_id}", JSON.stringify({self.external_conditions})) ''')

            # 断网的场景无法访问下面两个域名：
            # https://mic-open.mypaas.com.cn
            # https://fast-logstore.mypaas.com
            # waitUntil: 'load'：等待页面触发 load 事件（默认）。
            # waitUntil: 'domcontentloaded'：等待 HTML 解析完成（DOMContentLoaded 事件）。
            # waitUntil: 'networkidle0'：等待网络空闲（500ms 内无新请求）。
            # waitUntil: 'networkidle2'：等待网络基本空闲（2 个以内的活跃请求）。
            await page.goto(self.url, {'timeout': timeout * 1000, 'waitUntil': ['networkidle2']})
            # 设置视窗宽高
            await self.parse_layout(page)
            await page.setViewport({'width': self.width, 'height': self.height})
            # 等待浏览器reload
            time.sleep(5)
            await self.check_web_render_complete(page, timeout)
            # 导出pdf
            await page.emulateMedia('screen')
            await page.pdf(path=self.pdf_path, options={'width': self.width, 'height': self.height, 'printBackground': True})
            self.upload_pdf()
        finally:
            if browser:
                await browser.close()
            self.kill_crawler()
            time.sleep(1)

    async def check_web_render_complete(self, page, timeout=60):
        """
        检查前端是否已经渲染完成
        """
        start = time.time()
        while time.time() - start < timeout:
            rs = await page.evaluate("sessionStorage.getItem('is_all_component_complete')")
            # rs = await page.evaluate("a=true;a")
            print(f"检查渲染结果：{rs}, type: {type(rs)}")
            if rs is True or rs == 'true':
                print(f"检查到渲染完成，退出")
                return
            await asyncio.sleep(1.5)
        print(f"查询前段渲染结果超时，退出检测...")

    @property
    def pdf_name(self):
        if self.is_release:
            return f"{self.dashboard_name.replace('/', '_')}_发布.pdf"
        return f"{self.dashboard_name.replace('/', '_')}_预览.pdf"

    @property
    def local_pdf_name(self):
        if self.is_release:
            return f'{self.dashboard_id}_share.pdf'
        return f'{self.dashboard_id}_preview.pdf'

    @property
    def pdf_path(self):
        return os.path.join(os.path.realpath(os.path.join(os.path.dirname(os.path.realpath(__file__)), '../')),
                                self.local_pdf_name)

    def local_screenshot_name(self, filter_val):
        filter_val = self.shorten_filename(filter_val)
        return f"{self.dashboard_name.replace('/', '_')}_{filter_val}.png"

    def screenshot_path(self, filter_val):
        return os.path.join(os.path.realpath(os.path.join(os.path.dirname(os.path.realpath(__file__)), '../')),
                            self.local_screenshot_name(filter_val))

    def upload_pdf(self):
        oss_file_url = None
        try:
            file_name = f'dashboard_pdf/{self.pdf_name}'
            oss_file_name = f'dashboard_pdf/{urllib.parse.quote(self.pdf_name)}'

            oss_file_url = OSSFileProxy().upload(open(self.pdf_path, 'rb'),
                                                 root=config.get('OSS.root'),
                                                 file_name=file_name)
            oss_file_url = oss_file_url.replace(file_name, oss_file_name)
            print("上传报告pdf到oss：" + oss_file_url)
        except BaseException as ex:
            raise Exception('上传oss失败，错误内容：' + str(ex))
        finally:
            if oss_file_url:
                self.update_task_data({"download_url": oss_file_url, "status": 2})
            if os.path.exists(self.pdf_path):
                os.remove(self.pdf_path)

    def update_task_data(self, data):
        if not data:
            return
        with self.context.get_project_db() as db:
            data["modified_on"] = datetime.strftime(datetime.now(), "%Y-%m-%d %H:%M:%S")
            db.update('dap_bi_dashboard_pdf_export_task', data,
                      {'download_id': self.download_id})

    def extract_domain(self, url):
        domain = urlparse(url).netloc
        # 去除非标准端口
        prue_domain = domain.split(':')[0]
        # return f'.{prue_domain}'
        return prue_domain

    def gen_token_cookie(self):
        return {
            'domain': self.extract_domain(self.url),
            'name': config.get('App.custom_cookie_token_name', 'dap_token'),
            'value': self.token,
            'expires': int(time.time()) + 7200 + 3600 * 24 * 10,
            'path': app_hosts.get_cookie_path(),
            'httpOnly': False,
            'secure': False
        }

    def gen_code_cookie(self):
        return {
            'domain': self.extract_domain(self.url),
            'name': 'tenant_code',
            'value': self.context.project_code,
            'expires': int(time.time()) + 7200 + 3600 * 24 * 10,
            'path': '/',
            'httpOnly': False,
            'secure': False
        }

    def gen_extra_params_cookie(self):
        try:
            decode_token = jwt.decode(self.token, '', algorithms=['HS256'], options={'verify_signature': False})
            external_params = ','.join("{}={}".format(k, v) for k, v in decode_token.get("external_params", {}).items())
        except Exception as e:
            print(f'生成external_params失败： {str(e)}')
            external_params = ''
        return {
                'domain': self.extract_domain(self.url),
                'name': 'external_params',
                'value': external_params,
                'expires': int(time.time()) + 7200 + 3600 * 24 * 10,
                'path': '/',
                'httpOnly': False,
                'secure': False
            }

    def kill_crawler(self):
        cmd = 'ps -ef | grep chromium'
        lines = os.popen(cmd)
        for line in lines:
            if line.find('grep mx_skyfile_serv') != -1:
                continue
            vars = line.split()
            pid = vars[1]  # get pid
            proc = ''.join(vars[7:])  # get proc description 1
            proc = proc if len(proc) > 20 else proc[:20]
            out = os.system('kill -9 ' + pid)
            print(f'kill <{pid}, {out}> - ' + proc)

    async def run_export_screenshot(self, retries=1):
        while True:
            try:
                print(f"报告screenshot导出开始，名称<{self.dashboard_name}>")
                print(f"是否发布: {self.is_release}, url: {self.url}")
                await self.run_screenshot()
                print("screenshot导出成功")
                return
            except Exception as ex:
                print(traceback.format_exc())
                if retries == 0:
                    self.update_task_data({"status": 3})
                    raise Exception('导出screenshot失败，错误内容：' + str(ex))
                print(f'导出screenshot失败，错误内容：{str(ex)}')
                print(f'重试次数: {retries}')
                retries -= 1

    async def run_screenshot(self):
        # 统计计时
        st = time.time()
        path = config.get('Chromium.path', '/usr/lib/chromium/chromium')
        browser = None
        # 是否写入租户库
        is_record = True
        try:
            browser = await launch(headless=True, ignoreHTTPSErrors=True, dumpio=True, executablePath=path,
                                   args=['--disable-infobars', '--no-sandbox'])
            external_conditions = self.external_conditions if self.external_conditions else ''
            dashboard_list = []
            try:
                dashboard_list = json.loads(external_conditions)
            except JSONDecodeError:
                print("报告url json格式错误")
            if not dashboard_list:
                # 默认当前报告，如果是这种情况则不写入租户表
                is_record = False
                dashboard_list = [{"url": self.url, "filter_value": "default"}]

            dashboard_num = len(dashboard_list)
            print(f"报告截图总数量：{dashboard_num}")
            batch_dashboard_list = self.get_batch_dashboard_list(dashboard_list)

            batch_result = {}
            # 批次进行报告截图
            tasks = []
            for idx, batch_dashboard in enumerate(batch_dashboard_list):
                # 一次完成多个页面截图
                tasks.append(self.run_batch_screenshot(idx, browser, batch_dashboard, batch_result))
            await asyncio.wait(tasks)

            export_num = len(batch_result.keys())
            print(f"导出报告图片完成！报告总数量：{dashboard_num} 导出数量：{export_num}")
            if export_num == dashboard_num:
                self.update_task_data({"download_url": json.dumps(batch_result, ensure_ascii=False), "status": 2})
                # 数据更新到租户库
                if is_record:
                    self.write_dashboard_screenshot_record(dashboard_list, batch_result)
                    print("报告图片记录已写入租户库")
            else:
                raise Exception('部分报告截图失败，详细请查看日志')
        finally:
            if browser:
                await browser.close()
            self.kill_crawler()
            time.sleep(1)
            dr = int(time.time() - st)
            print(f"报告截图总耗时：{dr}s")

    def write_dashboard_screenshot_record(self, dashboard_list, batch_result):
        """
        将报告的截图oss file url写入租户库
        """
        data = []
        for dashboard in dashboard_list:
            filter_val = dashboard.get("filter_value")
            key = self.get_screenshot_key(filter_val)
            screenshot_rs = batch_result.get(key)
            oss_file_url = screenshot_rs.get("oss_file_url", "")
            item = {
                "dashboard_id": self.dashboard_id,
                "dashboard_name": self.dashboard_name,
                "filter_value": filter_val,
                "dashboard_url": dashboard.get("url"),
                "screenshot_url": oss_file_url,
            }
            data.append(item)
        if data:
            # 先删除后写入
            self.del_dashboard_screenshot_data_by_dashboard_id(self.dashboard_id)
            self.add_dashboard_screenshot_data(data)

    def add_dashboard_screenshot_data(self, data):
        """
        报告截图写入租户库
        """
        with self.context.get_project_db() as db:
            fields = ['id', 'dashboard_id', 'dashboard_name', 'filter_value', 'dashboard_url', 'screenshot_url']
            db.insert_multi_data('dap_bi_dashboard_screenshot_export_record', data, fields)

    def del_dashboard_screenshot_data_by_dashboard_id(self, dashboard_id):
        """
        按报告id删除报告截图
        """
        with self.context.get_project_db() as db:
            db.delete('dap_bi_dashboard_screenshot_export_record', {"dashboard_id": dashboard_id})

    @staticmethod
    def get_batch_dashboard_list(dashboard_list):
        """
        按chromium page数量，对报告进行分组
        报告页面10个，如果开启3个chromium page，那么就是分为三组 [[1, 2, 3], [4, 5, 6], [7, 8, 9, 10]]
        """
        page_num = int(config.get('PDF.screenshot_chromium_page_num') or 3)
        dashboard_num = len(dashboard_list)
        if dashboard_num <= page_num:
            batch_dashboard_list = [dashboard_list]
        else:
            page_size = dashboard_num // page_num
            page_div_mod = dashboard_num % page_num
            batch_dashboard_list = []
            for x in range(0, page_num):
                offset = x * page_size
                if x == (page_num - 1):
                    page_size = page_size + page_div_mod
                batch_dashboard_list.append(dashboard_list[offset:offset + page_size])
        return batch_dashboard_list

    async def run_batch_screenshot(self, idx, browser, dashboard_list, batch_result):
        """
        开启一个chromium page 处理每一组的报告数据
        """
        print(f"第{idx+1}批次报告截图导出")
        print(f"当前批次报告截图数量：{len(dashboard_list)}")
        if not dashboard_list:
            return
        page = await browser.newPage()
        # 设置cookie
        await page.setCookie(self.gen_token_cookie())
        await page.setCookie(self.gen_code_cookie())
        await page.setCookie(self.gen_extra_params_cookie())

        for dashboard in dashboard_list:
            await page.goto(dashboard.get("url"), {'timeout': 60 * 1000, 'waitUntil': ['networkidle2']})
            # 设置视窗宽高，满屏截图
            await self.parse_layout(page)
            await page.setViewport({'width': self.width, 'height': self.height})
            # 等待浏览器reload
            time.sleep(3)
            await page.emulateMedia('screen')
            filter_val = dashboard.get("filter_value")
            file_name = self.local_screenshot_name(filter_val)
            file_path = self.screenshot_path(filter_val)
            await page.screenshot(path=file_path, options={'fullPage': True})
            # 图片上传
            oss_file_url = self.upload_screenshot(file_name, file_path)
            # 数据存储
            key = self.get_screenshot_key(filter_val)
            batch_result[key] = {
                "oss_file_url": oss_file_url
            }

    def get_screenshot_key(self, filter_val):
        return self.dashboard_id + '_' + str(filter_val)

    @staticmethod
    def upload_screenshot(file_name, file_path):
        try:
            path_file_name = f'dashboard_screenshot/{file_name}'
            oss_file_name = f'dashboard_screenshot/{urllib.parse.quote(file_name)}'

            oss_file_url = OSSFileProxy().upload(open(file_path, 'rb'),
                                                 root=config.get('OSS.root'),
                                                 file_name=path_file_name)
            oss_file_url = oss_file_url.replace(path_file_name, oss_file_name)
            print(f"{file_name} 上传报告screenshot到oss：" + oss_file_url)
            return oss_file_url
        except BaseException as ex:
            raise Exception('上传oss失败，错误内容：' + str(ex))
        finally:
            if os.path.exists(file_path):
                os.remove(file_path)

    @staticmethod
    def shorten_filename(filename, limit=50):
        """
        返回合适长度文件名，中间用...显示
        """
        if len(filename) <= limit:
            return filename
        else:
            return filename[:int(limit / 2) - 3] + '...' + filename[len(filename) - int(limit / 2):]
