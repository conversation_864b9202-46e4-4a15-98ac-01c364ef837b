# -*- coding: UTF-8 -*-
"""
Created on 2016年8月31日

@author: chenc04
"""
import logging

import builtins

from log_handler import init_mysql_log
from node.node_result import NodeResult

logger = logging.getLogger(__name__)


class NodeExecution:

    def __init__(self, context, cur_node):
        """
        :param flow.flow_context.FlowContext context:
        :param flow.node.Node cur_node:
        """
        # flow_context : 流程上下文类（FlowContext）
        self.context = context
        self.cur_node = cur_node
        # 项目编码赋值
        builtins.code = self.context.project_code

    def execution(self):
        """
        :return NodeResult:
        """
        return NodeResult(True)

    def debug(self, msg, *args):
        self.node_log_init()
        logging.debug(msg, *args, extra={'node_id': self.cur_node.id})

    def info(self, msg, *args):
        self.node_log_init()
        logging.info(msg, *args, extra={'node_id': self.cur_node.id})

    def warning(self, msg, *args):
        self.node_log_init()
        logging.warning(msg, *args, extra={'node_id': self.cur_node.id})

    def error(self, msg, *args):
        self.node_log_init()
        logging.error(msg, *args, extra={'node_id': self.cur_node.id})

    def exception(self, msg, *args):
        self.node_log_init()
        logging.exception(msg, *args, extra={'node_id': self.cur_node.id})

    def node_log_init(self):
        init_mysql_log(self.context.project_code,
                       self.context.flow_instance_id,
                       self.cur_node.id,
                       self.context.flow_start_time)
