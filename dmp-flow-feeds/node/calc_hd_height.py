# -*- coding: UTF-8 -*-
import json
import os
import traceback
from datetime import datetime
from json import JSONDecodeError
from urllib.parse import urlparse

import asyncio
from pyppeteer import launch

from components.oss import OSSFileProxy
from flow.flow_context import FlowContext
from node.node import Node
from node.node_execution import <PERSON>deExecution, logger
from node.node_result import <PERSON>deR<PERSON>ult
from components import config

from http.client import HTTPException
from pyppeteer.errors import BrowserError
import pyppeteer.launcher
import time
from urllib.error import URLError
from urllib.request import urlopen
import urllib.parse

from time import strftime
#
#
# # https://github.com/pyppeteer/pyppeteer/issues/135
# def get_ws_endpoint(url) -> str:
#     url = url + '/json/version'
#     timeout = time.time() + 30
#     while True:
#         if time.time() > timeout:
#             raise BrowserError('Browser closed unexpectedly:\n')
#         try:
#             with urlopen(url) as f:
#                 data = json.loads(f.read().decode())
#             break
#         except (URL<PERSON>rror, HTTPException):
#             pass
#         time.sleep(0.1)
#
#     return data['webSocketDebuggerUrl']
#
#
# pyppeteer.launcher.get_ws_endpoint = get_ws_endpoint


class CalcHDHeight(NodeExecution):

    def __init__(self, context: FlowContext, cur_node: Node):
        logger.info("start  calc hd height")

        super().__init__(context, cur_node)
        self.url = None
        self.dashboard_name = None
        self.width = 1920
        self.height = 906
        self.tab_list = []
        self.page = None
        self.calc_height_arr = {}
        self.start_time = self.now_date()
        self.finish_time = self.now_date()

    def execution(self):
        try:

            sql = '''
            select dashboard_id,flow_id,status,open_url,modified_on
            from dap_bi_dashboard_hdchart_height_task
            where flow_id=%(flow_id)s 
            '''
            logger.info("start  calc hd execution")
            with self.context.get_project_db() as db:
                rv = db.query_one(sql, {'flow_id': self.context.flow_id})

            # 没有报告，可能已经被删除
            if not rv.get('flow_id'):
                raise Exception('0011 流程不存在')
            if rv.get('status') >= 2:
                return NodeResult(True, '0012 已经执行跳过计算hd高度')
            self.url = rv.get('open_url')

            asyncio.get_event_loop().run_until_complete(self.run_with_retry())
        except (asyncio.InvalidStateError, RuntimeError, KeyboardInterrupt) as e:
            logger.info(f'0013 计算hd高度失败失败, 错误内容: {str(e)}')
            raise Exception(f'0013 计算hd高度失败失败, 错误内容: {str(e)}') from e
        return NodeResult(True, '计算hd高度成功')

    async def run_with_retry(self, retries=1):
        while True:
            try:
                print(f"开始计算hd chart url: {self.url}")
                await self.run()
                print("计算高度完成")
                return
            except Exception as ex:
                print(traceback.format_exc())
                if retries == 0:
                    self.finish_time = self.now_date()
                    self.update_task_data({"status": 3, "error_msg": str(ex), "start_time": self.start_time,
                                           "finish_time": self.finish_time})
                    raise Exception('计算高度失败，错误内容：' + str(ex))
                print(f'计算高度失败，错误内容：{str(ex)}')
                print(f'重试次数: {retries}')
                retries -= 1

    async def run(self):
        path = config.get('Chromium.path', '/usr/lib/chromium/chromium')
        browser = None
        try:
            #browser = await launch(headless=False, userDataDir=r'D:\temporary', ignoreHTTPSErrors=True, dumpio=True,args=['--disable-infobars', '--no-sandbox'])

            browser = await launch(headless=True, ignoreHTTPSErrors=True, dumpio=True, executablePath=path,
                                   args=['--disable-infobars', '--no-sandbox'])
            self.page = await browser.newPage()
            await self.page.goto(self.url, {'timeout': 10 * 60 * 1000, 'waitUntil': ['networkidle2']})
            await self.page.setViewport({'width': self.width, 'height': self.height})
            # 等待浏览器reload
            time.sleep(2)
            tpl_data = await self.page.evaluate(pageFunction='''() => { 
                                    return  window.tplData.data
                                    }''', force_expr=False)  # force_expr=False  执行的是函数
            self.tab_list = []
            child_chart_id_arr = []
            for chart in tpl_data:
                if chart['type'] == 'tabs':
                    self.tab_list.append(chart)
                if not chart['parent_id']:
                    child_chart_id_arr.append(chart['component_set_id'])
            await self.calc_tab_child_height(child_chart_id_arr)
            await self.click_tab(None)
            print(self.calc_height_arr)
            self.finish_time = self.now_date()
            self.update_task_data(
                {"chart_height_data": json.dumps(self.calc_height_arr), "status": 2, "start_time": self.start_time,
                 "finish_time": self.finish_time})
        except BaseException as ex:
            logger.info(traceback.format_exc())
            print('catch error')
            print(traceback.format_exc())
            raise Exception('计算hd组件高度，错误内容：' + str(ex))
        finally:
            if self.page:
                self.page.close()
            if browser:
                await browser.close()
            self.kill_crawler()
            time.sleep(1)

    async def click_tab(self, click_com_id):  # NOSONAR
        for tab_chart in self.tab_list:
            if (not click_com_id and not tab_chart['parent_id']) or tab_chart['component_set_id'] == click_com_id:
                component_set_id = tab_chart['component_set_id']
                css_select = f'#component_{component_set_id}>div>div>ul>li'
                tab_children_list = []

                wait_m = 0
                while wait_m < 5:  # 组件加载是需要时间的，最多等待5s
                    tab_children_list = await self.page.querySelectorAll(css_select)
                    if not tab_children_list:
                        await asyncio.sleep(1)
                        wait_m += 1
                    else:
                        break

                for i in range(len(tab_children_list)):
                    await self.page.click(css_select + f':nth-child({i + 1})')
                    await asyncio.sleep(1)
                    print(component_set_id + ':' + str(i))
                    tab_children_coms = tab_chart['config']['list'][i]['coms']
                    tab_child_height_arr = [coms.get('com_id') for coms in tab_children_coms]
                    await self.calc_tab_child_height(tab_child_height_arr)  # 计算子类的高度
                    print(tab_child_height_arr)
                    for com in tab_children_coms:
                        if com['type'] == 'tabs':
                            await self.click_tab(com['com_id'])

    async def calc_tab_child_height(self, child_chart_id_arr):
        for child_chart_id in child_chart_id_arr:
            client_height = 0
            wait_m = 0
            while wait_m < 5:  # 组件加载是需要时间的，最多等待5s
                child_chart_obj = await self.page.querySelector(f'#component_{child_chart_id}')
                if child_chart_obj:
                    client_height = await child_chart_obj.getProperty("clientHeight")
                    break
                else:
                    await asyncio.sleep(1)
                    wait_m += 1

            self.calc_height_arr[child_chart_id] = (await client_height.jsonValue())

    def update_task_data(self, data):
        with self.context.get_project_db() as db:
            db.update('dap_bi_dashboard_hdchart_height_task', data,
                      {'flow_id': self.context.flow_id})

    @staticmethod
    def now_date():
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def kill_crawler(self):
        cmd = 'ps -ef | grep chromium'
        lines = os.popen(cmd)
        for line in lines:
            if line.find('grep mx_skyfile_serv') != -1:
                continue
            vars_line = line.split()
            pid = vars_line[1]  # get pid
            proc = ''.join(vars_line[7:])  # get proc description 1
            proc = proc if len(proc) > 20 else proc[:20]
            out = os.system('kill -9 ' + pid)
            print(f'kill <{pid}, {out}> - ' + proc)
