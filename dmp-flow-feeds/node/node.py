# -*- coding: UTF-8 -*-
"""
Created on 2016年8月31日

@author: chenc04
"""
from enum import Enum


class Node:
    __slots__ = ['id', 'name', 'flow_id', 'type', 'is_start', 'is_end', 'content', 'node_instance']

    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.name = kwargs.get('name')
        self.flow_id = kwargs.get('flow_id')
        self.type = kwargs.get('type')
        self.is_start = kwargs.get('is_start')
        self.is_end = kwargs.get('is_end')
        self.content = kwargs.get('content')
        self.node_instance = None


class NodeInstance:
    __slots__ = ['id', 'instance_id', 'node_id', 'is_start', 'is_end', 'type', 'content', 'startup_time', 'end_time',
                 'status',
                 'message']

    def __init__(self, **kwargs):
        self.id = kwargs.get('id')
        self.instance_id = kwargs.get('instance_id')
        self.node_id = kwargs.get('node_id')
        self.is_start = kwargs.get('is_start')
        self.is_end = kwargs.get('is_end')
        self.type = kwargs.get('type')
        self.content = kwargs.get('content')
        self.startup_time = kwargs.get('startup_time')
        self.end_time = kwargs.get('end_time')
        self.status = kwargs.get('status')
        self.message = kwargs.get('message')


class NodeType(Enum):
    COLLECT = '采集'
    ODPSSQL = 'ODPS_SQL'
    SYNC = '同步'
    LABEL = '标签'
    PORTRAIT = '360'
    SAAS = 'SaaS'
    MAPPING = '映射'
    CHART = '单图'
    GEOCODER = '地理编码'
    MAPGRID = '城市栅格'
    ORGANIZATION = '组织架构'
    DOWNLOAD = '下载'
    DATASET = '数据集'


class NodeInstanceStatus(Enum):
    CREATE = '已创建'
    RUNNING = '运行中'
    SUCCESS = '已成功'
    FAILED = '已失败'
