import sys
import time

from pika.adapters.blocking_connection import BlockingConnection
from pika.connection import ConnectionParameters
from pika.credentials import PlainCredentials
from components import config


def check_and_cleanup_rabbitmq():
    try:
        # 连接到RabbitMQ服务器
        credentials = PlainCredentials(config.get('RabbitMQ.user', 'guest'), config.get('RabbitMQ.password', 'guest'))
        connection = BlockingConnection(
            parameters=ConnectionParameters(config.get('RabbitMQ.host'), int(config.get('RabbitMQ.port', 5672)), credentials=credentials)
        )

        # 打开一个通道
        channel = connection.channel()

        # 定义一个测试队列
        test_queue_name = 'test_queue_{}'.format(int(time.time()))
        channel.queue_declare(queue=test_queue_name)

        # 发送一条测试消息
        channel.basic_publish(exchange='',
                              routing_key=test_queue_name,
                              body='Hello, RabbitMQ!')

        # 从队列获取消息（这只是为了验证队列非空，你可以忽略消息）
        method_frame, header_frame, body = channel.basic_get(queue=test_queue_name)
        if method_frame:
            print(f"Received a message: {body}")

        # 删除队列
        channel.queue_delete(queue=test_queue_name)

        # 关闭通道和连接
        channel.close()
        connection.close()

        print("RabbitMQ service is up and running, and the test queue has been deleted.")
        return True
    except Exception as e:
        print(f"RabbitMQ service is not available: {str(e)}")
        sys.exit(-1)


if __name__ == '__main__':
    check_and_cleanup_rabbitmq()
