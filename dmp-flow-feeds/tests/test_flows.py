import unittest
import traceback

from tests import BaseTest
from flow.flow_control import FlowControl


class TestProject(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='test', account='admin')

    def test_flow_control(self):

        data = {
            "project_code": "test",
            "flow_id": "39f759ad-fc5a-1030-27c5-6925d7a6b10c",
            "flow_instance_id": "39f76659-1b31-7988-1053-7c096c655d3c",
            "test_run": "1",
        }
        # FlowLauncher(**data).run_flow()
        FlowControl(**data).run()

    def test_flow_control_email(self):
        try:
            data = {
                "project_code": "uitest",
                "flow_id": "3a025680-e3d8-2230-1a4e-a79da4f621ef",
                "flow_instance_id": "3a025ba6-bf6c-c28e-f2b8-b18026963c05",
                "test_run": "1",
            }
            FlowControl(**data).run()
        except Exception as e:
            print(str(e))
            traceback.format_exc()


if __name__ == '__main__':
    unittest.main()
