import unittest
import functools

from tests import BaseTest
import asyncio
import aiohttp
import time

from components.async_openapi import async_openapi_request
from components.errors import UserError


class TestAsyncio(BaseTest):
    def __init__(self, method_name="runTest"):
        super().__init__(method_name, code='uitest', account='admin')

    async def get_info(self, url):
        async with aiohttp.ClientSession() as session:
            async with session.get(url, timeout=5) as resp:
                rs = await resp.text()
                return rs[0:10]

    @staticmethod
    def callback(url, result, future):
        """
        怎么获取异常信息
        :param url: 
        :param result: 
        :param future: 
        :return: 
        """
        rs = {'status': False, 'msg': '', 'data': {}}
        if future.exception():
            msg = future.exception().message if isinstance(future.exception(), UserError) else str(future.exception())
            rs['msg'] = msg
            result.update({url: rs})
        else:
            rs['status'] = True
            rs['data'] = future.result()
            result.update({url: rs})

    def test_openapi(self):
        st = time.time()
        tasks = []
        result_data = dict()
        for i in range(1, 6):
            future = asyncio.ensure_future(
                async_openapi_request(
                    project_code='uitest', open_api_route='/msg/content', params={'a': i})
            )
            future.add_done_callback(functools.partial(self.callback, i, result_data))
            tasks.append(future)
        loop = asyncio.get_event_loop()
        loop.run_until_complete(asyncio.wait(tasks))
        print(result_data)
        print('耗时', time.time() - st)

    def test_request(self):
        st = time.time()
        tasks = []
        result_data = dict()
        for i in range(1, 51):
            url = f"https://www.cnblogs.com/#p{i}"
            future = asyncio.ensure_future(self.get_info(url))
            future.add_done_callback(functools.partial(self.call_back, url, result_data))
            tasks.append(future)
        loop = asyncio.get_event_loop()
        result = loop.run_until_complete(asyncio.wait(tasks))
        if result:
            data = result[0]
            for item in data:
                print(item.result())

        print(result_data)
        print('耗时', time.time()-st)


if __name__ == '__main__':
    unittest.main()
