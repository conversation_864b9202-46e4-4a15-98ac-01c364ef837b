# -*- coding: UTF-8 -*-
from datetime import datetime
import asyncio
from json import JSONDecodeError

from pyppeteer import launch

from http.client import HTTPException
from pyppeteer.errors import BrowserError
import pyppeteer.launcher
from urllib.error import URLError
from urllib.request import urlopen

import json
import os
import time
import traceback
from email.mime.base import MIMEBase
from email.mime.image import MIMEImage
from urllib.parse import urlparse, urlencode, urlunparse

from components import config
from components import mail
from components import repository
from node.node_result import NodeResult

class Context:
    project_code = 'beta'


# https://github.com/pyppeteer/pyppeteer/issues/135
def get_ws_endpoint(url) -> str:
    url = url + '/json/version'
    timeout = time.time() + 30
    while True:
        if time.time() > timeout:
            raise BrowserError('Browser closed unexpectedly:\n')
        try:
            with urlopen(url) as f:
                data = json.loads(f.read().decode())
            break
        except (URL<PERSON>rror, HTTPException):
            pass
        time.sleep(0.1)

    return data['webSocketDebuggerUrl']


pyppeteer.launcher.get_ws_endpoint = get_ws_endpoint


class EmailFeedsNode:
    def __init__(self, context: Context):
        self.context = context

        self.id = None
        self.dashboard_id = None
        self.dashboard_name = None
        self.token = None
        self.release_url = None
        self.type_access_released = None
        self.share_secret_key = None

        # 邮件信息
        self.report_from = None
        self.subject = None
        self.message = None
        self.addresser = None
        self.recipients = None

        # flag
        self.img_flag = False
        self.attachment_flag = False
        self.cover_flag = False

        # 内部属性
        self._mime_image = None
        self._mime_attachment = None
        self._dashboard_cover = None

        self.html_image1_id = 'img1'

    def large(self):
        return {
            'id': '39fd4a09-f8f7-4893-5e3d-************',
            'dashboard_id': '39fd49ff-4bbc-5dc0-93ab-e9068a47497f',
            'release_url': 'http://dmp-test.mypaas.com.cn/dataview/share/39fd49ff-4bbc-5dc0-93ab-e9068a47497f?code=zdtest',
            'user_token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************.oxkC-CU-qqYLz5D9vJhLUk4xtc2bXpgaGBiCevsTMSk',
            'layout_type': '自由布局',
            'platform': 'pc',
            'layout': {"mode":"free","platform":"pc","ratio":"16:9","width":1920,"height":1080,"lattice":10,"toolbar":"show","screenHeader":"show","slider_top":0,"layout_mode":"none","card_radius":0},
        }

    def middle_short(self):
        return {
            'user_token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************.oxkC-CU-qqYLz5D9vJhLUk4xtc2bXpgaGBiCevsTMSk',
            'id': '39fd4a0a-4772-5258-3fa8-455b85dc9500',
            'release_url': 'http://dmp-test.mypaas.com.cn/dataview/share/39fd49fe-d9a8-9669-9c60-1bf36dca5799?code=zdtest',
            'dashboard_id': '39fd49fe-d9a8-9669-9c60-1bf36dca5799',
            'layout_type': '标准布局',
            'platform': 'pc',
            'layout': {"mode":"grid","platform":"pc","ratio":"16:9","width":1920,"height":1080,"lattice":10,"toolbar":"show","screenHeader":"show","slider_top":0,"layout_mode":"none","card_radius":0}
        }

    def middle_long(self):
        return {
            'user_token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************.oxkC-CU-qqYLz5D9vJhLUk4xtc2bXpgaGBiCevsTMSk',
            'id': '39fd4a0a-914c-126a-83d6-7dbb7fb76426',
            'release_url': 'http://dmp-test.mypaas.com.cn/dataview/share/39fd49fe-9a45-dce2-e8c0-be0c5c2435d1?code=zdtest',
            'dashboard_id': '39fd49fe-9a45-dce2-e8c0-be0c5c2435d1',
            'layout_type': '标准布局',
            'platform': 'pc',
            'layout': {"mode":"grid","platform":"pc","ratio":"16:9","width":1920,"height":1080,"lattice":10,"toolbar":"show","screenHeader":"show","slider_top":0,"layout_mode":"none","card_radius":0}
        }

    def mobile_short(self):
        return {
            'user_token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************.oxkC-CU-qqYLz5D9vJhLUk4xtc2bXpgaGBiCevsTMSk',
            'id': '39fd4a09-389a-9e4b-f21a-bd998e534a9f',
            'release_url': 'http://dmp-test.mypaas.com.cn/dataview/share/39fd49ff-d1e0-2e14-db9f-5cf9928438ed?code=zdtest',
            'dashboard_id': '39fd49ff-d1e0-2e14-db9f-5cf9928438ed',
            'layout_type': '标准布局',
            'platform': 'mobile',
            'layout': {"mode": "grid", "platform": "mobile", "toolbar": "show", "screenHeader": "show", "slider_top": 0,
                       "layout_mode": "none", "card_radius": 0},
        }

    def mobile_long(self):
        return {
            'user_token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************.oxkC-CU-qqYLz5D9vJhLUk4xtc2bXpgaGBiCevsTMSk',
            'id': '39fd4a09-9e76-ce26-7fdc-ca14dcf22852',
            'release_url': 'http://dmp-test.mypaas.com.cn/dataview/share/39fd49ff-8eeb-57d8-d267-95f5b9a8d934?code=zdtest',
            'dashboard_id': '39fd49ff-8eeb-57d8-d267-95f5b9a8d934',
            'layout_type': '标准布局',
            'platform': 'mobile',
            'layout': {"mode":"grid","platform":"mobile","toolbar":"show","screenHeader":"show","slider_top":0,"layout_mode":"none","card_radius":0}
        }

    def beta(self):
        return {
            'id': '39fd4098-f18c-b244-4f12-baf10343b912',
            'dashboard_id': '39fd4097-2021-fab2-ee95-b181ee3a9918',
            'release_url': 'http://dmp-dbeta.mypaas.com.cn/dataview/share/39fd4097-2021-fab2-ee95-b181ee3a9918?code=beta',
            'user_token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************.LLepEdSJAwe695pUnUCtm9yG_jq9xnyfQ-kEJMFV4CA',
            'layout_type': '标准布局',
            'platform': 'pc',
            'layout': {"mode":"grid","platform":"pc","ratio":"16:9","width":1920,"height":1080,"lattice":10,"toolbar":"show","screenHeader":"show","slider_top":0,"layout_mode":"none","card_radius":0}
        }



    def execution(self):
        # rv = self.mobile_long()
        # rv = self.mobile_short()
        rv = self.middle_long()
        # rv = self.middle_short()
        # rv = self.large()
        # rv = self.beta()

        self.id = rv.get('id')
        self.dashboard_id = rv.get('dashboard_id')
        self.dashboard_name = rv.get('dashboard_name')
        self.token = rv.get('user_token')
        self.release_url = rv.get('release_url')
        self.type_access_released = rv.get('type_access_released')
        self.share_secret_key = rv.get('share_secret_key')

        self.layout_type = rv.get('layout_type')
        self.platform = rv.get('platform')
        self.layout = rv.get('layout')

        # 邮件信息
        self.report_from = rv.get('report_from')
        self.subject = rv.get('subject_email')
        self.message = rv.get('message')
        self.addresser = rv.get('addresser')
        # self.recipients = json.loads(rv.get('recipients'))

        # 没有报告名称，说明已经被删除
        if not self.dashboard_id:
            raise Exception('报告不存在')

        self.update_email_detail()
        self.set_flags()

        try:
            asyncio.get_event_loop().run_until_complete(self.run_with_retry())
        except (asyncio.InvalidStateError, RuntimeError, KeyboardInterrupt) as e:
            raise Exception(f'邮件订阅失败, 错误内容: {str(e)}') from e
        return NodeResult(True, '邮件订阅成功')

    async def run_with_retry(self, retries=0):
        while True:
            try:
                print(f"导出报告<{self.dashboard_name}>, url: {self.dashboard_url}")
                await self.run()
                return
            except Exception as ex:
                print(traceback.format_exc())
                if retries == 0:
                    raise Exception('导出pdf失败，错误内容：' + str(ex))
                print(f'导出pdf失败，错误内容：{str(ex)}')
                print(f'重试次数: {retries}')
                retries -= 1

    def get_custom_redirect_url(self, dashboard_id):
        """
        获取第三方自定义跳转url
        :param dashboard_id:
        :return:
        """

        base_redirect_url = config.get("ThirdParty.base_redirect_url", "")

        # 获取biz_code
        result = repository.get_data("dap_bi_dashboard", {"id": dashboard_id}, ["biz_code"])
        biz_code = result.get("biz_code")

        # 拼接后的url请求参数
        parsed_url = urlparse(base_redirect_url)
        extend_url_params = urlencode({"biz_code": biz_code, "code": self.context.project_code})
        new_query_params = parsed_url.query + "&" + extend_url_params if parsed_url.query else extend_url_params

        # 重新组装url
        new_redirect_url = urlunparse(
            [
                parsed_url.scheme,
                parsed_url.netloc,
                parsed_url.path,
                parsed_url.params,
                new_query_params,
                parsed_url.fragment,
            ]
        )
        return new_redirect_url

    async def run(self):
        await self.screenshot()
        print(f"报告截图：{self.img_path}")

    def kill_crawler(self):
        cmd = 'ps -ef | grep chromium'
        lines = os.popen(cmd)
        for line in lines:
            if line.find('grep mx_skyfile_serv') != -1:
                continue
            vars = line.split()
            pid = vars[1]  # get pid
            proc = ''.join(vars[7:])  # get proc description 1
            proc = proc if len(proc) > 20 else proc[:20]
            out = os.system('kill -9 ' + pid)
            print(f'kill <{pid}, {out}> - ' + proc)

    def update_email_detail(self, oss_file_url=None):
        pass

    def send_mail(self):
        pass

    def get_user_email_content(self, is_clear_img=True, cover_flag=False):
        """
        获取邮件内容
        :param email_subscribe_data:
        :param is_clear_img:
        :param cover_flag:
        :return:
        """
        # 获取邮件模板
        sql = 'SELECT id,name,type,subject,content,send_mode FROM dap_bi_email_template WHERE type=%(type)s '
        with repository.get_master_db() as db:
            db.connect()
            email_type = 6 if cover_flag else 3
            template = db.query_one(sql, {'type': email_type})
        if not template:
            raise Exception('不存在邮件模板')
        replace_dict = {
            '{内容}': self.message,
            '{报告URL}': self.dashboard_url,
            '{报告截图}': '' if is_clear_img else 'cid:' + self.html_image1_id
        }
        if cover_flag:
            replace_dict['{报告封面}'] = self.dashboard_cover()
        return mail.replace_content(template.get('content'), replace_dict)

    @property
    def dashboard_cover(self):
        if self._dashboard_cover:
            return self._dashboard_cover
        cover = repository.get_data("dap_bi_dashboard", conditions={"id": self.dashboard_id}, fields=["cover"])
        # 默认封面
        self._dashboard_cover = "https://dmp-prod.oss-cn-hangzhou.aliyuncs.com/dmp-prod/39eeecac-0c1e-5466-37e0-eb8a6130a072.png"
        if cover and cover.get("cover"):
            self._dashboard_cover = cover.get("cover")
        return self._dashboard_cover

    @property
    def mime_attachment(self):
        """获取邮件图片附件MIMEApplication对象"""
        if self._mime_attachment:
            return self._mime_attachment
        fp = open(self.img_path, 'rb')
        part = MIMEBase("application", "msword")
        part.set_payload(fp.read(), 'utf-8')
        fp.close()
        part.add_header('Content-Disposition', 'attachment', filename=f'{self.subject}.jpg')
        self._mime_attachment = part
        return self._mime_attachment

    @property
    def mime_image(self):
        """获取邮件图片MIMEImage对象"""
        if self._mime_image:
            return self._mime_image
        fp = open(self.img_path, 'rb')
        msg_image = MIMEImage(fp.read())
        fp.close()
        msg_image.add_header('Content-ID', self.html_image1_id)
        self._mime_image = msg_image
        return self._mime_image

    def set_flags(self):
        pass

    @property
    def img_name(self):
        return f'suiyi.png'

    @property
    def img_path(self):
        return os.path.join(os.path.realpath(os.path.join(os.path.dirname(os.path.realpath(__file__)), '../')),
                                self.img_name)

    @property
    def dashboard_url(self):
        if self.type_access_released and self.type_access_released in ['3', 3]:
            url = self.get_custom_redirect_url(self.dashboard_id) or self.release_url
        else:
            url = self.release_url
        return f'{url}&email=1'

    def gen_token_cookie(self):
        return {
            'domain': f'.{urlparse(self.dashboard_url).netloc}',
            'name': 'token',
            'value': self.token,
            'expires': int(time.time()) + 7200 + 3600 * 24 * 10,
            'path': '/',
            'httpOnly': False,
            'secure': False
        }

    async def screenshot(self):
        """截图"""
        browser = None
        try:
            browser = await launch(headless=True,
                                   ignoreHTTPSErrors=True,
                                   dumpio=True,
                                   # executablePath=path,
                                   args=['--disable-infobars', '--no-sandbox'])
            page = await browser.newPage()
            # 设置cookie
            await page.setCookie(self.gen_token_cookie())
            await page.goto(self.dashboard_url, {'timeout': 60 * 1000, 'waitUntil': ['networkidle0']})
            # 设置视窗宽高
            await self.parse_layout(page)
            print(self.height)
            await page.setViewport({'width': self.width, 'height': self.height})
            # 等待浏览器reload
            time.sleep(5)

            # 导出pdf
            await page.screenshot({'path': self.img_path, 'fullPage': True})
        finally:
            if browser:
                await browser.close()
            time.sleep(1)
            self.kill_crawler()

    async def parse_layout(self, page):
        # 预设一个默认值
        self.width = 1920
        self.height = 906

        if self.layout_type == '自由布局':
            # 自由布局取元数据
            try:
                self.width = self.layout.get('width')
                self.height = self.layout.get('height')
            except JSONDecodeError:
                return
        elif self.layout_type == '标准布局':
            # 标准布局高度自己算
            if self.platform == 'mobile':
                self.width = 375
            elif self.platform == 'pc':
                self.width = 1920
            container = await page.querySelector('#dashboard-for-view-container')
            offset_height = await page.evaluate('container => container && container.offsetHeight', container)
            if offset_height:
                self.height = offset_height


if __name__ == '__main__':
    context = Context()

    node = EmailFeedsNode(context)
    print(datetime.now())
    node.execution()
    print(datetime.now())

