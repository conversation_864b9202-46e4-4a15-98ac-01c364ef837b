# -*- coding: UTF-8 -*-
"""
Created on 2016年8月31日

@author: chenc04
"""
import json
import os
import time
import datetime
import logging
import sys
import builtins
import traceback
import subprocess

from components import config
from components.message_queue import MessageQueue
from components.my_threading import Thread
from components.wechat_robot import SubscribeRobot
from flow.flow import FlowInstanceStatus
from flow.flow_context import FlowContext
from flow.flow_repository import FlowRepository
from log_handler import init_mysql_log

logger = logging.getLogger(__name__)


class FlowControl:
    def __init__(self, **kwargs):
        # 当前流程实例id
        self.project_code = kwargs.get('project_code')
        self.flow_id = kwargs.get('flow_id')
        self.flow_instance_id = kwargs.get('flow_instance_id')
        self.test_run = int(kwargs.get('test_run', 1)) == 1
        self.context = FlowContext(self.project_code, self.flow_id, self.flow_instance_id)
        self.flow_repository = FlowRepository(self.context)
        self._params_validate()
        self.flow_run_thread = None
        self.check_instance_thread = None
        self.current_parent_file_path = os.path.realpath(
            os.path.join(os.path.dirname(os.path.realpath(__file__)), '../'))
        self.current_process = None
        self.flow_run_result = (FlowInstanceStatus.FAILED.value, '错误')
        self.flow_start_timestamp = datetime.datetime.utcnow().timestamp()
        self._init_log_handler()

    def _init_log_handler(self):
        init_mysql_log(self.context.project_code, self.context.flow_instance_id, None, self.context.flow_start_time)

    def _params_validate(self):
        if not self.project_code:
            raise Exception('缺少项目编码')
        if not self.flow_id:
            raise Exception('缺少流程Id')
        if not self.test_run and not self.flow_instance_id:
            raise Exception('缺少流程实例Id')
        builtins.code = self.project_code
        flow_data = self.flow_repository.get_flow(self.flow_id)
        if not flow_data:
            raise Exception('流程不存在')
        elif not self.flow_repository.get_flow_instance_by_id(self.flow_instance_id):
            raise Exception('流程实例不存在')
        self.context.flow_start_time = datetime.datetime.now()

    def run(self):
        logger.info("--------------------------start control flow-----------------------")
        # 更新流程实例
        self.flow_repository.updata_flow_instance(
            {'status': FlowInstanceStatus.RUNNING.value,
             'startup_time': self.context.flow_start_time.strftime('%Y-%m-%d %H:%M:%S')
             }, self.flow_instance_id)

        self.flow_run_thread = Thread(target=self._run_flow)
        self.flow_run_thread.start()
        self.check_instance_thread = Thread(target=self._check_flow_instance_status)
        self.check_instance_thread.start()
        self.flow_run_thread.join()
        self.check_instance_thread.join()

        status, log = self.flow_run_result
        self.flow_repository.updata_flow_instance({'status': status, 'end_time': self.get_now_time()},
                                                  self.flow_instance_id)
        self.flow_repository.updata_activity_by_instance(
            {'status': FlowInstanceStatus.FAILED.value, 'end_time': self.get_now_time()},
            dict(instance_id=self.flow_instance_id, status=FlowInstanceStatus.RUNNING.value))
        logger.info(log) if status == FlowInstanceStatus.SUCCESS.value else logger.error(log)
        self.send_depend_flow_message()

        logger.info("--------------------------end control flow-----------------------")
        self._send_message_to_wechat()

    def _run_flow(self):
        """
       启动流程进程函数
       :return: pid 进程id
       """
        try:
            # 执行flow进程
            exec_export_path = os.path.join(self.current_parent_file_path, "app_flow_exec.py")
            command = [sys.executable, exec_export_path, self.project_code, self.flow_id, self.flow_instance_id,
                       '1' if self.test_run else '0']
            self.current_process = subprocess.Popen(command, stderr=subprocess.PIPE)
            logger.info("启动项目code：{project_code}，流程id：{flow_id}，流程实例id：{flow_instance_id}流程执行，"
                        "进程id：{pid}。".format(project_code=self.project_code, flow_id=self.flow_id,
                                             flow_instance_id=self.flow_instance_id, pid=str(self.current_process.pid)))
            logger.info("开始执行sub_process")
            stdout, stderr = self.current_process.communicate(timeout=172800)
            if self.current_process.returncode != 0:
                stderr = str(stderr, encoding='utf-8') if stderr else ''
                self._error_flow('Flow执行异常：' + stderr)
            else:
                self.flow_run_result = (FlowInstanceStatus.SUCCESS.value, '流程实例运行成功')
        except subprocess.TimeoutExpired:
            self.current_process.kill()
            logger.error("项目code：{project_code}，流程id：{flow_id}，流程实例id：{flow_instance_id}，"
                         "流程运行超过2天,系统强制停止！".format(project_code=self.project_code, flow_id=self.flow_id,
                                                   flow_instance_id=self.flow_instance_id))
            self.flow_run_result = (FlowInstanceStatus.TERMINATION.value, "流程运行超过2天,系统强制停止！")
        except BaseException as exception:
            self._error_flow("启动flow进程错误，错误内容：{e}".format(e=str(exception), ), exception)

    def _error_flow(self, error_str, exception=None):
        if self._get_flow_instance_status() != FlowInstanceStatus.TERMINATION.value:
            if exception:
                logger.error(traceback.format_exc())
            self.flow_run_result = (FlowInstanceStatus.FAILED.value, error_str)
        else:
            self.flow_run_result = (FlowInstanceStatus.TERMINATION.value, "中止流程")

    def _check_flow_instance_status(self):
        while True:
            if not self.flow_run_thread.is_alive():
                # 终止当前线程
                self.check_instance_thread.terminate()
            instance_status = self._get_flow_instance_status()
            if instance_status != FlowInstanceStatus.RUNNING.value:
                if instance_status == FlowInstanceStatus.TERMINATION.value:
                    self.current_process.kill()
                self.flow_run_thread.is_alive() and self.flow_run_thread.terminate()
                self.check_instance_thread.terminate()
            time.sleep(3)

    def _get_flow_instance_status(self):
        """
        获取实例状态
        :return:bool
        """
        try:
            instance = self.flow_repository.get_flow_instance_by_id(self.flow_instance_id)
        except BaseException as e:
            logger.error('获取实例状态失败：' + self.flow_instance_id)
            logger.error(e)
            return False
        if not instance:
            logger.error('实例不存在')
            return False
        return instance.get('status')

    def send_depend_flow_message(self):
        if self.test_run or self.flow_run_result[0] != FlowInstanceStatus.SUCCESS.value:
            return
        depend_flow_list = self.flow_repository.get_depend_flow(self.flow_id)
        if not depend_flow_list:
            return

        mq = MessageQueue()
        queue_name = config.get('RabbitMQ.queue_name_flow', 'Flow')
        for depend_flow in depend_flow_list:
            flow_data = self.flow_repository.get_flow(depend_flow.get('id'))
            if not flow_data:
                continue
            flow_instance_id = self.flow_repository.create_flow_instance(flow_data)
            body = {
                'project_code': self.context.project_code,
                'flow_id': depend_flow.get("id"),
                'flow_instance_id': flow_instance_id,
                'test_run': "0",
                "root_flow_id": self.flow_id
            }
            mq.send_message(queue_name, json.dumps(body), durable=False)
            logger.info("发送依赖流程消息：" + depend_flow.get('id'))

    def _get_flow_instance(self):
        """
        获取实例
        :return:bool
        """
        return self.flow_repository.get_flow_instance_by_id(self.flow_instance_id)

    def _send_message_to_wechat(self):
        """
        将异常订阅信息推送企业微信
        :return:
        """
        # get flow instance
        instance = self._get_flow_instance()
        name, flow_type, status = instance['name'], instance['type'], instance['status'] if instance else ('', '', '')
        # check flow type and status
        if flow_type == '订阅' and status == '已失败':
            url = config.get("Subscribe.warning_url")
            dommain = config.get("Domain.dmp")
            detail_url = "/".join(
                [dommain, "feeds/detail/mail", self.flow_id]
            )
            client = config.get("Product.env_name")
            robot = SubscribeRobot(
                robot_url=url,
                client=client,
                tenant=self.project_code, subscribe_name=name, detail_url=detail_url, sub_type="邮件")
            robot.send_message()
            return True
        return False

    @staticmethod
    def get_now_time():
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
