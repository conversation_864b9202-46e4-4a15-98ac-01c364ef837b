# -*- coding: UTF-8 -*-
"""
Created on 2016年8月31日

@author: chenc04
"""
from components.repository import SimpleMysql, get_master_db, get_db_config


class FlowContext:
    def __init__(self, project_code, flow_id, flow_instance_id=None, flow_start_time=None):
        self.project_code = project_code
        self.flow_id = flow_id
        self.flow_instance_id = flow_instance_id
        self.flow_start_time = flow_start_time
        self._project = None
        self._odps = None
        self._rds = None

    def get_project(self):
        """
        获取项目详细信息
        :return:
        """
        if self._project:
            return self._project
        sql = 'SELECT * FROM dap_p_tenant WHERE code = %(project_code)s'
        with get_master_db() as db:
            db.connect()
            self._project = db.query_one(sql, {'project_code': self.project_code})
        return self._project

    def get_rds_config(self, suffix=None):
        return get_db_config(self.project_code, suffix)

    def get_project_db(self):
        """
        根据项目编码获取项目库连接
        :return:
        """
        rds_config = self.get_rds_config()
        return SimpleMysql(**rds_config)

    def get_project_data_db(self):
        """
        根据项目编码获取项目Data库连接
        :return:
        """
        rds_config = self.get_rds_config('data')
        return SimpleMysql(**rds_config)

    @staticmethod
    def list_dict_group_by(data_list, key):
        result = {}
        if data_list and isinstance(data_list, (list, tuple)):
            for item in data_list:
                if isinstance(item, dict):
                    if isinstance(key, list):
                        result.setdefault('.'.join([item.get(k) for k in key]), []).append(item)
                    else:
                        result.setdefault(item.get(key), []).append(item)
        return result

    class UserException(Exception):
        pass
