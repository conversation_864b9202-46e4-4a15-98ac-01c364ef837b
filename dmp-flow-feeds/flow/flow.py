# -*- coding: UTF-8 -*-
'''
Created on 2016年8月31日

@author: chenc04
'''
from enum import Enum


class Flow:
    
    def __init__(self,flow_id,flow_name, **kw):
        self.id = flow_id
        self.name=flow_name


class FlowInstance:
    def __init__(self, flow_id, flow_name, **kw):
        self.id = flow_id
        self.name = flow_name


class FlowInstanceStatus(Enum):
    CREATE = '已创建'
    RUNNING = '运行中'
    SUCCESS = '已成功'
    FAILED = '已失败'
    TERMINATION = '已中止'
