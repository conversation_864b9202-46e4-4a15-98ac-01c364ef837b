# -*- coding: UTF-8 -*-
"""
Created on 2016年8月31日

@author: chenc04
"""
import datetime
import logging
import threading
from enum import Enum
from flow.flow_context import FlowContext
from flow.flow_repository import FlowRepository
from log_handler import init_mysql_log
from node.calc_hd_height import Calc<PERSON><PERSON><PERSON><PERSON>
from node.check_dashboard import CheckDashboard
from node.email_feeds_node import EmailFeedsNode
from node.node import NodeInstanceStatus
from node.node_result import NodeResult
from node.pdf_export_node import PdfExportNode

logger = logging.getLogger(__name__)


class NodeType(Enum):
    EmailFeeds = '邮件订阅'
    PdfExport = 'PDF导出'
    CalcHDHeight = '计算HD组件高度'
    CheckDashboard = '检测报表是否正常'


class FlowLauncher:
    def __init__(self, **kwargs):
        # 当前流程实例id
        self.project_code = kwargs.get('project_code')
        self.flow_id = kwargs.get('flow_id')
        self.flow_instance_id = kwargs.get('flow_instance_id')
        self.test_run = int(kwargs.get('test_run', 1)) == 1
        self.context = FlowContext(self.project_code, self.flow_id, self.flow_instance_id)
        self.flow_repository = FlowRepository(self.context)
        self._init_log_handler()
        self.flow_run_result = (False, '错误')
        self.flow_start_timestamp = datetime.datetime.utcnow().timestamp()
        self.context.flow_start_time = datetime.datetime.now()

    def _init_log_handler(self):
        init_mysql_log(self.context.project_code, self.context.flow_instance_id, None, self.context.flow_start_time)

    def run_flow(self):
        """
        运行流程
        :return:
        """
        # 当前所有运行的节点实例
        current_node_instances = {}

        # 获取流程下所有的节点
        list_dict_node = self.flow_repository.get_nodes(self.context.flow_id)

        # 校验流程是否合法
        flag = self.check_node_integrity(list_dict_node)
        if not flag:
            raise Exception('流程id：' + self.context.flow_id + '节点为空或包含多个根节点，请检查流程节点配置。')

        # 将列表节点转换为字典（node_id -> node）
        nodes = self.flow_repository.list_dict_to_dict_node(list_dict_node)

        # 获取根节点
        root_node = self.get_root_node(nodes)
        if not root_node:
            raise Exception('流程未设置根节点')

        # 创建执行节点实例
        node_instance = self.flow_repository.create_node_instance(self.flow_instance_id, root_node)

        # 更新节点实例的状态
        node_instance.status = NodeInstanceStatus.RUNNING.value
        self.flow_repository.updata_node_instance(dict(status=node_instance.status), node_instance.id)

        # 将节点实例加入当前运行节点列表中
        current_node_instances[root_node.id] = node_instance

        try:
            # 执行节点
            node_executor = NodeExecutor(self.context, self.flow_repository, root_node, node_instance)
            root_node_instance = node_executor.do_execution()
            # 根节点出错整个流程终止
            if root_node_instance.status != NodeInstanceStatus.SUCCESS.value:
                raise Exception(root_node_instance.message)
            return True, '流程运行成功'
        except BaseException as e:
            raise Exception('节点出错，流程终止。错误內容：' + str(e))

    @staticmethod
    def check_node_integrity(list_dict_node):
        flag = True
        i = 0
        if not list_dict_node:
            return False
        for d in list_dict_node:
            if d["is_start"] == 1:
                i += 1
        if i != 1:
            flag = False
        return flag

    @staticmethod
    def get_root_node(nodes):
        for k, v in nodes.items():
            if v.is_start == 1:
                return v
        return None

    @staticmethod
    def get_now_time():
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def check_previous_execution(self, node, current_node_instances):
        flag = True
        previous_node_ids = self.flow_repository.get_after_node_ids(node.id)
        for k in previous_node_ids:
            if k.get('ahead_node_id') not in current_node_instances:
                flag = False
                break
        return flag

    def __getstate__(self):
        self_dict = self.__dict__.copy()
        del self_dict['my_pool']
        return self_dict


class NodeExecThread(threading.Thread):
    def __init__(self, thread_id, context, flow_repository, node, node_instance):
        threading.Thread.__init__(self)
        self.threadID = thread_id
        self.context = context
        self.flow_repository = flow_repository
        self.node = node
        self.node_instance = node_instance
        self.flag = True

    def run(self):
        logger.error("run：" + self.node.id)
        node_executor = NodeExecutor(self.context, self.flow_repository, self.node, self.node_instance)
        self.flag = node_executor.do_execution()

    def get_result(self):
        logger.error("获取结果：" + self.node.id + "," + str(self.flag))
        return self.flag


class NodeExecutor:
    def __init__(self, context, flow_repository, node, node_instance):
        self.context = context
        self.flow_repository = flow_repository
        self.node = node
        self.node.node_instance = node_instance
        self.node_instance = node_instance

    def do_execution(self):
        # 节点运行状态，默认成功
        try:
            execution_node = self._get_execution_node()
            result = execution_node.execution()
        except BaseException as e:
            logging.exception(e)
            result = NodeResult(False, str(e))

        if result:
            # 根据执行返回结果更新节点实例的状态
            if result.result:
                self.node_instance.status = NodeInstanceStatus.SUCCESS.value
            else:
                self.node_instance.status = NodeInstanceStatus.FAILED.value

            self.node_instance.message = result.msg
            self.node_instance.end_time = datetime.datetime.now().strftime("%y-%m-%d %H:%M:%S")

            logging.info('节点类型：%s   节点状态：%s' % (self.node.type, self.node_instance.status))

            self.flow_repository.updata_node_instance(
                dict(status=self.node_instance.status, message=self.node_instance.message,
                     end_time=self.node_instance.end_time),
                self.node_instance.id)
        else:
            self.flow_repository.updata_node_instance(
                dict(status=NodeInstanceStatus.FAILED.value, message="节点执行错误。",
                     end_time=datetime.datetime.now().strftime("%y-%m-%d %H:%M:%S")),
                self.node_instance.id)
            self.node_instance.status = NodeInstanceStatus.FAILED.value

        return self.node_instance

    def _get_execution_node(self):
        """
        获取执行节点
        :return node.node_execution.NodeExecution
        """
        if NodeType.EmailFeeds.value == self.node.type:
            return EmailFeedsNode(self.context, self.node)
        if NodeType.PdfExport.value == self.node.type:
            return PdfExportNode(self.context, self.node)
        if NodeType.CalcHDHeight.value == self.node.type:
            return CalcHDHeight(self.context, self.node)
        if NodeType.CheckDashboard.value == self.node.type:
            return CheckDashboard(self.context, self.node)
        else:
            raise Exception('未知节点类型：' + str(self.node.type))
