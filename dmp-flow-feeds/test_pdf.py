# -*- coding: UTF-8 -*-
import json
import os
import time
import traceback
from urllib.parse import urlparse
import asyncio

from datetime import datetime
from pyppeteer import launch
import urllib.parse

class PdfNode:
    def __init__(self, context):
        self.context = context

    async def execution(self):
        # dashboard_id = '39f8416d-16fb-427a-9a13-c44c9ddf04f8' # 仪表盘超高
        # dashboard_id = '39e6a84f-05c5-800d-827e-bc3e3b355e1e' # 大屏
        # dashboard_id = '39f8cc99-ff7f-6ab3-bf83-548a823376bc' # 移动端
        # dashboard_id = '39f8c85b-8df9-52d8-c059-474db9b89c05' # failed - 仪表盘
        # dashboard_id = '39f84317-03ae-0e9b-d2ca-66e143d00432' #
        # dashboard_id = '39eb4811-33ef-d427-cd03-e6474e0dc406' # 100w
        # dashboard_id = '39e9bcf5-7acb-9f8f-6ef2-1803cc9bf5f4' # api 100w
        # dashboard_id = '39f1fbca-54ed-e962-e151-db844bb6fe52' # 移动
        # dashboard_id = '39f8f198-c5ff-afb9-0e64-4d4a4f71b77a' # 仪表盘
        # dashboard_id = '39f4fb00-fa4c-8631-d0bf-916cc5671ae4' # 地图
        # dashboard_id = '39f7402b-cf56-8055-42f1-5eca828cfd6b' # 时间筛选器
        dashboard_id = '39fd400b-333e-bee6-837c-610464eb375c'

        # release_url = f'http://dmp-test4.mypaas.com.cn/dataview/share/{dashboard_id}?code={self.context.project_code}' # pc
        # release_url = f'http://dmp-test.mypaas.com.cn/dataview-mobile/view/{dashboard_id}?code={self.context.project_code}' # 移动
        # release_url = f'http://dmp-test.mypaas.com.cn/dataview/share/{dashboard_id}?code={self.context.project_code}' # pc
        # token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************.zz5R5buBGymjdjOO69YT_7-ZmC4JSFkCISG6j_e1cDI'
        # token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************.mdzcC2td38ZcM71lY-1Z1Jl4gOGZV-026WbmUcUOAJs'

        release_url = 'https://dmp-dbeta.mypaas.com.cn/dataview/preview/39fd400b-333e-bee6-837c-610464eb375c'
        token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************.yqF3XrWbNsm6iEwSLHiUjgyEKNhuAyuD_zd1NlsFxSI'
        # release_url = 'http://dmp-test4.mypaas.com.cn/dataview/preview/39f8f6c4-c74b-b1e4-c317-b580375fdd8d?code=uitest'
        # release_url = 'https://dmp-test.mypaas.com.cn/dataview/preview/39f590ba-d29f-d6c7-6f03-650fe372cb80?code=test&external_conditions=%5B%5D'
        # token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************.7d_MptzMMn3ln7ADN6YOeu9FXwWvNftSv0vAEwkSWX8'
        # token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************.PNSq0ue9Zab7nUqAFUGb7m-ZYRNE7KauL6ocJzltMyw'
        ctx = {
            'dashboard_id': dashboard_id,
            'release_url': release_url,
            'user_token': token,
            'external_conditions': ''
        }
        await self.run(ctx)

    def get_token_cookie(self, ctx, url):
        return {
            'domain': f'.{urlparse(url).netloc}',
            'name': 'token',
            'value': ctx.get('user_token'),
            'expires': int(time.time()) + 7200 + 3600 * 24 * 100000,
            'path': '/',
            'httpOnly': False,
            'secure': False
        }

    def get_code_cookie(self, ctx, url):
        return {
            'domain': f'.{urlparse(url).netloc}',
            'name': 'tenant_code',
            'value': self.context.project_code,
            'expires': int(time.time()) + 7200 + 3600 * 24 * 100000,
            'path': '/',
            'httpOnly': False,
            'secure': False
        }

    async def get_width_height(self, page):
        # 自由布局(大屏)宽高从元数据拿(固定的, 1920 x 906)
        # 固定布局(仪表板和新移动端)宽高从页面中拿
        container = await page.querySelector('#dashboard-for-view-container')
        offset_height = await page.evaluate('container => container && container.offsetHeight', container)
        return 1920, offset_height + 80 if offset_height else 906

    async def screenshot_after(self, page, seconds):
        print(seconds)
        await asyncio.sleep(seconds)
        return await page.screenshot({'path': f'screenshot{seconds}.png'})

    # .dmp-dbeta.mypaas.com.cn
    async def run(self, ctx):
        url = self.generate_url(ctx.get('release_url'), ctx.get('external_conditions'))
        print(url)
        # path = '/Applications/Chromium.app/Contents/MacOS/Chromium'
        try:
            browser = await launch(headless=False,
                                   ignoreHTTPSErrors=True,
                                   # executablePath=path,
                                   dumpio=True,
                                   args=['--disable-infobars', '-–no-sandbox'])
            page = await browser.newPage()
            # 设置cookie
            external_conditions = ctx.get('external_conditions')
            code = f'''() => {{
                localStorage.setItem("external_conditions-{ctx.get('dashboard_id')}", JSON.stringify({external_conditions}));
            }}'''
            await page.evaluateOnNewDocument(code)
            await page.evaluateOnNewDocument(f'''() => {{ Object.defineProperty(window, 'DMP_EXPORT_PDF_MODE', {{ value: true }}); }} ''')
            await page.setCookie(self.get_token_cookie(ctx, url))
            await page.setCookie(self.get_code_cookie(ctx, url))
            await page.goto(url, {'timeout': 100 * 1000, 'waitUntil': ['networkidle0']}),

            # 设置视窗宽高
            time.sleep(10000)
            width, height = await self.get_width_height(page)
            time.sleep(10)
            await page.setViewport({'width': width, 'height': height})
            # wait reload
            time.sleep(5)
            # 导出pdf
            await page.emulateMedia('screen')
            await page.pdf(path='example.pdf', options={'width': width, 'height': height, 'printBackground': True})
        except BaseException as ex:
            print('catch error')
            print(traceback.format_exc())
            raise Exception('导出pdf失败，错误内容：' + str(ex))
        finally:
            self.kill_crawler()
            time.sleep(1)
        print("成功，更新数据。")

    def generate_url(self, url, external_conditions):
        # 如果前端传了external_conditions, 将external_conditions附加到url上
        if external_conditions:
            rv = urllib.parse.urlparse(url)
            qs = urllib.parse.parse_qs(rv.query)
            qs['external_conditions'] = [external_conditions]
            rv = rv._replace(query=urllib.parse.urlencode(qs, doseq=True))
            return rv.geturl()
        return url

    def kill_crawler(self):
        cmd = 'ps -ef | grep chromium'
        lines = os.popen(cmd)
        for line in lines:
            if line.find('grep mx_skyfile_serv') != -1:
                continue
            vars = line.split()
            pid = vars[1]  # get pid
            proc = ''.join(vars[7:])  # get proc description 1
            out = os.system('kill -9 ' + pid)
            if out == 0:
                print('success! kill ' + pid + ' ' + proc)
            else:
                print('failed! kill ' + pid + ' ' + proc)

class Context:
    project_code = 'beta'

if __name__ == '__main__':
    context = Context()

    node = PdfNode(context)
    print(datetime.now())
    asyncio.get_event_loop().run_until_complete(node.execution())
    print(datetime.now())

