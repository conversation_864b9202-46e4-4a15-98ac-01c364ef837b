#FROM python:3.9.17-buster as builder
FROM docker-cdv5ju.swr-pro.myhuaweicloud.com/global/base/python:3.9.17-full as builder

ARG PLATFORM=x86

ENV DM_HOME="/home/<USER>/dmp-flow-feeds/dmdbms"
ENV LD_LIBRARY_PATH="$DM_HOME/bin:${LD_LIBRARY_PATH}"
ENV PATH="$PATH:$DM_HOME/bin"

COPY requirement.txt ./

RUN pip install --upgrade pip setuptools -i https://mirrors.aliyun.com/pypi/simple/ && \
    pip install --no-cache-dir -r requirement.txt -i https://mirrors.aliyun.com/pypi/simple/

RUN if [ "$PLATFORM" = "arm" ]; then \
        wget -P /dmp-agent https://mic-open.oss-cn-hangzhou.aliyuncs.com/ops/config-agent/v5/agent-arm64 && mv /dmp-agent/agent-arm64 /dmp-agent/agent && \
        wget -P /home/<USER>/dmp-flow-feeds/dmdbms https://oss-cn-shenzhen.aliyuncs.com/dmp-test/tools/dm/arm64/dmdbms.zip && \
        cd /home/<USER>/dmp-flow-feeds/dmdbms && unzip dmdbms.zip && rm -rf dmdbms.zip; \
    else \
        wget -P /dmp-agent -o agent http://mic-open.oss-cn-hangzhou.aliyuncs.com/ops/agent && \
        wget -P /home/<USER>/dmp-flow-feeds/dmdbms https://oss-cn-shenzhen.aliyuncs.com/dmp-test/tools/dm/x86/dmdbms.zip && \
        cd /home/<USER>/dmp-flow-feeds/dmdbms && unzip dmdbms.zip && rm -rf dmdbms.zip; \
    fi


RUN cd /home/<USER>/dmp-flow-feeds/dmdbms/dmPython && python setup.py install && \
    cd /home/<USER>/dmp-flow-feeds/dmdbms/sqlalchemy2.0.0 && python setup.py install


#FROM python:3.9.17-slim-buster
FROM docker-cdv5ju.swr-pro.myhuaweicloud.com/global/base/python:3.9.17-slim-base

COPY --from=builder /usr/local/lib/python3.9 /usr/local/lib/python3.9
COPY --from=builder /dmp-agent/agent /dmp-agent/agent
COPY --from=builder /home/<USER>/dmp-flow-feeds/dmdbms /home/<USER>/dmp-flow-feeds/dmdbms

ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
ENV DM_HOME="/home/<USER>/dmp-flow-feeds/dmdbms"
ENV LD_LIBRARY_PATH="$DM_HOME/bin:${LD_LIBRARY_PATH}"
ENV PATH="$PATH:$DM_HOME/bin"

COPY run.sh /tmp/run.sh
COPY . /home/<USER>/dmp-flow-feeds/

RUN pip install --upgrade pip setuptools -i https://mirrors.aliyun.com/pypi/simple/  && \
#    echo "deb https://mirrors.aliyun.com/debian/ buster main non-free contrib" > /etc/apt/sources.list && \
#    echo "deb-src https://mirrors.aliyun.com/debian/ buster main non-free contrib" >> /etc/apt/sources.list && \
#    echo "deb https://mirrors.aliyun.com/debian-security buster/updates main" >> /etc/apt/sources.list && \
#    echo "deb-src https://mirrors.aliyun.com/debian-security buster/updates main" >> /etc/apt/sources.list && \
#    echo "deb https://mirrors.aliyun.com/debian/ buster-updates main non-free contrib" >> /etc/apt/sources.list && \
#    echo "deb-src https://mirrors.aliyun.com/debian/ buster-updates main non-free contrib" >> /etc/apt/sources.list && \
    apt-get update && \
    apt-get upgrade -y && \
#    apt-get install -y --allow-unauthenticated --no-install-recommends \
#    ttf-wqy-microhei \
#    ttf-wqy-zenhei \
#    perl \
#    gconf-service \
#    libasound2 \
#    libatk1.0-0 \
#    libc6 \
#    libcairo2 \
#    libcups2 \
#    libdbus-1-3 \
#    libexpat1 \
#    libfontconfig1 \
#    libgcc1 \
#    libgconf-2-4 \
#    libgdk-pixbuf2.0-0 \
#    libglib2.0-0 \
#    libgtk-3-0 \
#    libnspr4 \
#    libpango-1.0-0 \
#    libpangocairo-1.0-0 \
#    libstdc++6 \
#    libx11-6 \
#    libx11-xcb1 \
#    libxcb1 \
#    libxcomposite1 \
#    libxcursor1 \
#    libxdamage1 \
#    libxext6 \
#    libxfixes3 \
#    libxi6 \
#    libxrandr2 \
#    libxrender1 \
#    libxss1 \
#    libxtst6 \
#    ca-certificates \
#    fonts-liberation \
#    libappindicator1 \
#    libnss3 \
#    wget \
#    unzip \
#    chromium && \
#    mkdir /chrome && wget -P /chrome http://dmp-test.oss-cn-shenzhen.aliyuncs.com/tool/chrome-linux.zip && \
#    cd /chrome && unzip chrome-linux.zip && rm -rf chrome-linux.zip && \
#    mkdir -p /usr/lib/chromium && ln -s /chrome/chrome-linux/chrome /usr/lib/chromium/chromium && \
#    chmod +x /usr/lib/chromium/chromium && apt-get autoremove -y wget unzip && \
    apt-get autoremove -y wget unzip && \
    chmod +x /dmp-agent/agent && \
    chmod +x /tmp/run.sh && \
    cp -f /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    rm -rf /var/lib/apt/lists/*


COPY . /home/<USER>/dmp-flow-feeds/

RUN if [ "$PLATFORM" = "arm" ]; then \
        cd /home/<USER>/dmp-flow-feeds && mv lib/HTPClient components/HTPClient; \
    fi

WORKDIR /home/<USER>/dmp-flow-feeds/

CMD ["/tmp/run.sh"]
