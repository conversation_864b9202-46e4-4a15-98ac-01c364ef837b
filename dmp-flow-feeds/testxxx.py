# coding:utf8
import asyncio
import json

from pyppeteer import launch

from pyppeteer.network_manager import Request, Response

launch_args = {
    "headless": False,
    "args": [
        "--start-maximized",
        "--no-sandbox",
        "--disable-infobars",
        "--ignore-certificate-errors",
        "--log-level=3",
        "--enable-extensions",
        "--window-size=1020,1080",
        "--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.97 Safari/537.36",
    ],
}


async def modify_url(request: Request):
    print(f'{request.method}: {request.url}')
    await request.continue_()


async def get_content(response: Response):
    """
        # 注意这里不需要设置 page.setRequestInterception(True)
        page.on("response", get_content)
    :param response:
    :return:
    """
    if 'dashboard_chart/chart/data' in response.url or 'dashboard_chart/chart/get_total' in response.url:
        content = await response.text()
        content_obj = json.loads(content)
        if content_obj.get('data') and isinstance(content_obj.get('data'), dict):
            for key, data in content_obj.get('data').items():
                if data.get('execute_status') != 200:
                    list.append(f"{key}:{data.get('msg')}")
                    return


async def interception_test():
    # 启动浏览器
    browser = await launch(**launch_args)
    # 新建标签页
    page = await browser.newPage()
    # 设置页面打开超时时间
    page.setDefaultNavigationTimeout(10 * 1000)
    # 设置窗口大小
    await page.setViewport({"width": 1000, "height": 1000})

    # 启用拦截器
    await page.setRequestInterception(True)

    # 设置拦截器
    # 1. 修改请求的url
    page.on("request", modify_url)
    page.on("response", get_content)
    await page.goto(
        'http://dmp-test4.mypaas.com.cn/api/user/sso/login?_from=oa&access_token=ZXlKaGJHY2lPaUpJVXpJMU5pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SjBaVzVoYm5SZlkyOWtaU0k2SW5WcGRHVnpkQ0lzSW1GalkyOTFiblFpT2lKMWFYUmxjM1FpTENKeVpXUnBjbVZqZENJNklpOW9iMjFsSW4wLmxONUotLVRXS1NFUFRIQTR1ckE1eHVIcTZPM2dpT3Brcmkxem9TV0pRZWc=')
    await page.goto('http://dmp-test4.mypaas.com.cn/dataview/preview/3a0dbb35-c84d-b6c5-1bf9-78e9b2465b5d')

    await asyncio.sleep(10)
    print(list)
    # 关闭浏览器
    await page.close()
    await browser.close()
    return


list = []
if __name__ == "__main__":
    loop = asyncio.get_event_loop()
    loop.run_until_complete(interception_test())
