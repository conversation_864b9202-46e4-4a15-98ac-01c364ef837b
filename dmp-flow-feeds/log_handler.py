#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
    class
    Created by wangl10 on 2017/2/24.
"""
import datetime
import logging
import time

from components.repository import SimpleMysql
from components import config
from components.sql_adapter import adapter_sql

class MysqlHandler(logging.Handler):
    def __init__(self, rds, project_code, flow_instance_id, node_id=None, log_start_time=None, level=logging.NOTSET):
        super().__init__(level)
        self.rds = rds
        self.project_code = project_code
        self.flow_instance_id = flow_instance_id
        self.node_id = node_id
        self.table_name = (log_start_time if log_start_time else datetime.datetime.now()).strftime('%Y%m%d')
        self.conn = None
        self.cursor = None
        # self._create_log_table()

    def _get_connection(self):
        return SimpleMysql(host=self.rds.get('host'),
                           port=int(self.rds.get('port')),
                           database=self.rds.get('database'),
                           user=self.rds.get('username'),
                           password=self.rds.get('password'),
                           db_type=self.rds.get('DB.type')
                           )

    def _create_log_table(self):
        with self._get_connection() as db:
            sql = adapter_sql("table_is_exist", db.db_type)
            result = db.query_scalar(sql, {"table_name": self.table_name})
        if not result:
            with self._get_connection() as db:
                sql = adapter_sql("create_log_table", db.db_type).format(table_name=self.table_name)
                db.exec(sql)

    def emit(self, record):
        try:
            # 过滤系统日志
            if record.levelname == "INFO" or record.levelname == "WARNING":
                if record.name == "requests.packages.urllib3.connectionpool":
                    return
                if record.name == "pika.adapters.base_connection":
                    return
                if record.name == "pika.adapters.blocking_connection":
                    return
                if record.name == "pika.channel":
                    return
                if record.name == "pika.connection":
                    return
            if record.name == "rediscluster.client":
                return
            if 'redis' in record.name:
                return

            sql = (
                "INSERT INTO `{table_name}` ("
                "`project_code`,"
                "`flow_instance_id`,"
                "`node_id`,"
                "`level_name`,"
                "`level_no`,"
                "`created`,"
                "`file_name`,"
                "`module`,"
                "`func_name`,"
                "`path_name`,"
                "`line_no`,"
                "`process`,"
                "`process_name`,"
                "`thread`,"
                "`thread_name`,"
                "`exc_text`,"
                "`message`) "
                "VALUES (:project_code,:flow_instance_id,:node_id,:level_name,:level_no,:created,:file_name,:module,:func_name,:path_name,:line_no,:process,:process_name,:thread,:thread_name,:exc_text,:message)".format(
                    table_name=self.table_name)
            )

            params = {
                "project_code": self.project_code,
                "flow_instance_id": self.flow_instance_id,
                "node_id": record.node_id if 'node_id' in dir(record) else (self.node_id if self.node_id else ''),
                "level_name": record.levelname,
                "level_no": record.levelno if record.levelno else 0,
                "created": time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(record.created)),
                "file_name": record.filename,
                "module": record.module,
                "func_name": record.funcName,
                "path_name": record.pathname,
                "line_no": record.lineno if record.lineno else 0,
                "process": record.process if record.process else 0,
                "process_name": record.processName,
                "thread": record.thread if record.thread else 0,
                "thread_name": record.threadName,
                "exc_text": record.exc_text,
                "message": record.getMessage()
            }

            # with self._get_connection() as db:
            #     db.exec(sql, params)
        except Exception as ex:
            record.msg = record.getMessage() + ' WriteLogException:' + str(ex)
            self.handleError(record)


def remove_mysql_log():
    logger = logging.getLogger()
    if logger.hasHandlers():
        for handler in logger.handlers:
            if isinstance(handler, MysqlHandler):
                logger.removeHandler(handler)


def init_mysql_log(project_code, flow_instance_id, node_id=None, log_start_time=None):
    logging.basicConfig(level=config.get('Log.level'))
    logger = logging.getLogger()
    if logger.hasHandlers():
        for handler in logger.handlers:
            if isinstance(handler, MysqlHandler):
                return
    rds = {
        'host': config.get('DB.host'),
        'port': int(config.get('DB.port')),
        'database': config.get('DB.flow_log_database'),
        'username': config.get('DB.user'),
        'password': config.get('DB.password'),
        'DB.type': config.get('DB.db_type')
    }
    mysql_handler = MysqlHandler(rds, project_code, flow_instance_id, node_id, log_start_time)
    logger.addHandler(mysql_handler)
