#!/bin/bash
#set -e
#if [ "$DEPLOYMENT" == local ]
#then
#  echo local
#else
#  /dmp-agent/agent
#fi
#

cd /home/<USER>/datacloud-public-server

# 执行表结构和数据迁移
python -u backend/migrate.py &

# 启动发布中心服务
#python3 -m uvicorn backend.main:app --host 0.0.0.0 --port 9000 --workers 2
python -u backend/main.py


# 将所有的日志文件出输出到/dev/stdout
#rfp=/tmp/logs_path.txt
#supervisor_conf=deploy/supervisord.conf
#python  -u  supervisor_helper.py   $supervisor_conf   $rfp
#tail -F $( cat $rfp | sed ':a;N;$!ba;s/\n/ /g' ) | grep --line-buffered -v  '==> \/var\/log\/supervisor.* <=='  | grep -v '^$' >> /dev/stdout &

# 启动服务
# uvicorn backend.main:app --host 0.0.0.0 --port 9000 --workers 2
#supervisord -n -c /etc/supervisor/supervisord.conf

