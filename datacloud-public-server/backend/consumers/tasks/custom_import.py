import json
import os
import shutil

from backend.app.import_dist.crud.import_dist import update_one_import_record, get_rdc_detail, add_one_import_detail, \
    get_import_detail
from backend.app.import_dist.model.import_dist import ImportDist
from backend.common.enums import ImportStatus, TriggerDapImport, ImportPackageType
from backend.common.jwt import CookiesUser
from backend.common.log import log
from backend.consumers.consumer import Consumer
from backend.common.domain import get_server_domain
from backend.common.request import HTTPRequestUtil
from backend.common.task_context import TaskContextManager, TotalState, SpawnTaskManager
from backend.core.conf import settings
from backend.database.db_util import CurrentSession, get_db, db_session
from backend.utils.timezone import timezone
from backend.database.db_util import CurrentSession, uuid4_str
from backend.common import utils
from backend.common.oss.oss import OSSFileProxy
from backend.database.db_redis import redis_client
from backend.common import config


# def check_dmp_export_data(data: dict):
#     return data.get('data', {}).get('status') == '成功'
#
#
def check_dap_import_data(data: dict):
    datas = data.get('data') or []
    if datas:
        return all([d.get('status') in ['执行成功', '执行失败'] for d in datas])
    return False


def check_dmp_export_data(data: dict):
    return data.get('data', {}).get('status') == '成功' or \
        data.get('data', {}).get('status') == '失败'


def check_dap_export_data(data: dict):
    print(data)
    return data.get('data', {}).get('status') == '执行成功' or \
        data.get('result', False) is False or \
        '失败' in str(data)


class CustomImportConsumer(Consumer):
    """
    定制空间的包导入
    """
    timeout = settings.APP__IMPORT_TIMEOUT

    async def process(self, task):
        db = db_session()
        task_id = task.get('task_id') or ''
        dap_import_id = task.get('dap_import_id') or ''
        rdc_task_id = task.get('rdc_task_id') or ''
        dmp_args = task.get('dmp_args') or {}
        dap_args = task.get('dap_args') or {}
        user_data = task.get('user') or {}
        tenant_code = dmp_args.get('rdc_data', {}).get('tenant_code') or ''
        backup = task.get('backup') if task.get('backup') is not None else True
        user_model = CookiesUser(**user_data)

        dmp_host = get_server_domain('dmp')
        dmp_admin_host = get_server_domain('dmp_admin')
        dap_host = get_server_domain('dap')
        headers = await HTTPRequestUtil.build_common_access_headers(user_model)
        import_detail = await get_import_detail(db, task_id)

        # 置为运行中
        await update_one_import_record(
            db, task_id,
            {
                'status': ImportStatus.running.value, 'created_by': user_model.user_code,
                'updated_by': user_model.user_code, 'execute_update_time': timezone.now_str()
            }
        )
        total_state = TotalState(name='更新中心导入')
        spawn_task = SpawnTaskManager()

        try:
            # 先备份导入数据
            # 备份添加开关
            if backup:
                await self.import_backup(db, user_model, headers, dmp_host, dap_host, import_detail, total_state,
                                         task_id)

            # 1. 数见重新打包（根据勾选资源id）
            dmp_oss_url = dmp_args.get('oss_url')
            if dmp_oss_url:
                new_dmp_oss_url = await self.dmp_repackage(
                    db, user_model, rdc_task_id, dmp_host,
                    tenant_code, total_state, dmp_args, dmp_oss_url
                )

                # 2. 触发数见包导入，数见接口是同步导入
                await self.import_dmp_package(
                    db, user_model, task_id, dmp_admin_host, tenant_code, total_state, dmp_args,
                    new_dmp_oss_url
                )
                # 2.1 触发数见主题关系处理
                await self.dmp_subject_deal_after_import(
                    db, user_model, rdc_task_id, dmp_host, tenant_code, total_state, dmp_args,
                    new_dmp_oss_url
                )
            else:
                with TaskContextManager('数见资源处理', '导入没有数见资源', total_state=total_state,
                                        will_raise=True) as f:
                    f.record('没有导入数见资源，无需打包')

            if dap_import_id:
                # 3. 数芯确认导入
                await self.trigger_export_dap(dap_host, headers, total_state, dap_args, import_detail, db)

                # 4. 查询数芯导入结果
                dap_task = spawn_task.create_task(
                    self.import_dap_status(task_id, dap_host, headers, dap_import_id, total_state, user_model, db)
                )
                await spawn_task.wait_and_done()
            else:
                with TaskContextManager('数芯资源处理', '导入没有数芯资源', total_state=total_state,
                                        will_raise=True) as f:
                    f.record('导入没有数芯资源，无需打包')

            # 将状态置为已成功
            await update_one_import_record(
                db, task_id,
                {
                    'status': ImportStatus.importDone.value, 'created_by': user_model.user_code,
                    'log': json.dumps(total_state.model_dump(), ensure_ascii=False),
                    'updated_by': user_model.user_code,
                    'execute_update_time': timezone.now_str()
                }
            )
        except Exception as e:
            log.error(f"导入失败： {str(e)}")
            # 更新最终状态为失败
            await update_one_import_record(db, task_id, {
                'created_by': user_model.user_code, 'updated_by': user_model.user_code,
                'status': ImportStatus.importError.value,
                'log': json.dumps(total_state.model_dump(), ensure_ascii=False),
                'execute_update_time': timezone.now_str()
            })

    async def dmp_repackage(
            self, db, user_model, rdc_task_id, dmp_host,
            tenant_code, total_state, dmp_args, oss_url
    ):
        """dmp资源重新打包"""
        with TaskContextManager('数见资源打包', '根据选择资源重新打包', total_state=total_state, will_raise=True) as f:
            headers = await HTTPRequestUtil.build_common_access_headers(user_model)
            # rdc_data = dmp_args.get('rdc_data', {})
            dashboard_ids = dmp_args.get('dashboard_ids', [])
            datasource_mapping = dmp_args.get('datasource_mapping', [])
            # subject = dmp_args.get('subject', [])
            data = {
                "oss_url": oss_url,
                "task_id": rdc_task_id,
                "dashboard_ids": dashboard_ids,
                # "subject": subject,
                "dashboard_datasource": datasource_mapping
            }
            kwargs = {
                'url': f'{dmp_host}/api/pubserver/repackage_dmp_pkg',
                'headers': headers,
                'timeout': 300,
                'json': data
            }
            response = await HTTPRequestUtil.request('post', **kwargs)
            log.error(f"数见重新打包结果： {response.content.decode()}")
            f.record_detail(HTTPRequestUtil.httpx_request_to_curl(response.request))
            if response.json().get('result'):
                url = response.json().get('data')
                if url:
                    f.record(f"数见重新打包的地址： {url}")
                    return url
            raise Exception(f"数见重新打包失败： {response.content.decode()}")

    async def dmp_subject_deal_after_import(
            self, db, user_model, rdc_task_id, dmp_host,
            tenant_code, total_state, dmp_args, oss_url
    ):
        """数见主题标签处理"""
        with TaskContextManager('数见主题标签处理', '处理主题映射关系', total_state=total_state, will_raise=True) as f:
            headers = await HTTPRequestUtil.build_common_access_headers(user_model)
            # rdc_data = dmp_args.get('rdc_data', {})
            dashboard_ids = dmp_args.get('dashboard_ids', [])
            # datasource_mapping = dmp_args.get('datasource_mapping', [])
            subject = dmp_args.get('subject', [])
            data = {
                "task_id": rdc_task_id,
                "dashboard_ids": dashboard_ids,
                "subject": subject,
            }
            kwargs = {
                'url': f'{dmp_host}/api/pubserver/import_after_deal_tag',
                'headers': headers,
                'timeout': 300,
                'json': data
            }
            response = await HTTPRequestUtil.request('post', **kwargs)
            log.error(f"数见处理主题映射关系： {response.content.decode()}")
            f.record_detail(HTTPRequestUtil.httpx_request_to_curl(response.request))
            f.record(response.content.decode())
            if not response.json().get('result'):
                raise Exception(f"数见处理主题映射关系失败： {response.content.decode()}")

    async def import_dmp_package(
            self, db, user_model, task_id, dmp_admin_host,
            tenant_code, total_state, dmp_args, oss_url
    ):
        """触发dmp的导入，dmp接口是同步导入"""
        with TaskContextManager('数见导入', '请求数见导入接口', total_state=total_state, will_raise=True) as f:
            headers = await HTTPRequestUtil.build_rdc_access_headers({'tenant': tenant_code})
            rdc_data = dmp_args.get('rdc_data', {})
            data = {
                'tenantCode': rdc_data.get('tenant_code'),
                'tenantType': rdc_data.get('tenant_type'),
                'taskId': rdc_data.get('rdc_task_id'),
                'appCode': rdc_data.get('app_code'),
                'appKey': rdc_data.get('app_key'),
                'version': rdc_data.get('version'),
                'envCode': rdc_data.get('env_code'),
                'customerGuid': rdc_data.get('customer_guid'),
                'oss_url': oss_url,
                'force_update': '1'
            }
            kwargs = {
                'url': f'{dmp_admin_host}/api/publish_center/push_product',
                'headers': headers,
                'timeout': 120,
                'json': data
            }
            response = await HTTPRequestUtil.request('post', **kwargs)
            log.error(f"数见导入包结果： {response.content.decode()}")
            f.record(f"数见导入包结果： {response.content.decode()}")
            curl = HTTPRequestUtil.httpx_request_to_curl(response.request)
            f.record_detail(curl)
            # with TaskContextManager(
            #         '数见导入包结果', response.content.decode(), total_state=total_state,
            #         will_raise=True):
            # 添加一条详情保存记录
            await add_one_import_detail(db, **{
                'import_id': task_id, 'source': 'dmp',
                'log': json.dumps({'curl': curl, 'data': response.json()}, ensure_ascii=False),
                'created_by': user_model.user_code, 'updated_by': user_model.user_code,
            })

    async def trigger_export_dap(self, dap_host, headers, total_state, dap_args, import_detail: ImportDist, db):
        """
        确认数芯开始进行定制化的导入
        """
        # 1. 请求数芯导出接口
        with TaskContextManager('数芯导入', '请求数芯确认导入接口', total_state=total_state, will_raise=True) as f:
            # 判断数芯是否添加重试标识
            if import_detail.has_import_dap == TriggerDapImport.has_call.value:
                dap_args['retry'] = True
            else:
                dap_args['retry'] = False
            kwargs = {
                'url': f'{dap_host}/api/common/publish_center/import/task/map_reports',
                'headers': headers,
                'timeout': 120,
                'json': dap_args
            }
            response = await HTTPRequestUtil.request('post', **kwargs)
            log.info(f"数芯确认导入返回： {response.content.decode()}")
            f.record(f"数芯确认导入返回： {response.content.decode()}")
            f.record_detail(HTTPRequestUtil.httpx_request_to_curl(response.request))
            rs = response.json().get('result')
            if not rs:
                raise Exception(f"数芯接口导入接口返回异常：{response.content.decode()}")
            else:
                # 标记数芯是否重试标记
                if not import_detail.has_import_dap:
                    # 没有标记过成功调用
                    data = {'has_import_dap': TriggerDapImport.has_call.value}
                    await update_one_import_record(db, import_detail.id, data)
                    log.error(f"将导入数芯的确认导入接口记录置为：已经成功触发过")

    async def import_dap_status(self, task_id, dap_host, headers, dap_import_id, total_state, user_model, db):
        # 2. 请求数芯导出状态查询接口
        with TaskContextManager('数芯导入', '查询数芯导入状态', total_state=total_state, will_raise=True) as f:
            kwargs = {
                'url': f'{dap_host}/api/common/publish_center/import/task/process/get',
                'headers': headers,
                'params': {'id': dap_import_id}
            }
            # data, curl = {
            #     "result": True,
            #     "data": [
            #         {
            #             "object_type": "数据源",
            #             "status": "执行成功",
            #             "success_object_num": 2,
            #             "failed_object_num": 0,
            #             "duration": "31秒",
            #             "msg": '',
            #             "end_time": "2024-07-24 16:46:59",
            #             "begin_time": "2024-07-24 16:46:28"
            #         },
            #         {
            #             "object_type": "数据模型",
            #             "status": "执行成功",
            #             "success_object_num": 145,
            #             "failed_object_num": 2,
            #             "duration": "1分钟12秒",
            #             "msg": '',
            #             "end_time": "2024-07-24 16:48:12",
            #             "begin_time": "2024-07-24 16:47:00"
            #         },
            #         {
            #             "object_type": "指标",
            #             "status": "执行成功",
            #             "success_object_num": 30,
            #             "failed_object_num": 0,
            #             "duration": "31秒",
            #             "msg": '',
            #             "end_time": "2024-07-24 16:48:44",
            #             "begin_time": "2024-07-24 16:48:13"
            #         },
            #         {
            #             "object_type": "数据开发",
            #             "status": "执行成功",
            #             "success_object_num": 125,
            #             "failed_object_num": 1,
            #             "duration": "1分钟51秒",
            #             "msg": '',
            #             "end_time": "2024-07-24 16:50:04",
            #             "begin_time": "2024-07-24 16:49:21"
            #         },
            #         {
            #             "object_type": "数据质量",
            #             "status": "执行成功",
            #             "success_object_num": 5,
            #             "failed_object_num": 0,
            #             "duration": "29秒",
            #             "msg": '',
            #             "end_time": "2024-07-24 16:50:37",
            #             "begin_time": "2024-07-24 16:50:08"
            #         },
            #         {
            #             "object_type": "更新重启",
            #             "status": "执行成功",
            #             "success_object_num": 0,
            #             "failed_object_num": 103,
            #             "duration": "1分钟34秒",
            #             "msg": '',
            #             "end_time": "2024-07-24 16:52:13",
            #             "begin_time": "2024-07-24 16:50:39"
            #         }
            #     ],
            #     "code": 200,
            #     "msg": "成功"
            # }, 'xxx'
            data, curl = await HTTPRequestUtil.circle_reqeust(
                'get', timeout=self.timeout,
                check_func=check_dap_import_data, **kwargs
            )
            f.record(f"数芯导入结果： {json.dumps(data, ensure_ascii=False)}")
            f.record_detail(curl)
            # 添加一条详情保存记录
            await add_one_import_detail(db, **{
                'import_id': task_id, 'source': 'dap',
                'log': json.dumps({'curl': curl, 'data': data}, ensure_ascii=False),
                'created_by': user_model.user_code, 'updated_by': user_model.user_code,
            })
            datas = data.get('data') or []
            if datas:
                if any([d.get('status') in ['执行失败'] for d in datas]):
                    raise Exception(f"数芯导入失败或部分导入失败！")

    async def import_backup(self, db, user_model, headers, dmp_host, dap_host, import_detail, total_state, task_id):
        # 拆分组装dmp导出请求数据
        # 拆分组装dap导出请求数据
        # 导出dmp
        # 导出dap
        # 获取dmp\dap导出压缩包地址
        # 重新封装压缩包
        # 上传压缩包
        # 更新备份数据地址\备份内容到数据库
        with TaskContextManager('导入备份', '根据选择的导入内容备份', total_state=total_state, will_raise=True) as f:
            import_args = json.loads(import_detail.request_args)
            spawn_task = SpawnTaskManager()
            dmp_task = None
            dap_task = None
            dmp_oss_url = None
            dap_oss_url = None

            dmp_backup_req_data = {}
            dis = []
            if import_args['import_info']['data_dashboard_ids']:
                # 查询报表信息
                dmp_models = await self.get_dmp_model(import_detail.rdc_task_id, dmp_host, headers)
                if dmp_models:
                    dis = [item['id']
                           for dashboard_list in dmp_models.values()
                           for item in dashboard_list.get('dashboard', [])
                           if item.get('status') != '新增' and item.get('type') != '数据集']

                if dis:
                    dmp_backup_req_data['dashboard_ids'] = import_args['import_info']['data_dashboard_ids']
                    dmp_backup_req_data['name'] = '导入备份'
                    dmp_backup_req_data['description'] = f'导入备份导出import_id:{import_detail.id}'
                    dmp_export_id = await self.trigger_export_dmp(dmp_host, headers, dmp_backup_req_data, total_state)
                    dmp_task = spawn_task.create_task(
                        self.export_dmp_status(dmp_host, headers, dmp_export_id, total_state, user_model, db, task_id)
                    )

            dap_backup_req_data = {}
            if import_args['import_info']['data_apis'] or import_args['import_info']['data_models']:
                # 查询导入内容情况
                dap_models = await self.get_dap_model(import_detail.dap_import_id, dap_host, headers)

                if dap_models:
                    dap_backup_req_data['name'] = '导入备份'
                    dap_backup_req_data['description'] = f'导入备份导出import_id:{import_detail.id}'
                    dap_backup_req_data['content'] = {}
                    dap_backup_req_data['content']['apis'] = []
                    dap_backup_req_data['content']['models'] = []

                    apis = [item['code'] for item in
                            import_args['import_dependency']['data_api']['items']]
                    if dap_models[0]['data_service'] and apis:
                        dap_backup_req_data['content']['apis'] = [api for api in apis
                                                                  if any(
                                api == item['code'] and item['status'] != '新增' for item in dap_models[0]['data_service'])
                                                                  ]

                    models = [item['code'] for item in
                              import_args['import_dependency']['data_model']['items']]
                    if models and dap_models[0]['models']:
                        dap_backup_req_data['content']['models'] = [model for model in models
                                                                    if any(
                                model == item['code'] and item['status'] != '新增' for item in dap_models[0]['models'])
                                                                    ]

                    dap_backup_req_data['content']['subjects'] = [item['to']['subject_id'] for item in
                                                                  import_args['import_mapping']['subject']
                                                                  if item['op'] != 'insert']

                    if dap_backup_req_data['content']['models'] or dap_backup_req_data['content']['apis'] or \
                            dap_backup_req_data['content']['subjects']:
                        dap_export_id = await self.trigger_export_dap_real(dap_host, headers, dap_backup_req_data,
                                                                           total_state,
                                                                           '导入备份')
                        dap_task = spawn_task.create_task(
                            self.export_dap_status(dap_host, headers, dap_export_id, total_state, user_model, db,
                                                   task_id)
                        )

            await spawn_task.wait_and_done()
            if dmp_task:
                dmp_oss_url = spawn_task.get(dmp_task)
            if dap_task:
                dap_oss_url = spawn_task.get(dap_task)

            zip_name = f'导入备份-{timezone.now_str_no_sep()}.zip'

            if dmp_oss_url or dap_oss_url:
                final_path, oss_file_url, = await self.process_files(
                    total_state, zip_name, uuid4_str(), dmp_oss_url, dap_oss_url
                )

                dis_set = set(dis)
                items = [item for item in import_args['import_dependency']['dashboard']['items'] if
                         item['id'] in dis_set]
                reportData = {
                    'items': items,
                    'count': len(items)
                }
                backup_args = {'reportData': reportData,
                               'apiData': import_args['import_dependency']['data_api'],
                               'modelData': import_args['import_dependency']['data_model']}

                # 更新导入备份信息
                await update_one_import_record(
                    db, task_id,
                    {
                        'backup_oss_url': oss_file_url,
                        'backup_args': json.dumps(backup_args, ensure_ascii=False)
                    }
                )
                f.record('备份完成')
            else:
                f.record('无需备份')

            return

    async def trigger_export_dmp(self, dmp_host, headers, dmp_data, total_state):
        # 1. 请求报表导出接口
        with TaskContextManager('导入备份', '请求报表导出接口', total_state=total_state, will_raise=True) as f:
            kwargs = {
                'url': f'{dmp_host}/api/pubserver/export_distribution',
                'headers': headers,
                'json': dmp_data
            }
            response = await HTTPRequestUtil.request('post', **kwargs)
            dmp_export_id = response.json().get('data', {}).get('export_id', '') or ''
            f.record_detail(HTTPRequestUtil.httpx_request_to_curl(response.request))
            log.info(f"报表导出id： {dmp_export_id}")
            if not dmp_export_id:
                raise Exception(f'报表导出请求失败：{response.content.decode()}')
            return dmp_export_id

    async def export_dmp_status(self, dmp_host, headers, dmp_export_id, total_state, user_model, db, task_id):
        # 2. 请求报表导出状态查询接口
        with TaskContextManager('导入备份', '查询报表导出状态', total_state=total_state, will_raise=True) as f:
            kwargs = {
                'url': f'{dmp_host}/api/pubserver/get_distribution_status',
                'headers': headers,
                'params': {'export_id': dmp_export_id}
            }
            data, curl = await HTTPRequestUtil.circle_reqeust(
                'get', timeout=self.timeout,
                check_func=check_dmp_export_data, **kwargs
            )
            await add_one_import_detail(db, **{
                'import_id': task_id, 'source': 'dmp',
                'log': json.dumps({'curl': curl, 'data': data}, ensure_ascii=False),
                'created_by': user_model.user_code, 'updated_by': user_model.user_code,
            })
            # 导出的oss地址
            dmp_oss_url = data.get('data', {}).get('url', '')
            log.info(f"报表导出的oss地址： {dmp_oss_url}")
            f.record_detail(curl)
            if data.get('data', {}).get('status') == '失败':
                raise Exception(f'报表导出失败：{data}')
            return dmp_oss_url

    async def trigger_export_dap_real(self, dap_host, headers, dap_data, total_state, zip_name):
        # # TODO mock data
        # request_model.dap_data = {
        #     "name": "数据云发布中心导出测试10",
        #     "description": "stage1",
        #     "content": {
        #         "subjects": [
        #             "cd038788-c4dd-4d0c-b42b-561d0714add8",
        #             "15e99b1a-e150-4b2d-a52a-a7f25ae5f32d"
        #         ],
        #         "models": [
        #             "67fe0160-fec7-43f1-95a2-5f7a9b4a4974",
        #             "b20fe311-4fe8-4ab6-a7e2-1dceffaafee3",
        #             "faa7ae4b-ac6b-47fa-8a37-41019a6f8798",
        #             "49798c68-02c9-491e-9471-19af31951eea",
        #             "6f9c3eff-7213-11ee-8405-d67b479c6c02"
        #         ],
        #         "apis": [
        #             "1ffdfe5a-6993-11ee-955b-7218b7ab4e41",
        #             "1fbe04c2-6991-11ee-955b-7218b7ab4e41"
        #         ]
        #     }
        # }
        # headers = copy.deepcopy(headers)
        # headers[
        #     'Authorization'] = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0ZW5hbnRfY29kZSI6InNwcmludF9jb2RlX2JsIiwidXNlcl9jb2RlIjoiaHVhbmd3dzAxIiwidXNlcl9uYW1lIjoi6buE5paH5q2mIiwic291cmNlIjoiZG1wIiwiaWF0IjoxNzIwNjY1NTc0LCJpc3MiOiJkYXRhLWNsb3VkIn0.XAuQDMvG3pfzHh59XGXFxhYRyzzprxjKX2vFn9h0CII'
        # 宽表不允许导入同名的重复文件
        name = dap_data['name']
        from datetime import datetime
        date = datetime.now().strftime("%Y%m%d%H%M%S")
        dap_data['name'] = f'{name}-{date}'
        # 1. 请求宽表导出接口
        with TaskContextManager('导入备份', '请求宽表导出接口', total_state=total_state, will_raise=True) as f:
            kwargs = {
                'url': f'{dap_host}/api/dimensional-modeling/publish_center/export/task',
                'headers': headers,
                'json': dap_data
            }
            response = await HTTPRequestUtil.request('post', **kwargs)
            dap_export_id = response.json().get('data') or ''
            f.record_detail(HTTPRequestUtil.httpx_request_to_curl(response.request))
            log.info(f"宽表导出id： {dap_export_id}")
            if not dap_export_id:
                raise Exception(f'宽表导出请求失败：{response.content.decode()}')
            return dap_export_id

    async def export_dap_status(self, dap_host, headers, dap_export_id, total_state, user_model, db, task_id):
        # 2. 请求宽表导出状态查询接口
        with TaskContextManager('导入备份', '查询宽表导出状态', total_state=total_state, will_raise=True) as f:
            kwargs = {
                'url': f'{dap_host}/api/dimensional-modeling/publish_center/export/task/process',
                'headers': headers,
                'params': {'id': dap_export_id}
            }
            data, curl = await HTTPRequestUtil.circle_reqeust(
                'get', timeout=self.timeout,
                check_func=check_dap_export_data, **kwargs
            )
            await add_one_import_detail(db, **{
                'import_id': task_id, 'source': 'dap',
                'log': json.dumps({'curl': curl, 'data': data}, ensure_ascii=False),
                'created_by': user_model.user_code, 'updated_by': user_model.user_code,
            })
            if data.get('data', {}).get('status') != '执行成功':
                raise Exception(f"宽表导出失败: {data}")
            # 导出的oss地址
            dap_oss_url = data.get('data', {}).get('oss_url', '')
            log.info(f"宽表导出的oss地址： {dap_oss_url}")
            f.record_detail(curl)
            return dap_oss_url

    async def process_files(self, total_state, zip_name, export_id, dmp_oss_url, dap_oss_url):
        """
        处理下载的压缩包，合并压缩包
        """
        folder = f'/tmp/{settings.OSS_PUB_SERVER_BASE_FOLDER_NAME}/{export_id}'
        try:
            with TaskContextManager('导入备份', '解压报表压缩包', total_state=total_state, will_raise=True) as f:
                # 1. 解压报表的压缩包
                if dmp_oss_url:
                    dmp_filename, dmp_full_path = HTTPRequestUtil.download_oss_file(folder, url=dmp_oss_url)
                    dmp_unzip_folder = os.path.join(folder, 'dmp')
                    utils.unzip_files_to_folder(dmp_full_path, dmp_unzip_folder)
                else:
                    log.error(f"没有导出dmp的资源不执行下载dmp的资源")
                    f.record(f"没有导出报表的资源不处理报表的压缩包")
            with TaskContextManager('导入备份', '解压宽表压缩包', total_state=total_state, will_raise=True) as f:
                # 2. 解压宽表的压缩包
                if dap_oss_url:
                    dap_filename, dap_full_path = await HTTPRequestUtil.download_file(folder, url=dap_oss_url)
                    dap_unzip_folder = os.path.join(folder, 'bigdata')
                    utils.unzip_files_to_folder(dap_full_path, dap_unzip_folder)
                else:
                    log.error(f"没有导出dap的资源不执行下载dap的资源")
                    f.record(f"没有导出宽表的资源不处理宽表的压缩包")
            with TaskContextManager('导入备份', '合并压缩包', total_state=total_state, will_raise=True):
                # 3. 重新打包回zip
                # folders = [dmp_unzip_folder, dap_unzip_folder]
                data_folder = os.path.join(folder, 'data')
                os.makedirs(data_folder, exist_ok=True)
                if dmp_oss_url:
                    shutil.move(dmp_unzip_folder, data_folder)
                if dap_oss_url:
                    shutil.move(dap_unzip_folder, data_folder)
                final_path = utils.zip_files_from_folders([data_folder], zip_name, zip_folder=folder)
            with TaskContextManager('导入备份', '上传最终压缩包', total_state=total_state, will_raise=True):
                # 4. 上传压缩包
                root = settings.OSS_PUB_SERVER_BASE_FOLDER_NAME
                with open(final_path, 'rb') as fp:
                    oss = OSSFileProxy()
                    oss_file_url = oss.upload(fp, file_name=zip_name, root=root, key=f'{root}/{zip_name}')
                    oss_file_url = oss.get_sigh_url(oss_file_url, 604800, is_url=True, **{'sign_outer_url': True})
                    log.info(f'最终包的oss地址： {oss_file_url}')
        finally:
            shutil.rmtree(folder, ignore_errors=True)

        return final_path, oss_file_url

    async def get_dmp_model(self, rdc_task_id, dmp_host, headers):
        cache_key = 'import_parse_result:{}:{}'
        dmp_cache_key = cache_key.format('dmp', rdc_task_id)
        cache_result = redis_client.get(dmp_cache_key)
        if cache_result:
            dmp_data = json.loads(cache_result)
        else:
            kwargs = {
                'url': f'{dmp_host}/api/pubserver/parse_import_report',
                'headers': headers,
                'params': {'task_id': rdc_task_id}
            }
            resp, curl = await HTTPRequestUtil.circle_reqeust(
                'get', timeout=60,
                check_func=check_dmp_export_data, **kwargs
            )
            if not resp.get('result'):
                msg = resp.get('msg')
                raise Exception(f'查询数见报表信息失败{msg}')
            dmp_data = resp.get('data')
            redis_client.setex(dmp_cache_key, 600, json.dumps(dmp_data, ensure_ascii=False))
        return dmp_data

    async def get_dap_model(self, dap_task_id, dap_host, headers):
        kwargs = {
            'url': f'{dap_host}/api/common/publish_center/import/get_import_info',
            'headers': headers,
            'params': {'task_id': dap_task_id},
            'timeout': 30
        }

        response = await HTTPRequestUtil.request('get', **kwargs)

        info = response.content.decode()
        log.error(f"[查询接口[{kwargs.get('url')}]返回结果： {info}")
        rs = response.json()
        if response.status_code != 200:
            msg = rs.get('msg')
            raise Exception(f'查询数芯数据服务失败:{msg}')
        return rs.get('data')
