from backend.common.log import log
from backend.consumers.consumer import Consumer
from backend.common.domain import get_server_domain
from backend.common.request import HTTPRequestUtil


class TriggerDMPParserConsumer(Consumer):
    """
    RDC推包触发DMP包内解析动作
    """

    async def process(self, task):
        task_id = task.get('task_id') or ''
        oss_url = task.get('oss_url') or ''
        tenant_code = task.get('tenant_code') or ''
        app_code = task.get('app_code') or ''
        log.error(f"开始处理DMP包内解析动作： {task_id}")

        dmp_admin_host = get_server_domain('dmp_admin')
        headers = await HTTPRequestUtil.build_rdc_access_headers({'tenant': tenant_code})

        # 1. 先查询是否已经插入过数据
        data = {
            "task_id": task_id
        }
        kwargs = {
            'url': f'{dmp_admin_host}/api/publish_center/has_save_file_content',
            'headers': headers,
            'timeout': 200,
            'json': data
        }
        response = await HTTPRequestUtil.request('post', **kwargs)
        log.error(f"查询DMP是否解析过包： {response.content.decode()}")
        if response.json().get('data'):
            log.error(f"DMP已经解析过该定制包： {task_id}")
            return

        data = {
            "file_url": oss_url,
            "app_code": app_code,
            "task_id": task_id
        }
        kwargs = {
            'url': f'{dmp_admin_host}/api/publish_center/save_file_content',
            'headers': headers,
            'timeout': 200,
            'json': data
        }
        response = await HTTPRequestUtil.request('post', **kwargs)
        log.error(f"触发dmp的增量包解析动作： {response.content.decode()}")
