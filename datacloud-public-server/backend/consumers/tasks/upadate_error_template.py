import json
import time
import asyncio

from sqlalchemy.sql.elements import or_

from backend.app.import_dist.model.import_dist import ImportDist, RDC
from backend.common.enums import ImportStatus, RDCTenantType, TenantType
from backend.common.jwt import CookiesUser
from backend.common.log import log
from backend.common.task_context import TotalState, TaskContextManager
from backend.consumers.consumer import Consumer
from backend.database.db_util import CurrentSession, get_db, db_session
from backend.utils.timezone import timezone


class UpdateErrorTemplateConsumer(Consumer):
    """
    如果数芯的模板库安装失败，那么就此次其他的所有租户库安装记录置为异常状态
    """

    # timeout = 5 * 60

    async def process(self, task):
        db = db_session()
        # st = time.time()
        dap_task_id = task.get('dap_task_id') or ''
        data = task.get('data') or {}

        rs_datas = data.get('data') or []
        if not rs_datas:
            log.error(f"{dap_task_id}数芯返回数据为空！不能更新状态")
            return

        if all([d.get('status') in ['执行成功'] for d in rs_datas]):
            # 数芯的模板库导入成功了
            log.error(f"{dap_task_id}数芯模板空间更新成功！")
            await self.update_import_record(db, dap_task_id, '更新成功')
        elif any([d.get('status') in ['执行失败'] for d in rs_datas]):
            # 数芯的模板库导入失败了
            log.error(f"{dap_task_id}数芯模板空间更新失败！")
            await self.update_import_record(db, dap_task_id, '更新失败')
        else:
            log.error(f"{dap_task_id}不处理")

    async def update_import_record(self, db, dap_task_id, msg: str):
        rdc_task_id = db.query(ImportDist.rdc_task_id).filter(
            ImportDist.dap_import_id == dap_task_id
        ).limit(1).scalar()
        log.error(f"rdc_task_id: {rdc_task_id}")

        # 2. 检查是否是rdc重新触发的导入
        # rdc重新触发的导入会出现rdc_task_id不一样
        # 但是单个包重新触发的包还是一样的
        rdc = db.query(RDC).filter(RDC.rdc_task_id == rdc_task_id).limit(1).first()
        if not rdc:
            log.error(f"查不到rdc推包记录！")
            return

        # 3. 判断是否是重新触发的场景
        other_rdc = db.query(RDC).filter(
            RDC.app_code == rdc.app_code,
            RDC.version == rdc.version,
            RDC.app_key == rdc.app_key,
            RDC.customer_guid == rdc.customer_guid,
            RDC.tenant_code == RDCTenantType.template.value,
            RDC.rdc_task_id != rdc_task_id,
        ).all() or []

        if len(other_rdc) > 0:
            log.error(f"rdc重新推包触发的场景")
            is_rdc_repush = True
        else:
            log.error(f"rdc首次推包触发的场景")
            is_rdc_repush = False

        # 4. 同一个包的所有task_id
        task_ids = [rdc_task_id] + [r.rdc_task_id for r in other_rdc]
        # 目前task_ids的规则调整，taskId=f'{taskId}_{appCode}_{tenantCode}'，需要处理下去掉_{tenantCode}、_template
        new_task_ids = self.get_task_ids_prefix(task_ids) or []
        log.info(f"查询出需要更新的new_task_ids:{new_task_ids}")

        # 5. 操作这个包的租户更新记录
        # datas = db.query(ImportDist).filter(
        #     ImportDist.tenant_type == RDCTenantType.tenant.value,
        #     ImportDist.rdc_task_id.in_(task_ids)
        # ).all()

        # 5. 操作这个包的租户更新记录（因为task_id生成规则修改，需要使用like前缀匹配，添加%）
        datas = db.query(ImportDist).filter(
            ImportDist.tenant_type == RDCTenantType.tenant.value,
            or_(*[ImportDist.rdc_task_id.like(f"{task_id}%") for task_id in new_task_ids])
        ).all()
        log.info(f"查询出需要更新的datas:{datas}")

        for data in datas:
            # if data.status != ImportStatus.exception.value:
            if data.space_type == TenantType.public.value:
                # 不能修改标准/公共空间的推送状态
                log.error(f"跳过公共空间{data.tenant_code}的处理")
                continue

            if data.log:
                total_state = TotalState(**json.loads(data.log))
            else:
                total_state = TotalState(name='模板库推送更新')
            with TaskContextManager(f'数芯模板库{msg}', '更新租户状态', total_state=total_state) as f:
                if is_rdc_repush:
                    f.record("rdc重新推送这个包")
                else:
                    f.record("rdc首次推送这个包")
                if msg == '更新成功':
                    f.record(f"模板库推送成功，租户更新记录置为【未更新】")
                    log.info(f"模板库推送成功，租户更新记录置为【未更新】")
                    status = ImportStatus.not_import.value
                else:
                    f.record(f"模板库推送失败，租户更新记录置为【异常】")
                    log.info(f"模板库推送失败，租户更新记录置为【异常】")
                    status = ImportStatus.exception.value
                    raise Exception('模板库推送失败')
            db.query(ImportDist).filter(
                ImportDist.id == data.id,
            ).update(
                {
                    'status': status,
                    'log': json.dumps(total_state.model_dump(), ensure_ascii=False),
                }
            )
        db.commit()

        log.error(f"{dap_task_id}处理推包记录完成，置为： {msg}")

    @staticmethod
    def get_task_ids_prefix(task_ids):
        task_ids_prefix = []
        for task_id in task_ids:
            # 从右边分割一次，取分割后的第一部分(示例：d63305ae-6dec-42ee-80a4-ba8df3c9db4e_7050_template)
            new_task_id = task_id.rsplit('_', 1)[0] if '_' in task_id else task_id
            task_ids_prefix.append(new_task_id)
        return task_ids_prefix


if __name__ == '__main__':
    asyncio.run(UpdateErrorTemplateConsumer().process({
        'dap_task_id': "1ef549c5-b43c-6bf0-b38f-0b3dc390255d",
        'data': json.loads(
            """{"result":true,"data":[{"object_type":"数据源","status":"执行成功","success_object_num":5,"failed_object_num":0,"duration":"9秒","msg":null,"end_time":"2024-08-07 15:04:57","begin_time":"2024-08-07 15:04:48"},{"object_type":"查询API","status":"执行成功","success_object_num":1,"failed_object_num":0,"duration":"10秒","msg":null,"end_time":"2024-08-07 15:04:57","begin_time":"2024-08-07 15:04:47"},{"object_type":"数据模型","status":"执行成功","success_object_num":13,"failed_object_num":0,"duration":"1秒","msg":null,"end_time":"2024-08-07 15:04:58","begin_time":"2024-08-07 15:04:57"},{"object_type":"指标","status":"执行成功","success_object_num":5,"failed_object_num":0,"duration":"46秒","msg":null,"end_time":"2024-08-07 15:05:45","begin_time":"2024-08-07 15:04:59"},{"object_type":"数据开发","status":"执行失败","success_object_num":null,"failed_object_num":null,"duration":null,"msg":"离线开发模块:存在其它模块失败，设置为已中断;实时监听模块:模块超时: 10 分钟","end_time":"2024-08-07 15:16:07","begin_time":null},{"object_type":"数据权限","status":"执行失败","success_object_num":null,"failed_object_num":null,"duration":"0秒","msg":"存在其它模块失败，设置为已中断","end_time":"2024-08-07 15:16:07","begin_time":null},{"object_type":"数据质量","status":"执行失败","success_object_num":null,"failed_object_num":null,"duration":"0秒","msg":"存在其它模块失败，设置为已中断","end_time":"2024-08-07 15:16:07","begin_time":null},{"object_type":"更新重启","status":"执行失败","success_object_num":null,"failed_object_num":null,"duration":null,"msg":"存在其它模块失败，设置为已中断","end_time":"2024-08-07 15:16:07","begin_time":null}],"code":200,"msg":"成功"}""")
    }))


# def get_task_ids_prefix(task_ids):
#     task_ids_prefix = []
#     for task_id in task_ids:
#         # 从右边分割一次，取分割后的第一部分(示例：d63305ae-6dec-42ee-80a4-ba8df3c9db4e_7050_template)
#         new_task_id = task_id.rsplit('_', 1)[0] if '_' in task_id else task_id
#         # 后面加【％】号,方便后面用like进行过滤
#         task_ids_prefix.append(f"{new_task_id}%")
#     return task_ids_prefix
