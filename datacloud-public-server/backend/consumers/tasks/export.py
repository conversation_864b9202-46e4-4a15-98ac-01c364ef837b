import copy
import json
import os
import shutil
import zipfile

from backend.app.export_dist.crud.crud_export import add_one_export_detail, update_one_export
from backend.common import utils
from backend.common.enums import ExportTaskStatus
from backend.common.jwt import CookiesUser
from backend.common.log import log
from backend.common.oss.oss import OSSFileProxy
from backend.consumers.consumer import Consumer
from backend.common.domain import get_server_domain
from backend.common.request import HTTPRequestUtil
from backend.common.task_context import TaskContextManager, TotalState, SpawnTaskManager
from backend.app.export_dist.schema.export import ExportModel
from backend.core.conf import settings
from backend.database.db_util import CurrentSession, get_db, db_session
from backend.utils.timezone import timezone


def check_dmp_export_data(data: dict):
    return data.get('data', {}).get('status') == '成功' or \
        data.get('data', {}).get('status') == '失败'


def check_dap_export_data(data: dict):
    print(data)
    return data.get('data', {}).get('status') == '执行成功' or \
        data.get('result', False) is False or \
        '失败' in str(data)


class ExportConsumer(Consumer):
    timeout = settings.APP__EXPORT_TIMEOUT

    # is_log_wait = True

    def get_zip_name(self, name):
        return f'{name.replace(" ", "")}-{timezone.now_str_no_sep()}.zip'

    def has_select_dap(self, data):
        content = data.get('content') or {}
        return any([
            content.get('apis'),
            content.get('models'),
            content.get('subjects'),
        ])

    async def process(self, task):
        db = db_session()
        export_id = task.get('export_id') or ''
        user_data = task.get('user') or {}
        request_args = task.get('request_args') or {}
        user_model = CookiesUser(**user_data)
        request_model = ExportModel(**request_args)
        zip_name = self.get_zip_name(request_model.name)
        dmp_host = get_server_domain('dmp')
        dap_host = get_server_domain('dap')
        headers = await HTTPRequestUtil.build_common_access_headers(user_model)
        has_select_dmp = bool(request_model.dmp_data.get('dashboard_ids'))
        has_select_dap = self.has_select_dap(request_model.dap_data)

        # 置为运行中
        await update_one_export(
            db, export_id,
            {
                'status': ExportTaskStatus.running.value, 'created_by': user_model.user_code,
                'content': request_model.content, 'updated_by': user_model.user_code
            }
        )

        total_state = TotalState(name='更新中心导出')
        spawn_task = SpawnTaskManager()
        try:
            await self.export_check(total_state, has_select_dmp, has_select_dap)

            dmp_task = None
            dap_task = None
            dmp_oss_url = None
            dap_oss_url = None

            if has_select_dmp:
                dmp_export_id = await self.trigger_export_dmp(dmp_host, headers, request_model, total_state, export_id)
                dmp_task = spawn_task.create_task(
                    self.export_dmp_status(export_id, dmp_host, headers, dmp_export_id, total_state, user_model, db)
                )
            if has_select_dap:
                dap_export_id = await self.trigger_export_dap(dap_host, headers, request_model, total_state, zip_name)
                dap_task = spawn_task.create_task(
                    self.export_dap_status(export_id, dap_host, headers, dap_export_id, total_state, user_model, db)
                )

            # 并发等待所有的导出结果
            await spawn_task.wait_and_done()
            if dmp_task:
                dmp_oss_url = spawn_task.get(dmp_task)
            if dap_task:
                dap_oss_url = spawn_task.get(dap_task)
            # dmp_oss_url = 'https://dmp-test.oss-cn-shenzhen.aliyuncs.com/pubserver-dist/19abfb2f8709cf4f3a49ba729d30c7fa.zip'
            # dap_oss_url = 'https://obs.cn-south-1.myhuaweicloud.com/dap-test-new/rdc_product_package/1ef43e98-b8c6-6d71-9a2b-2dc8c068fd82_1721185717.zip?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=DYOKHWKGBCQDHV3O5UPM%2F20240717%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20240717T030837Z&X-Amz-Expires=32400&X-Amz-SignedHeaders=host&X-Amz-Signature=5a2b70130eebeee745ad8e01f37c24b39e771eb8c7a3d4ae49249bc893623700'
            # print('dmp_oss_url', dmp_oss_url, dap_oss_url)

            # 处理压缩包文件
            final_path, oss_file_url, = await self.process_files(
                total_state, zip_name, export_id, dmp_oss_url, dap_oss_url
            )
            # 更新最终状态为成功
            export_data = {
                'oss_url': oss_file_url,
                'created_by': user_model.user_code, 'updated_by': user_model.user_code,
                'status': ExportTaskStatus.exportDone.value,
                'log': json.dumps(total_state.model_dump(), ensure_ascii=False)
            }
            await update_one_export(db, export_id, export_data)
        except Exception as e:
            log.error(f"导出失败： {str(e)}")
            # 更新最终状态为失败
            export_data = {
                'created_by': user_model.user_code, 'updated_by': user_model.user_code,
                'status': ExportTaskStatus.exportError.value,
                'log': json.dumps(total_state.model_dump(), ensure_ascii=False)
            }
            await update_one_export(db, export_id, export_data)

    async def export_check(self, total_state, has_select_dmp, has_select_dap):
        """
        导出参数校验
        """
        with TaskContextManager(
                '导出参数校验', '校验是否勾选了报表、宽表的资源', total_state=total_state,
                will_raise=True
        ) as f:
            log.error(f"资源勾选情况：dmp: {has_select_dmp}, dap: {has_select_dap}")
            f.record(f"勾选了报表资源：{'是' if has_select_dmp else '否'}")
            f.record(f"勾选了宽表资源：{'是' if has_select_dap else '否'}")
            if not has_select_dmp and not has_select_dap:
                raise Exception(f"报表、宽表的资源都没有勾选！退出导出")

    async def process_files(self, total_state, zip_name, export_id, dmp_oss_url, dap_oss_url):
        """
        处理下载的压缩包，合并压缩包
        """
        folder = f'/tmp/{settings.OSS_PUB_SERVER_BASE_FOLDER_NAME}/{export_id}'
        try:
            with TaskContextManager('压缩包处理', '解压报表压缩包', total_state=total_state, will_raise=True) as f:
                # 1. 解压报表的压缩包
                if dmp_oss_url:
                    dmp_filename, dmp_full_path = HTTPRequestUtil.download_oss_file(folder, url=dmp_oss_url)
                    dmp_unzip_folder = os.path.join(folder, 'dmp')
                    utils.unzip_files_to_folder(dmp_full_path, dmp_unzip_folder)
                else:
                    log.error(f"没有导出dmp的资源不执行下载dmp的资源")
                    f.record(f"没有导出报表的资源不处理报表的压缩包")
            with TaskContextManager('压缩包处理', '解压宽表压缩包', total_state=total_state, will_raise=True) as f:
                # 2. 解压宽表的压缩包
                if dap_oss_url:
                    dap_filename, dap_full_path = await HTTPRequestUtil.download_file(folder, url=dap_oss_url)
                    dap_unzip_folder = os.path.join(folder, 'bigdata')
                    utils.unzip_files_to_folder(dap_full_path, dap_unzip_folder)
                else:
                    log.error(f"没有导出dap的资源不执行下载dap的资源")
                    f.record(f"没有导出宽表的资源不处理宽表的压缩包")
            with TaskContextManager('压缩包处理', '合并压缩包', total_state=total_state, will_raise=True):
                # 3. 重新打包回zip
                # folders = [dmp_unzip_folder, dap_unzip_folder]
                data_folder = os.path.join(folder, 'data')
                os.makedirs(data_folder, exist_ok=True)
                if dmp_oss_url:
                    shutil.move(dmp_unzip_folder, data_folder)
                if dap_oss_url:
                    shutil.move(dap_unzip_folder, data_folder)
                final_path = utils.zip_files_from_folders([data_folder], zip_name, zip_folder=folder)
            with TaskContextManager('压缩包处理', '上传最终压缩包', total_state=total_state, will_raise=True):
                # 4. 上传压缩包
                root = settings.OSS_PUB_SERVER_BASE_FOLDER_NAME
                with open(final_path, 'rb') as fp:
                    oss = OSSFileProxy()
                    oss_file_url = oss.upload(fp, file_name=zip_name, root=root, key=f'{root}/{zip_name}')
                    oss_file_url = oss.get_sigh_url(oss_file_url, 604800, is_url=True, **{'sign_outer_url': True})
                    # if settings.MINIO__INSIDE_ENDPOINT:
                        # log.info(f'111： {settings.MINIO__INSIDE_ENDPOINT}')
                        # log.info(f'222： {settings.MINIO__ENDPOINT}')
                        # log.info(f'333： {oss_file_url}')
                        # oss_file_url = oss_file_url.replace(settings.MINIO__INSIDE_ENDPOINT, settings.MINIO__ENDPOINT)
                    log.info(f'最终包的oss地址： {oss_file_url}')
        finally:
            shutil.rmtree(folder, ignore_errors=True)

        return final_path, oss_file_url

    async def trigger_export_dmp(self, dmp_host, headers, request_model, total_state, export_id):
        # 1. 请求报表导出接口
        with TaskContextManager('报表导出', '请求报表导出接口', total_state=total_state, will_raise=True) as f:
            request_model.dmp_data['name'] = request_model.name
            request_model.dmp_data['description'] = '更新中心导出'
            kwargs = {
                'url': f'{dmp_host}/api/pubserver/export_distribution',
                'headers': headers,
                'json': request_model.dmp_data
            }
            response = await HTTPRequestUtil.request('post', **kwargs)
            dmp_export_id = response.json().get('data', {}).get('export_id', '') or ''
            f.record_detail(HTTPRequestUtil.httpx_request_to_curl(response.request))
            log.info(f"报表导出id： {dmp_export_id}")
            if not dmp_export_id:
                raise Exception(f'报表导出请求失败：{response.content.decode()}')
            return dmp_export_id

    async def export_dmp_status(self, export_id, dmp_host, headers, dmp_export_id, total_state, user_model, db):
        # 2. 请求报表导出状态查询接口
        with TaskContextManager('报表导出', '查询报表导出状态', total_state=total_state, will_raise=True) as f:
            kwargs = {
                'url': f'{dmp_host}/api/pubserver/get_distribution_status',
                'headers': headers,
                'params': {'export_id': dmp_export_id}
            }
            data, curl = await HTTPRequestUtil.circle_reqeust(
                'get', timeout=self.timeout,
                check_func=check_dmp_export_data, **kwargs
            )
            # 导出的oss地址
            dmp_oss_url = data.get('data', {}).get('url', '')
            log.info(f"报表导出的oss地址： {dmp_oss_url}")
            f.record_detail(curl)
            # 添加一条详情保存记录
            await add_one_export_detail(db, **{
                'export_id': export_id, 'source': 'dmp', 'oss_url': dmp_oss_url, 'curl': curl,
                'created_by': user_model.user_code, 'updated_by': user_model.user_code,
            })
            if data.get('data', {}).get('status') == '失败':
                # message = data.get('data', {}).get('message')
                # f.record(message)
                raise Exception(f'报表导出失败：{data}')
            return dmp_oss_url

    async def trigger_export_dap(self, dap_host, headers, request_model, total_state, zip_name):
        # # TODO mock data
        # request_model.dap_data = {
        #     "name": "数据云发布中心导出测试10",
        #     "description": "stage1",
        #     "content": {
        #         "subjects": [
        #             "cd038788-c4dd-4d0c-b42b-561d0714add8",
        #             "15e99b1a-e150-4b2d-a52a-a7f25ae5f32d"
        #         ],
        #         "models": [
        #             "67fe0160-fec7-43f1-95a2-5f7a9b4a4974",
        #             "b20fe311-4fe8-4ab6-a7e2-1dceffaafee3",
        #             "faa7ae4b-ac6b-47fa-8a37-41019a6f8798",
        #             "49798c68-02c9-491e-9471-19af31951eea",
        #             "6f9c3eff-7213-11ee-8405-d67b479c6c02"
        #         ],
        #         "apis": [
        #             "1ffdfe5a-6993-11ee-955b-7218b7ab4e41",
        #             "1fbe04c2-6991-11ee-955b-7218b7ab4e41"
        #         ]
        #     }
        # }
        # headers = copy.deepcopy(headers)
        # headers[
        #     'Authorization'] = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0ZW5hbnRfY29kZSI6InNwcmludF9jb2RlX2JsIiwidXNlcl9jb2RlIjoiaHVhbmd3dzAxIiwidXNlcl9uYW1lIjoi6buE5paH5q2mIiwic291cmNlIjoiZG1wIiwiaWF0IjoxNzIwNjY1NTc0LCJpc3MiOiJkYXRhLWNsb3VkIn0.XAuQDMvG3pfzHh59XGXFxhYRyzzprxjKX2vFn9h0CII'
        # 宽表不允许导入同名的重复文件
        request_model.dap_data['name'] = zip_name.replace('.zip', '')
        # 1. 请求宽表导出接口
        with TaskContextManager('宽表导出', '请求宽表导出接口', total_state=total_state, will_raise=True) as f:
            kwargs = {
                'url': f'{dap_host}/api/dimensional-modeling/publish_center/export/task',
                'headers': headers,
                'json': request_model.dap_data
            }
            response = await HTTPRequestUtil.request('post', **kwargs)
            dap_export_id = response.json().get('data') or ''
            f.record_detail(HTTPRequestUtil.httpx_request_to_curl(response.request))
            log.info(f"宽表导出id： {dap_export_id}")
            if not dap_export_id:
                raise Exception(f'宽表导出请求失败：{response.content.decode()}')
            return dap_export_id

    async def export_dap_status(self, export_id, dap_host, headers, dap_export_id, total_state, user_model, db):
        # 2. 请求宽表导出状态查询接口
        with TaskContextManager('宽表导出', '查询宽表导出状态', total_state=total_state, will_raise=True) as f:
            kwargs = {
                'url': f'{dap_host}/api/dimensional-modeling/publish_center/export/task/process',
                'headers': headers,
                'params': {'id': dap_export_id}
            }
            data, curl = await HTTPRequestUtil.circle_reqeust(
                'get', timeout=self.timeout,
                check_func=check_dap_export_data, **kwargs
            )
            if data.get('data', {}).get('status') != '执行成功':
                raise Exception(f"宽表导出失败: {data}")
            # 导出的oss地址
            dap_oss_url = data.get('data', {}).get('oss_url', '')
            log.info(f"宽表导出的oss地址： {dap_oss_url}")
            f.record_detail(curl)
            # 添加一条详情保存记录
            await add_one_export_detail(db, **{
                'export_id': export_id, 'source': 'dap', 'oss_url': dap_oss_url, 'curl': curl,
                'created_by': user_model.user_code, 'updated_by': user_model.user_code,
            })
            return dap_oss_url
