import asyncio
import json
import traceback
import pkgutil
import importlib
import inspect
from typing import Dict, Type

import aio_pika
from backend.consumers.connect import get_connection, get_redis_connection
from backend.common.log import log
from pydantic import BaseModel

from backend.core.conf import settings

__ALL__ = ['MessageModel', 'Consumer', 'CONSUMERS']


class MessageModel(BaseModel):
    cls: str
    task: Dict

#
# semaphore = asyncio.Semaphore(5)
#
#
# # 定义一个包装任务的函数，确保信号量控制并发
# async def sem_task(func, body):
#     async with semaphore:
#         return await func(body)
#

class RedisConsumer:
    push_retries = 3
    key = f'{settings.RabbitMQ_QUEUE_NAME}_queue_key'

    async def push(self, **task: Dict) -> None:
        """
        推送异步消息到队列
        task: 任务信息
        """
        connection = None
        message = MessageModel(cls=self.__class__.__name__, task=task)
        body = json.dumps(message.model_dump(), ensure_ascii=False).encode()
        # 重试次数
        err = None
        for retry in range(self.push_retries):
            try:
                connection = get_redis_connection()
                connection.rpush(self.key, body)
                log.debug(f"发送消息： {body}")
                break
            except Exception as e:
                err = e
                log.error(f"发送消息失败： {body}， 原因： {traceback.format_exc(limit=1)}")
                await asyncio.sleep(3)  # 等待3s后重试
            # finally:
            #     if connection:
            #         await connection.aclose()
        # if err is not None:
        #     raise err

    async def process(self, task: Dict) -> None:
        """
        各个子类实现的消息处理方法
        """
        raise NotImplementedError

    @staticmethod
    async def consume(body: str) -> None:
        """
        消费者处理入口
        """
        from backend.consumers.consumer import CONSUMERS

        log.debug(f"接收到消息: {body}")
        try:
            message_data = json.loads(body)
            model = MessageModel(**message_data)
            cls = CONSUMERS.get(model.cls)
            if cls is None:
                log.error(f"消息没有对应的处理事件！ {body}")
            else:
                # debug 代码
                # await asyncio.sleep(60)
                # print('结束等待')
                await cls().process(model.task)
        except:
            log.error(f"处理消息失败：{body}，原因： {traceback.format_exc(limit=1)}")

    @staticmethod
    async def mian_consume_loop() -> None:
        while True:
            try:
                connection = get_redis_connection()
                body = connection.lpop(RedisConsumer.key)
                if body:
                    await RedisConsumer.consume(body)
                    # await asyncio.create_task(sem_task(RedisConsumer.consume, body ))
                    # await RedisConsumer.consume(body[1])
                await asyncio.sleep(1)
            except Exception:
                log.error(f"主流程其他错误： {traceback.format_exc(limit=1)}")
