import asyncio
import json
import traceback
from typing import Dict, Type

import aio_pika
from backend.consumers.connect import get_connection
from backend.common.log import log
from pydantic import BaseModel
from aio_pika.exceptions import AMQPConnectionError

__ALL__ = ['MessageModel', 'Consumer', 'CONSUMERS']


class MessageModel(BaseModel):
    cls: str
    task: Dict


class RabbitMqConsumer:
    push_retries = 3

    async def push(self, **task: Dict) -> None:
        """
        推送异步消息到队列
        task: 任务信息
        """
        connection = None
        message = MessageModel(cls=self.__class__.__name__, task=task)
        body = json.dumps(message.model_dump(), ensure_ascii=False).encode()
        # 重试次数
        err = None
        for retry in range(self.push_retries):
            try:
                connection, channel, queue = await get_connection()
                message = aio_pika.Message(body=body)
                await channel.default_exchange.publish(message, routing_key=queue.name)
                log.debug(f"发送消息： {body}")
                break
            except Exception as e:
                err = e
                log.error(f"发送消息失败： {body}， 原因： {traceback.format_exc(limit=1)}")
                await asyncio.sleep(3)  # 等待3s后重试
            finally:
                if connection:
                    await connection.close()
        # if err is not None:
        #     raise err

    async def process(self, task: Dict) -> None:
        """
        各个子类实现的消息处理方法
        """
        raise NotImplementedError

    @staticmethod
    async def consume(message: aio_pika.IncomingMessage) -> None:
        """
        消费者处理入口
        """
        from backend.consumers.consumer import CONSUMERS

        async with message.process():
            body = message.body.decode()
            log.debug(f"接收到消息: {body}, tag:{message.consumer_tag}, id:{message.message_id}")
            try:
                message_data = json.loads(body)
                model = MessageModel(**message_data)
                cls = CONSUMERS.get(model.cls)
                if cls is None:
                    log.error(f"消息没有对应的处理事件！ {body}")
                else:
                    await cls().process(model.task)
            except:
                log.error(f"处理消息失败：{body}，原因： {traceback.format_exc(limit=1)}")

    @staticmethod
    async def mian_consume_loop():
        while True:
            connection = None
            try:
                connection, channel, queue = await get_connection()
                log.info(f"--> 启动完成，等待消费任务")
                await queue.consume(RabbitMqConsumer.consume)
                # 保持消费者运行
                await asyncio.Future()
            except AMQPConnectionError:
                log.info(f"连接失败，等待5后重试")
                await asyncio.sleep(5)  # 等待后重试
            except Exception:
                log.error(f"主流程其他错误： {traceback.format_exc(limit=1)}")
            finally:
                if connection:
                    if not connection.is_closed:
                        await connection.close()

# def import_subclasses(package_name: str, base_class: Type[Consumer]) -> Dict[str, Type[Consumer]]:
#     """
#     查找所有继承的子类实现（所有的消费者处理方法）
#     """
#     package = importlib.import_module(package_name)
#     subclasses = {}
#     for _, module_name, _ in pkgutil.iter_modules(package.__path__):
#         module = importlib.import_module(f"{package_name}.{module_name}")
#         for name, obj in inspect.getmembers(module):
#             if inspect.isclass(obj) and issubclass(obj, base_class) and obj is not base_class:
#                 subclasses[name] = obj
#     return subclasses
#
#
# CONSUMERS = import_subclasses('backend.consumers.tasks', Consumer)
# # CONSUMERS = import_subclasses('backend.app', Consumer)
