import os
import sys
import asyncio

os.environ['PROMETHEUS_MULTIPROC_DIR'] = '/tmp'

p0 = os.path.abspath(os.path.join(os.path.dirname(__file__), '../'))
p1 = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../'))
p2 = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../'))
print('load package path: ', p0, p1, p2)
sys.path.insert(0, p0)
sys.path.insert(0, p1)
sys.path.insert(0, p2)

from backend.common.task_context import SpawnTaskManager
from backend.common.log import log
from backend.consumers.consumer import Consumer


async def start_consumer(num_processes):
    task_manager = SpawnTaskManager()
    for _ in range(num_processes):
        task = task_manager.create_task(Consumer.mian_consume_loop())
        log.info(f"--> 启动完成[{task.get_name()}]，等待消费任务")
    await task_manager.wait_and_done()


if __name__ == "__main__":
    num_processes = int(os.environ.get('NUM_CONSUMERS', 10))
    log.error(f"消费者数目：{num_processes}")

    try:
        asyncio.run(start_consumer(num_processes))
    except KeyboardInterrupt:
        log.info("Ctrl + C 退出")

    # with ProcessPoolExecutor(max_workers=num_processes) as executor:
    #     futures = [executor.submit(start_consumer) for _ in range(num_processes)]
    #     for future in futures:
    #         try:
    #             future.result()
    #         except KeyboardInterrupt:
    #             log.info("Ctrl + C 退出")
    #         except Exception as e:
    #             log.error(f"消费者进程错误: {e}")
