from urllib3.poolmanager import <PERSON><PERSON><PERSON><PERSON>, HTTPHeaderDict, Retry, MaxRetryError, log
from urllib3.packages import six
from urllib3.util.url import parse_url
from urllib.parse import urljoin


# def urlopen(self, method, url, redirect=True, **kw):
#     """
#     Same as :meth:`urllib3.HTTPConnectionPool.urlopen`
#     with custom cross-host redirect logic and only sends the request-uri
#     portion of the ``url``.
#
#     The given ``url`` parameter must be absolute, such that an appropriate
#     :class:`urllib3.connectionpool.ConnectionPool` can be chosen for it.
#     """
#     u = parse_url(url)
#     self._validate_proxy_scheme_url_selection(u.scheme)
#
#     conn = self.connection_from_host(u.host, port=u.port, scheme=u.scheme)
#
#     kw["assert_same_host"] = False
#     kw["redirect"] = False
#
#     if "headers" not in kw:
#         kw["headers"] = self.headers.copy()
#
#     if self._proxy_requires_url_absolute_form(u):
#         response = conn.urlopen(method, url, **kw)
#     else:
#         aaa = u.request_uri
#         # aaa = '/minio-api' + aaa
#         # aaa = '/mfs' + aaa
#         response = conn.urlopen(method, aaa, **kw)
#
#     redirect_location = redirect and response.get_redirect_location()
#     if not redirect_location:
#         return response
#
#     # Support relative URLs for redirecting.
#     redirect_location = urljoin(url, redirect_location)
#
#     if response.status == 303:
#         # Change the method according to RFC 9110, Section 15.4.4.
#         method = "GET"
#         # And lose the body not to transfer anything sensitive.
#         kw["body"] = None
#         kw["headers"] = HTTPHeaderDict(kw["headers"])._prepare_for_method_change()
#
#     retries = kw.get("retries")
#     if not isinstance(retries, Retry):
#         retries = Retry.from_int(retries, redirect=redirect)
#
#     # Strip headers marked as unsafe to forward to the redirected location.
#     # Check remove_headers_on_redirect to avoid a potential network call within
#     # conn.is_same_host() which may use socket.gethostbyname() in the future.
#     if retries.remove_headers_on_redirect and not conn.is_same_host(
#             redirect_location
#     ):
#         headers = list(six.iterkeys(kw["headers"]))
#         for header in headers:
#             if header.lower() in retries.remove_headers_on_redirect:
#                 kw["headers"].pop(header, None)
#
#     try:
#         retries = retries.increment(method, url, response=response, _pool=conn)
#     except MaxRetryError:
#         if retries.raise_on_redirect:
#             response.drain_conn()
#             raise
#         return response
#
#     kw["retries"] = retries
#     kw["redirect"] = redirect
#
#     log.info("Redirecting %s -> %s", url, redirect_location)
#
#     response.drain_conn()
#     return self.urlopen(method, redirect_location, **kw)



# def new_urlopen(self, method, url, redirect=True, **kw):
#     url = url.replace('myerp.v55-standard.mycyjg.com', 'myerp.v55-standard.mycyjg.com/mfs')
#     PoolManager.urlopen
# PoolManager.urlopen = new_urlopen

import sys
import os

os.environ['PROMETHEUS_MULTIPROC_DIR'] = '/tmp'

p0 = os.path.abspath(os.path.join(os.path.dirname(__file__), '../'))
p1 = os.path.abspath(os.path.join(os.path.dirname(__file__), './'))
sys.path.insert(0, p0)
sys.path.insert(0, p1)

from backend.common.oss.oss import OSSFileProxy, MinioService

# file_path = '/home/<USER>/datacloud-public-server/run.sh'
file_path = '/usr/local/lib/python3.9/site-packages/hug/input_format.py'

""""

String bucket = "jmpt-gxfw";
String endpoint = "https://minio-api1.clyy-cg.myspacex.cn:8443/minio-api";
String apiKey = "4O2tuMO6B6AmVEVjsmSw";
String secretKey = "lkTIVoWx40no7QfGHcY35uefYqZsju71IarPqucR";
        // 真实场景，这里读取是否开启统一域名的配置
       
"""
with open(file_path, 'rb') as fp:
    root = 'tmp'
    filename = 'run.sh'
    # oss = OSSFileProxy()
    # oss = MinioService(
    #     access_key_id='n0su9T8sHdvSuYGH',
    #     access_key_secret='5d3yb27XNtJQJSNF8SsB8m6tJ',
    #     bucket='jmpt-gxfw',
    #     endpoint='https://myerp.v55-standard.mycyjg.com/mfs',
    # )
    oss = MinioService(
        access_key_id='J48A4B2QRQ6D6EUN7STI',
        access_key_secret='jqElqmromBYXqC0woLHBVXds027Q79X77D05a44c',
        bucket='dm-v55-0103',
        endpoint='https://obs.cn-south-1.myhuaweicloud.com',
    )
    # oss = MinioService(
    #     access_key_id='4O2tuMO6B6AmVEVjsmSw',
    #     access_key_secret='lkTIVoWx40no7QfGHcY35uefYqZsju71IarPqucR',
    #     bucket='jmpt-gxfw',
    #     endpoint='https://minio-api1.clyy-cg.myspacex.cn:8443/minio-api',
    # )
    # oss.intranet_minio = oss
    # oss = MinioService(
    #     access_key_id='XNfBvKDSOSWHeVNg',
    #     access_key_secret='6qnQcLdzyvycaMa3Qnu4T3gPW',
    #     bucket='tj-dmp',
    #     endpoint='https://minio-api.gccs-test.mycyjg.com',
    # )
    # oss_file_url = oss.upload(fp, file_name=filename, root=root, key=f'{root}/{filename}', return_intranet_url=0)
    # print(oss_file_url)
    oss_file_url = 'https://obs.cn-south-1.myhuaweicloud.com/dm-v55-0103/jmpt_gxfw/packages/yzsapp/*******/*******.zip'
    # oss_file_url = 'https://myerp.v55-standard.mycyjg.com/jmpt-gxfw/dp/datacloud-server/11-20241213212456.zip'
    # oss_file_url = 'https://minio-api.gccs-test.mycyjg.com/tmp/run.sh'
    sigh_url = oss.sign_url(oss_file_url, 604800, is_url=True, **{'sign_outer_url': True})
    print(sigh_url)
#
# from urllib.parse import urlparse, unquote, quote
#
# # def url_to_obj_key(oss_file_url, bucket=None):
# #     """
# #     从OSS URL地址获取object key
# #     :param str oss_file_url:
# #     :return:
# #     """
# #     if bucket:
# #         start_index = oss_file_url.find(bucket)
# #         # 如果找到了，返回从起始值开始到结束的部分
# #         if start_index != -1:
# #             return (oss_file_url[start_index:]).lstrip('/')
# #     return urlparse(oss_file_url).path.lstrip('/')
# #
# #
# # print()
#
# a=  urlparse('https://myerp.v55-standard.mycyjg.com/mfs')
# print(a)
#
