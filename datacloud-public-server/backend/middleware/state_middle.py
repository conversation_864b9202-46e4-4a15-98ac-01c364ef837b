#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
from datetime import datetime

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from backend.common.shared_data import shared_data_var, get_shared_data

from backend.common.shared_data import CrossData
from backend.common.log import log


class StateMiddleware(BaseHTTPMiddleware):
    """
    记录请求日志
    """

    async def dispatch(self, request: Request, call_next) -> Response:
        shared_data_var.set(CrossData())
        response = await call_next(request)
        # 检查响应类型并修改内容
        content_type = str(request.headers.get("content-type") or '')
        if "application/json" in content_type or '':
            try:
                body = b""
                async for chunk in response.body_iterator:
                    body += chunk
                body_data = json.loads(body)
                g = get_shared_data()
                body_data["profiling"] = g.model_dump()
                headers = response.headers
                del headers['content-length']
                # response.body = json.dumps(body_data, ensure_ascii=False)
                # response.init_headers(response.headers)
                response = JSONResponse(content=body_data, status_code=response.status_code, headers=response.headers)
            except Exception as e:
                log.error(f"StateMiddleware log error: {str(e)}")
        return response
