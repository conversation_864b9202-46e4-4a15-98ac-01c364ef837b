import os
import sys
import asyncio
import importlib.util
from types import ModuleType
from typing import List, Type, Any

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../')))
from backend.database.db_util import create_table
from backend.core.path_conf import BasePath


def find_model_files(base_dir) -> List[str]:
    """
    Recursively find all Python files in 'model' directories under the base directory.
    """
    model_files = []
    for root, _, files in os.walk(base_dir):
        if os.path.basename(root) == 'model':
            for file in files:
                if file.endswith('.py') and file != '__init__.py':
                    model_files.append(os.path.join(root, file))
    return model_files


def import_module_from_file(module_name, file_path) -> ModuleType:
    """
    Import a module from a given file path.
    """
    spec = importlib.util.spec_from_file_location(module_name, file_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module


def import_all_models(base_dir, package_prefix) -> list[type]:
    """
    Import all Python files in 'model' directories and collect all classes.
    """
    model_files = find_model_files(base_dir)
    all_classes = []

    for file_path in model_files:
        # Generate a module name from the file path
        relative_path = os.path.relpath(file_path, base_dir)  # noqa
        module_name = relative_path.replace(os.sep, '.').replace('.py', '')
        full_module_name = f"{package_prefix}.{module_name}"

        module = import_module_from_file(full_module_name, file_path)

        for attr_name in dir(module):
            attr = getattr(module, attr_name)
            if isinstance(attr, type):  # Check if it is a class
                all_classes.append(attr)
                # print(f"Imported {attr} from {module.__name__}")

    return all_classes


async def main():
    # 动态加载 backend/app/*/model 目录下所有 .py 文件中的model类
    base_dir = os.path.join(BasePath, 'backend/app')
    package_prefix = 'app'
    import_all_models(base_dir, package_prefix)
    # 创建所有表以及表变动
    engine = await create_table()
    # if

# def add_colf

if __name__ == '__main__':
    asyncio.run(main())
