#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
import os
import urllib.parse
from datetime import datetime

from fastapi import Depends
from sqlalchemy import select, update, desc, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from sqlalchemy.sql import Select

from backend.common import jwt
from backend.app.export_dist.schema.export import ExportModel as ExportSchemaModel
from backend.app.export_dist.model.export import Export as ExportTableModel, ExportDistDetail
from backend.common.enums import ExportTaskStatus
from backend.common.jwt import CookiesUser
from backend.common.pagination import PaginationParams
from backend.database.db_util import uuid4_str, CurrentSession


async def get_exports(db: CurrentSession, user: CookiesUser, pagination: PaginationParams, keyword: str):
    query = db.query(ExportTableModel).filter(
        ExportTableModel.tenant_code == user.tenant_code,
        ExportTableModel.is_delete == 0,
    )
    if keyword:
        query = query.filter(ExportTableModel.name.like(f'%{keyword}%'))
    total = query.count()
    datas = (
        query.order_by(ExportTableModel.created_at.desc()).
        offset(pagination.offset).
        limit(pagination.limit).all()
    )
    result = [
        dict(
            id=data.id,
            tenant_code=data.tenant_code,
            name=data.name,
            description=data.description,
            status=data.status,
            oss_url=data.oss_url,
            filename=__format_filename(data),
            content=data.content,
            # created_at=data.formatted_created_at(),
            # updated_at=data.formatted_updated_at(),
            created_at=data.created_at,
            updated_at=data.updated_at,
            created_by=data.created_by,
            updated_by=data.updated_by,
        )
        for data in datas
    ]
    db.commit()
    return {'items': result, 'total': total}


def __format_filename(data):
    if data.oss_url:
        filename = os.path.basename(data.oss_url.split('?')[0])
        return urllib.parse.unquote(filename)
    else:
        return ''


async def add_one_export(db: CurrentSession, export_model: ExportSchemaModel, user: CookiesUser) -> str:
    """
    添加一条导出记录
    """
    model_id = uuid4_str()
    add_model = ExportTableModel(
        id=model_id,
        name=export_model.name,
        description=export_model.desc,
        request_args=json.dumps(export_model.model_dump(), ensure_ascii=False),
        tenant_code=user.tenant_code,
        created_by=user.user_name,
        updated_by=user.user_name,
        status=ExportTaskStatus.init.value,
    )
    db.add(add_model)
    db.commit()
    return model_id


async def add_one_export_detail(db: CurrentSession, **kwargs) -> str:
    """
    添加一条导出明细
    """
    model_id = uuid4_str()
    add_model = ExportDistDetail(id=model_id, **kwargs)
    db.add(add_model)
    db.commit()
    return model_id


async def update_one_export(db: CurrentSession, export_id: str, export_data: dict):
    """
    添加一条导出记录
    """
    db.query(ExportTableModel).filter(ExportTableModel.id == export_id).update(export_data)
    db.commit()


async def get_one_export_log(db: CurrentSession, export_id: str):
    """
    添加一条导出记录
    """
    data = db.query(ExportTableModel.log).filter(ExportTableModel.id == export_id).first()
    if data.log:
        log_data = json.loads(data.log)
    else:
        log_data = {}
    db.commit()
    return log_data
