#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from datetime import datetime

from sqlalchemy import String, SmallInteger, Column, DateTime, Text, BLOB, INT
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.dialects.mysql import MEDIUMTEXT

from backend.common.model import BaseModel, table
from backend.core.conf import settings
from backend.database.db_util import uuid4_str
from backend.utils.timezone import timezone


class Export(BaseModel):
    """导出表"""

    __tablename__ = table('export_dist')
    __table_args__ = {'comment': '数据发布中心导出导出数据包表', }

    id: Mapped[uuid4_str] = mapped_column(String(36), comment='导出数据包名称', primary_key=True, default=uuid4_str)

    tenant_code: Mapped[str] = mapped_column(String(128), comment='租户code', default='', nullable=True)
    name: Mapped[str] = mapped_column(String(2048), comment='导出数据包名称', default='', nullable=True)
    # file: Mapped[str] = mapped_column(String(4096), comment='导出数据包文件名称', default='', nullable=True)
    description: Mapped[str] = mapped_column(Text, comment='描述', default='', nullable=True)
    request_args: Mapped[str] = mapped_column(Text, comment='请求的参数详情', default='', nullable=True)
    status: Mapped[str] = mapped_column(String(64), comment='导出状态', default='', nullable=True)
    is_delete: Mapped[str] = mapped_column(INT, comment='是否删除', default=0, nullable=True)
    oss_url: Mapped[str] = mapped_column(String(4096), comment='导出oss链接地址', default='', nullable=True)
    if settings.DB_TYPE.upper() == "DM":
        content: Mapped[str] = mapped_column(Text, comment='导出的文件内容', nullable=True)
        log: Mapped[str] = mapped_column(Text, comment='整个更新中心导出的日志', nullable=True)
    else:
        content: Mapped[str] = mapped_column(MEDIUMTEXT, comment='导出的文件内容', nullable=True)
        log: Mapped[str] = mapped_column(MEDIUMTEXT, comment='整个更新中心导出的日志', nullable=True)


class ExportDistDetail(BaseModel):
    """数见数芯导出的详情记录"""

    __tablename__ = table('export_dist_detail')
    __table_args__ = {'comment': '数据发布中心各产品导出导出数据包表详情记录表', }

    id: Mapped[uuid4_str] = mapped_column(String(36), comment='id', primary_key=True, default=uuid4_str)
    export_id: Mapped[uuid4_str] = mapped_column(String(36), comment='导出任务id', primary_key=True, default=uuid4_str)

    source: Mapped[str] = mapped_column(String(128), comment='产品来源', default='', nullable=True)
    oss_url: Mapped[str] = mapped_column(String(4096), comment='产品给出的导出地址', default='', nullable=True)
    if settings.DB_TYPE.upper() == "DM":
        curl: Mapped[str] = mapped_column(Text, comment='请求导出参数详情', nullable=True)
        log: Mapped[str] = mapped_column(Text, comment='请求导出日志', nullable=True)
    else:
        curl: Mapped[str] = mapped_column(MEDIUMTEXT, comment='请求导出参数详情', nullable=True)
        log: Mapped[str] = mapped_column(MEDIUMTEXT, comment='请求导出日志', nullable=True)
