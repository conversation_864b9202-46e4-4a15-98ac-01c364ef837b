from backend.app.export_dist.crud.crud_export import add_one_export, get_exports, update_one_export, get_one_export_log
from backend.app.export_dist.schema.export import ExportModel
from backend.common.pagination import PaginationParams
from backend.common.request import HTTPRequestUtil
from backend.common.domain import get_server_domain
from backend.common.utils import timed_lru_cache
from backend.common.jwt import CookiesUser, DependsJwtUser
from backend.consumers.tasks.export import ExportConsumer
from backend.database.db_util import CurrentSession


async def get_export_tasks(db: CurrentSession, user: CookiesUser, pagination: PaginationParams, keyword: str):
    return await get_exports(db, user, pagination, keyword)


async def get_export_log(db: CurrentSession, export_id: str):
    return await get_one_export_log(db, export_id)


async def trigger_export_task(db: CurrentSession, user: CookiesUser, model: ExportModel):
    """
    触发导入任务
    """

    # 1. 新建一条导出记录
    export_id = await add_one_export(db, model, user)

    # 2. 触发异步任务
    task = {
        'user': user,
        'export_id': export_id,
        'request_args': model.model_dump(),
    }
    await ExportConsumer().push(**task)
    return export_id


async def update_export(db: CurrentSession, export_id: str, export_data: dict):
    return await update_one_export(db, export_id, export_data)
