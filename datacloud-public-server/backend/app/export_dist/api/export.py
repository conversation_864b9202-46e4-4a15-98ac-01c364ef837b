#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from fastapi import APIRouter, Query, Depends

from backend.common.pagination import DependsPaginationParams, PaginationParams
from backend.common.response.response_schema import response_base, ResponseModel, CustomResponseCode

from backend.common.jwt import CookiesUser, DependsJwtUser
from backend.app.export_dist.service import export_service
from backend.app.export_dist.schema.export import ExportModel, DeleteExportModel
from backend.database.db_util import CurrentSession

router = APIRouter()


# @router.get('/get_dmp_reports', summary='从dmp获取要导出的报告列表')
# async def get_dmp_reports(user: CookiesUser = DependsJwtUser, business_tags: str = Query()) -> ResponseModel:
#     data = await export_service.get_dmp_reports(user, business_tags)
#     return ResponseModel(data=data.get('data') or [], msg=data.get('msg') or '')


@router.post('/export', summary='点击导出')
async def export(db: CurrentSession, model: ExportModel, user: CookiesUser = DependsJwtUser) -> ResponseModel:
    export_id = await export_service.trigger_export_task(db, user, model)
    return ResponseModel(data=export_id)


@router.get('/export_list', summary='导出列表查询')
async def export_list(
        db: CurrentSession,
        pagination: PaginationParams = DependsPaginationParams,
        user: CookiesUser = DependsJwtUser,
        keyword: str = ''
) -> ResponseModel:
    export_id = await export_service.get_export_tasks(db, user, pagination, keyword)
    return ResponseModel(data=export_id)


@router.get('/export_log', summary='导出日志获取')
async def export_log(
        db: CurrentSession,
        export_id: str = Query(),
        user: CookiesUser = DependsJwtUser,
) -> ResponseModel:
    export_id = await export_service.get_one_export_log(db, export_id)
    return ResponseModel(data=export_id)


@router.post('/delete', summary='删除导出')
async def delete(db: CurrentSession, model: DeleteExportModel, user: CookiesUser = DependsJwtUser) -> ResponseModel:
    if not model.export_id:
        return ResponseModel(msg='缺少export_id')
    await export_service.update_export(db, model.export_id, {'is_delete': 1})
    return ResponseModel(data='')
