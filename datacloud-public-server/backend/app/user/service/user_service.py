# #!/usr/bin/env python3
# # -*- coding: utf-8 -*-
from typing import <PERSON><PERSON>, Any

from fastapi.responses import RedirectResponse

from backend.app.import_dist.crud.import_dist import has_unimport_tasks
from backend.common.jwt import third_verify_login, set_cookies
from backend.database.db_util import CurrentSession


async def third_api_login(token: str, redirect: str) -> tuple[Any, Any, RedirectResponse]:
    """
    api集成登录跳转更新中心
    token: url上的token
    """
    rs, rs_txt, model = await third_verify_login(token)
    if not redirect:
        path = '/'
    else:
        path = redirect
    response = RedirectResponse(url=f'/datacloud{path}')
    if rs:
        await set_cookies(data=model.model_dump(), response=response)
        return rs, rs_txt, response
    else:
        return rs, rs_txt, None


async def has_unimport_task(user, db: CurrentSession) -> tuple[Any, Any, RedirectResponse]:
    """
    token: url上的token
    # """
    # rs, rs_txt, model = await third_verify_login(token)
    # if rs:
    has_status = await  has_unimport_tasks(db, user.tenant_code)
    return has_status
    # else:
    #     return rs, rs_txt, None
