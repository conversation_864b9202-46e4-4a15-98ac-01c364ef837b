#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import fastapi
from fastapi import APIRouter, Query, Depends

from backend.app.user.schema.user import LoginTokenPayload
from backend.common.domain import get_server_domain
from backend.common.log import log
from backend.common.request import HTTPRequestUtil
# from backend.common.jwt import_dist CurrentUser, DependsJwtUser
# from backend.common.pagination import_dist paging_data, DependsPagination
from backend.common.response.response_schema import response_base, ResponseModel, CustomResponseCode

# from backend.database.db_mysql import_dist CurrentSession
# from backend.app.user.schema.user import_dist CreateUser, GetUserInfo, ResetPassword, UpdateUser, Avatar
from backend.app.user.service import user_service
from backend.common.jwt import CookiesUser, DependsJwtUser, set_cookies
from backend.database.db_util import CurrentSession

# from backend.app.user.service.user_service import_dist UserService
# from backend.utils.serializers import_dist select_as_dict

router = APIRouter()


@router.get('/token_login', summary='api集成跳转登录接口')
async def token_login(token: str = Query(), redirect: str = Query()) -> ResponseModel:
    rs, rs_txt, response = await user_service.third_api_login(token, redirect)
    if rs:
        return response
    else:
        return await response_base.fail(data=rs_txt)


@router.get('/has_unimport', summary='租户是否有待更新提示')
async def has_import(
        db: CurrentSession,
        user: CookiesUser = DependsJwtUser,
        # token: str = Query(),
):
    has_status = await user_service.has_unimport_task(user, db)
    data = {"code": 200, "msg": "", "data": has_status, "result": True}
    return data


@router.get('/test', summary='test')
async def test(user: CookiesUser = DependsJwtUser) -> ResponseModel:
    from backend.common.oss.oss import OSSFileProxy

    file_path = '/home/<USER>/datacloud-public-server/run.sh'
    with open(file_path, 'rb') as fp:
        root = 'tmp'
        filename = 'run.sh'
        oss = OSSFileProxy()
        oss_file_url = oss.upload(fp, file_name=filename, root=root, key=f'{root}/{filename}', return_intranet_url=True)
        sigh_url = oss.get_sigh_url(oss_file_url, 604800, is_url=True)
        log.error(f"oss_file_url:::::::: {oss_file_url}")
        print('xxx', oss.get_object(oss_file_url, is_url=True).resp.response.content)

    return await response_base.success(data={
        'oss_file_url': oss_file_url,
        'sigh_url': sigh_url,
    })


@router.get('/current', summary='当前用户信息')
async def current_user(
        db: CurrentSession,
        user: CookiesUser = DependsJwtUser,
) -> ResponseModel:
    user_data = user.model_dump()
    user_data.pop('expire', None)
    return ResponseModel(data=user_data)


@router.get('/relate_code', summary='可选择的租户切换列表')
async def relate_code(
        db: CurrentSession,
        user: CookiesUser = DependsJwtUser,
) -> ResponseModel:
    headers = await HTTPRequestUtil.build_common_access_headers(user)
    host = get_server_domain('dmp')
    kwargs = {
        'url': f'{host}/api/pubserver/account_ref_code',
        'headers': headers,
        'params': {'account': user.user_code}
    }
    response = None
    try:
        response = await HTTPRequestUtil.request('get', **kwargs)
        rs = response.json()
    except Exception as e:
        rs = {'error': str(e), 'response': response.text if response else ''}
    # if response:
    #     curl = HTTPRequestUtil.httpx_request_to_curl(response.request)
    # rs['curl'] = curl
    return await response_base.success(data=rs)


@router.post('/switch', summary='切换用户登录')
async def switch_user(
        response: fastapi.Response,
        db: CurrentSession,
        post_user: LoginTokenPayload,
        user: CookiesUser = DependsJwtUser,
) -> ResponseModel:
    user_data = post_user.model_dump()
    user_data.pop('expire', None)
    await set_cookies(data=user_data, response=response)
    return ResponseModel(data={})
