#!/usr/bin/env python3
# -*- coding: utf-8 -*-


from fastapi import APIRouter

from backend.app.user.api import v1 as user_v1
from backend.app.export_dist.api import v1 as export_dist_v1
from backend.app.import_dist.api import v1 as import_dist_v1
from backend.app.proxy.api import v1 as proxy_v1
from backend.app.publish_center.api import v1 as publish_center_v1
from backend.app.upload.api import v1 as upload_v1

route = APIRouter()

route.include_router(user_v1)
route.include_router(export_dist_v1)
route.include_router(import_dist_v1)
route.include_router(proxy_v1)
route.include_router(publish_center_v1)
route.include_router(upload_v1)


