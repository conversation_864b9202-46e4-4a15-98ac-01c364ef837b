import asyncio
import io

from backend.common.oss.oss import OSSFileProxy


async def upload_file(file, file_name, upload_location):
    """
    上传文件
    max_size: 104,857,600 bytes
    :param file:
    :return:
    """
    try:
        data = {}
        root = upload_location.split('/')[0] if '/' in upload_location else ''

        # 读取文件内容
        contents = await file.read()

        # 创建一个类似文件对象的 BytesIO 对象
        file_obj = io.BytesIO(contents)
        # 添加 name 属性，模拟普通文件对象
        file_obj.name = file.filename or file_name

        oss_url = OSSFileProxy().upload(file_obj, file_name=file_name, root=root, key=upload_location)
    except Exception as e:
        raise Exception(str(e))

    data['url'] = oss_url
    data['file_name'] = file_name
    return data
