from fastapi import APIRouter, Query, File, UploadFile, Form

from backend.common.response.response_schema import response_base, ResponseModel
from backend.common.jwt import CookiesUser, DependsJwtUser

from backend.app.upload.service import upload_service as upload_service

router = APIRouter()


@router.post('/upload_file', summary='上传文件')
async def upload_file(
        user: CookiesUser = DependsJwtUser,
        file: UploadFile = File(...),
        file_name: str = Form(...),
        upload_location: str = Form(...),
) -> ResponseModel:
    data = await upload_service.upload_file(file, file_name, upload_location)
    return ResponseModel(data=data)
