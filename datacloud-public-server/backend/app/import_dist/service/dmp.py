from backend.common.domain import get_server_domain
from backend.common.jwt import CookiesUser
from backend.common.request import HTTPRequestUtil


async def get_dmp_import_report_dependency(user: CookiesUser, task_id: str, report_id_list: list[str]):
    """
    解析dmp包内容
    """
    headers = await HTTPRequestUtil.build_common_access_headers(user)
    dap_domain = get_server_domain('dmp')
    kwargs = {
        'url': f'{dap_domain}/api/pubserver/get_import_report_dependency',
        'headers': headers,
        'json': {
            'task_id': task_id,
            'update_reports': report_id_list
        }
    }
    response = await HTTPRequestUtil.request('post', **kwargs)
    result = response.json()
    if result.get('result'):
        return result.get('data')
    raise Exception(f'解析dmp包依赖失败:{result.get("msg")}｝')


async def get_dmp_import_mapping(user: CookiesUser, task_id: str, report_id_list: list[str]):
    headers = await HTTPRequestUtil.build_common_access_headers(user)
    dap_domain = get_server_domain('dmp')
    kwargs = {
        'url': f'{dap_domain}/api/pubserver/get_dmp_import_mapping',
        'headers': headers,
        'json': {
            'task_id': task_id,
            'update_reports': report_id_list
        }
    }
    response = await HTTPRequestUtil.request('post', **kwargs)
    result = response.json()
    if result.get('result'):
        return result.get('data')
    raise Exception(f'解析dmp包映射失败:{result.get("msg")}｝')
