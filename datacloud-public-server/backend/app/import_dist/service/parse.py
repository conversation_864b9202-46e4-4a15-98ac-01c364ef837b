import asyncio
import json
import zipfile
import io
import os
import shutil

from backend.app.import_dist.service.convert.dmp import convert as dmpConvert
from urllib import parse
from backend.common.oss.oss import OSSFileProxy
from backend.common.request import HTTPRequestUtil
from backend.core.conf import settings
from backend.utils.timezone import timezone
from backend.common import utils
from backend.app.import_dist.service.rdc import upload_oss


class ParseIntegra:
    VERSION_LAST = 'version_last'  # 最新5.5的压缩包
    VERSION_DMP_NEW = 'version_dmp_new'  # dmp5.0
    VERSION_DMP_FIRST = 'version_dmp_first'  # dmp 4.9 及以下
    VERSION_DAP_NEW = 'version_dap_new'  # dap 对标dmp5.0

    def __init__(self, file_oss_url):
        self.file_oss_url = file_oss_url
        self.version = self.VERSION_LAST

    def check_version(self):
        oss_file = OSSFileProxy()
        obj_result = oss_file.get_object(parse.unquote(self.file_oss_url), True)
        if not obj_result:
            raise Exception(400, '读取文件失败')

        bytes_data = obj_result.read()
        if len(bytes_data) == 0:
            raise Exception(400, '文件为空，读取失败')

        # v5.5
        bmpdir = 'data/dmp/mysql/base/info.json'
        bigdatadir = 'data/bigdata/product_public_space/main/env.json'

        # v5.0
        bmp_base_info_path = 'base/info.json'
        bigdata_env_info_path = 'env.json'

        # v5.0 以下，直接读取文件内容

        bytes_io = io.BytesIO(bytes_data)

        with zipfile.ZipFile(bytes_io, "r") as zip_file:
            if bmpdir in zip_file.namelist() or bigdatadir in zip_file.namelist():
                self.version = self.VERSION_LAST
            elif bmp_base_info_path in zip_file.namelist():
                self.version = self.VERSION_DMP_NEW
            elif bigdata_env_info_path in zip_file.namelist():
                # self.version = self.VERSION_DAP_NEW
                raise Exception('暂不支持数芯老版本导入')
            else:
                self.version = self.VERSION_DMP_FIRST

        return self.version

    def download_file(self, model_id):
        folder = f'/tmp/{settings.OSS_PUB_SERVER_BASE_FOLDER_NAME}/{model_id}'

        # 下载、保存文件到本地
        filename, full_path = HTTPRequestUtil.download_oss_file(folder, url=self.file_oss_url)

        return folder, full_path

    def get_base_info(self, unzip_folder):
        base_info = {
            'env_code': 'local',
            'version': '5.5'
        }

        if self.version == self.VERSION_LAST:
            env_info_path = os.path.join(unzip_folder, 'data/bigdata/product_public_space/main/env.json')
            if os.path.exists(env_info_path):
                with open(env_info_path, 'r', encoding='utf-8') as file:
                    env_info = json.load(file)
                    if env_info:
                        base_info['env_code'] = env_info['client_code']
        if self.version == self.VERSION_DMP_FIRST:
            json_files = [name for name in os.listdir(unzip_folder) if name.endswith('.json')]
            for file_name in json_files:
                with open(os.path.join(unzip_folder, file_name), 'r', encoding='utf-8') as file:
                    r_data = file.read()
                    if r_data:
                        try:
                            export_data = json.loads(r_data)
                            base_info['env_code'] = export_data['env']
                            base_info['version'] = '4.9'
                        except:
                            raise Exception('zip压缩文件内容格式有误')
                        break
        if self.version == self.VERSION_DAP_NEW:
            env_path = os.path.join(unzip_folder, 'env.json')
            with open(env_path, 'r', encoding='utf-8') as f:
                env_info = json.load(f)
                if env_info:
                    base_info['version'] = '5.0'
                    base_info['env_code'] = env_info['client_code']
        if self.version == self.VERSION_DMP_NEW:
            base_info['version'] = '5.0'

        return base_info

    def unzip(self, folder, local_file_path):
        unzip_folder = os.path.join(folder, 'all')
        utils.unzip_files_to_folder(local_file_path, unzip_folder)
        return unzip_folder

    async def rezip(self, unzip_folder):

        dmp_zip_url = dap_zip_url = ''
        # 5.5 的重新压缩即可
        if self.version == self.VERSION_LAST:
            base_info_file_path = os.path.join(unzip_folder, 'data/dmp/mysql/base/info.json')
            dmp_zip_path = ''
            if os.path.exists(base_info_file_path):
                dmp_mysql_path = os.path.join(unzip_folder, 'data/dmp/mysql')
                dmp_file_paths = [os.path.join(dmp_mysql_path, item) for item in os.listdir(dmp_mysql_path)
                                  if os.path.isdir(os.path.join(dmp_mysql_path, item)) or os.path.isfile(
                        os.path.join(dmp_mysql_path, item))]
                dmp_zip_path = utils.zip_files_from_folders(dmp_file_paths, f'dmp-{timezone.now_str_no_sep()}.zip',

                                                            zip_folder=unzip_folder)
            dap_zip_path = ''
            dap_file_path = os.path.join(unzip_folder, 'data/bigdata/')
            if os.path.exists(dap_file_path):
                dap_file_paths = [os.path.join(dap_file_path, item) for item in os.listdir(dap_file_path)
                                  if os.path.isdir(os.path.join(dap_file_path, item)) or os.path.isfile(
                        os.path.join(dap_file_path, item))]
                dap_zip_path = utils.zip_files_from_folders(dap_file_paths, f'bigdata-{timezone.now_str_no_sep()}.zip',
                                                            zip_folder=unzip_folder)
            # 重新上传
            if os.path.exists(dmp_zip_path):
                dmp_zip_url = await upload_oss(dmp_zip_path)
            if os.path.exists(dap_zip_path):
                dap_zip_url = await upload_oss(dap_zip_path)

        # 5.0数见
        elif self.version == self.VERSION_DMP_NEW:
            dirs = os.listdir(unzip_folder)
            if (len(dirs) == 2 and 'base' in dirs and 'datasets' in dirs) or (
                    len(dirs) == 3 and 'base' in dirs and 'datasets' in dirs and 'excel' in dirs):
                raise Exception('暂不支持单独导入数据集')
            base_info_file_path = os.path.join(unzip_folder, 'base/info.json')
            if os.path.exists(base_info_file_path):
                dmp_file_paths = [os.path.join(unzip_folder, item) for item in os.listdir(unzip_folder)
                                  if os.path.isdir(os.path.join(unzip_folder, item)) or os.path.isfile(
                        os.path.join(unzip_folder, item))]
                dmp_zip_path = utils.zip_files_from_folders(dmp_file_paths, f'dmp-{timezone.now_str_no_sep()}.zip',
                                                            zip_folder=unzip_folder)
                dmp_zip_url = await upload_oss(dmp_zip_path)
            else:
                raise Exception(f'文件格式错误，缺少base目录和相关文件')
        # 4.9 数见
        elif self.version == self.VERSION_DMP_FIRST:
            new_folder = dmpConvert(unzip_folder)
            dmp_file_paths = [os.path.join(new_folder, item) for item in os.listdir(new_folder)
                              if os.path.isdir(os.path.join(new_folder, item)) or os.path.isfile(
                    os.path.join(new_folder, item))]
            dmp_zip_path = utils.zip_files_from_folders(dmp_file_paths, f'dmp-{timezone.now_str_no_sep()}.zip',
                                                        zip_folder=new_folder)
            dmp_zip_url = await upload_oss(dmp_zip_path)
        else:
            # 5.0 数芯
            env_path = os.path.join(unzip_folder, 'env.json')
            if os.path.exists(env_path):
                space_dir = os.path.join(unzip_folder, 'bigdata/product_public_space/main/')
                if not os.path.exists(space_dir):
                    os.makedirs(space_dir, exist_ok=True)
                bigdata_dir = os.path.join(unzip_folder, 'bigdata')
                for file in os.listdir(unzip_folder):
                    source_path = os.path.join(unzip_folder, file)
                    if source_path == bigdata_dir:
                        continue
                    target_path = os.path.join(space_dir, file)
                    shutil.move(source_path, target_path)
                dap_file_paths = [os.path.join(bigdata_dir, item) for item in os.listdir(bigdata_dir)
                                  if os.path.isdir(os.path.join(bigdata_dir, item)) or os.path.isfile(
                        os.path.join(bigdata_dir, item))]
                dap_zip_path = utils.zip_files_from_folders(dap_file_paths, f'dap-{timezone.now_str_no_sep()}.zip',
                                                            zip_folder=bigdata_dir)
                dap_zip_url = await upload_oss(dap_zip_path)

        return dmp_zip_url, dap_zip_url


#   验证文件是否合法
#       根目录有文件夹 data 认为是最新的5.5的
#       根目录有文件夹 base 认为是5.0的数见
#       根目录有文件 env.json 认为是数芯的
#       如果都没有直接读取所有json文件，认为是5.0以下的数见
#   根据不同的版本转化成最新版本
#   重新压缩 and upload
#   生成定制导入记录


def test_verify():
    # minio_url = 'http://***********:9000/tj-dmp/dp/datacloud-server/rdc/e562e5d086788a9417b0e2802d7e9728.zip'
    # minio_url = 'http://***********:9000/tj-dmp/dp/导出数据结构-只有大屏_20250524140745.zip'
    minio_url = 'http://***********:9000/tj-dmp/dp/导出数据看看-20250526143424-main-20250526143543.zip'
    aa = ParseIntegra(minio_url)
    aa.check_version()
    folder, full_path = aa.download_file('vvvvv')
    unzippath = aa.unzip(folder, full_path)
    asyncio.run(aa.rezip(unzippath))
    aa.check_version()
    return True
