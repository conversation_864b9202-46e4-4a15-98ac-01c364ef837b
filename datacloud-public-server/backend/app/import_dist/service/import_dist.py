import copy
import json
import zipfile
import io
import os
import shutil
from datetime import datetime
from time import time
from urllib.parse import unquote
from urllib import parse
from collections import defaultdict
from urllib.parse import urlparse, urlunparse

from backend.app.import_dist.service.parse import ParseIntegra
from backend.utils.timezone import timezone

from packaging import version as packaging_version

from backend.app.import_dist.crud.import_dist import get_imports, get_import_detail, get_rdc_detail, get_one_import_log, \
    update_one_import_record, get_rdcs, add_one_import_detail
from backend.common import utils
from backend.common.domain import get_server_domain
from backend.common.enums import RDCTenantType, ImportStatus, TenantType, ImportPackageType
from backend.common.log import log
from backend.app.import_dist.crud.import_dist import get_imports, get_import_detail
from backend.app.import_dist.model.import_dist import ImportDist, BiDirectionalDict, RDC, ImportDistDetail
from backend.common.exception.errors import RequestError, ServerError
from backend.app.import_dist.service import dap, dmp
from backend.common.oss.oss import OSSFileProxy
from backend.common.pagination import PaginationParams
from backend.common.jwt import CookiesUser, DependsJwtUser
from backend.common.task_context import TotalState, TaskContextManager
from backend.common.utils import get_filename, remove_oss_endpoint
from backend.consumers.tasks.custom_import import CustomImportConsumer
from backend.common.request import HTTPRequestUtil
from backend.consumers.tasks.trigger_dmp_parse import TriggerDMPParserConsumer
from backend.database.db_redis import redis_client
from backend.database.db_util import CurrentSession, uuid4_str
from backend.app.import_dist.schema.import_dist import RdcParams, ImportRequestParams, ListModifiedReportsParams, \
    ImportSelectedParams, ListModelRelationsParams, SummaryModelRefsParams, TriggerDapImportParams, \
    MergeVersionSourceLogDetail, MergeVersionSourceLog
from backend.common import utils
from backend.app.import_dist.service.rdc import upload_oss, RDCDispatcher, add_one_rdc_record, add_one_import_record
from backend.core.conf import settings

MERGE_SOURCE_TEXT = 'merge_version'
MERGE_PART = 'merge_to'


async def get_all_imports(db: CurrentSession, user: CookiesUser, pagination: PaginationParams, keyword: str):
    """
    获取导出的列表
    """
    result = await get_imports(db, user, pagination, keyword)

    # 添加跨版本合并的合并信息
    for data in result.get('items', []):
        status = data.get('status')
        import_id = data.get('id')
        merge_info = {}
        if status == ImportStatus.skip.value:
            record = db.query(ImportDistDetail).filter(
                ImportDistDetail.import_id == import_id,
                ImportDistDetail.source == MERGE_PART,
                ImportDistDetail.status == '成功',
            ).order_by(ImportDistDetail.import_id.desc()).limit(1).first()
            try:
                merge_info = json.loads(record.log)
            except:
                pass
        data['merge_info'] = merge_info
    return result


async def get_all_rdc_records(db: CurrentSession, user: CookiesUser, pagination: PaginationParams, keyword: str):
    """
    获取rdc推送记录
    """
    return await get_rdcs(db, user, pagination, keyword)


async def get_import_log(db: CurrentSession, id: str):
    return await get_one_import_log(db, id)


async def get_one_import(db: CurrentSession, user: CookiesUser, id: str):
    """
    获取导出的列表
    """
    data = await get_import_detail(db, id)
    dist = data.to_dict()

    rdc = await get_rdc_detail(db, data.rdc_record_id)
    if rdc:
        rdc_data = rdc.to_dict()
    else:
        rdc_data = {}

    dist['app_name'] = rdc_data.get('app_name')
    dist['version'] = rdc_data.get('version')
    dist['app_code'] = rdc_data.get('app_code')
    dist['app_key'] = rdc_data.get('app_key')
    dist['env_code'] = rdc_data.get('env_code')
    dist['business_mode'] = rdc_data.get('business_mode')
    dist['operator'] = rdc_data.get('operator')
    dist['upgrade_scene'] = rdc_data.get('upgrade_scene')
    return dist


async def do_import(db: CurrentSession, user: CookiesUser, params: ImportRequestParams):
    """
    执行用户导入
    """
    import_detail = await get_import_detail(db, params.task_id)
    if not import_detail:
        raise Exception(f"导入id不存在！")
    if import_detail.package_type == '产品更新包':
        if not import_detail.dap_import_id:
            raise Exception(f"数芯的导入id为空！")
    if import_detail.status not in [ImportStatus.not_import.value, ImportStatus.importError.value]:
        raise Exception(f"任务状态为：{import_detail.status}, 拒绝更新！")
    rdc = await get_rdc_detail(db, import_detail.rdc_record_id)
    if not rdc:
        raise Exception(f"RDC记录不存在！")
    # 触发导入确定前，确定是否有其他导入在进行中
    if import_detail.package_type == ImportPackageType.custom.value:
        ret = await check_upload_update(db, user)
        if ret == False:
            raise Exception('请等待其他包更新完成')
    else:
        await check_version_update(db, user, rdc.app_code, rdc.version, rdc.created_at)

    # 更新导入的参数
    await update_one_import_record(db, import_detail.id, {"request_args": params.request_args})

    rdc_data = rdc.to_dict()
    data = copy.deepcopy(params.model_dump(by_alias=True))
    # 1. 拆分数见数芯的参数
    dmp_args = {
        'rdc_data': rdc_data,
        'oss_url': import_detail.dmp_oss_url,
        'subject': data.get('subject') or [],
        'datasource_mapping': data.get('dashboard_datasource') or [],
        'dashboard_ids': data.get('import_info', {}).get('data_dashboard_ids') or []
    }

    dap_args = data
    dap_args.pop('task_id', None)
    dap_args.pop('dashboard_datasource', None)
    dap_args.get('import_info', {}).pop('data_dashboard_ids', None)
    dap_args['id'] = import_detail.dap_import_id
    dap_args['app_code'] = rdc.app_code
    # if import_detail.status == ImportStatus.not_import.value:
    #     dap_args['retry'] = False
    # elif import_detail.status == ImportStatus.importError.value:
    #     dap_args['retry'] = True
    # else:
    #     dap_args['retry'] = False

    # 2. 导入
    await CustomImportConsumer().push(**{
        "task_id": import_detail.id,
        "rdc_task_id": import_detail.rdc_task_id,
        "dmp_args": dmp_args,
        "dap_args": dap_args,
        "dap_import_id": import_detail.dap_import_id,
        'user': user,
        'backup':params.backup,
    })
    return params.task_id


async def summary_model_refs(db: CurrentSession, user: CookiesUser, params: SummaryModelRefsParams):
    # 步骤二异常提示
    detail: ImportDist = await get_import_detail(db, params.task_id)
    task_id = detail.rdc_task_id
    dap_task_id = detail.dap_import_id
    mapping: dict = await _get_dashboard_table_mapping(task_id, dap_task_id, user) or {}
    dir = _get_direc(mapping)
    dir.construct_simple_dict_by_id(['id', 'name', 'type'])
    selected_dashboards = params.dashboard_ids or []
    # 获取选择的数见报表关联的未选择更新的宽表模型
    selected_tables = params.data_model_import_report_names or []
    model_ref_not_update = await _get_dmp_refs_not_update(selected_dashboards, selected_tables, dir)
    # 获取选择的个性化宽表关联的已有数见报表
    selected_tables = params.individual_data_model_import_names or []
    dmp_inffect_map = await _get_dmp_inffect_data(selected_tables, selected_dashboards, dir, user)
    if dap_task_id:
        params.task_id = dap_task_id
        # 获取数芯数据，并做合并
        inffect_data, model_ref_not_update = await _merge_dap_data(params, model_ref_not_update, dmp_inffect_map, user)
    else:
        inffect_data = []
    return {'remote_impacts': inffect_data, 'model_not_check_errs': model_ref_not_update}


def _get_direc(mapping: dict):
    dir = BiDirectionalDict(mapping.get('key_to_value'), mapping.get('value_to_key'))
    return dir


async def _merge_dap_data(params, model_ref_not_update, dmp_inffect_map, user: CookiesUser):
    resp = await _request_dap('/api/common/publish_center/import/update_check', params.__dict__, 'post', user)
    if not resp.get('result'):
        raise ServerError(msg=f'数芯接口异常:{resp.get("msg")}')
    dap_data = resp.get('data')
    inffect_data = dap_data.get('data_models') or []
    inffect_data = [item for item in inffect_data if item.get('impact_info')]
    # 合并数见数芯未选择更新的宽表
    model_ref_not_update += dap_data.get('data_services') or []
    # 合并个性化宽表关联的报表、数据服务
    for item in inffect_data:
        impact_info = item.get("impact_info")
        val = dmp_inffect_map.get(item.get('name'))
        if not impact_info and not val:
            continue
        if val:
            if impact_info:
                val += f',{impact_info}'
            del dmp_inffect_map[item.get('name')]
        else:
            val = impact_info
        item['impact_info'] = val
    for k, v in dmp_inffect_map.items():
        if not v:
            continue
        inffect_data.append({'name': k, 'impact_info': v})
    return inffect_data, model_ref_not_update


async def _get_dmp_inffect_data(selected_tables, selected_dashboards, dir: BiDirectionalDict, user: CookiesUser):
    # 获取宽表关联的数见报表
    dmp_inffect_map = {}
    if not selected_tables:
        return dmp_inffect_map
    resp = await _request_dmp('/api/pubserver/get_relation_dashboard', {'table_names': selected_tables},
                              'post', user)
    if not resp.get('result'):
        raise ServerError(msg=f'数见接口异常:{resp.get("msg")}')
    dmp_exists_data = resp.get('data')
    if dmp_exists_data:
        for k, v in dmp_exists_data.items():
            const = dir.get_simple_const(k)
            k = const.get('name') if const else k
            dashboard_names = [item.get('name') for item in v if
                               item.get('id') not in selected_dashboards and item.get('name')]
            dmp_inffect_map[k] = ','.join(dashboard_names)
    return dmp_inffect_map


async def _get_dmp_refs_not_update(dashboard_ids, selected_tables, dir: BiDirectionalDict):
    # 获取数见报表关联的宽表模型
    model_ref_not_update = []
    if not dashboard_ids:
        return model_ref_not_update
    for dashboard_id in dashboard_ids:
        simple_const = dir.get_simple_const(dashboard_id)
        dashboard_name = dashboard_id
        if simple_const:
            dashboard_name = simple_const.get('name')
        tables = dir.get_values_by_key({'id': dashboard_id})
        if tables:
            not_selected_tables = []
            for table in tables:
                if table.get('table_name') not in selected_tables:
                    not_selected_tables.append(table.get('name'))
            if not_selected_tables:
                table_names = ','.join(list(set(not_selected_tables)))
                model_ref_not_update.append({'name': dashboard_name, 'impact_info': table_names})
    return model_ref_not_update


async def list_model_relation(db: CurrentSession, user: CookiesUser, params: ListModelRelationsParams):
    # 影响待更新内容、影响已有内容
    if not params.import_report_name:
        raise ServerError(msg='表名为空')
    detail: ImportDist = await get_import_detail(db, params.task_id)
    task_id = detail.rdc_task_id
    dap_task_id = detail.dap_import_id
    mapping: dict = await _get_dashboard_table_mapping(task_id, dap_task_id, user)
    dir = BiDirectionalDict(mapping.get('key_to_value'), mapping.get('value_to_key'))
    # 获取影响数见已有内容
    dmp_updated = dir.get_keys_by_value({'id': params.import_report_name})
    resp = await _request_dmp('/api/pubserver/get_relation_dashboard', {'table_names': [params.import_report_name]},
                              'post', user)
    if not resp.get('result'):
        raise ServerError(msg=f'数见接口异常:{resp.get("msg")}')
    dmp_exists_data = resp.get('data')
    # 获取数芯数据
    resp = await _request_dap('/api/common/publish_center/import/get_model_related_info',
                              {'import_report_id': params.import_report_id,
                               'data_service_import_report_ids': params.data_service_import_report_ids,
                               'task_id': dap_task_id}, 'post', user)
    if not resp.get('result'):
        raise ServerError(msg=f'数芯接口异常:{resp.get("msg")}')
    dap_data = resp.get('data')
    # 合并数芯数见数据
    updated = dap_data.get('update_content') or []
    arr = []
    for item in dmp_updated:
        data_name = f"{item.get('name')}"
        if data_name not in arr:
            updated.append(
                {'name': data_name, 'type': item.get('type'), 'is_chosen': item.get('id') in params.dashboard_ids,
                 'is_individuation': item.get('is_individuation')})
            arr.append(data_name)
    exists = dap_data.get('exist_content') or []
    arr = []
    if dmp_exists_data:
        refs = dmp_exists_data.get(params.import_report_name)
        if refs:
            for item in refs:
                data_name = f"{item.get('name')}_{item.get('type')}"
                if data_name not in arr:
                    exists.append({'name': item.get('name'), 'type': item.get('type')})
                    arr.append(data_name)
    return {'update_content': updated, 'exist_content': exists}


async def list_modified_reports(db: CurrentSession, user: CookiesUser, params: ListModifiedReportsParams):
    detail: ImportDist = await get_import_detail(db, params.task_id)
    task_id = detail.rdc_task_id
    dap_task_id = detail.dap_import_id
    dmp_data = defaultdict(dict)
    dap_data = defaultdict(dict)
    if detail.dmp_oss_url:
        dmp_data = await _get_dmp_model(task_id, user)
    if dap_task_id:
        dap_data = await _get_dap_model(dap_task_id, user)
    models, mapping = await _merge_dmp_model(dmp_data, dap_task_id, user)
    await _save_dashboard_table_mapping(task_id, mapping)
    models = _merge_dap_model(models, dap_data)
    return list(models.values())


cache_key = 'import_parse_result:{}:{}'


async def _get_dmp_model(task_id, user: CookiesUser = DependsJwtUser):
    dmp_cache_key = cache_key.format('dmp', task_id)
    cache_result = redis_client.get(dmp_cache_key)
    if cache_result:
        dmp_data = json.loads(cache_result)
    else:
        resp = await _request_dmp('/api/pubserver/parse_import_report', {'task_id': task_id}, 'get', user)
        if not resp.get('result'):
            raise ServerError(msg=f'查询数见报表信息失败:{resp.get("msg")}')
        dmp_data = resp.get('data')
        redis_client.setex(dmp_cache_key, 600, json.dumps(dmp_data, ensure_ascii=False))
    return dmp_data


async def _get_dap_model(task_id, user: CookiesUser = DependsJwtUser):
    # dmp_cache_key = cache_key.format('dap', task_id)
    # cache_result = await redis_client.get(dmp_cache_key)
    # if cache_result:
    #     dap_data = json.loads(cache_result)
    # else:
    #     resp = await _request_dap('/api/common/publish_center/import/get_import_info', {'task_id': task_id}, 'get',
    #                               user)
    #     if not resp.get('result'):
    #         raise ServerError(msg=f'查询数芯数据服务失败:{resp.get("msg")}')
    #     dap_data = resp.get('data')
    #     await redis_client.setex(dmp_cache_key, 600, json.dumps(dap_data, ensure_ascii=False))

    resp = await _request_dap('/api/common/publish_center/import/get_import_info', {'task_id': task_id}, 'get',
                              user)
    if not resp.get('result'):
        raise ServerError(msg=f'查询数芯数据服务失败:{resp.get("msg")}')
    dap_data = resp.get('data')
    return dap_data


def _merge_dap_model(models, dap_data):
    for item in dap_data:
        subject_id = item.get('subject_id')
        data = models.get(subject_id)
        if data:
            data.update(item)
        else:
            models[subject_id] = item
    return models


async def _merge_dmp_model(dmp_data, dap_task_id, user: CookiesUser = DependsJwtUser):
    table_name = []
    table_mapping = {}
    for k, v in dmp_data.items():
        data = v.get('dashboard')
        for item in data:
            models = item.get('models')
            if models:
                table_name += models

    table_name = list(set(table_name))
    resp = await _request_dap('/api/dimensional-modeling/publish_center/get_model_info',
                              {'table_names': table_name, 'task_id': dap_task_id},
                              'post', user)
    if not resp.get('result'):
        raise ServerError(msg=f'查询数芯宽表信息失败:{resp.get("msg")}')
    data = resp.get('data')
    for item in data:
        table_mapping[item.get('table_name')] = item
    mapping = BiDirectionalDict()
    for k, v in dmp_data.items():
        data = v.get('dashboard')
        for item in data:
            tables = item.get('models') or []
            table_models = []
            changed = False
            for table in tables:
                table_model = table_mapping.get(table)
                if table_model:
                    table_models.append(table_model)
                    if not changed:
                        changed = table_model.get('is_individuation')
            item['models'] = table_models
            item['is_individuation'] = changed
            for table in table_models:
                _save_as_directional(mapping, item, table)
    return dmp_data, mapping


async def _save_dashboard_table_mapping(task_id, dir: BiDirectionalDict):
    redis_client.setex(f'dashboard_table_mapping:{task_id}', 600, json.dumps(dir.__dict__, ensure_ascii=False))


async def _get_dashboard_table_mapping(task_id, dap_task_id, user: CookiesUser):
    dmp_data = await _get_dmp_model(task_id, user)
    mapping = BiDirectionalDict()
    if dap_task_id:
        models, mapping = await _merge_dmp_model(dmp_data, dap_task_id, user)
    await _save_dashboard_table_mapping(task_id, mapping)
    return mapping.__dict__


def _save_as_directional(dir: BiDirectionalDict, dashboard, table):
    key = {'id': dashboard.get('id'), 'name': dashboard.get('name'), 'type': dashboard.get('type'),
           'is_individuation': dashboard.get('is_individuation')}
    value = {'id': table.get('table_name'), 'table_name': table.get('table_name'), 'name': table.get('name'),
             'type': table.get('type'),
             'is_individuation': table.get('is_individuation')}
    dir.add(key, value)


async def _request_dmp(uri: str, params: dict, method, user: CookiesUser = DependsJwtUser):
    return await _request(uri, params, True, method, user)


async def _request_dap(uri: str, params: dict, method, user: CookiesUser = DependsJwtUser):
    return await _request(uri, params, False, method, user)


async def _request(uri: str, params: dict, is_dmp: bool, method: str, user: CookiesUser = DependsJwtUser):
    headers = await HTTPRequestUtil.build_common_access_headers(user)
    if is_dmp:
        host = get_server_domain('dmp')
    else:
        host = get_server_domain('dap')
    kwargs = {
        'url': f'{host}{uri}',
        'headers': headers,
    }
    if method.lower() == 'get':
        kwargs['params'] = params
    else:
        kwargs['json'] = params
    kwargs['timeout'] = 60
    begin = time()
    response = await HTTPRequestUtil.request(method, **kwargs)
    info = response.content.decode()
    app = '数见' if is_dmp else '数芯'
    log.error(f"[{time() - begin}s]查询{app}接口[{uri}]返回结果： {info}")
    if response.status_code != 200:
        raise ServerError(msg=f'HTTP请求{app}接口异常：{info}')
    rs = response.json()
    return rs


async def get_import_dependency(db: CurrentSession, user: CookiesUser, params: ImportSelectedParams):
    import_detail = await get_import_detail(db, params.task_id)
    if not import_detail:
        raise Exception(f"导入id不存在！")
    dap_dependency, dmp_dependency = {}, {}
    if import_detail.dap_import_id:
        dap_dependency = await dap.get_dap_import_dependency(user, dap_task_id=import_detail.dap_import_id,
                                                             update_apis=params.update_apis,
                                                             update_models=params.update_models)
    if import_detail.dmp_oss_url:
        dmp_dependency = await dmp.get_dmp_import_report_dependency(user, task_id=import_detail.rdc_task_id,
                                                                    report_id_list=params.update_dashboards)
    dashboard_items = dmp_dependency.get('items', [])
    return {
        'data_model': dap_dependency.get('data_model'),
        'data_api': dap_dependency.get('data_api'),
        'dashboard': {
            'items': dashboard_items,
            'count': len(dashboard_items)
        }
    }


async def get_import_mapping(db: CurrentSession, user: CookiesUser, params: ImportSelectedParams):
    import_detail = await get_import_detail(db, params.task_id)
    if not import_detail:
        raise Exception(f"导入id不存在！")
    dap_mapping, dmp_mapping = {}, {}
    if import_detail.dap_import_id:
        dap_mapping = await dap.get_dap_import_mapping(user, dap_task_id=import_detail.dap_import_id)
    if import_detail.dmp_oss_url:
        dmp_mapping = await dmp.get_dmp_import_mapping(user, task_id=import_detail.rdc_task_id,
                                                       report_id_list=params.update_dashboards)
    result = {
        'subject': {'from': [], 'to': []},
        'dashboard_datasource': dmp_mapping.get('dashboard_datasource'),
        'data_source': dap_mapping.get('data_source'),
        'application': dap_mapping.get('application'),
        'data_permission': dap_mapping.get('data_permission'),
    }
    from_id_list = set()
    to_id_list = set()
    for dap_from in dap_mapping.get('subject', {}).get('from', []) or []:
        if dap_from.get('subject_id') not in from_id_list:
            from_id_list.add(dap_from.get('subject_id'))
            result['subject']['from'].append(dap_from)

    for dmp_from in dmp_mapping.get('subject', {}).get('from', []) or []:
        if dmp_from.get('subject_id') not in from_id_list:
            from_id_list.add(dmp_from.get('subject_id'))
            result['subject']['from'].append(dmp_from)

    for dap_to in dap_mapping.get('subject', {}).get('to', []) or []:
        if dap_to.get('subject_id') not in to_id_list:
            to_id_list.add(dap_to.get('subject_id'))
            result['subject']['to'].append(dap_to)

    for dmp_to in dmp_mapping.get('subject', {}).get('to', []) or []:
        if dmp_to.get('subject_id') not in to_id_list:
            to_id_list.add(dmp_to.get('subject_id'))
            result['subject']['to'].append(dmp_to)
    return result


async def check_version_update(db: CurrentSession, user: CookiesUser, app_code: str, version: str, created_at_end: str):
    """
    正常状态的记录只能一条条更新
    """
    # return True

    page = PaginationParams(offset=0, limit=1000000)
    data = await get_imports(db, user, page, '', app_code=app_code, created_at_end=created_at_end)
    records = data.get('items', [])
    if not records:
        return True

    p_version = packaging_version.parse(version)
    for record in records:
        current_version = record.get('version') or ''
        current_status = record.get('status') or ''
        app_name = record.get('app_name') or ''
        if current_version and version != current_version:
            c_version = packaging_version.parse(current_version)
            # print(c_version, p_version, current_status)
            # if c_version < p_version and current_status == ImportStatus.not_import.value:
            #     raise Exception(f"产品【{app_name}】【{app_code}】存在之前未更新的版本【{c_version}】，请先执行这个更新！")
            if c_version < p_version and current_status == ImportStatus.running.value:
                raise Exception(f"产品【{app_name}】【{app_code}】的版本【{c_version}】正在更新，请稍等！")
            if c_version < p_version and current_status == ImportStatus.importError.value:
                raise Exception(
                    f"产品【{app_name}】【{app_code}】的版本【{c_version}】{ImportStatus.importError.value}，请先处理！")


async def check_upload_update(db: CurrentSession, user: CookiesUser):
    page = PaginationParams(offset=0, limit=1)
    data = await get_imports(db, user, page, '', status=ImportStatus.running.value)
    records = data.get('items', [])
    if not records:
        return True
    return False


async def check_update(
        db: CurrentSession,
        user: CookiesUser,
        params: TriggerDapImportParams
):
    import_record = await get_import_detail(db, params.task_id)
    if not import_record:
        raise Exception(f"导入记录不存在！")

    # if import_record.status not in [ImportStatus.not_import.value, ImportStatus.importError.value]:
    #     raise Exception(f"任务状态为：{import_record.status}, 拒绝更新！")

    result = {
        'update_info': {
            'can_update': True,
            'update_msg': '',
        },
        'merge_info': {
            'merge_msg': '',
            'can_merge': False,
            'merge_info': [],
        },
        'dap_import_id': ''
    }
    rdc_record = await get_rdc_detail(db, import_record.rdc_record_id)

    # 当导入的 package_type 是 定制包的时候 必须等其他在更新的更新完才能更新 且 暂时不支持合并更新
    if import_record.package_type == ImportPackageType.custom.value:
        can_update = await check_upload_update(db, user)
        result['update_info']['can_update'] = can_update
        return result

    # 1.检查版本是否可以更新
    # 更新中、更新失败直接阻止不能更新
    try:
        await check_version_update(db, user, rdc_record.app_code, rdc_record.version, rdc_record.created_at)
    except Exception as e:
        msg = str(e)
        result['update_info']['update_msg'] = msg
        result['update_info']['can_update'] = False
        return result

    result['dap_import_id'] = import_record.dap_import_id
    # 2. 检查是否可以合并跨版本
    merge_data = await check_merge_version(db, user, params)
    result['merge_info'] = merge_data
    return result


async def trigger_something_before_import(
        db: CurrentSession,
        user: CookiesUser,
        params: TriggerDapImportParams
):
    import_record = await get_import_detail(db, params.task_id)
    if not import_record:
        raise Exception(f"导入记录不存在！")
    if import_record.dap_import_id:
        return import_record.dap_import_id

    rdc_record = await get_rdc_detail(db, import_record.rdc_record_id)

    if import_record.package_type == ImportPackageType.custom.value:
        ret = await check_upload_update(db, user)
        if ret == False:
            raise Exception('请等待其他包更新完成')
    else:
        await check_version_update(db, user, rdc_record.app_code, rdc_record.version, rdc_record.created_at)

    if import_record.dmp_oss_url:
        # 1. 触发数见的包解析
        try:
            await TriggerDMPParserConsumer().process(
                {
                    'task_id': rdc_record.rdc_task_id, 'tenant_code': rdc_record.tenant_code,
                    'oss_url': import_record.dmp_oss_url, 'app_code': rdc_record.app_code
                }
            )
        except Exception as e:
            log.exception(e)
            raise Exception(f"触发数见解析包报错：{str(e)}")

    if import_record.dap_oss_url:
        # 2. 触发数芯的包导入
        dap_import_id = await trigger_dap_import(db, user, import_record, rdc_record, rdc_record.app_code)
        return dap_import_id
    return datetime.now().timestamp() * 1000


async def get_import_dists_since_last_success(db: CurrentSession, user: CookiesUser, curr_task_id):
    """
    获取距离上次成功的记录到当前记录的所有记录
    """
    curr_import_record = await get_import_detail(db, curr_task_id)
    curr_rdc_record = await get_rdc_detail(db, curr_import_record.rdc_record_id)

    # 上次更新成功的信息
    last_success = db.query(ImportDist).join(
        RDC, RDC.id == ImportDist.rdc_record_id,
    ).filter(
        ImportDist.status == ImportStatus.importDone.value,
        RDC.tenant_code == user.tenant_code,
        RDC.app_code == curr_rdc_record.app_code,
    ).order_by(ImportDist.created_at.desc()).limit(1).first()

    records_query = db.query(
        ImportDist,
    ).add_columns(
        RDC.app_code.label("app_code"),
        RDC.app_name.label("app_name"),
        RDC.version.label("version"),
        RDC.tenant_code.label("tenant_code")
    ).join(
        RDC, RDC.id == ImportDist.rdc_record_id,
    ).filter(
        RDC.tenant_code == user.tenant_code,
        RDC.app_code == curr_rdc_record.app_code,
    ).order_by(ImportDist.created_at.desc())
    if last_success:
        # 最近的一次更新成功
        records_query = records_query.filter(
            ImportDist.created_at >= last_success.created_at
        ).filter(
            ImportDist.created_at <= curr_import_record.created_at
        )
    else:
        log.error(f"当前还没有更新成功过，查询出所有的租户推包记录")
        # 从来没有更新过，查询出所有
        pass
    datas = records_query.all()
    result = []
    for data in datas:
        one = data[0].to_dict()
        one.update({
            'app_code': data.app_code,
            'app_name': data.app_name,
            'version': data.version,
            'tenant_code': data.tenant_code,
        })
        result.append(one)
    return result


async def check_merge_version(
        db: CurrentSession,
        user: CookiesUser,
        params: TriggerDapImportParams
):
    """
    检查是否需要跨版本合并
    """
    data = {
        'merge_msg': '',
        'can_merge': False,
        'merge_info': [],
    }
    # 检查是否需要合并版本（是否已经合并过）
    detail_log = get_curr_merge_data(db, curr_task_id=params.task_id)
    if detail_log and detail_log.status == '成功':
        data['merge_msg'] = '之前已经合并过！'
        return data
    records = await get_import_dists_since_last_success(db, user, curr_task_id=params.task_id)
    all_status = [r.get('status') for r in records]
    merge_records = get_merge_versions(all_status, records)
    if not merge_records:
        return data
    if merge_records:
        data['merge_msg'] = '获取可以合并的版本成功！'
        data['can_merge'] = True
        data['merge_info'] = [
            {
                'app_name': r.get('app_name'),
                'app_code': r.get('app_code'),
                'version': r.get('version'),
            } for r in merge_records
        ]
    return data


def get_merge_versions(status_list, records):
    # 从新到旧排列
    records = sorted(records, key=lambda x: x.get('created_at'), reverse=True)
    if ImportStatus.importDone.value not in status_list:
        merge_records = records
    else:
        succ_idx = status_list.index(ImportStatus.importDone.value)
        merge_records = records[:succ_idx]
    return merge_records


async def merge_version(
        db: CurrentSession,
        user: CookiesUser,
        params: TriggerDapImportParams
):
    """
    跨版本合并
    """
    records = await get_import_dists_since_last_success(db, user, curr_task_id=params.task_id)
    all_status = [r.get('status') for r in records]
    merge_records = get_merge_versions(all_status, records)
    if not merge_records:
        return False, '没有需要合并版本'

    app_code = records[0].get('app_code') or ''
    await merge_package_versions_since_last_updated(db, user, params.task_id, app_code, merge_records)
    return True, '合并完成'


def get_curr_merge_data(db, curr_task_id):
    return db.query(ImportDistDetail).filter(
        ImportDistDetail.import_id == curr_task_id,
        ImportDistDetail.source == MERGE_SOURCE_TEXT,
    ).order_by(ImportDistDetail.created_at.desc()).limit(1).first()


async def merge_package_versions_since_last_updated(db, user, curr_task_id, app_code, merge_records):
    """
    合并版本，从上一次的更新成功算起，之间所有版本的包
    """

    # 1. 查出所有的需要合并的版本
    log.error(f"开始处理跨版本的包合并")

    ### 加载合并版本日志
    detail_log = get_curr_merge_data(db, curr_task_id)
    # if detail_log and detail_log.status == '成功':
    #     raise Exception('之前已经合并过！')
    if not detail_log:
        detail_log_id = await add_one_import_detail(db, **{
            'import_id': curr_task_id,
            'status': '初始化',
            'source': MERGE_SOURCE_TEXT
        })
        merge_state = TotalState(name=f'合并版本包: {user.tenant_code}-{app_code}')
    else:
        detail_log_id = detail_log.id
        try:
            merge_state = TotalState(**json.loads(detail_log.log))
        except Exception as e:
            merge_state = TotalState(name=f'合并版本包: {user.tenant_code}-{app_code}')
    ### 加载合并版本日志

    merge_records = sorted(merge_records, key=lambda x: x.get('created_at'))
    root_dst = os.path.join('/tmp', f'merge_{uuid4_str()}')
    os.makedirs(root_dst, exist_ok=True)
    dmp_dst = os.path.join(root_dst, 'dmp_dst')
    dap_dst = os.path.join(root_dst, 'dap_dst')
    os.makedirs(dmp_dst, exist_ok=True)
    os.makedirs(dap_dst, exist_ok=True)
    try:
        merge_result = {}
        if merge_records:
            version_desc = f'{merge_records[0].get("version")}_to_{merge_records[-1].get("version")}'.replace('.', '_')
        else:
            version_desc = 'version_null'
        need_skip_ids = []
        curr_import_record = await get_import_detail(db, curr_task_id)
        curr_rdc_record = await get_rdc_detail(db, curr_import_record.rdc_record_id)
        merge_target_model = MergeVersionSourceLogDetail(**curr_rdc_record.to_dict())
        for record in merge_records:
            import_dist_id = record.get('id')
            status = record.get('status')
            version = record.get('version')
            app_name = record.get('app_name')
            dmp_oss_url = record.get('dmp_oss_url')
            dap_oss_url = record.get('dap_oss_url')
            created_at = record.get('created_at')
            merge_source_model = MergeVersionSourceLogDetail(**record)
            with TaskContextManager(f'处理合并', f'{app_name}_{version}', total_state=merge_state) as f:
                if status not in [
                    ImportStatus.init.value,
                    # ImportStatus.importError.value, # 设计是未更新的必须要更新成功，才能进行下一步
                    ImportStatus.not_import.value
                ]:
                    msg = f'【{import_dist_id}】【{app_name}】【{version}】【{status}】跳过合并'
                    log.error(msg), f.record(msg)
                    continue
                msg = f"准备处理【{import_dist_id}{app_name}】【{version}】【{created_at}】"
                log.error(msg), f.record(msg)
                if dmp_oss_url:
                    download_and_copy_to_dst(dmp_oss_url, dmp_dst, root_dst)
                    msg = f"【{import_dist_id}】【{app_name}】【{version}】【{status}】数见包下载合并完成"
                    log.error(msg), f.record(msg)
                    merge_result['dmp'] = True
                if dap_oss_url:
                    download_and_copy_to_dst(dap_oss_url, dap_dst, root_dst)
                    msg = f"【{import_dist_id}】【{app_name}】【{version}】【{status}】数芯包下载合并完成"
                    log.error(msg), f.record(msg)
                    merge_result['dap'] = True
                need_skip_ids.append(import_dist_id)
                # 记录当前的推包记录库合并到了哪一个版本
                merge_log = MergeVersionSourceLog(source=merge_source_model, target=merge_target_model)
                await add_one_import_detail(db, **{
                    'import_id': import_dist_id,
                    'status': '成功',
                    'source': MERGE_PART,
                    'log': merge_log.model_dump_json(),
                })
        # 准备整体打包
        root = f'{app_code}/{version_desc}'
        final_dmp_oss_url = ''
        final_dap_oss_url = ''
        with TaskContextManager(f'合并版本的最后处理', f'{app_code}', total_state=merge_state) as f:
            if merge_result.get('dmp'):
                filename = f'{user.tenant_code}_dmp_merge.zip'
                fp = utils.zip_folder_v1(dmp_dst, zip_name=filename, zip_folder=root_dst)
                final_dmp_oss_url = await utils.upload_oss(root, fp, filename)
                msg = f"数见合并的文件： {final_dmp_oss_url}"
                log.error(msg), f.record(msg)
            if merge_result.get('dap'):
                filename = f'{user.tenant_code}_dap_merge.zip'
                fp = utils.zip_folder_v1(dap_dst, zip_name=filename, zip_folder=root_dst)
                final_dap_oss_url = await utils.upload_oss(root, fp, filename)
                msg = f"数芯合并的文件： {final_dap_oss_url}"
                log.error(msg), f.record(msg)

        # 更改合并的状态
        db.query(ImportDistDetail).filter(ImportDistDetail.id == detail_log_id).update(
            {'status': '成功', 'log': merge_state.model_dump_json(), 'source': MERGE_SOURCE_TEXT}
        )
        if need_skip_ids:
            # 更新已经合并的合并的任务状态为已跳过
            db.query(ImportDist).filter(ImportDist.id.in_(need_skip_ids)).update({'status': ImportStatus.skip.value})
            # 当前的任务置为未更新, 同时更新oss地址
            update_data = {
                'status': ImportStatus.not_import.value,
                'dap_import_id': '',  # 数芯的导入id置为空
            }
            if final_dmp_oss_url:
                update_data['dmp_oss_url'] = final_dmp_oss_url
            if final_dap_oss_url:
                update_data['dap_oss_url'] = final_dap_oss_url
            db.query(ImportDist).filter(ImportDist.id == curr_task_id).update(update_data)
        db.commit()
    finally:
        shutil.rmtree(root_dst, ignore_errors=True)
    log.error(f"结束处理跨版本的包合并")


def download_and_copy_to_dst(oss_url, dst, root_dst):
    """
    将每个oss压缩包下载解压，不用将文件读取到内存
    """
    tmp_folder = os.path.join(root_dst, f"part_{uuid4_str()}")
    tmp_ex_folder = os.path.join(root_dst, f"part_ex_{uuid4_str()}")
    os.makedirs(tmp_folder, exist_ok=True)
    os.makedirs(tmp_ex_folder, exist_ok=True)

    file_path, full_path = HTTPRequestUtil.download_oss_file_v3(tmp_folder, oss_url)
    utils.unzip_files_to_folder(full_path, tmp_ex_folder)
    # 遍历源文件夹中的所有文件和子文件夹
    for item in os.listdir(tmp_ex_folder):
        src_item = os.path.join(tmp_ex_folder, item)
        dst_item = os.path.join(dst, item)
        # 移动文件或文件夹
        shutil.move(src_item, dst_item)

    shutil.rmtree(tmp_folder, ignore_errors=True)
    shutil.rmtree(tmp_ex_folder, ignore_errors=True)


async def trigger_dap_import(
        db: CurrentSession,
        user: CookiesUser,
        import_record,
        rdc_record,
        app_code
):
    """
    触发数见包导入，得到导入id
    """
    oss_url = import_record.dap_oss_url
    headers = await HTTPRequestUtil.build_common_access_headers(user)
    dap_domain = get_server_domain('dap')
    rdc_data = rdc_record.to_dict()
    rdc_data.pop('log', None)
    url = unquote(oss_url)
    url = url.split('?')[0]
    url = OSSFileProxy().get_sigh_url(key=url, is_url=True, **{'sign_outer_url': False})
    data = {
        "file_name": get_filename(oss_url),
        "file_url": url,
        "app_code": app_code,
        'name': f'{rdc_record.app_name} 版本:{rdc_record.version}',
        "description": f"发布中心更新: {json.dumps(rdc_data, ensure_ascii=False)}"
    }
    kwargs = {
        'url': f'{dap_domain}/api/common/publish_center/import/customize_task',
        'headers': headers,
        'json': data
    }
    response = await HTTPRequestUtil.request('post', **kwargs)
    log.error(f"RDC导入数芯的任务返回： {response.content.decode()}")
    data = response.json()
    dap_import_id = data.get('data', '')
    if not data.get('result') or not dap_import_id:
        raise Exception(
            f"数芯提交导入任务失败：{response.content.decode()}, curl: {HTTPRequestUtil.httpx_request_to_curl(response.request)}"
        )
    await update_one_import_record(db, import_record.id, {'dap_import_id': dap_import_id})
    return dap_import_id


async def import_save(
        db: CurrentSession,
        user: CookiesUser,
        file_oss_url: str,
        file_name: str,
        remark: str
):
    folder = ''
    try:
        parse_srv = ParseIntegra(file_oss_url)
        parse_srv.check_version()
        model_id = uuid4_str()
        folder, full_path = parse_srv.download_file(model_id)
        unzip_path = parse_srv.unzip(folder, full_path)
        base_info = parse_srv.get_base_info(unzip_path)
        dmp_zip_url, dap_zip_url = await parse_srv.rezip(unzip_path)

        # 走5.5导入逻辑
        params = RdcParams(
            appCode='',
            appKey='',
            appName=file_name if file_name else "定制包导入",
            appType='',
            businessMode='',
            customerGuid='',
            envCode=base_info['env_code'],
            operator=user.user_name,
            taskId=model_id,
            tenantCode=user.tenant_code,
            tenantType=RDCTenantType.tenant.value,
            upgradeScene='UPDATE',
            version=base_info['version'],
        )

        rdc_record_id = await add_one_rdc_record(db, params, file_oss_url)
        import_data = {
            'rdc_record_id': rdc_record_id,
            'rdc_task_id': params.taskId,
            'tenant_code': params.tenantCode,
            'status': ImportStatus.not_import.value,
            'dmp_oss_url': dmp_zip_url,
            'dap_oss_url': dap_zip_url,
            'dap_import_id': '',
            'tenant_type': RDCTenantType.tenant.value,
            'space_type': TenantType.custom.value,
            'package_type': ImportPackageType.custom.value
        }
        await add_one_import_record(db, **import_data)

    finally:
        if folder:
            shutil.rmtree(folder)
    return model_id


async def import_reset(
        db: CurrentSession,
        user: CookiesUser,
        import_id: str
):
    """
    导入重置
    """
    # 导入数据信息
    detail: ImportDist = await get_import_detail(db, import_id)
    if not detail:
        raise Exception(f"导入id不存在！")
    rdc = await get_rdc_detail(db, detail.rdc_record_id)
    if rdc:
        rdc_data = rdc.to_dict()
    else:
        raise Exception(f"rdc数据异常")
    # 导入状态必须成功
    if detail.status != ImportStatus.importDone.value:
        raise Exception(f"导入状态异常，无法重置")
    # 触发重置
    # 下载备份包
    if not detail.backup_oss_url:
        raise Exception('没有备份文件无法重置')

    folder = ''
    oss_url = parse.unquote(detail.backup_oss_url).split('?')[0]
    try:
        parse_srv = ParseIntegra(oss_url)
        parse_srv.check_version()
        model_id = uuid4_str()
        folder, full_path = parse_srv.download_file(model_id)
        unzip_path = parse_srv.unzip(folder, full_path)
        base_info = parse_srv.get_base_info(unzip_path)
        dmp_zip_url, dap_zip_url = await parse_srv.rezip(unzip_path)

        app_name = rdc_data.get('app_name')

        # 走5.5导入逻辑
        params = RdcParams(
            appCode='',
            appKey='',
            appName=f'【{app_name}】数据包还原',
            appType='',
            businessMode='',
            customerGuid='',
            envCode=base_info['env_code'],
            operator=user.user_name,
            taskId=model_id,
            tenantCode=user.tenant_code,
            tenantType=RDCTenantType.tenant.value,
            upgradeScene='UPDATE',
            version=base_info['version'],
        )

        rdc_record_id = await add_one_rdc_record(db, params, oss_url)
        import_data = {
            'rdc_record_id': rdc_record_id,
            'rdc_task_id': params.taskId,
            'tenant_code': params.tenantCode,
            'status': ImportStatus.not_import.value,
            'dmp_oss_url': dmp_zip_url,
            'dap_oss_url': dap_zip_url,
            'dap_import_id': '',
            'tenant_type': RDCTenantType.tenant.value,
            'space_type': TenantType.custom.value,
            'package_type': ImportPackageType.custom.value
        }
        await add_one_import_record(db, **import_data)

    finally:
        if folder:
            shutil.rmtree(folder)
    return model_id
