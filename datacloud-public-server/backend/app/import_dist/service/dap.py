from backend.common.domain import get_server_domain
from backend.common.jwt import CookiesUser
from backend.common.request import HTTPRequestUtil


async def get_dap_package_content(tenant_code: str, task_id: str):
    """
    解析dmp包内容
    """
    user = CookiesUser(tenant_code=tenant_code)
    headers = await HTTPRequestUtil.build_common_access_headers(user)
    dap_domain = get_server_domain('dap')
    kwargs = {
        'url': f'{dap_domain}/api/pubserver/parse_import_zip_file',
        'headers': headers,
        'params': {
            'task_id': task_id
        }
    }
    response = await HTTPRequestUtil.request('get', **kwargs)
    result = response.json()
    if result.get('result'):
        return result.get('data')
    raise Exception(f'解析dmp包内容失败:{result.get("msg")}｝')


async def get_dap_import_dependency(user: CookiesUser, dap_task_id: str, update_apis: list[str],
                                    update_models: list[str]):
    headers = await HTTPRequestUtil.build_common_access_headers(user)
    dap_domain = get_server_domain('dap')
    kwargs = {
        'url': f'{dap_domain}/api/common/publish_center/import/get_import_dependency',
        'headers': headers,
        'json': {
            'task_id': dap_task_id,
            'update_apis': update_apis,
            'update_models': update_models
        }
    }
    response = await HTTPRequestUtil.request('post', **kwargs)
    result = response.json()
    if result.get('result'):
        return result.get('data')
    raise Exception(f'请求dap失败:{result.get("msg")}｝')


async def get_dap_import_mapping(user: CookiesUser, dap_task_id: str):
    headers = await HTTPRequestUtil.build_common_access_headers(user)
    dap_domain = get_server_domain('dap')
    kwargs = {
        'url': f'{dap_domain}/api/common/publish_center/import/task/mapping',
        'headers': headers,
        'params': {
            'id': dap_task_id
        }
    }
    response = await HTTPRequestUtil.request('get', **kwargs)
    result = response.json()
    if result.get('result'):
        return result.get('data')
    raise Exception(f'请求dap失败:{result.get("msg")}｝')
