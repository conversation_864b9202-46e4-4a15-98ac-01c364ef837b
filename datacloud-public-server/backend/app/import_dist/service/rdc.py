# #!/usr/bin/env python3
# # -*- coding: utf-8 -*-
import base64
import json
import os
import shutil
from fastapi import APIRouter, Query, UploadFile, File, Form

from pydantic import BaseModel
from backend.app.import_dist.crud.rdc import add_one_rdc_record, add_one_import_record, update_one_rdc_record,\
    get_completed_record_appcodes, get_completed_record_version
from backend.app.import_dist.schema.import_dist import RdcParams
from backend.common.domain import get_server_domain
from backend.common.enums import RDCTenantType, ImportStatus, TenantType
from backend.common.jwt import CookiesUser
from backend.common.log import log
from backend.common.oss.oss import OSSFileProxy
from backend.common.request import HTTPRequestUtil
from backend.common.task_context import TotalState, TaskContextManager
from backend.common.utils import unzip_files_to_folder, get_file_md5, encode_file_to_base64, encode_file_to_base64_v2
from backend.consumers.tasks.trigger_dmp_parse import Trigger<PERSON>PParserConsumer
from backend.consumers.tasks.trigger_dmp_template_parse import TriggerDMPTemplateParserConsumer
from backend.core.conf import settings
from backend.database.db_util import uuid4_str, CurrentSession


async def file_bytes_to_zip_file(file: UploadFile, zip_full_path: str):
    """
    RDC POST的file内容写到本地文件
    """
    with open(zip_full_path, "wb") as buffer:
        # 逐块读取并写入文件
        shutil.copyfileobj(file.file, buffer)


async def upload_oss(file_path: str) -> str:
    """
    将任务的文件上传到oss
    """
    filename = get_file_md5(file_path) + '.zip'  # 真正oss的存储名字使用md5值，解决文件多份重复问题
    root = settings.OSS_PUB_SERVER_BASE_FOLDER_NAME + '/rdc'
    with open(file_path, 'rb') as fp:
        oss = OSSFileProxy()
        oss_file_url = oss.upload(fp, file_name=filename, root=root, key=f'{root}/{filename}', return_intranet_url=True)
        sigh_url = oss.get_sigh_url(oss_file_url, 604800, is_url=True)
        return sigh_url


class RDCResponse(BaseModel):
    dmp: dict = {}
    dap: dict = {}
    data: dict = {}


class RDCDispatcher:
    """
    处理rdc的推包动作
    """

    def __init__(self, db: CurrentSession, params: RdcParams):
        self.db = db
        self.params = params
        self.result: RDCResponse = RDCResponse()
        self.total_state = TotalState(name=f'RDC推包: {params.tenantType}-{params.tenantCode or ""}')
        # 临时文件以及目录处理
        self.special_id = uuid4_str()
        self.tmp_folder = f'/tmp/rdc/{self.special_id}'
        self.rdc_zip_path = f'/tmp/rdc/{self.special_id}/{self.special_id}.zip'
        self.__init_zip_file_path()

    def __init_zip_file_path(self):
        os.makedirs(self.tmp_folder, exist_ok=True)

    async def decompress_rdc_package(self):
        """
        解压上传rdc的包
        """
        with TaskContextManager(
                '解压RDC包', '解压数芯、数见包', total_state=self.total_state, will_raise=True
        ):
            dmp_zip_path, dap_zip_path = decompress_production(self.rdc_zip_path, tmp_folder=self.tmp_folder)
        with TaskContextManager('上传OSS', '上传解压的数芯、数见包到oss', total_state=self.total_state, will_raise=True):
            dmp_zip_url = await upload_oss(dmp_zip_path)
            dap_zip_url = await upload_oss(dap_zip_path)
            return dmp_zip_url, dap_zip_url

    async def dispatch(self, file: UploadFile):
        """
        推包主流程
        """
        rdc_record_id = ''
        params = self.params
        params_log_data = params.model_dump()
        params_log_data.pop('file_byte', None)
        log.error(f"RDC request params: {params_log_data}")

        try:
            log.error(f"RDC task_id: {params.taskId}, 临时目录： {self.tmp_folder}")

            # 1. rdc的zip包保存到本地
            await file_bytes_to_zip_file(file, self.rdc_zip_path)
            oss_file_url = await upload_oss(self.rdc_zip_path)

            # 2. 将任务信息保存到数据库记录下来
            rdc_record_id = await add_one_rdc_record(self.db, params, oss_file_url)

            # TODO DEBUG
            # params.tenantType = RDCTenantType.template.value
            # params.tenantCode = 'sprint_code_bl'
            # params.tenantCode = 'ompqjtcszhc'
            # params.upgradeScene = 'UPDATE'

            # 3. 解压数见数芯的包，并上传oss
            dmp_zip_url, dap_zip_url = await self.decompress_rdc_package()

            # 3. 处理不同文件类型
            log.error(f'{"-" * 10} 开始处理{params.tenantType}-{params.tenantCode or ""} {"-" * 10}')

            if params.tenantType == RDCTenantType.public.value:
                log.error(f"公共库不处理")
            elif params.tenantType == RDCTenantType.tenant.value:
                await self.dispatch_tenant_package(rdc_record_id, dmp_zip_url, dap_zip_url)
            elif params.tenantType == RDCTenantType.template.value:
                await self.dispatch_template_package(rdc_record_id, dmp_zip_url, dap_zip_url)
            else:
                raise Exception(f"未知的tenantType： {params.tenantType}")

            log.error(f'{"-" * 10} 结束处理{params.tenantType}-{params.tenantCode or ""} {"-" * 10}')

            return self.result.model_dump()
        finally:
            # 删除临时目录
            shutil.rmtree(self.tmp_folder, ignore_errors=True)
            log.error(f"删除临时目录： {self.tmp_folder}")
            # 更新推包日志
            if rdc_record_id:
                await update_one_rdc_record(
                    self.db, rdc_record_id, {'log': json.dumps(self.total_state.model_dump(), ensure_ascii=False)}
                )
                log.error(f"完成保存rdc表<{rdc_record_id}>的推送日志")

    async def dispatch_template_package(
            self,
            rdc_record_id,
            dmp_zip_url, dap_zip_url
    ):
        """
        处理模板库的文件
        """
        log.error(f"来自模版库的文件")
        # 1. 数见推包
        dmp_rs = await push_package_to_dmp(self.total_state, dmp_zip_url, params=self.params)
        if not dmp_rs.get('result'):
            raise Exception(f"数见推送模板库失败：{dmp_rs}")
        # 触发数见全量包解析
        log.error(f"数见模板包推送到dmp进行解析")
        await TriggerDMPTemplateParserConsumer().push(
            **{
                'task_id': self.params.taskId, 'tenant_code': self.params.tenantCode, 'oss_url': dmp_zip_url,
                'app_code': self.params.appCode
            }
        )
        # 2. 数芯推包
        dap_rs = await push_package_to_dap_public_space(self.total_state, dap_zip_url, params=self.params)
        if not dap_rs.get('result'):
            raise Exception(f"数芯推送模板库失败：{dap_rs}")

        # TODO 暂时不考虑模板库的安装结果
        # 直接置为成功
        # 添加导入记录
        if dmp_rs.get('result') and dap_rs.get('result'):
            status = ImportStatus.importDone.value
        else:
            status = ImportStatus.importError.value
        import_data = {
            'rdc_record_id': rdc_record_id,
            'rdc_task_id': self.params.taskId,
            'tenant_code': self.params.tenantCode,
            'dmp_oss_url': dmp_zip_url,
            'dap_oss_url': dap_zip_url,
            'dap_import_id': dap_rs.get('data', ''),
            'status': status,
            'tenant_type': RDCTenantType.template.value,

        }
        await add_one_import_record(self.db, **import_data)
        self.result.dmp = dmp_rs
        self.result.dap = dap_rs

    async def dispatch_tenant_package(self, rdc_record_id, dmp_zip_url, dap_zip_url):
        """
        处理租户库的文件
        """
        log.error(f"来自租户库<{self.params.tenantCode}>的文件")

        # 1. 判断是否是定制空间
        category = await dap_tenant_is_custom(self.total_state, self.params)
        if category == 2:
            # 公共空间
            await self.push_package_to_public(rdc_record_id, dmp_zip_url, dap_zip_url)
        elif category == 1:
            # 定制空间
            await self.push_package_to_custom(rdc_record_id, dmp_zip_url, dap_zip_url)
        else:
            # 未知的类型
            log.error(f"未知的租户类型: {category}")

    async def push_package_to_public(self, rdc_record_id, dmp_zip_url, dap_zip_url):
        # 租户库的包推到公共空间
        # 2. 数见的包推送到RDC, 数芯的不处理
        log.error(f"是公共空间，开始处理")
        # 1. 数见推包
        dmp_rs = await push_package_to_dmp(self.total_state, dmp_zip_url, params=self.params)
        if not dmp_rs.get('result'):
            log.error(f"数见推送租户库-公共空间包失败：{dmp_rs}")
            raise Exception(f"数见推送租户库-公共空间包失败：{dmp_rs}")
        # 2. 数见推包
        with TaskContextManager(
                f'租户库{self.params.tenantCode}公共空间推包', '推送到数芯', total_state=self.total_state,
                will_raise=True
        ) as f:
            log.info(f"公共空间忽略推包")
            f.record('公共空间忽略推包')

        # 添加导入记录
        if dmp_rs.get('result'):
            status = ImportStatus.importDone.value
        else:
            status = ImportStatus.importError.value
        import_data = {
            'rdc_record_id': rdc_record_id,
            'rdc_task_id': self.params.taskId,
            'tenant_code': self.params.tenantCode,
            'dmp_oss_url': dmp_zip_url,
            'dap_oss_url': dap_zip_url,
            'dap_import_id': '',
            'status': status,
            'tenant_type': RDCTenantType.tenant.value,
            'space_type': TenantType.public.value,
        }
        await add_one_import_record(self.db, **import_data)

        # TODO 待处理数芯的公共空间包的日志
        self.result.dmp = dmp_rs
        self.result.dap = {'msg': '租户属于公共空间忽略推包'}

    async def get_custom_is_contain_app_for_install(self):
        """
        查询数据库dap_bi_project_create_record表，判断租户是否包含这个产品
        """
        log.info(f"判断是否是租户是否有包含产品: {self.params.appCode}")

        # 查询是否已经安装过该应用
        app_codes = await get_completed_record_appcodes(self.db, self.params.tenantCode)
        log.info(f"当前租户已安装的产品: {app_codes}")
        if not app_codes:
            return False

        if self.params.appCode in app_codes:
            log.info(f"租户[{self.params.tenantCode}]已经安装成功过下列产品：{app_codes}")
            return True
        return False

    async def get_completed_record_version_for_install(self):
        """
        查询数据库dap_bi_project_create_record表，判断租户是否包含这个产品
        """

        app_versions = await get_completed_record_version(self.db, self.params.tenantCode, self.params.appCode)
        log.info("判断租户是否包含这个产品.app_versions:%s" % app_versions)
        # 对应的应用没有一条无对应的版本，不需要更新
        if not app_versions:
            return False, False
        # 对应的应用版本中，存在当前的版本，也不需要更新，跳过处理
        # todo　考虑版本合并时如何处理
        log.info("对应的应用版本中，存在当前的版本.self.params.version:%s" % self.params.version)
        if self.params.version in app_versions:
            return False, True
        # 有对应的更新记录，且版本不一致的情况下，才需要推送更新记录
        return True, False

    async def push_package_to_custom(self, rdc_record_id, dmp_zip_url, dap_zip_url):
        """
        租户库的包推到定制空间
        """
        log.error(f"是定制空间，开始处理")

        if self.params.upgradeScene == 'INSTALL':
            # 原本初装是不需要处理的，但综合业务升级场景中，如果从23年930升级到v5.5版本时。
            # 不卸载应用，而是重新安装应用，这种常见需要处理
            # 查询是否已经安装过该应用
            contain_app = await self.get_custom_is_contain_app_for_install()
            if contain_app:
                # 安装成功过这个产品，如果有更新，需要进行版本比较
                need_update, same_version = await self.get_completed_record_version_for_install()
                # 存在相同的版本，不进行更新
                if same_version:
                    self.result.data = {'msg': f"租户：{self.params.tenantCode}已在更新中心创建相同版本，本次忽略"}
                    return
                # 进行更新
                if need_update:
                    # 添加导入记录
                    import_data = {
                        'rdc_record_id': rdc_record_id,
                        'rdc_task_id': self.params.taskId,
                        'tenant_code': self.params.tenantCode,
                        'status': ImportStatus.init.value,
                        'dmp_oss_url': dmp_zip_url,
                        'dap_oss_url': dap_zip_url,
                        'dap_import_id': '',
                        'tenant_type': RDCTenantType.tenant.value,
                        'space_type': TenantType.custom.value,
                    }
                    await add_one_import_record(self.db, **import_data)
                    self.result.data = {'msg': f"租户：{self.params.tenantCode}已经成功在更新中心创建更新记录"}
                    return

            # 初装, 不处理(默认情况下)
            log.error(f"定制空间初装，不处理")
            with TaskContextManager(
                    f'租户库{self.params.tenantCode}定制空间推包', '推包',
                    total_state=self.total_state, will_raise=True
            ) as f:
                f.record('定制空间初装，不处理')
            self.result.data = {'msg': '定制空间初装，不处理'}
        elif self.params.upgradeScene == 'UPDATE':
            # contain_app = await get_custom_is_contain_app(self.total_state, self.params)
            contain_app = True
            if contain_app:
                log.error(f"租户[{self.params.tenantCode}]有这个产品：{self.params.appCode}")
                # 得到数芯导入的taskid
                # dap_import_id = await get_dap_import_id_from_custom_space(total_state, params, dap_zip_url)
                # 触发数见的包解析
                # log.error(f"定制空间数见包推送到dmp进行解析")
                # await TriggerDMPParserConsumer().push(
                #     **{
                #         'task_id': params.taskId, 'tenant_code': params.tenantCode,
                #         'oss_url': dmp_zip_url, 'app_code': params.appCode
                #     }
                # )
                # 添加导入记录
                import_data = {
                    'rdc_record_id': rdc_record_id,
                    'rdc_task_id': self.params.taskId,
                    'tenant_code': self.params.tenantCode,
                    'status': ImportStatus.init.value,
                    'dmp_oss_url': dmp_zip_url,
                    'dap_oss_url': dap_zip_url,
                    'dap_import_id': '',
                    'tenant_type': RDCTenantType.tenant.value,
                    'space_type': TenantType.custom.value,
                }
                await add_one_import_record(self.db, **import_data)
                self.result.data = {'msg': f"租户：{self.params.tenantCode}已经成功在更新中心创建更新记录"}
            else:
                log.error(f"租户[{self.params.tenantCode}]没有这个产品：{self.params.appCode}")
                self.result.data = {'msg': f"租户不包含产品{self.params.appCode}，不会更新"}
        else:
            with TaskContextManager(
                    f'租户库{self.params.tenantCode}定制空间推包',
                    f"未知类型",
                    total_state=self.total_state, will_raise=True
            ) as f:
                log.error(f"未知的upgradeScene类型: {self.params.upgradeScene}")
                f.record(f"未知的upgradeScene类型: {self.params.upgradeScene}")
            self.result.data = {'msg': f"未知的upgradeScene类型: {self.params.upgradeScene}"}


def decompress_production(zip_full_path: str, tmp_folder: str):
    """
    无论是模板还是租户库的推包，解压出数见数见的产品包
    """
    # 解压
    unzip_files_to_folder(zip_full_path, tmp_folder)
    files = os.listdir(tmp_folder)
    if 'dmp.zip' not in files:
        raise Exception(f'压缩包缺少dmp.zip')
    if 'bigdata.zip' not in files:
        raise Exception(f'压缩包缺少bigdata.zip')
    dmp_zip_path = os.path.join(tmp_folder, 'dmp.zip')
    dap_zip_path = os.path.join(tmp_folder, 'bigdata.zip')
    return dmp_zip_path, dap_zip_path


async def push_package_to_dmp(total_state, oss_url, params: RdcParams):
    """
    将包推送给数见
    """
    if params.tenantType == RDCTenantType.template.value:
        level2_name = '推送模板包'
    else:
        level2_name = f'推送租户包: {params.tenantCode}'
    with TaskContextManager(
            f'推包到数见', level2_name,
            total_state=total_state, will_raise=True
    ) as f:
        # oss_url = await upload_oss(zip_path)
        # dmp_zip_data = encode_file_to_base64_v2(zip_path)
        data = params.model_dump()
        data['force_update'] = True  # 数见租户库的包强制更新
        data['oss_url'] = oss_url
        data.pop('file_byte', None)
        dmp_admin_domain = get_server_domain('dmp_admin')
        headers = await HTTPRequestUtil.build_rdc_access_headers({'tenant': params.tenantCode})
        kwargs = {
            'url': f'{dmp_admin_domain}/api/publish_center/push_product',
            'json': data,
            'headers': headers,
        }
        response = await HTTPRequestUtil.request('post', **kwargs)
        log.error(f"推送给数见的结果： {response.content.decode()}")
        f.record_http_response('推送给数见的结果：', response=response)
        check_response(response, '推送给数见的结果')
        data = response.json()
        data['curl'] = HTTPRequestUtil.httpx_request_to_curl(response.request)
        return data


async def push_package_to_dap_public_space(total_state, oss_url, params: RdcParams):
    """
    公共空间，直接触发公共空间的导入
    将包推送给数芯
    """
    with TaskContextManager(
            f'公共空间数芯推包', '调用数芯导入公共空间接口',
            total_state=total_state, will_raise=True
    ) as f:
        # oss_url = await upload_oss(zip_path)
        prue_oss_url = oss_url.split('?')[0] if '?' in oss_url else oss_url
        data = {
            'file_name': os.path.basename(prue_oss_url),
            'file_url': oss_url,
            "app_code": params.appCode,
            'name': f'{params.appName} 版本:{params.version}'
        }
        dap_domain = get_server_domain('dap')
        user = CookiesUser(tenant_code='template')
        headers = await HTTPRequestUtil.build_common_access_headers(user)
        kwargs = {
            'url': f'{dap_domain}/api/common/publish_center/import/public_task',
            'json': data,
            'headers': headers,
        }
        response = await HTTPRequestUtil.request('post', **kwargs)
        log.error(f"推送给数芯公共空间的结果： {response.content.decode()}")
        f.record_http_response('推送给数芯公共空间的结果：', response=response)
        # {"result": false, "data": null, "code": 5000, "msg": "项目空间[ompqjtcszhc]不是公共空间"}
        check_response(response, '获取推送给数芯公共空间的结果')
        data = response.json()
        data['curl'] = HTTPRequestUtil.httpx_request_to_curl(response.request)
        return data


async def dap_tenant_is_custom(total_state, params):
    """
    判断数芯空间是都是定制空间
    """
    log.error(f"调用数芯接口判断是否为定制空间，1： 定制空间，2：公共空间")
    with TaskContextManager(
            f'租户库{params.tenantCode}是否是定制空间', '调用数芯判断接口',
            total_state=total_state, will_raise=True
    ) as f:
        user = CookiesUser(tenant_code=params.tenantCode)
        headers = await HTTPRequestUtil.build_common_access_headers(user)
        dap_domain = get_server_domain('dap')
        kwargs = {
            'url': f'{dap_domain}/api/common/publish_center/project',
            'headers': headers,
        }
        response = await HTTPRequestUtil.request('get', **kwargs)
        log.error(f"获取数芯获取空间信息： {response.content.decode()}")
        f.record_http_response('获取数芯获取空间信息：', response=response)
        if "系统繁忙，请稍后再试" in response.content.decode():
            log.error(f"该租户可能在数芯不存在！")
        check_response(response, '获取数芯获取空间信息')
        return (response.json().get('data') or {}).get('category')


# async def get_dap_import_id_from_custom_space(total_state, params, oss_url):
#     """
#     数芯定制空间
#     获取数芯导入id
#     """
#     with TaskContextManager(
#             f'租户库{params.tenantCode}提前导入定制空间',
#             '得到数芯定制空间导入id',
#             total_state=total_state, will_raise=True
#     ) as f:
#         user = CookiesUser(tenant_code=params.tenantCode)
#         headers = await HTTPRequestUtil.build_common_access_headers(user)
#         dap_domain = get_server_domain('dap')
#         data = {
#             "file_name": os.path.basename(oss_url),
#             "file_url": oss_url,
#             'name': f'{params.appName} 版本：{params.version}',
#             "description": f"发布中心更新: {params.model_dump_json()}"
#         }
#         kwargs = {
#             'url': f'{dap_domain}/api/common/publish_center/import/customize_task',
#             'headers': headers,
#             'json': data
#         }
#         response = await HTTPRequestUtil.request('post', **kwargs)
#         log.error(f"RDC导入数芯的任务返回： {response.content.decode()}")
#         f.record_http_response('RDC导入数芯的任务返回：', response=response)
#         if not response.json().get('result'):
#             raise Exception(
#                 f"数芯提交导入任务失败：{response.content.decode()}, curl: {HTTPRequestUtil.httpx_request_to_curl(response.request)}")
#         return response.json().get('data', '')


async def get_custom_is_contain_app(total_state, params):
    """
    调用数见接口判断是否租户是否包含这个产品
    """
    log.error(f"调用数见接口判断是否是租户是否有包含产品: {params.appCode}")
    with TaskContextManager(
            f'租户库{params.tenantCode}判断是否包含产品',
            f'判断是否包含{params.appCode}',
            total_state=total_state, will_raise=True
    ) as f:
        headers = await HTTPRequestUtil.build_rdc_access_headers({'tenant': params.tenantCode})
        dmp_admin_host = get_server_domain('dmp_admin')

        data = {
            "app_code": params.appCode,
            "tenant_code": params.tenantCode,
        }
        kwargs = {
            'url': f'{dmp_admin_host}/api/publish_center/check_tenant_appcode',
            'headers': headers,
            'json': data
        }
        response = await HTTPRequestUtil.request('post', **kwargs)
        log.error(f"判断是否包含产品返回： {response.content.decode()}")
        f.record_http_response('判断是否包含产品：', response=response)
        check_response(response, '获取数芯是否包含产品')
        return response.json().get('data')


def check_response(response, text):
    try:
        response.json()
    except:
        # 获取curl
        try:
            curl = HTTPRequestUtil.httpx_request_to_curl(response.request)
        except:
            curl = ''
        raise Exception(f'{text}，返回内容有误： {response.content.decode()}, CURL: {curl}')
