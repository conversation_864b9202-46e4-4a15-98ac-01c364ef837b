# #!/usr/bin/env python3
# # -*- coding: utf-8 -*-

from pydantic import BaseModel, Field


class RdcParams(BaseModel):
    file_byte: bytes = b''
    tenantCode: str
    tenantType: str
    taskId: str
    appCode: str
    version: str
    appKey: str
    envCode: str
    customerGuid: str
    operator: str
    businessMode: str  # op：op商务、private：saas私有化、saas：saas公有云
    appType: str  # 应用类型
    appName: str  # 应用名称
    upgradeScene: str  # 更新场景


class ImportSourceMapping(BaseModel):
    op: str
    from_: dict = Field(alias='from')
    to: dict


class ImportRequestParams(BaseModel):
    task_id: str  # 发布中心的id
    request_args: str = ''

    dashboard_datasource: list[ImportSourceMapping]  # 数见参数

    application: list[ImportSourceMapping]
    subject: list[ImportSourceMapping]
    data_source: list[ImportSourceMapping]
    data_permission: list[ImportSourceMapping]
    backup: bool = True

    import_info: dict


class ImportSelectedParams(BaseModel):
    task_id: str
    update_apis: list[str]
    update_models: list[str]
    update_dashboards: list[str]


class ListModifiedReportsParams(BaseModel):
    task_id: str


class ListModelRelationsParams(BaseModel):
    task_id: str
    import_report_name: str  # 实际含义是宽表名
    import_report_id: str  # 宽表code
    data_service_import_report_ids: list[str]
    dashboard_ids: list[str]


class SummaryModelRefsParams(BaseModel):
    task_id: str
    individual_data_model_import_report_ids: list[str]
    data_model_import_report_ids: list[str]
    data_model_import_report_names: list[str]
    individual_data_model_import_names: list[str]
    data_service_import_report_ids: list[str]
    dashboard_ids: list[str]


class TriggerDapImportParams(BaseModel):
    task_id: str


class MergeVersionSourceLogDetail(BaseModel):
    app_name: str
    app_code: str
    version: str
    tenant_code: str


# 跨版本合并的具体日志
# 具体合并到哪个版本中的具体日志
class MergeVersionSourceLog(BaseModel):
    source: MergeVersionSourceLogDetail
    target: MergeVersionSourceLogDetail
