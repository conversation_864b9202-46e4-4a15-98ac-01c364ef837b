#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from fastapi import APIRouter, Query, UploadFile, File, Form

from backend.common.response.response_schema import response_base, ResponseModel
from backend.common.jwt import CookiesUser, DependsJwtUser

from backend.app.import_dist.schema.import_dist import RdcParams, ImportRequestParams, ImportSelectedParams, \
    ListModifiedReportsParams, ListModelRelationsParams, SummaryModelRefsParams, TriggerDapImportParams
from backend.database.db_util import CurrentSession
from backend.common.pagination import DependsPaginationParams, PaginationParams
from backend.app.import_dist.service import import_dist as import_dist_service

router = APIRouter()


# @router.post('/rdc', summary='RDC的推包接口')
# async def rdc(request: fastapi.Request, db: CurrentSession, params: RdcParams):
#     auth = request.headers.get('authorization') or ''
#     rs, _ = await rdc_verify(auth)
#     if not rs:
#         return await response_base.fail(data='jwt校验失败!')
#     data = await dispatch_rdc_package(db, params)
#     # return await response_base.success(data=data)
#     return {
#         "result": True,
#         "msg": "ok",
#         "data": data
#     }


@router.get('/import_log', summary='导出日志获取')
async def import_log(
        db: CurrentSession,
        import_id: str = Query(),
        user: CookiesUser = DependsJwtUser,
) -> ResponseModel:
    export_id = await import_dist_service.get_import_log(db, import_id)
    return ResponseModel(data=export_id)


@router.get('/import_list', summary='导入的列表页')
async def import_list(
        db: CurrentSession,
        pagination: PaginationParams = DependsPaginationParams,
        user: CookiesUser = DependsJwtUser,
        keyword: str = ''
) -> ResponseModel:
    data = await import_dist_service.get_all_imports(db, user, pagination, keyword)
    return ResponseModel(data=data)


@router.get('/rdc_list', summary='查询所有rdc推送记录')
async def rdc_list(
        db: CurrentSession,
        pagination: PaginationParams = DependsPaginationParams,
        user: CookiesUser = DependsJwtUser,
        keyword: str = ''
) -> ResponseModel:
    data = await import_dist_service.get_all_rdc_records(db, user, pagination, keyword)
    return ResponseModel(data=data)


@router.get('/import_detail', summary='导入的列表页')
async def import_detail(
        id: str,
        db: CurrentSession,
        user: CookiesUser = DependsJwtUser,
) -> ResponseModel:
    data = await import_dist_service.get_one_import(db, user, id)
    return ResponseModel(data=data)


@router.post('/import', summary='执行导入')
async def import_action(
        db: CurrentSession,
        params: ImportRequestParams,
        user: CookiesUser = DependsJwtUser,
) -> ResponseModel:
    data = await import_dist_service.do_import(db, user, params)
    return ResponseModel(data=data)


@router.post('/update_check_error', summary='导入页步骤二:汇总提示')
async def summary_model_refs(
        db: CurrentSession,
        params: SummaryModelRefsParams,
        user: CookiesUser = DependsJwtUser,
) -> ResponseModel:
    data = await import_dist_service.summary_model_refs(db, user, params)
    return ResponseModel(data=data)


@router.post('/model_ref_info', summary='导入页步骤二:根据表名查询变动')
async def list_model_relation(
        db: CurrentSession,
        params: ListModelRelationsParams,
        user: CookiesUser = DependsJwtUser,
) -> ResponseModel:
    data = await import_dist_service.list_model_relation(db, user, params)
    return ResponseModel(data=data)


@router.post('/stage', summary='导入页步骤一:要更新的报表和数据服务列表')
async def list_modified_reports(
        db: CurrentSession,
        params: ListModifiedReportsParams,
        user: CookiesUser = DependsJwtUser,
) -> ResponseModel:
    data = await import_dist_service.list_modified_reports(db, user, params)
    return ResponseModel(data=data)


@router.post('/trigger_dap_import', summary='导入页步骤一:触发数芯包的导入动作，得到')
async def trigger_dap_import(
        db: CurrentSession,
        params: TriggerDapImportParams,
        user: CookiesUser = DependsJwtUser,
) -> ResponseModel:
    """
    导入不了不能生成数芯的id执行导入
    """
    data = await import_dist_service.trigger_something_before_import(db, user, params)
    return ResponseModel(data=data)


# @router.post('/check_merge_version', summary='检查是否需要跨版本合并')
# async def check_merge_version(
#         db: CurrentSession,
#         params: TriggerDapImportParams,
#         user: CookiesUser = DependsJwtUser,
# ) -> ResponseModel:
#     """
#     检查是否需要跨版本合并
#     """
#     data = await import_dist_service.check_merge_version(db, user, params)
#     return ResponseModel(data=data)


@router.post('/do_merge_version', summary='跨版本合并')
async def do_merge_version(
        db: CurrentSession,
        params: TriggerDapImportParams,
        user: CookiesUser = DependsJwtUser,
) -> ResponseModel:
    """
    跨版本合并
    """
    rs, txt = await import_dist_service.merge_version(db, user, params)
    return ResponseModel(data={'result': rs, 'merge_msg': txt})


@router.post('/check_update', summary='导入列表页: 检查是否可以更新这条记录')
async def check_update(
        db: CurrentSession,
        params: TriggerDapImportParams,
        user: CookiesUser = DependsJwtUser,
) -> ResponseModel:
    """
    检查是否可以更新这条记录
    """
    data = await import_dist_service.check_update(db, user, params)
    return ResponseModel(data=data)


@router.post('/get_import_dependency', summary='获取导入依赖')
async def get_import_dependency(
        db: CurrentSession,
        params: ImportSelectedParams,
        user: CookiesUser = DependsJwtUser,
) -> ResponseModel:
    data = await import_dist_service.get_import_dependency(db, user, params)
    return ResponseModel(data=data)


@router.post('/get_import_mapping', summary='获取导入映射')
async def get_import_mapping(
        db: CurrentSession,
        params: ImportSelectedParams,
        user: CookiesUser = DependsJwtUser,
) -> ResponseModel:
    data = await import_dist_service.get_import_mapping(db, user, params)
    return ResponseModel(data=data)


@router.post('/import_save', summary='导入的列表页')
async def import_save(
        db: CurrentSession,
        user: CookiesUser = DependsJwtUser,
        file_oss_url: str = Form(...),
        file_name: str = Form(default=''),
        remark: str = Form(default=''),
) -> ResponseModel:
    data = await import_dist_service.import_save(db, user, file_oss_url, file_name, remark)
    return ResponseModel(data=data)


@router.post('/import_reset', summary='导入重置')
async def import_reset(
        db: CurrentSession,
        user: CookiesUser = DependsJwtUser,
        import_id: str = Form(...)
) -> ResponseModel:
    data = await import_dist_service.import_reset(db, user, import_id)
    return ResponseModel(data=data)
