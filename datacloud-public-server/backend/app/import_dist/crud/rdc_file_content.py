from sqlalchemy.orm import Session

from backend.app.import_dist.model.import_dist import RDCFileContent
from backend.database.db_util import CurrentSession


# async def get_rdc_file_content_by_task_id(db: CurrentSession, task_id: str) -> list[RDCFileContent]:
#     """
#     获取导入的文件列表list
#     """
#     query = db.query(RDCFileContent).filter(
#         RDCFileContent.task_id == task_id
#     )
#
#     datas = query.all()
#     return datas
