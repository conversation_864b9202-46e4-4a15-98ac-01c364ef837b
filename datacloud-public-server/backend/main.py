#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import os

os.environ['PROMETHEUS_MULTIPROC_DIR'] = '/tmp'
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).resolve().parent.parent))
import uvicorn

from backend.core.registrar import register_app

app = register_app()

if __name__ == '__main__':
    # 如果你喜欢在 IDE 中进行 DEBUG，main 启动方法会很有帮助
    # 如果你喜欢通过日志方式进行调试，建议使用 fastapi cli 方式启动服务

    uvicorn.run(app=f'{Path(__file__).stem}:app', port=9000, host='0.0.0.0', workers=2)
    # uvicorn.run(app=f'{Path(__file__).stem}:app', reload=True, port=9000, host='0.0.0.0', workers=2)

