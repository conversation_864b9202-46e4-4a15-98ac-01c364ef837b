#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on 2017年10月30日

@author: chenc04
"""
from __future__ import absolute_import

import sys
import json
from datetime import date, datetime
from loguru import logger
from functools import lru_cache
from traceback import FrameSummary
import redis as pyredis
from redis import sentinel
from redis.cluster import RedisCluster
from redis.exceptions import TimeoutError, AuthenticationError
from decimal import Decimal
from backend.common.log import log

from backend.common import config


# redis模式
REDIS_SENTINEL = 'sentinel'
REDIS_STANDALONE = 'standalone'
REDIS_SINGLE = 'single'
REDIS_CLUSTER = 'cluster'

# 定义redis profiling的trace深度
REDIS_PROFILE_TRACE_DEEP = 7


g_redis_connection_pool = None
g_sentinel_obj = None
g_redis_cluster_pool = None


@lru_cache()
def redis_mode():
    try:
        if config.get('Redis.mode') == 'sentinel':
            return REDIS_SENTINEL
        if config.get('Redis.mode') == 'cluster':
            return REDIS_CLUSTER
        else:
            return REDIS_STANDALONE
    except Exception as e:
        logger.error(f"error: {e}", exception=1)
        return REDIS_STANDALONE


class BaseRedis(object):

    def connect(self, db=None):
        raise NotImplementedError("This method has not been implemented yet.")

    def open(self):
        """
        触发初始化连接

        :return:
        """
        try:
            db = config.get('Redis.db', 0)
            conns = self.connect(db)
            if isinstance(conns, tuple):
                conns = conns[0]
            conns.ping()
        except TimeoutError:
            log.error('❌ 数据库 redis 连接超时')
            sys.exit()
        except AuthenticationError:
            log.error('❌ 数据库 redis 连接认证失败')
            sys.exit()
        except Exception as e:
            log.error('❌ 数据库 redis 连接异常 {}', e)
            sys.exit()


class RedisSentinelConnection(BaseRedis):

    def __init__(self, connect_timeout=None):
        self.connect_timeout = (connect_timeout or 3)*1000

    @staticmethod
    def __parse_address(address):
        if ':' not in address:
            return address
        # 按逗号分割字符串，得到多个 "ip:port" 格式的字符串
        ip_port_list = address.split(',')

        # 解析每个 "ip:port" 字符串，将其拆分为 IP 和端口
        result = []
        for item in ip_port_list:
            ip, port = item.split(':')
            result.append((ip, int(port)))  # 将端口转换为整数

        return result

    def connect(self, db=None):
        addresses = self.__parse_address(config.get("Redis.addresses"))
        db = int(config.get('Redis.db', 0)) if db is None else 0
        svc_name = config.get("Redis.svc_name") or 'mymaster'

        global g_sentinel_obj

        if g_sentinel_obj is None:
            g_sentinel_obj = sentinel.Sentinel(
                addresses,
                db=db,  # 数据库，默认为0
                socket_timeout=self.connect_timeout,  # 超时时间,单位为毫秒
                # username=config.get('Redis.username') or '',
                password=config.get('Redis.password')
            )
        master_conn = g_sentinel_obj.master_for(svc_name, socket_timeout=self.connect_timeout)
        slave_conn = g_sentinel_obj.slave_for(svc_name, socket_timeout=self.connect_timeout)

        return master_conn, slave_conn


class RedisStandalonePoolConnection(BaseRedis):

    def __init__(self, connect_timeout=None):
        self.connect_timeout = connect_timeout

    def connect(self, db=None):
        db = int(config.get('Redis.db', 0)) if db is None else 0
        global g_redis_connection_pool
        if g_redis_connection_pool is None:
            g_redis_connection_pool = pyredis.ConnectionPool.from_url(
                url='redis://{username}:{password}@{host}:{port}/{db}'.format(
                    host=config.get("Redis.host"),
                    port=config.get('Redis.port'),
                    username=config.get('Redis.username') or '',
                    db=db,
                    password=config.get('Redis.password')
                ),
                socket_timeout=self.connect_timeout,
                max_connections=int(config.get('Redis.max_connections', 100) or 100),
            )
        return pyredis.StrictRedis(connection_pool=g_redis_connection_pool)


class RedisSingleConnection(BaseRedis):
    def __init__(self, connect_timeout=None):
        self.connect_timeout = connect_timeout

    def connect(self, db=None):
        return pyredis.StrictRedis.from_url(
            url='redis://{username}:{password}@{host}:{port}/{db}'.format(
                host=config.get('Redis.host'),
                port=config.get('Redis.port'),
                username=config.get('Redis.username') or '',
                db=db,
                password=config.get('Redis.password') or ''
            ),
            socket_timeout=self.connect_timeout
        )


class RedisClusterConnection(object):
    def __init__(self, connect_timeout=None):
        self.connect_timeout = connect_timeout

    def connect(self, db=None):
        host = config.get("Redis.host")
        port = int(config.get('Redis.port'))
        # db = int(config.get('Redis.db', 0)) if db is None else 0

        return RedisCluster(
            host=host,
            port=port,
            username=config.get('Redis.username') or '',
            socket_timeout=self.connect_timeout,
            max_connections_per_node=True,
            max_connections=int(config.get('Redis.max_connections', 100) or 100),
            skip_full_coverage_check=True
        )


class RedisConnectionFactory(object):

    def __init__(self, connect_timeout=None):
        self.connect_timeout = connect_timeout or 10

    def create_connection(self, mode=None, db=None):
        if not mode:
            mode = redis_mode()
        if mode == REDIS_SENTINEL:
            connection = RedisSentinelConnection
        elif mode == REDIS_SINGLE:
            connection = RedisSingleConnection
        elif mode == REDIS_CLUSTER:
            connection = RedisClusterConnection
        else:
            connection = RedisStandalonePoolConnection
        return connection(self.connect_timeout).connect(db=db)


def get_redis_conn(db=None, mode=None):
    factory = RedisConnectionFactory()
    if db is None:
        db = config.get('Redis.db', 0)
    conns = factory.create_connection(mode=mode, db=db)
    if isinstance(conns, tuple):
        conns = conns[0]
    return conns


class ObjectEncoder(json.JSONEncoder):
    def default(self, o):  # pylint: disable=method-hidden
        if isinstance(o, Decimal):
            return float(o)
        elif isinstance(o, datetime):
            return o.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(o, date):
            return o.strftime('%Y-%m-%d')
        else:
            return json.JSONEncoder.default(self, o)


redis_client = get_redis_conn()