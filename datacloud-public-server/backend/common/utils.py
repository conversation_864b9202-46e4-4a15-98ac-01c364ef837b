import base64
import datetime
import hashlib
import os
import zipfile
from datetime import timed<PERSON><PERSON>
from functools import lru_cache, wraps

from backend.common.oss.oss import OSSFileProxy
from backend.core.conf import settings


def timed_lru_cache(seconds: int = 120, maxsize: int = 128):
    def wrapper_cache(func):
        func = lru_cache(maxsize=maxsize)(func)
        func.lifetime = timedelta(seconds=seconds)
        func.expiration = datetime.datetime.utcnow() + func.lifetime

        if not hasattr(func, 'lru_cache'):
            setattr(func, 'lru_cache', func)

        @wraps(func)
        def wrapped_func(*args, **kwargs):
            if datetime.datetime.utcnow() >= func.expiration:
                func.cache_clear()
                func.expiration = datetime.datetime.utcnow() + func.lifetime
            return func(*args, **kwargs)

        return wrapped_func

    return wrapper_cache


def unzip_files_to_folder(zip_fp: str, target_folder: str):
    """
    解压文件到指定目录
    """
    os.makedirs(target_folder, exist_ok=True)
    with zipfile.ZipFile(zip_fp, 'r') as zip_ref:
        zip_ref.extractall(target_folder)


def zip_files_from_folders(folders: list, zip_name: str, zip_folder: str) -> str:
    """
    指定一些文件夹打包成zip
    """
    os.makedirs(zip_folder, exist_ok=True)
    fp = os.path.join(zip_folder, zip_name)
    with zipfile.ZipFile(fp, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for folder in folders:
            for root, _, files in os.walk(folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, os.path.join(folder, '..'))  # noqa
                    zipf.write(file_path, arcname)  # noqa
    return fp


def zip_folder_v1(folder_path, zip_name: str, zip_folder: str):
    """
    这个是实现将文件夹打包成zip
    """
    zip_filename = os.path.join(zip_folder, zip_name)
    # 创建一个 Zip 文件
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        # 遍历文件夹及其子文件夹
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                # 获取文件的完整路径
                file_path = os.path.join(root, file)
                # 将文件压缩进 zip 文件，arcname 是为了去除文件夹路径，只保留相对路径
                arcname = os.path.relpath(file_path, start=folder_path)  # noqa
                zipf.write(file_path, arcname=arcname)  # noqa
    return zip_filename


def get_file_md5(file_path: str):
    """
    计算本地文件的md5值
    """
    md5 = hashlib.md5()
    with open(file_path, 'rb') as file:
        for chunk in iter(lambda: file.read(4096), b""):
            md5.update(chunk)
    return md5.hexdigest()


def encode_file_to_base64(file_path: str, chunk_size=4):
    """
    将包文件读取成base64
    """
    base64_encoded_str = ""
    with open(file_path, 'rb') as file:
        base64_encoded_str += base64.b64encode(file.read()).decode('utf-8')
    return base64_encoded_str


def encode_file_to_base64_v2(file_path: str, chunk_size=300):
    """
    将大文件读取成base64编码，确保分块编码结果与整体编码结果一致
    """
    base64_encoded_str = ""
    remainder = b""

    with open(file_path, 'rb') as file:
        while chunk := file.read(chunk_size):
            if len(chunk) < chunk_size:
                remainder = chunk
                break
            encoded_chunk = base64.b64encode(chunk)
            base64_encoded_str += encoded_chunk.decode('utf-8')

    if remainder:
        encoded_chunk = base64.b64encode(remainder)
        base64_encoded_str += encoded_chunk.decode('utf-8')

    return base64_encoded_str


def get_filename(oss_url):
    """
    从oss地址获取文件名称
    """
    return os.path.basename(oss_url.split('?')[0])


def remove_oss_endpoint(oss_url, keep_params=False):
    oss_url = oss_url.strip()
    oss = OSSFileProxy()
    if not keep_params:
        _url = oss_url.split('?')[0]
    else:
        _url = oss_url
    if _url.startswith(oss.proxy.endpoint):
        return _url.replace(oss.proxy.endpoint, '')
    elif hasattr(oss.proxy, 'intranet_host') and _url.startswith(oss.proxy.intranet_host):
        return _url.replace(oss.proxy.intranet_host, '')
    else:
        return _url


async def upload_oss(custom_root: str, file_path: str, filename: str) -> str:
    """
    将任务的文件上传到oss
    """
    # filename = get_file_md5(file_path) + '.zip'  # 真正oss的存储名字使用md5值，解决文件多份重复问题
    root = settings.OSS_PUB_SERVER_BASE_FOLDER_NAME + '/rdc'
    ff_root = os.path.join(root, custom_root)
    with open(file_path, 'rb') as fp:
        oss = OSSFileProxy()
        oss_file_url = oss.upload(fp, file_name=filename, root=ff_root, key=f'{ff_root}/{filename}',
                                  return_intranet_url=True)
        # sigh_url = oss.get_sigh_url(oss_file_url, 604800, is_url=True)
        return oss_file_url


if __name__ == '__main__':
    a = encode_file_to_base64('/tmp/rdc/3a13e037-962f-8093-3413-9fcd37664c06/dmp.zip')
    a2 = encode_file_to_base64_v2('/tmp/rdc/3a13e037-962f-8093-3413-9fcd37664c06/dmp.zip')
    print()
    print(a == a2)
