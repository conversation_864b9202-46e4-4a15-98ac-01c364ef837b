import asyncio
import base64
import os
import time
import traceback
from urllib import parse

import httpx

from jose import jwt

from backend.common.shared_data import get_shared_data
from backend.common.log import log
from backend.core.conf import settings
from backend.common.jwt import CookiesUser, DependsJwtUser
from backend.common.oss.oss import OSSFileProxy


class HTTPRequestUtil:
    """
    HTTP 请求统一管理类
    """

    @staticmethod
    def httpx_request_to_curl(request: httpx.Request) -> str:
        method = request.method
        url = request.url
        headers = ' '.join(f"-H '{k}: {v}'" for k, v in request.headers.items())
        data = f"--data '{request.content.decode()}'" if request.content else ''
        return f"curl -X {method} {headers} {data} '{url}'"

    @staticmethod
    async def request(method: str, is_log=True, **kwargs) -> httpx.Response:
        method = method.lower()
        async with httpx.AsyncClient() as client:
            func = getattr(client, method)
            if 'timeout' not in kwargs:
                kwargs['timeout'] = 120
            try:
                response = await func(**kwargs)
                curl = HTTPRequestUtil.httpx_request_to_curl(response.request)
                HTTPRequestUtil.record_curl(curl)
            except (httpx.ReadTimeout, httpx.ConnectTimeout) as e:
                raise Exception(f"请求接口超时：timeout: {kwargs['timeout']}, url: {kwargs.get('url')}")
            if is_log:
                log.info(f'HTTP CURL: {curl}')
            return response

    @staticmethod
    def record_curl(curl):
        g = get_shared_data()
        g.curls.append(curl)

    @staticmethod
    async def build_dmp_access_token(user: CookiesUser = DependsJwtUser) -> str:
        secret = 'XkVKzY5tcGsCS7Au'
        data = jwt.encode(user.model_dump(), secret, algorithm=settings.TOKEN_ALGORITHM)
        return data

    @staticmethod
    async def build_common_access_headers(user: CookiesUser = DependsJwtUser) -> dict:
        secret = 'XkVKzY5tcGsCS7Au'
        token = jwt.encode(user.model_dump(), secret, algorithm=settings.TOKEN_ALGORITHM)
        headers = {
            'Authorization': f'Bearer {token}',
        }
        return headers

    @staticmethod
    async def build_rdc_access_headers(data: dict) -> dict:
        secret = 'aijqgdgNQII2h7ZyHemQmzQ7fldsNXbVb2bMzkjT8otxRMhrI4R2BUamDn1XzM6n'
        token = jwt.encode(data, secret, algorithm=settings.TOKEN_ALGORITHM)
        headers = {
            'Authorization': f'{token}',
        }
        return headers

    @staticmethod
    async def circle_reqeust(method: str, timeout: int = 5 * 60, check_func=None, **kwargs):
        """
        提供一个指定时间内循环请求某个接口，达到条件再跳出循环
        """
        start_time = time.time()
        has_log_curl = False
        data = None
        while time.time() - start_time <= timeout:
            response = None
            curl = ''
            try:
                response = await HTTPRequestUtil.request(method, is_log=False, **kwargs)
                curl = HTTPRequestUtil.httpx_request_to_curl(response.request)
                if not has_log_curl:
                    log.info(f"开始循环请求， CURL: {curl}")
                    has_log_curl = True
                data = response.json()
                if check_func is not None and check_func(data):
                    return data, curl
            except Exception as e:
                if response:
                    log.error(f'循环请求获取失败：{str(e)}, CURL：{curl}')
                else:
                    log.error(f'循环请求获取失败：{traceback.format_exc(limit=1)}')
            await asyncio.sleep(3)
        raise Exception(f"循环请求获取指定结果超时结果: {method}, {kwargs}, 返回结果：{data}")

    @staticmethod
    async def download_file(folder: str, url: str):
        """
        下载一个资源到本地文件夹
        """
        os.makedirs(folder, exist_ok=True)
        file_path = os.path.basename(url.split('?')[0])
        full_path = os.path.join(folder, file_path)
        chunk_size = 1024
        with httpx.stream('GET', url) as response:
            response.raise_for_status()
            with open(full_path, 'wb') as f:
                for chunk in response.iter_bytes(chunk_size=chunk_size):
                    f.write(chunk)
        return file_path, full_path

    @staticmethod
    def download_oss_file(folder: str, url: str):
        """
        下载一个资源到本地文件夹
        """
        os.makedirs(folder, exist_ok=True)
        file_path = os.path.basename(url.split('?')[0])
        full_path = os.path.join(folder, file_path)
        # chunk_size = 1024
        oss_file = OSSFileProxy()
        # 来自数见的oss，就是同一个oss，直接用客户端获取
        obj_result = oss_file.get_object(parse.unquote(url).split('?')[0], True)

        # with httpx.stream('GET', url) as response:
        #     response.raise_for_status()
        with open(full_path, 'wb') as f:
            # for chunk in response.iter_bytes(chunk_size=chunk_size):
            f.write(obj_result.read())
        return file_path, full_path

    @staticmethod
    def download_oss_file_v3(folder: str, url: str):
        """
        下载一个资源到本地文件夹
        """
        os.makedirs(folder, exist_ok=True)
        file_path = os.path.basename(url.split('?')[0])
        full_path = os.path.join(folder, file_path)
        chunk_size = 1024 * 64
        oss_file = OSSFileProxy()
        # 来自数见的oss
        file_url = parse.unquote(url).split('?')[0]
        # 使用内网加签
        sign_url = oss_file.get_sigh_url(key=file_url, is_url=True, **{'sign_outer_url': False})
        with httpx.stream('GET', sign_url) as response:
            response.raise_for_status()
            with open(full_path, 'wb') as f:
                for chunk in response.iter_bytes(chunk_size=chunk_size):
                    f.write(chunk)
        return file_path, full_path
