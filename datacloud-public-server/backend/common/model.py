#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from datetime import datetime
import random

import math
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column, declared_attr, MappedAsDataclass, declarative_base
from sqlalchemy import String, Column, DateTime, TypeDecorator
from typing_extensions import Annotated
from backend.core.conf import settings
from backend.utils.timezone import timezone

# # 通用 Mapped 类型主键, 需手动添加，参考以下使用方式
# # MappedBase -> id: Mapped[id_key]
# # DataClassBase && Base -> id: Mapped[id_key] = mapped_column(init=False)
# id_key = Annotated[
#     int, mapped_column(primary_key=True, index=True, autoincrement=True, sort_order=-999, comment='主键id')
# ]
#
#
# # Mixin: 一种面向对象编程概念, 使结构变得更加清晰, `Wiki <https://en.wikipedia.org/wiki/Mixin/>`__
# class UserMixin(MappedAsDataclass):
#     """用户 Mixin 数据类"""
#
#     create_user: Mapped[int] = mapped_column(sort_order=998, comment='创建者')
#     update_user: Mapped[int] = mapped_column(init=False, default=None, sort_order=998, comment='修改者')
#
#
# class DateTimeMixin(MappedAsDataclass):
#     """日期时间 Mixin 数据类"""
#
#     created_at: Mapped[datetime] = mapped_column(
#         init=False, default_factory=timezone.now, sort_order=999, comment='创建时间'
#     )
#     updated_at: Mapped[datetime] = mapped_column(
#         init=False, onupdate=timezone.now, sort_order=999, comment='更新时间'
#     )
#
#     created_by: Mapped[str] = mapped_column(
#         String(256),
#         init=True, default='', sort_order=999, comment='创建人'
#     )
#     updated_by: Mapped[str] = mapped_column(
#         String(256),
#         init=True, default='', sort_order=999, comment='更新人',
#     )
#
#
# class MappedBase(DeclarativeBase):
#     """
#     声明性基类, 原始 DeclarativeBase 类, 作为所有基类或数据模型类的父类而存在
#
#     `DeclarativeBase <https://docs.sqlalchemy.org/en/20/orm/declarative_config.html>`__
#     `mapped_column() <https://docs.sqlalchemy.org/en/20/orm/mapping_api.html#sqlalchemy.orm.mapped_column>`__
#     """
#
#     __abstract__ = True
#
#
# class DataClassBase(MappedAsDataclass, MappedBase):
#     """
#     声明性数据类基类, 它将带有数据类集成, 允许使用更高级配置, 但你必须注意它的一些特性, 尤其是和 DeclarativeBase 一起使用时
#
#     `MappedAsDataclass <https://docs.sqlalchemy.org/en/20/orm/dataclasses.html#orm-declarative-native-dataclasses>`__
#     """  # noqa: E501
#
#     __abstract__ = True
#
#
# class Base(DataClassBase, DateTimeMixin):
#     """
#     声明性 Mixin 数据类基类, 带有数据类集成, 并包含 MiXin 数据类基础表结构, 你可以简单的理解它为含有基础表结构的数据类基类
#     """  # noqa: E501
#
#     __abstract__ = True

class Error(Exception):
    pass  # 错误基类


Base = declarative_base()


class FormattedDateTime(TypeDecorator):
    impl = DateTime
    cache_ok = True

    # def process_bind_param(self, value, dialect):
    #     return value

    def process_result_value(self, value, dialect):
        if value is not None:
            return value.strftime("%Y-%m-%d %H:%M:%S")
        return value


# 定义包含通用字段的基类
class BaseModel(Base):
    __abstract__ = True  # 标记为抽象类，不会创建表

    created_at = Column(FormattedDateTime, default=timezone.now_str, nullable=False)
    updated_at = Column(FormattedDateTime, default=timezone.now_str, onupdate=timezone.now_str, nullable=False)
    created_by = Column(String(128), nullable=True)
    updated_by = Column(String(128), nullable=True)

    def formatted_created_at(self):
        return self.created_at.strftime("%Y-%m-%d %H:%M:%S")

    def formatted_updated_at(self):
        return self.updated_at.strftime("%Y-%m-%d %H:%M:%S")

    def to_dict(self):
        return {column.name: getattr(self, column.name) for column in self.__table__.columns}


def table(tb_name: str) -> str:
    """
    处理表名
    """
    if settings.DB_TB_PREFIX:
        return "%s%s" % (settings.DB_TB_PREFIX, tb_name)
    else:
        return tb_name


def _get_random_chars(char_length):
    chars = 'abcdef0123456789'
    i = 0
    res = ''
    while i < char_length:
        idx = math.floor(1 + random.random() * 16)
        res += chars[idx - 1: idx]
        i += 1
    return res


def seq_id():
    """
    获取有序GUID与 db中fn_newSeqId 算法保持一致
    :return:str
    """
    now = datetime.now().timestamp()
    ticks = hex(round(now * 1000000))[2:]
    old_ticks = hex(round(now * 1000 + 62135625600000))[2:]
    return '%s-%s-%s%s-%s-%s' % (
        old_ticks[:8],
        old_ticks[8:12],
        ticks[10:13],
        _get_random_chars(1),
        _get_random_chars(4),
        _get_random_chars(12),
    )
