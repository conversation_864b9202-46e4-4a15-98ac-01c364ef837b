import asyncio
import time
import traceback
from typing import Dict, List
import concurrent.futures

import httpx
from pydantic import BaseModel, Field

from backend.common.log import log
from backend.common.request import HTTPRequestUtil


class PartState(BaseModel):
    """
    执行的片段记录
    """
    level1_name: str = ''
    level2_name: str = ''
    start_time: int = 0  # 执行开始
    end_time: int = 0  # 执行结束
    duration: int = 0  # 执行耗时
    has_error: bool = False  # 是否报错
    short_err: str = ''  # 错误记录
    detail_err: str = ''  # 错误记录
    datas: list[str] = []  # 一些需要直接输出打印出来的日志记录
    detail_datas: list[str] = []  # 一些不需要直接输出打印出来的日志记录，但是很重要，需要排查记录的数据


class TotalState(BaseModel):
    """
    执行的总记录
    """
    name: str = ''
    data: List[PartState] = Field(default_factory=list)
    start_time: int = 0  # 执行开始
    end_time: int = 0  # 执行结束
    duration: int = 0  # 执行总耗时
    has_error: bool = False  # 整个过程是否正常


class TaskContextManager:
    """
    管理执行的任务代码片段，执行状态耗时记录
    """

    def __init__(
            self,
            level1_name: str,
            level2_name: str,
            total_state: TotalState,
            will_raise: bool = False
    ):
        """
        level1_name: 任务记录的一级名称
        level2_name: 任务记录的二级名称
        total_state: 整个任务状态管理对象
        will_raise: 是否抛错，中断执行
        """
        self.level1_name = level1_name
        self.level2_name = level2_name
        # self.error = ''
        self.will_raise = will_raise
        self.total_state = total_state
        self.state = PartState(level1_name=level1_name, level2_name=level2_name)

    def __enter__(self):
        log.debug(f"[{self.level1_name}][{self.level2_name}] 开始执行")
        self.state.start_time = int(time.time() * 1000)
        return self

    def __exit__(self, exc_type, exc_value, exc_tb):
        self.state.end_time = int(time.time() * 1000)
        self.state.duration = self.state.end_time - self.state.start_time
        self.state.has_error = exc_type is not None
        self.__update_total_state_time()
        log.debug(f"[{self.level1_name}][{self.level2_name}] 结束执行. 执行时间: {self.state.duration} ms")
        if exc_type is not None:
            log.error(
                f"[{self.level1_name}][{self.level2_name}] "
                f"Traceback: {''.join(traceback.format_exception(exc_type, exc_value, exc_tb))}"
            )
            self.state.short_err = f' {exc_type.__name__}: {exc_value}'
            self.state.detail_err = f'{"".join(traceback.format_exception(exc_type, exc_value, exc_tb))}'
        self.total_state.data.append(self.state)
        # 返回 True 以抑制异常传播，返回 False 将会重新引发异常
        return not bool(self.will_raise)

    def record(self, record_str: str):
        self.state.datas.append(record_str)

    def record_detail(self, record_str: str):
        self.state.detail_datas.append(record_str)

    def __update_total_state_time(self):
        """
        更新整个过程的执行时间
        """
        if self.total_state.start_time == 0:
            self.total_state.start_time = self.state.start_time
        self.total_state.end_time = self.state.end_time
        self.total_state.duration = self.total_state.end_time - self.total_state.start_time
        if self.state.has_error:
            self.total_state.has_error = True

    def record_http_response(self, text: str, response: httpx.Response):
        """
        这是一个公共组合方法,主要记录http请求类型的日志数据
        """
        if text:
            self.record(text)
        self.record(response.content.decode())
        self.record_detail(HTTPRequestUtil.httpx_request_to_curl(response.request))


class SpawnTaskManager:
    """
    将代码异步执行的管理器
    """

    def __init__(self):
        self.tasks = []
        self._result = {}

    def create_task(self, coro, *, name=None):
        task = asyncio.create_task(coro, name=name)
        self.tasks.append(task)
        return task

    async def wait_and_done(self):
        results = await asyncio.gather(*self.tasks)
        for task, result in zip(self.tasks, results):
            self._result[task] = result

    def get(self, task, default=None):
        return self._result.get(task, default)
