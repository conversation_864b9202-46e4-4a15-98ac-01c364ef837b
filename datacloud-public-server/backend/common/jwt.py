#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import json
from datetime import datetime, timedelta
from typing import Any, List, Tuple, Optional

import fastapi
from asgiref.sync import sync_to_async
from fastapi import Depends, FastAPI, Request, HTTPException, Response
from fastapi.security import O<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from jose import jwt
from passlib.context import CryptContext
from pydantic import ValidationError
from starlette.status import HTTP_401_UNAUTHORIZED
from typing_extensions import Annotated

from backend.common.exception.errors import TokenError, AuthorizationError
from backend.common.log import log
from backend.core.conf import settings
# from backend.app.user.crud.crud_user import UserDao
# from backend.database.db_util import CurrentSession
from pydantic import BaseModel

from backend.app.user.schema.user import LoginTokenPayload
from backend.utils.timezone import timezone

pwd_context = CryptContext(schemes=['bcrypt'], deprecated='auto')

oauth2_schema = OAuth2PasswordBearer(tokenUrl=settings.TOKEN_URL_SWAGGER)

COOKIE_TOKEN_NAME = 'datapubToken'


# cookies中的用户信息
class CookiesUser(BaseModel):
    tenant_code: str
    user_code: str = ''
    user_name: str = ''
    source: str = ''
    expire: int = 0


@sync_to_async
def get_hash_password(password: str) -> str:
    """
    使用 hash 算法加密密码

    :param password: 密码
    :return: 加密后的密码
    """
    return pwd_context.hash(password)


@sync_to_async
def password_verify(plain_password: str, hashed_password: str) -> bool:
    """
    密码校验

    :param plain_password: 要验证的密码
    :param hashed_password: 要比较的hash密码
    :return: 比较密码之后的结果
    """
    return pwd_context.verify(plain_password, hashed_password)


# @sync_to_async
# def create_access_token(data: int, expires_delta: timedelta = None) -> str:
#     """
#     生成加密 token
#
#     :param data: 传进来的值
#     :param expires_delta: 增加的到期时间
#     :return: 加密token
#     """
#     if expires_delta:
#         expires = datetime.utcnow() + expires_delta
#     else:
#         expires = datetime.utcnow() + timedelta(settings.TOKEN_EXPIRE_MINUTES)
#     to_encode = {'exp': expires, 'sub': str(data)}
#     encoded_jwt = jwt.encode(to_encode, settings.TOKEN_SECRET_KEY, settings.TOKEN_ALGORITHM)
#     return encoded_jwt


class TokenGetter:
    """
    token获取依赖
    """

    async def __call__(self, request: Request) -> Optional[CookiesUser]:
        # token = request.cookies.get(COOKIE_TOKEN_NAME)
        # if not token:
        #     raise HTTPException(
        #         status_code=HTTP_401_UNAUTHORIZED,
        #         detail="请求中没有找到token，token缺失",
        #     )
        user = CookiesUser(
            tenant_code=request.cookies.get('dap_project_code', '') or request.cookies.get('tenant_code', ''),
            user_code=request.cookies.get('dap_account', ''),
            user_name=request.cookies.get('dap_account', ''),
        )
        return user


async def get_current_user(user: CookiesUser = Depends(TokenGetter()), response: Response = None) -> CookiesUser:
    """
    通过 token 获取当前用户信息

    :param user: 依赖注入获取的user
    :param response: 依赖注入获取的response
    :return:
    """
    return user
    # try:
    #     # 解密token
    #     # payload = jwt.decode(token, settings.TOKEN_SECRET_KEY, algorithms=[settings.TOKEN_ALGORITHM])
    #     user = CookiesUser(**payload)
    #     # await cookies_expire(user, response)
    # except (jwt.JWTError, ValidationError):
    #     log.error(f"token: {token}")
    #     raise TokenError(msg="jwt解密失败")
    # return user


async def cookies_expire(user: CookiesUser, response: Response = None):
    """
    处理cookies过期和续期
    """
    if user.expire == 0:
        return
    delta = user.expire - timezone.now().timestamp()
    if delta < 0:
        raise TokenError(msg='cookies已经过期')
    if delta < settings.COOKIE_EXPIRE_MINUTES * 60 / 2:
        await set_cookies(user.model_dump(), response)


async def third_verify_login(token: str) -> tuple[bool, str, LoginTokenPayload]:
    """
    集成登录的token校验
    :param token: 用户传入的token
    :return:
    """
    try:
        secret = 'XkVKzY5tcGsCS7Au'
        payload = jwt.decode(token, secret, algorithms=[settings.TOKEN_ALGORITHM])
        model = LoginTokenPayload(**payload)
    except Exception as e:
        log.error(f"jwt解密失败： {str(e)}")
        raise TokenError(msg="jwt解密失败")
    return True, 'jwt校验成功', model


async def rdc_verify(token: str) -> tuple[bool, str]:
    """
    集成登录的token校验
    :param token: 用户传入的token
    :return:
    """
    try:
        secret = 'aijqgdgNQII2h7ZyHemQmzQ7fldsNXbVb2bMzkjT8otxRMhrI4R2BUamDn1XzM6n'
        payload = jwt.decode(token, secret, algorithms=[settings.TOKEN_ALGORITHM])
    except Exception as e:
        log.error(f"RDC jwt解密失败： {str(e)}")
        raise TokenError(msg="jwt解密失败")
    return True, 'jwt校验成功'

async def set_cookies(data: dict, response: fastapi.Response) -> None:
    """
    设置发布中心的cookies
    """
    # from dmplib.components.auth_util import get_cookie_path
    # data['expire'] = int((timezone.now() + timedelta(minutes=settings.COOKIE_EXPIRE_MINUTES)).timestamp())
    # token = jwt.encode(data, settings.TOKEN_SECRET_KEY, algorithm=settings.TOKEN_ALGORITHM)
    # response.set_cookie(key=COOKIE_TOKEN_NAME, value=token, path=get_cookie_path(), httponly=True, max_age=settings.COOKIE_EXPIRE_MINUTES * 60)
    pass


# @sync_to_async
# def superuser_verify(user: User):
#     """
#     验证当前用户是否为超级用户
#
#     :param user:
#     :return:
#     """
#     is_superuser = user.is_superuser
#     if not is_superuser:
#         raise AuthorizationError
#     return is_superuser
#
#
# # 用户依赖注入
# CurrentUser = Annotated[User, Depends(get_current_user)]

# 当前用户权限依赖注入
DependsJwtUser = Depends(get_current_user)
