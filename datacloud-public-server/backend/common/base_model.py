import json
import re
from datetime import date, datetime, time


class Error(Exception):
    pass  # 错误基类


class UserError(Error):
    def __init__(self, code=None, message='', trans=False):
        """
        业务异常基类, 返回message给用户
        :param int code:
        :param str message:
        """

        self.code = code or 500
        self.message = message
        super().__init__()

    def __str__(self):
        return self.message


def string(config, attr_name, value):
    """
    校验字符串
    :param config:
    :param attr_name:
    :param value:
    :return:
    """
    value_length = len(value)
    length = config.get('len')
    max_length = config.get('max')
    min_length = config.get('min')
    if length and value_length != length:
        msg = config.get('msg')
        raise UserError(message=msg if msg else attr_name + 'The length must be ' + str(length))
    elif max_length and value_length > max_length:
        max_msg = config.get('max_msg')
        raise UserError(message=max_msg if max_msg else attr_name + 'Exceeding maximum length ' + str(max_length))
    elif min_length and value_length < min_length:
        min_msg = config.get('min_msg')
        raise UserError(message=min_msg if min_msg else attr_name + 'Insufficient minimum length ' + str(min_length))
    else:
        return True


def in_range(config, attr_name, value):
    """
    校验是否在集合中
    :param config:
    :param attr_name:
    :param value:
    :return:
    """
    rang = config.get('range')
    if rang and value not in rang:
        msg = config.get('msg')
        raise UserError(message=msg if msg else attr_name + 'not in ' + ','.join([str(r) for r in rang]) + ' range')


def match(config, attr_name, value):
    """
    正则表达式匹配
    :param config:
    :param attr_name:
    :param value:
    :return:
    """
    pattern = config.get('pattern')
    if pattern and not re.match(pattern, value):
        msg = config.get('msg')
        raise UserError(message=msg if msg else attr_name + ' Verification failed')


def match_ip(config, attr_name, value):
    """
    IP地址校验
    :param config:
    :param attr_name:
    :param value:
    :return:
    """
    config['pattern'] = r'^((?:(2[0-4]\d)|(25[0-5])|([01]?\d\d?))\\.){3}(?:(2[0-4]\d)|(255[0-5])|([01]?\d\d?))$'
    match(config, attr_name, value)


def match_mail(config, attr_name, value):
    config['pattern'] = r'^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$'
    match(config, attr_name, value)


def array(config, attr_name, value):
    """
    校验list类型
    :param config:
    :param attr_name:
    :param value:
    :return:
    """
    if not isinstance(value, list):
        msg = config.get('msg')
        raise UserError(message=msg if msg else attr_name + ' Must be of type list')
    return True


class ValidatorModel:
    @staticmethod
    def rules():
        return []

    def validate(self):
        rules = self.rules()
        if not rules:
            return
        for rule in rules:
            attr_names = rule[0]
            rule_len = len(rule)
            config = rule[2] if rule_len == 3 else {}
            func_name = rule[1] if rule_len > 1 else None
            self._validate_rule(attr_names if isinstance(attr_names, list) else [attr_names], func_name, config)

    def _validate_rule(self, attr_names, func_name, config):
        """
        校验规则
        :param list attr_names:
        :param str func_name:
        :param dict config:
        :return:
        """
        for attr_name in attr_names:
            if not hasattr(self, attr_name):
                return
            value = getattr(self, attr_name)
            if not value:
                self.validate_required(config, attr_name, value)
            elif func_name:
                validate_func = self.get_validate_func(func_name)
                validate_func(config, attr_name, value)

    @staticmethod
    def validate_required(config, attr_name, value):
        """
        校验必填，所有规则 默认为必填
        :param dict config:
        :param str attr_name:
        :param value:
        :return:
        """
        if config.get('required', True) and not value and value != 0:
            empty_msg = config.get('msg')
            raise UserError(message=empty_msg if empty_msg else attr_name + ' Required')

    def get_validate_func(self, name):
        """
        获取校验函数
        :param name:
        :return:
        """
        try:
            if hasattr(self, name):
                return getattr(self, name)
            return eval(name)
        except NameError as e:
            raise UserError(message='Verification function %s does not exist! error：%s' % (name, str(e)))


class BaseModel(ValidatorModel):
    def __init__(self, **kwargs):
        """
        BaseModel
        :param kwargs:
        """
        self.set_attributes(**kwargs)

    def set_attributes(self, **kwargs):
        """
        将dict数据初始化到对象属性
        :param kwargs:
        :return:
        """
        if not kwargs:
            return
        dirs = [a for a in dir(self) if a[0:1] != '_' and not callable(getattr(self, a))]
        dirs = set(dirs).intersection(kwargs.keys())
        if not dirs:
            return
        for k in dirs:

            attribute = getattr(self, k)
            val = kwargs.get(k)
            if isinstance(attribute, BaseModel):
                if not val:
                    continue
                if isinstance(val, dict):
                    attribute.set_attributes(**val)
                elif isinstance(val, str):
                    attribute.set_attributes(**json.loads(val))
                else:
                    setattr(self, k, val)
            else:
                setattr(self, k, val)

    def get_dict(self, attributes=None):
        """
        获取属性字典
        注：当申明__slots__之后 self.__dict__将为空，必须使用dir
        :param list attributes:
        :return dict:
        """
        attr_dict = {}
        dirs = dir(self)
        if attributes:
            dirs = list(set(dirs).intersection(set(attributes)))
        for attribute in dirs:
            if attribute[0:1] == '_':
                continue
            value = getattr(self, attribute)
            if callable(value):
                continue
            attr_dict[attribute] = value
        return attr_dict

    def get_update_dict(self, attributes=None, sift_out=None):
        """
        获取属性字典，对需要修改的字段进行更新
        注：当申明__slots__之后 self.__dict__将为空，必须使用dir
        :param sift_out:
        :param dict attributes:
        :return dict:
        """
        dirs = dir(self)
        temp_attribute_set = set(dirs)
        attribute_dict = {}
        for attribute in temp_attribute_set:
            if attribute[0:1] == '_':
                continue
            value = getattr(self, attribute)
            if callable(value):
                continue
            if sift_out and attribute in sift_out:
                continue
            attribute_dict[attribute] = value

        if attributes:
            dirs = list(temp_attribute_set.intersection(set(attributes)))

        for col_name in dirs:
            if col_name in attribute_dict.keys():
                attribute_dict[col_name] = attributes[col_name]
        return attribute_dict

    def __str__(self):
        return json.dumps(self.get_dict(), cls=BaseModelEncoder)


class BaseModelEncoder(json.JSONEncoder):
    def default(self, o):  # pylint: disable=E0202
        if isinstance(o, BaseModel):
            return o.get_dict()
        if isinstance(o, datetime):
            return o.strftime('%Y-%m-%d %H:%M:%S')
        if isinstance(o, date):
            return o.strftime('%Y-%m-%d')
        if isinstance(o, time):
            return o.strftime('%H:%M:%S')
        if isinstance(o, bytes):
            return o.decode(encoding='utf-8')
        return json.JSONEncoder.default(self, o)


class QueryBaseModel(BaseModel):
    __slots__ = ['page', 'page_size', 'keyword', 'items', 'total', 'sorts']

    def __init__(self, **kwargs):
        self.page = 1
        self.page_size = 20
        self.keyword = None
        self.items = None
        self.total = None
        self.sorts = None
        super().__init__(**kwargs)
        self.page = int(self.page)
        self.page_size = int(self.page_size)
        self.sorts_to_model()

    @property
    def keyword_escape(self):
        return self.keyword.replace('_', '\\_') if self.keyword else None

    @property
    def skip(self):
        """
        :return int:
        """
        return ((int(self.page) if self.page else 1) - 1) * (int(self.page_size) if self.page_size else 20)

    def get_result_dict(self):
        return self.get_dict(['items', 'total'])

    def sorts_to_model(self):
        if not self.sorts:
            return
        tmp = []
        if isinstance(self.sorts, str):
            try:
                self.sorts = json.loads(self.sorts)
            except json.JSONDecodeError as e:
                raise UserError(message='JSON cannot convert:' + str(e))
        for sort in self.sorts:
            if isinstance(sort, dict):
                model = QuerySortModel(**sort)
                model.validate()
                tmp.append(model)
            elif isinstance(sort, QuerySortModel):
                tmp.append(sort)
        self.sorts = tmp


class QuerySortModel(BaseModel):
    __slots__ = ['id', 'method']

    def __init__(self, **kwargs):
        self.id = None
        self.method = None
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('id', 'string', {'max': 36}))
        rules.append(('method', 'in_range', {'range': ['ASC', 'DESC']}))
        return rules


class RankModel(BaseModel):
    __slots__ = ['source_id', 'target_id', 'table_name', 'rank_col_name', 'id_col_name']

    def __init__(self, **kwargs):
        # 当前数据源id
        self.source_id = None
        # 移动目前位置  若没有，则是移动到末尾
        self.target_id = None

        self.table_name = ''
        self.rank_col_name = ''
        self.id_col_name = ''
        super().__init__(**kwargs)

    def rules(self):
        rules = super().rules()
        rules.append(('source_id', 'string', {'max': 36}))
        rules.append(('target_id', 'string', {'max': 36, 'required': False}))
        rules.append((['table_name', 'rank_col_name', 'id_col_name'],))
        return rules
