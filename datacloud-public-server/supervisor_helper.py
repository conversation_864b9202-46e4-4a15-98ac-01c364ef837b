import configparser
import os
import sys

# 创建 ConfigParser 对象
config = configparser.ConfigParser()

if len(sys.argv) < 3:
    print('缺少文件参数退出，指定supervisor配置文件路径，以及日志文件路径记录临时路径')

fp = sys.argv[1]
record_fp = sys.argv[2]
# 读取配置文件
config.read(fp, encoding='utf-8')

# 初始化一个空列表，用于存储所有项目的配置信息
stderr_logfiles = []
stdout_logfiles = []

# 遍历配置文件中所有的部分
for section_name in config.sections():
    program_config = config[section_name]
    # 获取 stderr_logfile
    if 'stderr_logfile' in program_config:
        ef = program_config.get('stderr_logfile')
        if ef:
            stderr_logfiles.append(ef)
    # 获取 stdout_logfile
    if 'stdout_logfile' in program_config:
        of = program_config.get('stdout_logfile')
        if of:
            stdout_logfiles.append(of)

stderr_logfiles = list(filter(lambda x: x and x not in ['/dev/stderr', '/dev/stdout'], stderr_logfiles))
stdout_logfiles = list(filter(lambda x: x and x not in ['/dev/stderr', '/dev/stdout'], stdout_logfiles))
all_logfiles = stdout_logfiles + stderr_logfiles

# 循环创建所有日志文件
for fp in all_logfiles:
    if not os.path.exists(fp):
        os.makedirs(os.path.dirname(fp), exist_ok=True)
        with open(fp, 'w', encoding='utf-8') as f:
            f.write('')

# 记录所有日志文件
# files_name = 'logs_path.txt'
print(f'The following log file(will stdout to console) has been created:')
with open(record_fp, 'w', encoding='utf-8') as f:
    for fp in all_logfiles:
        print(f"    {fp}")
        f.write(fp + '\n')
