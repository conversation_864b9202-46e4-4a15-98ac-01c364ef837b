FROM docker-prod-registry.cn-hangzhou.cr.aliyuncs.com/dmp/datacloud-pub:base

ENV TZ=Asia/Shanghai
WORKDIR /home/<USER>/webapp

ADD run.sh /tmp/run.sh
COPY . /home/<USER>/webapp

RUN pip install  -r requirements.txt && \
    mkdir -p /etc/supervisor /var/log/supervisor/ && cp /home/<USER>/webapp/deploy/supervisord.conf /etc/supervisor  && \
    cp -rf /home/<USER>/webapp/deploy/supervisor_pkg/*  /usr/local/lib/python3.10/site-packages/supervisor/ && \
    groupadd -g 1001 dmp &&  useradd -m -u 1001 -g dmp dmp  && \
    chown -R 1001:1001 /home/<USER>
    mkdir -p /app && chown -R 1001:1001 /app && \
#    chown -R 1001:1001 /etc && \
    chown -R 1001:1001 /var/log/supervisor/

USER dmp

EXPOSE 9000
EXPOSE 19000

CMD ["bash", "/tmp/run.sh"]
