version: "3.10"

networks:
  fsm_network:
    driver: bridge

volumes:
  fsm_mysql:
  fsm_redis:
  fsm_static:

services:
  fsm_server:
    build:
      context: ../../
      dockerfile: Dockerfile
    container_name: fsm_server
    restart: always
    depends_on:
      - fsm_mysql
      - fsm_redis
    volumes:
      - fsm_static:/fsm/backend/static
    networks:
      - fsm_network
    command:
      - bash
      - -c
      - |
        wait-for-it -s mysql:3306 -s redis:6379 -t 300
        supervisord -c /fsm/deploy/supervisor.conf

  fsm_mysql:
    image: mysql:8.0.29
    ports:
      - "3306:3306"
    container_name: fsm_mysql
    restart: always
    environment:
      MYSQL_DATABASE: fsm
      MYSQL_ROOT_PASSWORD: 123456
      TZ: Asia/Shanghai
    volumes:
      - fba_mysql:/var/lib/mysql
    networks:
      - fsm_network
    command:
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --lower_case_table_names=1

  fsm_redis:
    image: redis:6.2.7
    ports:
      - "6379:6379"
    container_name: fsm_redis
    restart: always
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - fsm_redis:/var/lib/redis
    networks:
      - fsm_network

  fsm_nginx:
    image: nginx
    ports:
      - "8000:80"
    container_name: fsm_nginx
    restart: always
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - fsm_static:/www/fsm/backend/static
    networks:
      - fsm_network
