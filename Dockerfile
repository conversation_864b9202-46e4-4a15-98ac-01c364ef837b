# Main builder stage
#FROM python:3.9.17-buster AS builder
FROM docker-cdv5ju.swr-pro.myhuaweicloud.com/global/base/python:3.9.17-full AS builder

ARG PLATFORM=x86

ENV DM_HOME="/home/<USER>/dmdbms"
ENV LD_LIBRARY_PATH="$DM_HOME/bin:${LD_LIBRARY_PATH}"
ENV PATH="$PATH:$DM_HOME/bin"

COPY ./dmp-vendor/requirements.txt /tmp/vendor_requirements.txt
COPY ./dmp/requirements.txt /tmp/dmp_requirements.txt
COPY ./dmp-flow/requirement.txt /tmp/flow_requirements.txt
COPY ./dmp-flow-feeds/requirement.txt /tmp/flow_feeds_requirements.txt
COPY ./dmp-task-executor/requirements.txt /tmp/task_executor_requirements.txt
COPY ./dmp-admin/requirements.txt /tmp/dmp_admin_requirements.txt
COPY ./datacloud-public-server/requirements.base.txt /tmp/datacloud_public_server_requirements.base.txt
COPY ./datacloud-public-server/requirements.txt /tmp/datacloud_public_server_requirements.txt

# Install Python 3.9 dependencies
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple  && \
    pip install --upgrade pip setuptools   && \
    pip install --no-cache-dir -r /tmp/dmp_requirements.txt   && \
    pip install --no-cache-dir -r /tmp/vendor_requirements.txt   && \
    pip install --no-cache-dir -r /tmp/flow_requirements.txt   && \
    pip install --no-cache-dir -r /tmp/flow_feeds_requirements.txt  && \
    pip install --no-cache-dir -r /tmp/task_executor_requirements.txt && \
    pip install --no-cache-dir -r /tmp/dmp_admin_requirements.txt && \
    pip install --no-cache-dir -r /tmp/datacloud_public_server_requirements.base.txt && \
    pip install --no-cache-dir -r /tmp/datacloud_public_server_requirements.txt

RUN if [ "$PLATFORM" = "arm" ]; then \
#        wget -P /dmp-agent https://mic-open.oss-cn-hangzhou.aliyuncs.com/ops/config-agent/v5/agent-arm64 && mv /dmp-agent/agent-arm64 /dmp-agent/agent && \
        wget -P /home/<USER>/dmdbms https://oss-cn-shenzhen.aliyuncs.com/dmp-test/tools/dm/arm64/dmdbms.zip && \
        cd /home/<USER>/dmdbms && unzip dmdbms.zip && rm -rf dmdbms.zip && \
        mv bin bin_bak && mkdir bin && cp -f bin_bak/libcrypto.so bin_bak/libdmdpi.so bin_bak/libssl.so bin/ && rm -rf bin_bak; \
    else \
#        wget -P /dmp-agent -o agent http://mic-open.oss-cn-hangzhou.aliyuncs.com/ops/agent && \
        wget -P /home/<USER>/dmdbms https://oss-cn-shenzhen.aliyuncs.com/dmp-test/tools/dm/x86/dmdbms.zip && \
        cd /home/<USER>/dmdbms && unzip dmdbms.zip && rm -rf dmdbms.zip && \
        mv bin bin_bak && mkdir bin && cp -f bin_bak/libcrypto.so bin_bak/libdmdpi.so bin_bak/libssl.so bin/ && rm -rf bin_bak; \
    fi

#COPY ./dmp-vendor /tmp/dmp-lib
#RUN cd /tmp/dmp-lib && python setup.py install && \
#    rm -rdf /tmp/dmp-lib && \
RUN cd /home/<USER>/dmdbms/dmPython && pip install . && \
    cd /home/<USER>/dmdbms/sqlalchemy2.0.0 && pip install .

#FROM python:3.9.17-slim-buster
#FROM docker-cdv5ju.swr-pro.myhuaweicloud.com/global/base/python:3.9.17
FROM docker-cdv5ju.swr-pro.myhuaweicloud.com/global/base/python-and-ng-slim-base:**********

ARG PLATFORM=x86

ENV DM_HOME="/home/<USER>/dmdbms"
ENV LD_LIBRARY_PATH="$DM_HOME/bin:${LD_LIBRARY_PATH}"
ENV PATH="$PATH:$DM_HOME/bin"

COPY --from=builder /usr/local/lib/python3.9 /usr/local/lib/python3.9
COPY --from=builder /usr/local/bin/gunicorn /usr/local/bin/gunicorn
COPY --from=builder /usr/local/bin/celery /usr/local/bin/celery
COPY --from=builder /usr/local/bin/fast-boot /usr/local/bin/fast-boot
COPY --from=builder /usr/local/bin/uvicorn /usr/local/bin/uvicorn
COPY --from=builder /home/<USER>/dmdbms /home/<USER>/dmdbms

COPY ./run.sh /tmp/app.sh
COPY ./dmp/ /home/<USER>/webapp/
COPY ./dmp_run.sh /home/<USER>/webapp/dmp-run.sh
COPY ./dmp-flow-feeds/ /home/<USER>/dmp-flow-feeds/
COPY ./dmp-flow/ /home/<USER>/dmp-flow/
COPY ./dmp-task-executor/ /home/<USER>/dmp-task-executor/
COPY ./dmp-admin/ /home/<USER>/dmp-admin/
COPY ./dmp-vendor/dmplib /home/<USER>/dmp-admin/dmplib
COPY ./dmp-vendor/dmplib /home/<USER>/webapp/dmplib
COPY ./datacloud-public-server/ /home/<USER>/datacloud-public-server/
#COPY ./dmp-mcp/ /home/<USER>/dmp-mcp/
#COPY ./dmp-vendor/dmplib /home/<USER>/datacloud-public-server/backend/dmplib

RUN mkdir -p /var/log/supervisor && \
    chmod +x /tmp/app.sh && \
    chmod +x /home/<USER>/webapp/dmp-run.sh && \
#    chmod +x /home/<USER>/dmdbms/bin/dimp && chmod +x /home/<USER>/dmdbms/bin/dexp && \
    rm -rf /usr/local/lib/python3.9/site-packages/gevent/tests/server.key /usr/local/lib/python3.9/site-packages/gevent/tests/test_server.key /usr/local/lib/python3.9/site-packages/tornado/test/test.key

RUN if [ "$PLATFORM" = "arm" ]; then \
        rm -rf /home/<USER>/dmp-task-executor/components/HTPClient && mv /home/<USER>/dmp-task-executor/lib/HTPClient /home/<USER>/dmp-task-executor/components/HTPClient && \
        rm -rf /home/<USER>/dmp-flow-feeds/components/HTPClient && mv /home/<USER>/dmp-flow-feeds/lib/HTPClient /home/<USER>/dmp-flow-feeds/components/HTPClient && \
        rm -rf /home/<USER>/dmp-flow/components/HTPClient && mv /home/<USER>/dmp-flow/lib/HTPClient /home/<USER>/dmp-flow/components/HTPClient && \
        rm -rf /home/<USER>/webapp/components/HTPClient && mv /home/<USER>/webapp/lib/HTPClient /home/<USER>/webapp/components/HTPClient; \
    fi

# nginx
#ADD  ./dmp-web-v2/nginx/default.conf /etc/nginx/conf.d/default.conf
#ADD  ./dmp-web-v2/nginx/nginx.conf /etc/nginx/nginx.conf
#COPY ./dmp-web-v2/test_wechat_support/hBeLsOt727.txt /www/dmp-web/static/hBeLsOt727.txt
#RUN #sed -i 's/dmp:/localhost:/g' /etc/nginx/conf.d/default.conf && \
#    sed -i "s|nginx -g 'daemon off;'|nginx -g 'daemon off;' 2>\&1 | tail -F /var/log/nginx/access.log /var/log/nginx/error.log|g" /tmp/fastTrackLog_env_map.sh && \
#    chmod +x /home/<USER>/dmdbms/bin/dimp && chmod +x /home/<USER>/dmdbms/bin/dexp

#  supervisor自带的webui在ng二级路由反代会有很多问题需要处理下
COPY  supervisor_pkg/web.py /usr/local/lib/python3.9/site-packages/supervisor/web.py
COPY  supervisor_pkg/http.py /usr/local/lib/python3.9/site-packages/supervisor/http.py
COPY supervisor /etc/supervisor

# 添加酷炫大屏的模板
ADD https://oss-cn-shenzhen.aliyuncs.com/dmp-test/tools/dashbaord-templs/dashbaord-templs-v3-20240705.tar /home/<USER>/webapp/design_assets/dashbaord-templs-v3-20240705.tar
RUN cd /home/<USER>/webapp/design_assets/ && \
    tar -zxvf dashbaord-templs-v3-20240705.tar  && rm -rf dashbaord-templs-v3-20240705.tar && cd -

WORKDIR /home/<USER>/

CMD ["/tmp/app.sh"]

EXPOSE 18001
EXPOSE 9000
EXPOSE 9001
EXPOSE 9002
EXPOSE 18081
EXPOSE 8000
EXPOSE 8001
EXPOSE 5555
EXPOSE 80